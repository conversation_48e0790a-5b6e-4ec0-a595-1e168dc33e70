/**
    Body styles when header is fixed or mobile navigation is visivle
 */
.fixedHeader({
    padding-top: @headerHeight;

    .mobileHeader({
        padding-top: @headerHeightMobile;
    })
});

.navigationVisible({
    .mobileHeader({
        overflow: hidden;
    });
});
/**
    End of Body styles
 */

.main-header {
    background: #FFFFFF;
    height: @headerHeight;
    border-bottom: 1px solid @gray-light;

    .mobileHeader({
        .fixedHeaderRules();
        position: static;

        .fixedHeader({
            position: fixed;
        });

        .navigationVisible({
            background-color: @gray-lighter;
        });
    });

    .fixedHeader({
        .fixedHeaderRules();
    });

    .container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100%;

        .desktopHeader({
            justify-content: flex-start;
        });

        &::before, &::after {
            display: none;
        }
    }

    .mobile-navigation-wrap {
        display: none;

        .desktopHeader({
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 100%;
            width: 100%;
        });

        .fixedHeader({
            top: @headerHeightMobile;
        });

        .mobileHeader({
            .navigationVisible({
                display: flex;
                flex-direction: column;
                position: fixed;
                top: @headerHeightMobile;
                left: 0;
                right: 0;
                bottom: 0;
                padding: 1rem;
                background-color: #FFFFFF;
                z-index: 300;
                overflow: auto;
            });
        });
    }

    .mobile-navigation-wrap {
        & > * {
            flex-shrink: 0;

            .breakpointMax(lg, {
                width: 100%;
                max-width: (@container-md - @grid-gutter-width) !important;
                margin-left: auto !important;
                margin-right: auto !important;
            });

            .breakpointMax(md, {
                max-width: (@container-sm - @grid-gutter-width) !important;
            });
        }
    }

    .logo {
        width: 10.4rem;
        height: 2.875rem;
        margin-right: 1rem;
        flex-shrink: 0;

        .breakpoint(xl, {
            margin-right: .rem(28px)[@value];
        });

        .icon {
            max-width: 100%;
            max-height: 100%;
        }
    }

    .main-nav-wrap {
        position: relative;

        .mobileHeader({
            overflow-x: hidden;
            margin-right: 0;
        })
    }

    .menu-button {
        .btn();
        .btn-lighter();
        .fontSize(18px);
        display: none;
        position: relative;
        font-weight: 600;
        padding: 1.1rem 1.1rem 1.1rem .rem(54px)[@value];
        border-bottom-width: 2px;

        .fixedHeader({
            padding: .45rem 1rem .45rem .rem(52px)[@value];
        });

        .mobileHeader({
            padding: .45rem 0 !important;
            background-color: transparent;
            border: transparent;

            &#main-nav-toggle-mobile {
                display: inline-block;
            }
        });

        .desktopHeader({
            &#main-nav-toggle-desktop {
                display: inline-block;
            }
        });

        .content {
            display: none;

            .desktopHeader({
                display: inline-block;
            });
        }

        svg {
            position: absolute;
            top: 1.4rem;
            left: 1.1rem;
            width: .rem(28px)[@value];
            height: .rem(20px)[@value];
            margin-right: .rem(6px)[@value];
            fill: @text-color;

            .fixedHeader({
                top: .7rem;
                left: 1rem;
            });

            .mobileHeader({
                top: -0.1rem !important;
                left: 0 !important;
                margin-left: .rem(-28px)[@value];
            });
        }

        .icon-close {
            transform: scale(0);
        }

        .icon-open {
            transform: scale(1);
        }

        &.active {
            &, &:focus {
                border-bottom: 2px solid @brand-primary !important;
            }

            .icon-open {
                animation: @navigationButtonAnimationDuration menuIconClose forwards;
            }

            .icon-close {
                animation: @navigationButtonAnimationDuration menuIconOpen forwards .25s;
            }
        }

        &.inactive {
            .icon-open {
                transform: scale(0);
                animation: @navigationButtonAnimationDuration menuIconOpen forwards .25s;
            }

            .icon-close {
                animation: @navigationButtonAnimationDuration menuIconClose forwards;
            }
        }

        @keyframes menuIconClose {
            0% {
                transform: scale(1);
            }

            30% {
                transform: scale(1.2);
            }

            100% {
                transform: scale(0);
            }
        }

        @keyframes menuIconOpen {
            0% {
                transform: scale(0);
            }

            70% {
                transform: scale(1.2);
            }

            100% {
                transform: scale(1);
            }
        }
    }

    .mobile-header-icon-buttons {
        display: none;
        flex-shrink: 0;
        margin-left: auto;
        margin-right: 3rem;

        .mobileHeader({
            display: flex;

            .navigationVisible({
                display: none;
            });
        });

        .icon-button-mobile {
            .btn();
            background-color: transparent;
            border: transparent;
            padding: .rem(6px)[@value];
            margin-left: 1rem;

            &:first-child {
                margin-left: 0;
            }

            .icon {
                display: block;
                width: 1.5rem;
                height: 1.5rem;
            }
        }
    }


    .search {
        margin: 0 1rem;
        flex-shrink: 1;

        .mobileHeader({
            margin: 0 0 .rem(22px)[@value];
            order: -1;
        });

        .breakpoint(xl, {
            margin: 0 2rem 0 2rem;
        });

        input {
            border: none;
            height: .rem(58px)[@value];
            padding: 1rem 2.75rem 1rem 0;
            border-bottom: 2px solid @text-color;
            font-size: .rem(18px)[@value];
            border-radius: 0;

            .fixedHeader({
                .mobileSearchInputRules();

                &:focus {
                    padding-right: 10rem;
                }
            });

            .mobileHeader({
                &:focus {
                    border-color: @brand-secondary;
                }
            });

            .mobileHeader({
                .mobileSearchInputRules();
            });

            .customPlaceholder({
                font-size: .rem(18px)[@value];

                .fixedHeader({
                    .mobileSearchInputPlaceholderRules();
                });

                .mobileHeader({
                    .mobileSearchInputPlaceholderRules();
                });
            });

            &:focus {
                .desktopHeader({
                    border-color: @brand-secondary;
                    padding: 1rem 10rem 1rem 0;
                });

            }
        }

        button {
            body & {
                right: 0;
                top: .rem(10px)[@value];
            }

            .fixedHeader({
                right: .rem(4px)[@value];
                top: .rem(4px)[@value];
            });

            .mobileHeader({
                position: static;
                margin-left: .rem(-50px)[@value];
            });
        }

        .search-cancel {
            .desktopHeader({
                right: 8rem;
            });

            .fixedHeader({
                right: 8.25rem;
            });
        }
    }

    .site-settings {
        display: flex;
        justify-content: flex-end;

        .mobileHeader({
            display: none;
        });
    }

    .header-dropdown {
        .btn {
            border: 1px solid @gray-light;
            padding: .rem(6px)[@value] .rem(10px)[@value] .rem(6px)[@value];
            border-radius: .rem(20px)[@value];

            display: flex;
            align-items: center;
            font-weight: 500;
        }

        .icon {
            width: .rem(24px)[@value];
            margin-right: .rem(6px)[@value];
            border-radius: 50%;
        }

        .dropdown-item {
            display: flex;
            align-items: center;
        }
    }

    .language {
        margin-right: .rem(10px)[@value];

        .list-of-languages .dropdown-item {
            .icon {
                width: .rem(24px)[@value];
                margin-right: .rem(6px)[@value];
            }
        }
    }

    .mobile-language {
        display: none;

        .mobileHeader({
            .language();
            display: block;
        });

        &, button {
            width: 100%;
        }

        .mobile-language-open-button {
            margin-left: auto;
            color: @brand-secondary;
        }
    }

    .mobile-account {
        display: none;

        .mobileHeader({
            display: flex;
            justify-content: space-between;
            margin-bottom: 1rem;
        });

        .login-button {
            .mobileHeader({
                height: .rem(36px)[@value];
                width: calc(50% - .375rem);
                flex-basis: calc(50% - .375rem);
                flex-shrink: 0;
                padding: .rem(9px)[@value] .rem(12px)[@value] .rem(9px)[@value];
            });
        }
    }

    .login-button {
        line-height: 1rem;
        font-weight: 600;
        padding: .rem(6px)[@value] .rem(10px)[@value] .rem(6px)[@value];
        border-radius: .rem(20px)[@value];

        .icon {
            width: .rem(24px)[@value];
            height: .rem(24px)[@value];
            margin-right: .rem(6px)[@value];
        }

        span {
            vertical-align: middle;
        }
    }

    .shopping-cart {
        display: flex;
        height: .rem(38px)[@value];
        border-radius: 2rem;
        margin-left: .rem(10px)[@value];

        .icon {
            width: .rem(24px)[@value];
            height: .rem(24px)[@value];
            margin-right: .5rem;
        }
    }


    .quick-menu {
        .desktopHeader({
            .fixedHeader({
                display: none;
            });
        });

        ul {
            display: flex;
            justify-content: flex-end;
            margin-top: .5rem;
            margin-bottom: 0;
            padding: 0;
            list-style-type: none;

            .mobileHeader({
                flex-direction: column;
                margin-bottom: 1.5rem;
            });

            li {
                margin-right: .5rem;

                .mobileHeader({
                    margin-right: 0;
                    margin-bottom: .5rem;
                });

                &:last-child {
                    margin-right: 0;
                }
            }

            a {
                padding: .5rem;
                font-size: .rem(14px)[@value];
                font-weight: 600;
                color: @text-color-light;
            }
        }
    }
}
