@font-face{font-family:lg;src:url(../fonts/lg.ttf?22t19m) format("truetype"),url(../fonts/lg.woff?22t19m) format("woff"),url(../fonts/lg.svg?22t19m#lg) format("svg");font-weight:400;font-style:normal;font-display:block}.lg-icon{font-family:lg!important;speak:never;font-style:normal;font-weight:400;font-variant:normal;text-transform:none;line-height:1;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.lg-actions .lg-next,.lg-actions .lg-prev{background-color:rgba(0,0,0,.45);border-radius:2px;color:#999;cursor:pointer;display:block;font-size:22px;margin-top:-10px;padding:8px 10px 9px;position:absolute;top:50%;z-index:1080;border:none;outline:0}.lg-actions .lg-next.disabled,.lg-actions .lg-prev.disabled{pointer-events:none;opacity:.5}.lg-actions .lg-next:hover,.lg-actions .lg-prev:hover{color:#FFF}.lg-actions .lg-next{right:20px}.lg-actions .lg-next:before{content:"\e095"}.lg-actions .lg-prev{left:20px}.lg-actions .lg-prev:after{content:"\e094"}@-webkit-keyframes lg-right-end{0%,100%{left:0}50%{left:-30px}}@keyframes lg-right-end{0%,100%{left:0}50%{left:-30px}}@-webkit-keyframes lg-left-end{0%,100%{left:0}50%{left:30px}}@keyframes lg-left-end{0%,100%{left:0}50%{left:30px}}.lg-outer.lg-right-end .lg-object{-webkit-animation:lg-right-end .3s;animation:lg-right-end .3s;position:relative}.lg-outer.lg-left-end .lg-object{-webkit-animation:lg-left-end .3s;animation:lg-left-end .3s;position:relative}.lg-toolbar{z-index:1082;left:0;position:absolute;top:0;width:100%;background-color:rgba(0,0,0,.45)}.lg-toolbar .lg-icon{color:#999;cursor:pointer;float:right;font-size:24px;height:47px;line-height:27px;padding:10px 0;text-align:center;width:50px;text-decoration:none!important;outline:0;background:0 0;border:none;-webkit-box-shadow:none;box-shadow:none;-webkit-transition:color .2s linear;transition:color .2s linear}.lg-toolbar .lg-icon:hover{color:#FFF}.lg-toolbar .lg-close:after{content:"\e070"}.lg-toolbar .lg-download:after{content:"\e0f2"}.lg-sub-html{background-color:rgba(0,0,0,.45);bottom:0;color:#EEE;font-size:16px;left:0;padding:10px 40px;position:fixed;right:0;text-align:center;z-index:1080}.lg-sub-html h4{margin:0;font-size:13px;font-weight:700}.lg-sub-html p{font-size:12px;margin:5px 0 0}#lg-counter{color:#999;display:inline-block;font-size:16px;padding-left:20px;padding-top:12px;vertical-align:middle}.lg-next,.lg-prev,.lg-toolbar{opacity:1;-webkit-transition:-webkit-transform .35s cubic-bezier(0,0,.25,1) 0s,opacity .35s cubic-bezier(0,0,.25,1) 0s,color .2s linear;-webkit-transition:opacity .35s cubic-bezier(0,0,.25,1) 0s,color .2s linear,-webkit-transform .35s cubic-bezier(0,0,.25,1) 0s;transition:opacity .35s cubic-bezier(0,0,.25,1) 0s,color .2s linear,-webkit-transform .35s cubic-bezier(0,0,.25,1) 0s;transition:transform .35s cubic-bezier(0,0,.25,1) 0s,opacity .35s cubic-bezier(0,0,.25,1) 0s,color .2s linear;transition:transform .35s cubic-bezier(0,0,.25,1) 0s,opacity .35s cubic-bezier(0,0,.25,1) 0s,color .2s linear,-webkit-transform .35s cubic-bezier(0,0,.25,1) 0s}.lg-hide-items .lg-prev{opacity:0;-webkit-transform:translate3d(-10px,0,0);transform:translate3d(-10px,0,0)}.lg-hide-items .lg-next{opacity:0;-webkit-transform:translate3d(10px,0,0);transform:translate3d(10px,0,0)}.lg-hide-items .lg-toolbar{opacity:0;-webkit-transform:translate3d(0,-10px,0);transform:translate3d(0,-10px,0)}body:not(.lg-from-hash) .lg-outer.lg-start-zoom .lg-object{-webkit-transform:scale3d(.5,.5,.5);transform:scale3d(.5,.5,.5);opacity:0;-webkit-transition:-webkit-transform 250ms cubic-bezier(0,0,.25,1) 0s,opacity 250ms cubic-bezier(0,0,.25,1)!important;-webkit-transition:opacity 250ms cubic-bezier(0,0,.25,1),-webkit-transform 250ms cubic-bezier(0,0,.25,1) 0s!important;transition:opacity 250ms cubic-bezier(0,0,.25,1),-webkit-transform 250ms cubic-bezier(0,0,.25,1) 0s!important;transition:transform 250ms cubic-bezier(0,0,.25,1) 0s,opacity 250ms cubic-bezier(0,0,.25,1)!important;transition:transform 250ms cubic-bezier(0,0,.25,1) 0s,opacity 250ms cubic-bezier(0,0,.25,1),-webkit-transform 250ms cubic-bezier(0,0,.25,1) 0s!important;-webkit-transform-origin:50% 50%;transform-origin:50% 50%}body:not(.lg-from-hash) .lg-outer.lg-start-zoom .lg-item.lg-complete .lg-object{-webkit-transform:scale3d(1,1,1);transform:scale3d(1,1,1);opacity:1}.lg-outer .lg-thumb-outer{background-color:#0D0A0A;bottom:0;position:absolute;width:100%;z-index:1080;max-height:350px;-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0);-webkit-transition:-webkit-transform .25s cubic-bezier(0,0,.25,1) 0s;transition:-webkit-transform .25s cubic-bezier(0,0,.25,1) 0s;transition:transform .25s cubic-bezier(0,0,.25,1) 0s;transition:transform .25s cubic-bezier(0,0,.25,1) 0s, -webkit-transform .25s cubic-bezier(0,0,.25,1) 0s}.lg-outer .lg-thumb-outer.lg-grab .lg-thumb-item{cursor:-webkit-grab;cursor:-o-grab;cursor:-ms-grab;cursor:grab}.lg-outer .lg-thumb-outer.lg-grabbing .lg-thumb-item{cursor:move;cursor:-webkit-grabbing;cursor:-o-grabbing;cursor:-ms-grabbing;cursor:grabbing}.lg-outer .lg-thumb-outer.lg-dragging .lg-thumb{-webkit-transition-duration:0s!important;transition-duration:0s!important}.lg-outer.lg-thumb-open .lg-thumb-outer{-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}.lg-outer .lg-thumb{padding:10px 0;height:100%;margin-bottom:-5px}.lg-outer .lg-thumb-item{cursor:pointer;float:left;overflow:hidden;height:100%;border:2px solid #FFF;border-radius:4px;margin-bottom:5px}@media (min-width:1025px){.lg-outer .lg-thumb-item{-webkit-transition:border-color .25s ease;transition:border-color .25s ease}}.lg-outer .lg-thumb-item.active,.lg-outer .lg-thumb-item:hover{border-color:#a90707}.lg-outer .lg-thumb-item img{width:100%;height:100%;-o-object-fit:cover;object-fit:cover}.lg-outer.lg-has-thumb .lg-item{padding-bottom:120px}.lg-outer.lg-can-toggle .lg-item{padding-bottom:0}.lg-outer.lg-pull-caption-up .lg-sub-html{-webkit-transition:bottom .25s ease;transition:bottom .25s ease}.lg-outer.lg-pull-caption-up.lg-thumb-open .lg-sub-html{bottom:100px}.lg-outer .lg-toogle-thumb{background-color:#0D0A0A;border-radius:2px 2px 0 0;color:#999;cursor:pointer;font-size:24px;height:39px;line-height:27px;padding:5px 0;position:absolute;right:20px;text-align:center;top:-39px;width:50px;outline:0;border:none}.lg-outer .lg-toogle-thumb:after{content:"\e1ff"}.lg-outer .lg-toogle-thumb:hover{color:#FFF}.lg-outer .lg-video-cont{display:inline-block;vertical-align:middle;max-width:1140px;max-height:100%;width:100%;padding:0 5px}.lg-outer .lg-video{width:100%;height:0;padding-bottom:56.25%;overflow:hidden;position:relative}.lg-outer .lg-video .lg-object{display:inline-block;position:absolute;top:0;left:0;width:100%!important;height:100%!important}.lg-outer .lg-video .lg-video-play{width:84px;height:59px;position:absolute;left:50%;top:50%;margin-left:-42px;margin-top:-30px;z-index:1080;cursor:pointer}.lg-outer .lg-has-iframe .lg-video{-webkit-overflow-scrolling:touch;overflow:auto}.lg-outer .lg-has-vimeo .lg-video-play{background:url(../img/vimeo-play.png) no-repeat}.lg-outer .lg-has-vimeo:hover .lg-video-play{background:url(../img/vimeo-play.png) 0 -58px no-repeat}.lg-outer .lg-has-html5 .lg-video-play{background:url(../img/video-play.png) no-repeat;height:64px;margin-left:-32px;margin-top:-32px;width:64px;opacity:.8}.lg-outer .lg-has-html5:hover .lg-video-play{opacity:1}.lg-outer .lg-has-youtube .lg-video-play{background:url(../img/youtube-play.png) no-repeat}.lg-outer .lg-has-youtube:hover .lg-video-play{background:url(../img/youtube-play.png) 0 -60px no-repeat}.lg-outer .lg-video-object{width:100%!important;height:100%!important;position:absolute;top:0;left:0}.lg-outer .lg-has-video .lg-video-object{visibility:hidden}.lg-outer .lg-has-video.lg-video-playing .lg-object,.lg-outer .lg-has-video.lg-video-playing .lg-video-play{display:none}.lg-outer .lg-has-video.lg-video-playing .lg-video-object{visibility:visible}.lg-progress-bar{background-color:#333;height:5px;left:0;position:absolute;top:0;width:100%;z-index:1083;opacity:0;-webkit-transition:opacity 80ms ease 0s;transition:opacity 80ms ease 0s}.lg-progress-bar .lg-progress{background-color:#a90707;height:5px;width:0}.lg-progress-bar.lg-start .lg-progress{width:100%}.lg-show-autoplay .lg-progress-bar{opacity:1}.lg-autoplay-button:after{content:"\e01d"}.lg-show-autoplay .lg-autoplay-button:after{content:"\e01a"}.lg-outer.lg-css3.lg-zoom-dragging .lg-item.lg-complete.lg-zoomable .lg-image,.lg-outer.lg-css3.lg-zoom-dragging .lg-item.lg-complete.lg-zoomable .lg-img-wrap{-webkit-transition-duration:0s;transition-duration:0s}.lg-outer.lg-use-transition-for-zoom .lg-item.lg-complete.lg-zoomable .lg-img-wrap{-webkit-transition:-webkit-transform .3s cubic-bezier(0,0,.25,1) 0s;transition:-webkit-transform .3s cubic-bezier(0,0,.25,1) 0s;transition:transform .3s cubic-bezier(0,0,.25,1) 0s;transition:transform .3s cubic-bezier(0,0,.25,1) 0s, -webkit-transform .3s cubic-bezier(0,0,.25,1) 0s}.lg-outer.lg-use-left-for-zoom .lg-item.lg-complete.lg-zoomable .lg-img-wrap{-webkit-transition:left .3s cubic-bezier(0,0,.25,1) 0s,top .3s cubic-bezier(0,0,.25,1) 0s;transition:left .3s cubic-bezier(0,0,.25,1) 0s,top .3s cubic-bezier(0,0,.25,1) 0s}.lg-outer .lg-item.lg-complete.lg-zoomable .lg-img-wrap{-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0);-webkit-backface-visibility:hidden;backface-visibility:hidden}.lg-outer .lg-item.lg-complete.lg-zoomable .lg-image{-webkit-transform:scale3d(1,1,1);transform:scale3d(1,1,1);-webkit-transition:-webkit-transform .3s cubic-bezier(0,0,.25,1) 0s,opacity .15s!important;-webkit-transition:opacity .15s,-webkit-transform .3s cubic-bezier(0,0,.25,1) 0s!important;transition:opacity .15s,-webkit-transform .3s cubic-bezier(0,0,.25,1) 0s!important;transition:transform .3s cubic-bezier(0,0,.25,1) 0s,opacity .15s!important;transition:transform .3s cubic-bezier(0,0,.25,1) 0s,opacity .15s,-webkit-transform .3s cubic-bezier(0,0,.25,1) 0s!important;-webkit-transform-origin:0 0;transform-origin:0 0;-webkit-backface-visibility:hidden;backface-visibility:hidden}#lg-zoom-in:after{content:"\e311"}#lg-actual-size{font-size:20px}#lg-actual-size:after{content:"\e033"}#lg-zoom-out{opacity:.5;pointer-events:none}#lg-zoom-out:after{content:"\e312"}.lg-zoomed #lg-zoom-out{opacity:1;pointer-events:auto}.lg-outer .lg-pager-outer{bottom:60px;left:0;position:absolute;right:0;text-align:center;z-index:1080;height:10px}.lg-outer .lg-pager-outer.lg-pager-hover .lg-pager-cont{overflow:visible}.lg-outer .lg-pager-cont{cursor:pointer;display:inline-block;overflow:hidden;position:relative;vertical-align:top;margin:0 5px}.lg-outer .lg-pager-cont:hover .lg-pager-thumb-cont{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}.lg-outer .lg-pager-cont.lg-pager-active .lg-pager{-webkit-box-shadow:0 0 0 2px #fff inset;box-shadow:0 0 0 2px #fff inset}.lg-outer .lg-pager-thumb-cont{background-color:#fff;color:#FFF;bottom:100%;height:83px;left:0;margin-bottom:20px;margin-left:-60px;opacity:0;padding:5px;position:absolute;width:120px;border-radius:3px;-webkit-transition:opacity .15s ease 0s,-webkit-transform .15s ease 0s;transition:opacity .15s ease 0s,-webkit-transform .15s ease 0s;transition:opacity .15s ease 0s,transform .15s ease 0s;transition:opacity .15s ease 0s,transform .15s ease 0s,-webkit-transform .15s ease 0s;-webkit-transform:translate3d(0,5px,0);transform:translate3d(0,5px,0)}.lg-outer .lg-pager-thumb-cont img{width:100%;height:100%}.lg-outer .lg-pager{background-color:rgba(255,255,255,.5);border-radius:50%;-webkit-box-shadow:0 0 0 8px rgba(255,255,255,.7) inset;box-shadow:0 0 0 8px rgba(255,255,255,.7) inset;display:block;height:12px;-webkit-transition:box-shadow .3s ease 0s;-webkit-transition:-webkit-box-shadow .3s ease 0s;transition:-webkit-box-shadow .3s ease 0s;transition:box-shadow .3s ease 0s;transition:box-shadow .3s ease 0s, -webkit-box-shadow .3s ease 0s;width:12px}.lg-outer .lg-pager:focus,.lg-outer .lg-pager:hover{-webkit-box-shadow:0 0 0 8px #fff inset;box-shadow:0 0 0 8px #fff inset}.lg-outer .lg-caret{border-left:10px solid transparent;border-right:10px solid transparent;border-top:10px dashed;bottom:-10px;display:inline-block;height:0;left:50%;margin-left:-5px;position:absolute;vertical-align:middle;width:0}.lg-fullscreen:after{content:"\e20c"}.lg-fullscreen-on .lg-fullscreen:after{content:"\e20d"}.lg-outer #lg-dropdown-overlay{background-color:rgba(0,0,0,.25);bottom:0;cursor:default;left:0;position:fixed;right:0;top:0;z-index:1081;opacity:0;visibility:hidden;-webkit-transition:visibility 0s linear .18s,opacity .18s linear 0s;transition:visibility 0s linear .18s,opacity .18s linear 0s}.lg-outer.lg-dropdown-active #lg-dropdown-overlay,.lg-outer.lg-dropdown-active .lg-dropdown{-webkit-transition-delay:0s;transition-delay:0s;-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0);opacity:1;visibility:visible}.lg-outer.lg-dropdown-active #lg-share{color:#FFF}.lg-outer .lg-dropdown{background-color:#fff;border-radius:2px;font-size:14px;list-style-type:none;margin:0;padding:10px 0;position:absolute;right:0;text-align:left;top:50px;opacity:0;visibility:hidden;-webkit-transform:translate3d(0,5px,0);transform:translate3d(0,5px,0);-webkit-transition:-webkit-transform .18s linear 0s,visibility 0s linear .5s,opacity .18s linear 0s;-webkit-transition:visibility 0s linear .5s,opacity .18s linear 0s,-webkit-transform .18s linear 0s;transition:visibility 0s linear .5s,opacity .18s linear 0s,-webkit-transform .18s linear 0s;transition:transform .18s linear 0s,visibility 0s linear .5s,opacity .18s linear 0s;transition:transform .18s linear 0s,visibility 0s linear .5s,opacity .18s linear 0s,-webkit-transform .18s linear 0s}.lg-outer .lg-dropdown:after{content:"";display:block;height:0;width:0;position:absolute;border:8px solid transparent;border-bottom-color:#FFF;right:16px;top:-16px}.lg-outer .lg-dropdown>li:last-child{margin-bottom:0}.lg-outer .lg-dropdown>li:hover .lg-icon,.lg-outer .lg-dropdown>li:hover a{color:#333}.lg-outer .lg-dropdown a{color:#333;display:block;white-space:pre;padding:4px 12px;font-family:"Open Sans","Helvetica Neue",Helvetica,Arial,sans-serif;font-size:12px}.lg-outer .lg-dropdown a:hover{background-color:rgba(0,0,0,.07)}.lg-outer .lg-dropdown .lg-dropdown-text{display:inline-block;line-height:1;margin-top:-3px;vertical-align:middle}.lg-outer .lg-dropdown .lg-icon{color:#333;display:inline-block;float:none;font-size:20px;height:auto;line-height:1;margin-right:8px;padding:0;vertical-align:middle;width:auto}.lg-outer,.lg-outer .lg,.lg-outer .lg-inner{height:100%;width:100%}.lg-outer #lg-share{position:relative}.lg-outer #lg-share:after{content:"\e80d"}.lg-outer #lg-share-facebook .lg-icon{color:#3b5998}.lg-outer #lg-share-facebook .lg-icon:after{content:"\e904"}.lg-outer #lg-share-twitter .lg-icon{color:#00aced}.lg-outer #lg-share-twitter .lg-icon:after{content:"\e907"}.lg-outer #lg-share-googleplus .lg-icon{color:#dd4b39}.lg-outer #lg-share-googleplus .lg-icon:after{content:"\e905"}.lg-outer #lg-share-pinterest .lg-icon{color:#cb2027}.lg-outer #lg-share-pinterest .lg-icon:after{content:"\e906"}.lg-outer .lg-img-rotate{position:absolute;padding:0 5px;left:0;right:0;top:0;bottom:0;-webkit-transition:-webkit-transform .3s cubic-bezier(.32,0,.67,0) 0s;transition:-webkit-transform .3s cubic-bezier(.32,0,.67,0) 0s;transition:transform .3s cubic-bezier(.32,0,.67,0) 0s;transition:transform .3s cubic-bezier(.32,0,.67,0) 0s, -webkit-transform .3s cubic-bezier(.32,0,.67,0) 0s}.lg-rotate-left:after{content:"\e900"}.lg-rotate-right:after{content:"\e901"}.lg-icon.lg-flip-hor,.lg-icon.lg-flip-ver{font-size:26px}.lg-flip-ver:after{content:"\e903"}.lg-flip-hor:after{content:"\e902"}.lg-group:after{content:"";display:table;clear:both}.lg-outer{position:fixed;top:0;left:0;z-index:1050;text-align:left;opacity:0;outline:0;-webkit-transition:opacity .15s ease 0s;transition:opacity .15s ease 0s}.lg-outer *{-webkit-box-sizing:border-box;box-sizing:border-box}.lg-outer.lg-visible{opacity:1}.lg-outer.lg-css3 .lg-item.lg-current,.lg-outer.lg-css3 .lg-item.lg-next-slide,.lg-outer.lg-css3 .lg-item.lg-prev-slide{-webkit-transition-duration:inherit!important;transition-duration:inherit!important;-webkit-transition-timing-function:inherit!important;transition-timing-function:inherit!important}.lg-outer.lg-css3.lg-dragging .lg-item.lg-current,.lg-outer.lg-css3.lg-dragging .lg-item.lg-next-slide,.lg-outer.lg-css3.lg-dragging .lg-item.lg-prev-slide{-webkit-transition-duration:0s!important;transition-duration:0s!important;opacity:1}.lg-outer.lg-grab img.lg-object{cursor:-webkit-grab;cursor:-o-grab;cursor:-ms-grab;cursor:grab}.lg-outer.lg-grabbing img.lg-object{cursor:move;cursor:-webkit-grabbing;cursor:-o-grabbing;cursor:-ms-grabbing;cursor:grabbing}.lg-outer .lg{position:relative;overflow:hidden;margin-left:auto;margin-right:auto;max-width:100%;max-height:100%}.lg-outer .lg-inner{position:absolute;left:0;top:0;white-space:nowrap}.lg-outer .lg-item{background:url(../img/loading.gif) center center no-repeat;display:none!important}.lg-outer.lg-css .lg-current,.lg-outer.lg-css3 .lg-current,.lg-outer.lg-css3 .lg-next-slide,.lg-outer.lg-css3 .lg-prev-slide{display:inline-block!important}.lg-outer .lg-img-wrap,.lg-outer .lg-item{display:inline-block;text-align:center;position:absolute;width:100%;height:100%}.lg-outer .lg-img-wrap:before,.lg-outer .lg-item:before{content:"";display:inline-block;height:50%;width:1px;margin-right:-1px}.lg-outer .lg-img-wrap{position:absolute;padding:0 5px;left:0;right:0;top:0;bottom:0}.lg-outer .lg-item.lg-complete{background-image:none}.lg-outer .lg-item.lg-current{z-index:1060}.lg-outer .lg-image{display:inline-block;vertical-align:middle;max-width:100%;max-height:100%;width:auto!important;height:auto!important}.lg-outer.lg-show-after-load .lg-item .lg-object,.lg-outer.lg-show-after-load .lg-item .lg-video-play{opacity:0;-webkit-transition:opacity .15s ease 0s;transition:opacity .15s ease 0s}.lg-outer.lg-show-after-load .lg-item.lg-complete .lg-object,.lg-outer.lg-show-after-load .lg-item.lg-complete .lg-video-play{opacity:1}.lg-outer .lg-empty-html,.lg-outer.lg-hide-download #lg-download{display:none}.lg-backdrop{position:fixed;top:0;left:0;right:0;bottom:0;z-index:1040;background-color:#000;opacity:0;-webkit-transition:opacity .15s ease 0s;transition:opacity .15s ease 0s}.lg-backdrop.in{opacity:1}.lg-css3.lg-no-trans .lg-current,.lg-css3.lg-no-trans .lg-next-slide,.lg-css3.lg-no-trans .lg-prev-slide{-webkit-transition:none 0s ease 0s!important;transition:none 0s ease 0s!important}.lg-css3.lg-use-css3 .lg-item,.lg-css3.lg-use-left .lg-item{-webkit-backface-visibility:hidden;backface-visibility:hidden}.lg-css3.lg-fade .lg-item{opacity:0}.lg-css3.lg-fade .lg-item.lg-current{opacity:1}.lg-css3.lg-fade .lg-item.lg-current,.lg-css3.lg-fade .lg-item.lg-next-slide,.lg-css3.lg-fade .lg-item.lg-prev-slide{-webkit-transition:opacity .1s ease 0s;transition:opacity .1s ease 0s}.lg-css3.lg-slide.lg-use-css3 .lg-item{opacity:0}.lg-css3.lg-slide.lg-use-css3 .lg-item.lg-prev-slide{-webkit-transform:translate3d(-100%,0,0);transform:translate3d(-100%,0,0)}.lg-css3.lg-slide.lg-use-css3 .lg-item.lg-next-slide{-webkit-transform:translate3d(100%,0,0);transform:translate3d(100%,0,0)}.lg-css3.lg-slide.lg-use-css3 .lg-item.lg-current{-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0);opacity:1}.lg-css3.lg-slide.lg-use-css3 .lg-item.lg-current,.lg-css3.lg-slide.lg-use-css3 .lg-item.lg-next-slide,.lg-css3.lg-slide.lg-use-css3 .lg-item.lg-prev-slide{-webkit-transition:-webkit-transform 1s cubic-bezier(0,0,.25,1) 0s,opacity .1s ease 0s;-webkit-transition:opacity .1s ease 0s,-webkit-transform 1s cubic-bezier(0,0,.25,1) 0s;transition:opacity .1s ease 0s,-webkit-transform 1s cubic-bezier(0,0,.25,1) 0s;transition:transform 1s cubic-bezier(0,0,.25,1) 0s,opacity .1s ease 0s;transition:transform 1s cubic-bezier(0,0,.25,1) 0s,opacity .1s ease 0s,-webkit-transform 1s cubic-bezier(0,0,.25,1) 0s}.lg-css3.lg-slide.lg-use-left .lg-item{opacity:0;position:absolute;left:0}.lg-css3.lg-slide.lg-use-left .lg-item.lg-prev-slide{left:-100%}.lg-css3.lg-slide.lg-use-left .lg-item.lg-next-slide{left:100%}.lg-css3.lg-slide.lg-use-left .lg-item.lg-current{left:0;opacity:1}.lg-css3.lg-slide.lg-use-left .lg-item.lg-current,.lg-css3.lg-slide.lg-use-left .lg-item.lg-next-slide,.lg-css3.lg-slide.lg-use-left .lg-item.lg-prev-slide{-webkit-transition:left 1s cubic-bezier(0,0,.25,1) 0s,opacity .1s ease 0s;transition:left 1s cubic-bezier(0,0,.25,1) 0s,opacity .1s ease 0s}
@charset "UTF-8";
/**
    Bootstrap Config
 */
/**
    Grid
 */
/**
    Container
 */
/**
    Breakpoints
 */
/**
    Typography
 */
/**
    General components
 */
/**
    Button variables
 */
/**
    Bootstrap Dropdown Config
 */
/**
    Bootstrap Breadcrumb Config
 */
/**
    Forms
 */
/**
    Table
 */
/*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */
html {
  font-family: sans-serif;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}
body {
  margin: 0;
}
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
  display: block;
}
audio,
canvas,
progress,
video {
  display: inline-block;
  vertical-align: baseline;
}
audio:not([controls]) {
  display: none;
  height: 0;
}
[hidden],
template {
  display: none;
}
a {
  background-color: transparent;
}
a:active,
a:hover {
  outline: 0;
}
abbr[title] {
  border-bottom: none;
  text-decoration: underline;
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}
b,
strong {
  font-weight: bold;
}
dfn {
  font-style: italic;
}
h1 {
  font-size: 2em;
  margin: 0.67em 0;
}
mark {
  background: #ff0;
  color: #000;
}
small {
  font-size: 80%;
}
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sup {
  top: -0.5em;
}
sub {
  bottom: -0.25em;
}
img {
  border: 0;
}
svg:not(:root) {
  overflow: hidden;
}
figure {
  margin: 1em 40px;
}
hr {
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  height: 0;
}
pre {
  overflow: auto;
}
code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}
button,
input,
optgroup,
select,
textarea {
  color: inherit;
  font: inherit;
  margin: 0;
}
button {
  overflow: visible;
}
button,
select {
  text-transform: none;
}
button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  cursor: pointer;
}
button[disabled],
html input[disabled] {
  cursor: default;
}
button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}
input {
  line-height: normal;
}
input[type="checkbox"],
input[type="radio"] {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding: 0;
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  height: auto;
}
input[type="search"] {
  -webkit-appearance: textfield;
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
}
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}
legend {
  border: 0;
  padding: 0;
}
textarea {
  overflow: auto;
}
optgroup {
  font-weight: bold;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
td,
th {
  padding: 0;
}
/*! Source: https://github.com/h5bp/html5-boilerplate/blob/master/src/css/main.css */
@media print {
  *,
  *:before,
  *:after {
    color: #000 !important;
    text-shadow: none !important;
    background: transparent !important;
    -webkit-box-shadow: none !important;
            box-shadow: none !important;
  }
  a,
  a:visited {
    text-decoration: underline;
  }
  a[href]:after {
    content: " (" attr(href) ")";
  }
  abbr[title]:after {
    content: " (" attr(title) ")";
  }
  a[href^="#"]:after,
  a[href^="javascript:"]:after {
    content: "";
  }
  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }
  thead {
    display: table-header-group;
  }
  tr,
  img {
    page-break-inside: avoid;
  }
  img {
    max-width: 100% !important;
  }
  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }
  h2,
  h3 {
    page-break-after: avoid;
  }
  .navbar {
    display: none;
  }
  .btn > .caret,
  .dropup > .btn > .caret {
    border-top-color: #000 !important;
  }
  .label {
    border: 1px solid #000;
  }
  .table {
    border-collapse: collapse !important;
  }
  .table td,
  .table th {
    background-color: #fff !important;
  }
  .table-bordered th,
  .table-bordered td {
    border: 1px solid #ddd !important;
  }
}
@font-face {
  font-family: "Glyphicons Halflings";
  src: url("../../node_modules/bootstrap/fonts/glyphicons-halflings-regular.eot");
  src: url("../../node_modules/bootstrap/fonts/glyphicons-halflings-regular.eot?#iefix") format("embedded-opentype"), url("../../node_modules/bootstrap/fonts/glyphicons-halflings-regular.woff2") format("woff2"), url("../../node_modules/bootstrap/fonts/glyphicons-halflings-regular.woff") format("woff"), url("../../node_modules/bootstrap/fonts/glyphicons-halflings-regular.ttf") format("truetype"), url("../../node_modules/bootstrap/fonts/glyphicons-halflings-regular.svg#glyphicons_halflingsregular") format("svg");
}
.glyphicon {
  position: relative;
  top: 1px;
  display: inline-block;
  font-family: "Glyphicons Halflings";
  font-style: normal;
  font-weight: 400;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.glyphicon-asterisk:before {
  content: "\002a";
}
.glyphicon-plus:before {
  content: "\002b";
}
.glyphicon-euro:before,
.glyphicon-eur:before {
  content: "\20ac";
}
.glyphicon-minus:before {
  content: "\2212";
}
.glyphicon-cloud:before {
  content: "\2601";
}
.glyphicon-envelope:before {
  content: "\2709";
}
.glyphicon-pencil:before {
  content: "\270f";
}
.glyphicon-glass:before {
  content: "\e001";
}
.glyphicon-music:before {
  content: "\e002";
}
.glyphicon-search:before {
  content: "\e003";
}
.glyphicon-heart:before {
  content: "\e005";
}
.glyphicon-star:before {
  content: "\e006";
}
.glyphicon-star-empty:before {
  content: "\e007";
}
.glyphicon-user:before {
  content: "\e008";
}
.glyphicon-film:before {
  content: "\e009";
}
.glyphicon-th-large:before {
  content: "\e010";
}
.glyphicon-th:before {
  content: "\e011";
}
.glyphicon-th-list:before {
  content: "\e012";
}
.glyphicon-ok:before {
  content: "\e013";
}
.glyphicon-remove:before {
  content: "\e014";
}
.glyphicon-zoom-in:before {
  content: "\e015";
}
.glyphicon-zoom-out:before {
  content: "\e016";
}
.glyphicon-off:before {
  content: "\e017";
}
.glyphicon-signal:before {
  content: "\e018";
}
.glyphicon-cog:before {
  content: "\e019";
}
.glyphicon-trash:before {
  content: "\e020";
}
.glyphicon-home:before {
  content: "\e021";
}
.glyphicon-file:before {
  content: "\e022";
}
.glyphicon-time:before {
  content: "\e023";
}
.glyphicon-road:before {
  content: "\e024";
}
.glyphicon-download-alt:before {
  content: "\e025";
}
.glyphicon-download:before {
  content: "\e026";
}
.glyphicon-upload:before {
  content: "\e027";
}
.glyphicon-inbox:before {
  content: "\e028";
}
.glyphicon-play-circle:before {
  content: "\e029";
}
.glyphicon-repeat:before {
  content: "\e030";
}
.glyphicon-refresh:before {
  content: "\e031";
}
.glyphicon-list-alt:before {
  content: "\e032";
}
.glyphicon-lock:before {
  content: "\e033";
}
.glyphicon-flag:before {
  content: "\e034";
}
.glyphicon-headphones:before {
  content: "\e035";
}
.glyphicon-volume-off:before {
  content: "\e036";
}
.glyphicon-volume-down:before {
  content: "\e037";
}
.glyphicon-volume-up:before {
  content: "\e038";
}
.glyphicon-qrcode:before {
  content: "\e039";
}
.glyphicon-barcode:before {
  content: "\e040";
}
.glyphicon-tag:before {
  content: "\e041";
}
.glyphicon-tags:before {
  content: "\e042";
}
.glyphicon-book:before {
  content: "\e043";
}
.glyphicon-bookmark:before {
  content: "\e044";
}
.glyphicon-print:before {
  content: "\e045";
}
.glyphicon-camera:before {
  content: "\e046";
}
.glyphicon-font:before {
  content: "\e047";
}
.glyphicon-bold:before {
  content: "\e048";
}
.glyphicon-italic:before {
  content: "\e049";
}
.glyphicon-text-height:before {
  content: "\e050";
}
.glyphicon-text-width:before {
  content: "\e051";
}
.glyphicon-align-left:before {
  content: "\e052";
}
.glyphicon-align-center:before {
  content: "\e053";
}
.glyphicon-align-right:before {
  content: "\e054";
}
.glyphicon-align-justify:before {
  content: "\e055";
}
.glyphicon-list:before {
  content: "\e056";
}
.glyphicon-indent-left:before {
  content: "\e057";
}
.glyphicon-indent-right:before {
  content: "\e058";
}
.glyphicon-facetime-video:before {
  content: "\e059";
}
.glyphicon-picture:before {
  content: "\e060";
}
.glyphicon-map-marker:before {
  content: "\e062";
}
.glyphicon-adjust:before {
  content: "\e063";
}
.glyphicon-tint:before {
  content: "\e064";
}
.glyphicon-edit:before {
  content: "\e065";
}
.glyphicon-share:before {
  content: "\e066";
}
.glyphicon-check:before {
  content: "\e067";
}
.glyphicon-move:before {
  content: "\e068";
}
.glyphicon-step-backward:before {
  content: "\e069";
}
.glyphicon-fast-backward:before {
  content: "\e070";
}
.glyphicon-backward:before {
  content: "\e071";
}
.glyphicon-play:before {
  content: "\e072";
}
.glyphicon-pause:before {
  content: "\e073";
}
.glyphicon-stop:before {
  content: "\e074";
}
.glyphicon-forward:before {
  content: "\e075";
}
.glyphicon-fast-forward:before {
  content: "\e076";
}
.glyphicon-step-forward:before {
  content: "\e077";
}
.glyphicon-eject:before {
  content: "\e078";
}
.glyphicon-chevron-left:before {
  content: "\e079";
}
.glyphicon-chevron-right:before {
  content: "\e080";
}
.glyphicon-plus-sign:before {
  content: "\e081";
}
.glyphicon-minus-sign:before {
  content: "\e082";
}
.glyphicon-remove-sign:before {
  content: "\e083";
}
.glyphicon-ok-sign:before {
  content: "\e084";
}
.glyphicon-question-sign:before {
  content: "\e085";
}
.glyphicon-info-sign:before {
  content: "\e086";
}
.glyphicon-screenshot:before {
  content: "\e087";
}
.glyphicon-remove-circle:before {
  content: "\e088";
}
.glyphicon-ok-circle:before {
  content: "\e089";
}
.glyphicon-ban-circle:before {
  content: "\e090";
}
.glyphicon-arrow-left:before {
  content: "\e091";
}
.glyphicon-arrow-right:before {
  content: "\e092";
}
.glyphicon-arrow-up:before {
  content: "\e093";
}
.glyphicon-arrow-down:before {
  content: "\e094";
}
.glyphicon-share-alt:before {
  content: "\e095";
}
.glyphicon-resize-full:before {
  content: "\e096";
}
.glyphicon-resize-small:before {
  content: "\e097";
}
.glyphicon-exclamation-sign:before {
  content: "\e101";
}
.glyphicon-gift:before {
  content: "\e102";
}
.glyphicon-leaf:before {
  content: "\e103";
}
.glyphicon-fire:before {
  content: "\e104";
}
.glyphicon-eye-open:before {
  content: "\e105";
}
.glyphicon-eye-close:before {
  content: "\e106";
}
.glyphicon-warning-sign:before {
  content: "\e107";
}
.glyphicon-plane:before {
  content: "\e108";
}
.glyphicon-calendar:before {
  content: "\e109";
}
.glyphicon-random:before {
  content: "\e110";
}
.glyphicon-comment:before {
  content: "\e111";
}
.glyphicon-magnet:before {
  content: "\e112";
}
.glyphicon-chevron-up:before {
  content: "\e113";
}
.glyphicon-chevron-down:before {
  content: "\e114";
}
.glyphicon-retweet:before {
  content: "\e115";
}
.glyphicon-shopping-cart:before {
  content: "\e116";
}
.glyphicon-folder-close:before {
  content: "\e117";
}
.glyphicon-folder-open:before {
  content: "\e118";
}
.glyphicon-resize-vertical:before {
  content: "\e119";
}
.glyphicon-resize-horizontal:before {
  content: "\e120";
}
.glyphicon-hdd:before {
  content: "\e121";
}
.glyphicon-bullhorn:before {
  content: "\e122";
}
.glyphicon-bell:before {
  content: "\e123";
}
.glyphicon-certificate:before {
  content: "\e124";
}
.glyphicon-thumbs-up:before {
  content: "\e125";
}
.glyphicon-thumbs-down:before {
  content: "\e126";
}
.glyphicon-hand-right:before {
  content: "\e127";
}
.glyphicon-hand-left:before {
  content: "\e128";
}
.glyphicon-hand-up:before {
  content: "\e129";
}
.glyphicon-hand-down:before {
  content: "\e130";
}
.glyphicon-circle-arrow-right:before {
  content: "\e131";
}
.glyphicon-circle-arrow-left:before {
  content: "\e132";
}
.glyphicon-circle-arrow-up:before {
  content: "\e133";
}
.glyphicon-circle-arrow-down:before {
  content: "\e134";
}
.glyphicon-globe:before {
  content: "\e135";
}
.glyphicon-wrench:before {
  content: "\e136";
}
.glyphicon-tasks:before {
  content: "\e137";
}
.glyphicon-filter:before {
  content: "\e138";
}
.glyphicon-briefcase:before {
  content: "\e139";
}
.glyphicon-fullscreen:before {
  content: "\e140";
}
.glyphicon-dashboard:before {
  content: "\e141";
}
.glyphicon-paperclip:before {
  content: "\e142";
}
.glyphicon-heart-empty:before {
  content: "\e143";
}
.glyphicon-link:before {
  content: "\e144";
}
.glyphicon-phone:before {
  content: "\e145";
}
.glyphicon-pushpin:before {
  content: "\e146";
}
.glyphicon-usd:before {
  content: "\e148";
}
.glyphicon-gbp:before {
  content: "\e149";
}
.glyphicon-sort:before {
  content: "\e150";
}
.glyphicon-sort-by-alphabet:before {
  content: "\e151";
}
.glyphicon-sort-by-alphabet-alt:before {
  content: "\e152";
}
.glyphicon-sort-by-order:before {
  content: "\e153";
}
.glyphicon-sort-by-order-alt:before {
  content: "\e154";
}
.glyphicon-sort-by-attributes:before {
  content: "\e155";
}
.glyphicon-sort-by-attributes-alt:before {
  content: "\e156";
}
.glyphicon-unchecked:before {
  content: "\e157";
}
.glyphicon-expand:before {
  content: "\e158";
}
.glyphicon-collapse-down:before {
  content: "\e159";
}
.glyphicon-collapse-up:before {
  content: "\e160";
}
.glyphicon-log-in:before {
  content: "\e161";
}
.glyphicon-flash:before {
  content: "\e162";
}
.glyphicon-log-out:before {
  content: "\e163";
}
.glyphicon-new-window:before {
  content: "\e164";
}
.glyphicon-record:before {
  content: "\e165";
}
.glyphicon-save:before {
  content: "\e166";
}
.glyphicon-open:before {
  content: "\e167";
}
.glyphicon-saved:before {
  content: "\e168";
}
.glyphicon-import:before {
  content: "\e169";
}
.glyphicon-export:before {
  content: "\e170";
}
.glyphicon-send:before {
  content: "\e171";
}
.glyphicon-floppy-disk:before {
  content: "\e172";
}
.glyphicon-floppy-saved:before {
  content: "\e173";
}
.glyphicon-floppy-remove:before {
  content: "\e174";
}
.glyphicon-floppy-save:before {
  content: "\e175";
}
.glyphicon-floppy-open:before {
  content: "\e176";
}
.glyphicon-credit-card:before {
  content: "\e177";
}
.glyphicon-transfer:before {
  content: "\e178";
}
.glyphicon-cutlery:before {
  content: "\e179";
}
.glyphicon-header:before {
  content: "\e180";
}
.glyphicon-compressed:before {
  content: "\e181";
}
.glyphicon-earphone:before {
  content: "\e182";
}
.glyphicon-phone-alt:before {
  content: "\e183";
}
.glyphicon-tower:before {
  content: "\e184";
}
.glyphicon-stats:before {
  content: "\e185";
}
.glyphicon-sd-video:before {
  content: "\e186";
}
.glyphicon-hd-video:before {
  content: "\e187";
}
.glyphicon-subtitles:before {
  content: "\e188";
}
.glyphicon-sound-stereo:before {
  content: "\e189";
}
.glyphicon-sound-dolby:before {
  content: "\e190";
}
.glyphicon-sound-5-1:before {
  content: "\e191";
}
.glyphicon-sound-6-1:before {
  content: "\e192";
}
.glyphicon-sound-7-1:before {
  content: "\e193";
}
.glyphicon-copyright-mark:before {
  content: "\e194";
}
.glyphicon-registration-mark:before {
  content: "\e195";
}
.glyphicon-cloud-download:before {
  content: "\e197";
}
.glyphicon-cloud-upload:before {
  content: "\e198";
}
.glyphicon-tree-conifer:before {
  content: "\e199";
}
.glyphicon-tree-deciduous:before {
  content: "\e200";
}
.glyphicon-cd:before {
  content: "\e201";
}
.glyphicon-save-file:before {
  content: "\e202";
}
.glyphicon-open-file:before {
  content: "\e203";
}
.glyphicon-level-up:before {
  content: "\e204";
}
.glyphicon-copy:before {
  content: "\e205";
}
.glyphicon-paste:before {
  content: "\e206";
}
.glyphicon-alert:before {
  content: "\e209";
}
.glyphicon-equalizer:before {
  content: "\e210";
}
.glyphicon-king:before {
  content: "\e211";
}
.glyphicon-queen:before {
  content: "\e212";
}
.glyphicon-pawn:before {
  content: "\e213";
}
.glyphicon-bishop:before {
  content: "\e214";
}
.glyphicon-knight:before {
  content: "\e215";
}
.glyphicon-baby-formula:before {
  content: "\e216";
}
.glyphicon-tent:before {
  content: "\26fa";
}
.glyphicon-blackboard:before {
  content: "\e218";
}
.glyphicon-bed:before {
  content: "\e219";
}
.glyphicon-apple:before {
  content: "\f8ff";
}
.glyphicon-erase:before {
  content: "\e221";
}
.glyphicon-hourglass:before {
  content: "\231b";
}
.glyphicon-lamp:before {
  content: "\e223";
}
.glyphicon-duplicate:before {
  content: "\e224";
}
.glyphicon-piggy-bank:before {
  content: "\e225";
}
.glyphicon-scissors:before {
  content: "\e226";
}
.glyphicon-bitcoin:before {
  content: "\e227";
}
.glyphicon-btc:before {
  content: "\e227";
}
.glyphicon-xbt:before {
  content: "\e227";
}
.glyphicon-yen:before {
  content: "\00a5";
}
.glyphicon-jpy:before {
  content: "\00a5";
}
.glyphicon-ruble:before {
  content: "\20bd";
}
.glyphicon-rub:before {
  content: "\20bd";
}
.glyphicon-scale:before {
  content: "\e230";
}
.glyphicon-ice-lolly:before {
  content: "\e231";
}
.glyphicon-ice-lolly-tasted:before {
  content: "\e232";
}
.glyphicon-education:before {
  content: "\e233";
}
.glyphicon-option-horizontal:before {
  content: "\e234";
}
.glyphicon-option-vertical:before {
  content: "\e235";
}
.glyphicon-menu-hamburger:before {
  content: "\e236";
}
.glyphicon-modal-window:before {
  content: "\e237";
}
.glyphicon-oil:before {
  content: "\e238";
}
.glyphicon-grain:before {
  content: "\e239";
}
.glyphicon-sunglasses:before {
  content: "\e240";
}
.glyphicon-text-size:before {
  content: "\e241";
}
.glyphicon-text-color:before {
  content: "\e242";
}
.glyphicon-text-background:before {
  content: "\e243";
}
.glyphicon-object-align-top:before {
  content: "\e244";
}
.glyphicon-object-align-bottom:before {
  content: "\e245";
}
.glyphicon-object-align-horizontal:before {
  content: "\e246";
}
.glyphicon-object-align-left:before {
  content: "\e247";
}
.glyphicon-object-align-vertical:before {
  content: "\e248";
}
.glyphicon-object-align-right:before {
  content: "\e249";
}
.glyphicon-triangle-right:before {
  content: "\e250";
}
.glyphicon-triangle-left:before {
  content: "\e251";
}
.glyphicon-triangle-bottom:before {
  content: "\e252";
}
.glyphicon-triangle-top:before {
  content: "\e253";
}
.glyphicon-console:before {
  content: "\e254";
}
.glyphicon-superscript:before {
  content: "\e255";
}
.glyphicon-subscript:before {
  content: "\e256";
}
.glyphicon-menu-left:before {
  content: "\e257";
}
.glyphicon-menu-right:before {
  content: "\e258";
}
.glyphicon-menu-down:before {
  content: "\e259";
}
.glyphicon-menu-up:before {
  content: "\e260";
}
* {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
*:before,
*:after {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
html {
  font-size: 10px;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
body {
  font-family: 'Bai Jamjuree', sans-serif;
  font-size: 16px;
  line-height: 1.5;
  color: #2F3538;
  background-color: #fff;
}
input,
button,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}
a {
  color: #DF0000;
  text-decoration: none;
}
a:hover,
a:focus {
  color: #930000;
  text-decoration: underline;
}
a:focus {
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
  outline: none;
}
figure {
  margin: 0;
}
img {
  vertical-align: middle;
}
.img-responsive,
.thumbnail > img,
.thumbnail a > img,
.carousel-inner > .item > img,
.carousel-inner > .item > a > img {
  display: block;
  max-width: 100%;
  height: auto;
}
.img-rounded {
  border-radius: 6px;
}
.img-thumbnail {
  padding: 4px;
  line-height: 1.5;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  display: inline-block;
  max-width: 100%;
  height: auto;
}
.img-circle {
  border-radius: 50%;
}
hr {
  margin-top: 24px;
  margin-bottom: 24px;
  border: 0;
  border-top: 1px solid #F7F7F7;
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}
.sr-only-focusable:active,
.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto;
}
[role="button"] {
  cursor: pointer;
}
h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  font-family: inherit;
  font-weight: 600;
  line-height: 1.1;
  color: inherit;
}
h1 small,
h2 small,
h3 small,
h4 small,
h5 small,
h6 small,
.h1 small,
.h2 small,
.h3 small,
.h4 small,
.h5 small,
.h6 small,
h1 .small,
h2 .small,
h3 .small,
h4 .small,
h5 .small,
h6 .small,
.h1 .small,
.h2 .small,
.h3 .small,
.h4 .small,
.h5 .small,
.h6 .small {
  font-weight: 400;
  line-height: 1;
  color: #EBEBEB;
}
h1,
.h1,
h2,
.h2,
h3,
.h3 {
  margin-top: 24px;
  margin-bottom: 12px;
}
h1 small,
.h1 small,
h2 small,
.h2 small,
h3 small,
.h3 small,
h1 .small,
.h1 .small,
h2 .small,
.h2 .small,
h3 .small,
.h3 .small {
  font-size: 65%;
}
h4,
.h4,
h5,
.h5,
h6,
.h6 {
  margin-top: 12px;
  margin-bottom: 12px;
}
h4 small,
.h4 small,
h5 small,
.h5 small,
h6 small,
.h6 small,
h4 .small,
.h4 .small,
h5 .small,
.h5 .small,
h6 .small,
.h6 .small {
  font-size: 75%;
}
h1,
.h1 {
  font-size: 2rem;
}
h2,
.h2 {
  font-size: 1.75rem;
}
h3,
.h3 {
  font-size: 1.5rem;
}
h4,
.h4 {
  font-size: 1.25rem;
}
h5,
.h5 {
  font-size: 1.125rem;
}
h6,
.h6 {
  font-size: 1rem;
}
p {
  margin: 0 0 12px;
}
.lead {
  margin-bottom: 24px;
  font-size: 18px;
  font-weight: 300;
  line-height: 1.4;
}
@media (min-width: 576px) {
  .lead {
    font-size: 24px;
  }
}
small,
.small {
  font-size: 87%;
}
mark,
.mark {
  padding: 0.2em;
  background-color: #fcf8e3;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.text-center {
  text-align: center;
}
.text-justify {
  text-align: justify;
}
.text-nowrap {
  white-space: nowrap;
}
.text-lowercase {
  text-transform: lowercase;
}
.text-uppercase {
  text-transform: uppercase;
}
.text-capitalize {
  text-transform: capitalize;
}
.text-muted {
  color: rgba(47, 53, 56, 0.6);
}
.text-primary {
  color: #DF0000;
}
a.text-primary:hover,
a.text-primary:focus {
  color: #ac0000;
}
.text-success {
  color: #3c763d;
}
a.text-success:hover,
a.text-success:focus {
  color: #2b542c;
}
.text-info {
  color: #31708f;
}
a.text-info:hover,
a.text-info:focus {
  color: #245269;
}
.text-warning {
  color: #8a6d3b;
}
a.text-warning:hover,
a.text-warning:focus {
  color: #66512c;
}
.text-danger {
  color: #a94442;
}
a.text-danger:hover,
a.text-danger:focus {
  color: #843534;
}
.bg-primary {
  color: #fff;
  background-color: #DF0000;
}
a.bg-primary:hover,
a.bg-primary:focus {
  background-color: #ac0000;
}
.bg-success {
  background-color: #dff0d8;
}
a.bg-success:hover,
a.bg-success:focus {
  background-color: #c1e2b3;
}
.bg-info {
  background-color: #d9edf7;
}
a.bg-info:hover,
a.bg-info:focus {
  background-color: #afd9ee;
}
.bg-warning {
  background-color: #fcf8e3;
}
a.bg-warning:hover,
a.bg-warning:focus {
  background-color: #f7ecb5;
}
.bg-danger {
  background-color: #f2dede;
}
a.bg-danger:hover,
a.bg-danger:focus {
  background-color: #e4b9b9;
}
.page-header {
  padding-bottom: 11px;
  margin: 48px 0 24px;
  border-bottom: 1px solid #F7F7F7;
}
ul,
ol {
  margin-top: 0;
  margin-bottom: 12px;
}
ul ul,
ol ul,
ul ol,
ol ol {
  margin-bottom: 0;
}
.list-unstyled {
  padding-left: 0;
  list-style: none;
}
.list-inline {
  padding-left: 0;
  list-style: none;
  margin-left: -5px;
}
.list-inline > li {
  display: inline-block;
  padding-right: 5px;
  padding-left: 5px;
}
dl {
  margin-top: 0;
  margin-bottom: 24px;
}
dt,
dd {
  line-height: 1.5;
}
dt {
  font-weight: 700;
}
dd {
  margin-left: 0;
}
@media (min-width: 576px) {
  .dl-horizontal dt {
    float: left;
    width: 160px;
    clear: left;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .dl-horizontal dd {
    margin-left: 180px;
  }
}
abbr[title],
abbr[data-original-title] {
  cursor: help;
}
.initialism {
  font-size: 90%;
  text-transform: uppercase;
}
blockquote {
  padding: 12px 24px;
  margin: 0 0 24px;
  font-size: 20px;
  border-left: 5px solid #F7F7F7;
}
blockquote p:last-child,
blockquote ul:last-child,
blockquote ol:last-child {
  margin-bottom: 0;
}
blockquote footer,
blockquote small,
blockquote .small {
  display: block;
  font-size: 80%;
  line-height: 1.5;
  color: #EBEBEB;
}
blockquote footer:before,
blockquote small:before,
blockquote .small:before {
  content: "\2014 \00A0";
}
.blockquote-reverse,
blockquote.pull-right {
  padding-right: 15px;
  padding-left: 0;
  text-align: right;
  border-right: 5px solid #F7F7F7;
  border-left: 0;
}
.blockquote-reverse footer:before,
blockquote.pull-right footer:before,
.blockquote-reverse small:before,
blockquote.pull-right small:before,
.blockquote-reverse .small:before,
blockquote.pull-right .small:before {
  content: "";
}
.blockquote-reverse footer:after,
blockquote.pull-right footer:after,
.blockquote-reverse small:after,
blockquote.pull-right small:after,
.blockquote-reverse .small:after,
blockquote.pull-right .small:after {
  content: "\00A0 \2014";
}
address {
  margin-bottom: 24px;
  font-style: normal;
  line-height: 1.5;
}
code,
kbd,
pre,
samp {
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
}
code {
  padding: 2px 4px;
  font-size: 90%;
  color: #c7254e;
  background-color: #f9f2f4;
  border-radius: 4px;
}
kbd {
  padding: 2px 4px;
  font-size: 90%;
  color: #fff;
  background-color: #333;
  border-radius: 3px;
  -webkit-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.25);
          box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.25);
}
kbd kbd {
  padding: 0;
  font-size: 100%;
  font-weight: 700;
  -webkit-box-shadow: none;
          box-shadow: none;
}
pre {
  display: block;
  padding: 11.5px;
  margin: 0 0 12px;
  font-size: 15px;
  line-height: 1.5;
  color: #2F3538;
  word-break: break-all;
  word-wrap: break-word;
  background-color: #f5f5f5;
  border: 1px solid #ccc;
  border-radius: 4px;
}
pre code {
  padding: 0;
  font-size: inherit;
  color: inherit;
  white-space: pre-wrap;
  background-color: transparent;
  border-radius: 0;
}
.pre-scrollable {
  max-height: 340px;
  overflow-y: scroll;
}
.container {
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}
@media (min-width: 576px) {
  .container {
    width: 570px;
  }
}
@media (min-width: 768px) {
  .container {
    width: 750px;
  }
}
@media (min-width: 992px) {
  .container {
    width: 990px;
  }
}
.container-fluid {
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}
.row {
  margin-right: -15px;
  margin-left: -15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.row::before,
.row::after {
  display: none;
}
.row-no-gutters {
  margin-right: 0;
  margin-left: 0;
}
.row-no-gutters [class*="col-"] {
  padding-right: 0;
  padding-left: 0;
}
.col-xs-1,
.col-sm-1,
.col-md-1,
.col-lg-1,
.col-xs-2,
.col-sm-2,
.col-md-2,
.col-lg-2,
.col-xs-3,
.col-sm-3,
.col-md-3,
.col-lg-3,
.col-xs-4,
.col-sm-4,
.col-md-4,
.col-lg-4,
.col-xs-5,
.col-sm-5,
.col-md-5,
.col-lg-5,
.col-xs-6,
.col-sm-6,
.col-md-6,
.col-lg-6,
.col-xs-7,
.col-sm-7,
.col-md-7,
.col-lg-7,
.col-xs-8,
.col-sm-8,
.col-md-8,
.col-lg-8,
.col-xs-9,
.col-sm-9,
.col-md-9,
.col-lg-9,
.col-xs-10,
.col-sm-10,
.col-md-10,
.col-lg-10,
.col-xs-11,
.col-sm-11,
.col-md-11,
.col-lg-11,
.col-xs-12,
.col-sm-12,
.col-md-12,
.col-lg-12 {
  position: relative;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
}
.col-xs-1,
.col-xs-2,
.col-xs-3,
.col-xs-4,
.col-xs-5,
.col-xs-6,
.col-xs-7,
.col-xs-8,
.col-xs-9,
.col-xs-10,
.col-xs-11,
.col-xs-12 {
  float: left;
}
.col-xs-12 {
  width: 100%;
}
.col-xs-12 {
  width: 100%;
  -ms-flex-preferred-size: 100%;
      flex-basis: 100%;
  float: none;
}
.col-xs-11 {
  width: 91.66666667%;
}
.col-xs-11 {
  width: 91.66666667%;
  -ms-flex-preferred-size: 91.66666667%;
      flex-basis: 91.66666667%;
  float: none;
}
.col-xs-10 {
  width: 83.33333333%;
}
.col-xs-10 {
  width: 83.33333333%;
  -ms-flex-preferred-size: 83.33333333%;
      flex-basis: 83.33333333%;
  float: none;
}
.col-xs-9 {
  width: 75%;
}
.col-xs-9 {
  width: 75%;
  -ms-flex-preferred-size: 75%;
      flex-basis: 75%;
  float: none;
}
.col-xs-8 {
  width: 66.66666667%;
}
.col-xs-8 {
  width: 66.66666667%;
  -ms-flex-preferred-size: 66.66666667%;
      flex-basis: 66.66666667%;
  float: none;
}
.col-xs-7 {
  width: 58.33333333%;
}
.col-xs-7 {
  width: 58.33333333%;
  -ms-flex-preferred-size: 58.33333333%;
      flex-basis: 58.33333333%;
  float: none;
}
.col-xs-6 {
  width: 50%;
}
.col-xs-6 {
  width: 50%;
  -ms-flex-preferred-size: 50%;
      flex-basis: 50%;
  float: none;
}
.col-xs-5 {
  width: 41.66666667%;
}
.col-xs-5 {
  width: 41.66666667%;
  -ms-flex-preferred-size: 41.66666667%;
      flex-basis: 41.66666667%;
  float: none;
}
.col-xs-4 {
  width: 33.33333333%;
}
.col-xs-4 {
  width: 33.33333333%;
  -ms-flex-preferred-size: 33.33333333%;
      flex-basis: 33.33333333%;
  float: none;
}
.col-xs-3 {
  width: 25%;
}
.col-xs-3 {
  width: 25%;
  -ms-flex-preferred-size: 25%;
      flex-basis: 25%;
  float: none;
}
.col-xs-2 {
  width: 16.66666667%;
}
.col-xs-2 {
  width: 16.66666667%;
  -ms-flex-preferred-size: 16.66666667%;
      flex-basis: 16.66666667%;
  float: none;
}
.col-xs-1 {
  width: 8.33333333%;
}
.col-xs-1 {
  width: 8.33333333%;
  -ms-flex-preferred-size: 8.33333333%;
      flex-basis: 8.33333333%;
  float: none;
}
.col-xs-pull-12 {
  right: 100%;
}
.col-xs-pull-11 {
  right: 91.66666667%;
}
.col-xs-pull-10 {
  right: 83.33333333%;
}
.col-xs-pull-9 {
  right: 75%;
}
.col-xs-pull-8 {
  right: 66.66666667%;
}
.col-xs-pull-7 {
  right: 58.33333333%;
}
.col-xs-pull-6 {
  right: 50%;
}
.col-xs-pull-5 {
  right: 41.66666667%;
}
.col-xs-pull-4 {
  right: 33.33333333%;
}
.col-xs-pull-3 {
  right: 25%;
}
.col-xs-pull-2 {
  right: 16.66666667%;
}
.col-xs-pull-1 {
  right: 8.33333333%;
}
.col-xs-pull-0 {
  right: auto;
}
.col-xs-push-12 {
  left: 100%;
}
.col-xs-push-11 {
  left: 91.66666667%;
}
.col-xs-push-10 {
  left: 83.33333333%;
}
.col-xs-push-9 {
  left: 75%;
}
.col-xs-push-8 {
  left: 66.66666667%;
}
.col-xs-push-7 {
  left: 58.33333333%;
}
.col-xs-push-6 {
  left: 50%;
}
.col-xs-push-5 {
  left: 41.66666667%;
}
.col-xs-push-4 {
  left: 33.33333333%;
}
.col-xs-push-3 {
  left: 25%;
}
.col-xs-push-2 {
  left: 16.66666667%;
}
.col-xs-push-1 {
  left: 8.33333333%;
}
.col-xs-push-0 {
  left: auto;
}
.col-xs-offset-12 {
  margin-left: 100%;
}
.col-xs-offset-11 {
  margin-left: 91.66666667%;
}
.col-xs-offset-10 {
  margin-left: 83.33333333%;
}
.col-xs-offset-9 {
  margin-left: 75%;
}
.col-xs-offset-8 {
  margin-left: 66.66666667%;
}
.col-xs-offset-7 {
  margin-left: 58.33333333%;
}
.col-xs-offset-6 {
  margin-left: 50%;
}
.col-xs-offset-5 {
  margin-left: 41.66666667%;
}
.col-xs-offset-4 {
  margin-left: 33.33333333%;
}
.col-xs-offset-3 {
  margin-left: 25%;
}
.col-xs-offset-2 {
  margin-left: 16.66666667%;
}
.col-xs-offset-1 {
  margin-left: 8.33333333%;
}
.col-xs-offset-0 {
  margin-left: 0%;
}
@media (min-width: 576px) {
  .col-sm-1,
  .col-sm-2,
  .col-sm-3,
  .col-sm-4,
  .col-sm-5,
  .col-sm-6,
  .col-sm-7,
  .col-sm-8,
  .col-sm-9,
  .col-sm-10,
  .col-sm-11,
  .col-sm-12 {
    float: left;
  }
  .col-sm-12 {
    width: 100%;
  }
  .col-sm-12 {
    width: 100%;
    -ms-flex-preferred-size: 100%;
        flex-basis: 100%;
    float: none;
  }
  .col-sm-11 {
    width: 91.66666667%;
  }
  .col-sm-11 {
    width: 91.66666667%;
    -ms-flex-preferred-size: 91.66666667%;
        flex-basis: 91.66666667%;
    float: none;
  }
  .col-sm-10 {
    width: 83.33333333%;
  }
  .col-sm-10 {
    width: 83.33333333%;
    -ms-flex-preferred-size: 83.33333333%;
        flex-basis: 83.33333333%;
    float: none;
  }
  .col-sm-9 {
    width: 75%;
  }
  .col-sm-9 {
    width: 75%;
    -ms-flex-preferred-size: 75%;
        flex-basis: 75%;
    float: none;
  }
  .col-sm-8 {
    width: 66.66666667%;
  }
  .col-sm-8 {
    width: 66.66666667%;
    -ms-flex-preferred-size: 66.66666667%;
        flex-basis: 66.66666667%;
    float: none;
  }
  .col-sm-7 {
    width: 58.33333333%;
  }
  .col-sm-7 {
    width: 58.33333333%;
    -ms-flex-preferred-size: 58.33333333%;
        flex-basis: 58.33333333%;
    float: none;
  }
  .col-sm-6 {
    width: 50%;
  }
  .col-sm-6 {
    width: 50%;
    -ms-flex-preferred-size: 50%;
        flex-basis: 50%;
    float: none;
  }
  .col-sm-5 {
    width: 41.66666667%;
  }
  .col-sm-5 {
    width: 41.66666667%;
    -ms-flex-preferred-size: 41.66666667%;
        flex-basis: 41.66666667%;
    float: none;
  }
  .col-sm-4 {
    width: 33.33333333%;
  }
  .col-sm-4 {
    width: 33.33333333%;
    -ms-flex-preferred-size: 33.33333333%;
        flex-basis: 33.33333333%;
    float: none;
  }
  .col-sm-3 {
    width: 25%;
  }
  .col-sm-3 {
    width: 25%;
    -ms-flex-preferred-size: 25%;
        flex-basis: 25%;
    float: none;
  }
  .col-sm-2 {
    width: 16.66666667%;
  }
  .col-sm-2 {
    width: 16.66666667%;
    -ms-flex-preferred-size: 16.66666667%;
        flex-basis: 16.66666667%;
    float: none;
  }
  .col-sm-1 {
    width: 8.33333333%;
  }
  .col-sm-1 {
    width: 8.33333333%;
    -ms-flex-preferred-size: 8.33333333%;
        flex-basis: 8.33333333%;
    float: none;
  }
  .col-sm-pull-12 {
    right: 100%;
  }
  .col-sm-pull-11 {
    right: 91.66666667%;
  }
  .col-sm-pull-10 {
    right: 83.33333333%;
  }
  .col-sm-pull-9 {
    right: 75%;
  }
  .col-sm-pull-8 {
    right: 66.66666667%;
  }
  .col-sm-pull-7 {
    right: 58.33333333%;
  }
  .col-sm-pull-6 {
    right: 50%;
  }
  .col-sm-pull-5 {
    right: 41.66666667%;
  }
  .col-sm-pull-4 {
    right: 33.33333333%;
  }
  .col-sm-pull-3 {
    right: 25%;
  }
  .col-sm-pull-2 {
    right: 16.66666667%;
  }
  .col-sm-pull-1 {
    right: 8.33333333%;
  }
  .col-sm-pull-0 {
    right: auto;
  }
  .col-sm-push-12 {
    left: 100%;
  }
  .col-sm-push-11 {
    left: 91.66666667%;
  }
  .col-sm-push-10 {
    left: 83.33333333%;
  }
  .col-sm-push-9 {
    left: 75%;
  }
  .col-sm-push-8 {
    left: 66.66666667%;
  }
  .col-sm-push-7 {
    left: 58.33333333%;
  }
  .col-sm-push-6 {
    left: 50%;
  }
  .col-sm-push-5 {
    left: 41.66666667%;
  }
  .col-sm-push-4 {
    left: 33.33333333%;
  }
  .col-sm-push-3 {
    left: 25%;
  }
  .col-sm-push-2 {
    left: 16.66666667%;
  }
  .col-sm-push-1 {
    left: 8.33333333%;
  }
  .col-sm-push-0 {
    left: auto;
  }
  .col-sm-offset-12 {
    margin-left: 100%;
  }
  .col-sm-offset-11 {
    margin-left: 91.66666667%;
  }
  .col-sm-offset-10 {
    margin-left: 83.33333333%;
  }
  .col-sm-offset-9 {
    margin-left: 75%;
  }
  .col-sm-offset-8 {
    margin-left: 66.66666667%;
  }
  .col-sm-offset-7 {
    margin-left: 58.33333333%;
  }
  .col-sm-offset-6 {
    margin-left: 50%;
  }
  .col-sm-offset-5 {
    margin-left: 41.66666667%;
  }
  .col-sm-offset-4 {
    margin-left: 33.33333333%;
  }
  .col-sm-offset-3 {
    margin-left: 25%;
  }
  .col-sm-offset-2 {
    margin-left: 16.66666667%;
  }
  .col-sm-offset-1 {
    margin-left: 8.33333333%;
  }
  .col-sm-offset-0 {
    margin-left: 0%;
  }
}
@media (min-width: 768px) {
  .col-md-1,
  .col-md-2,
  .col-md-3,
  .col-md-4,
  .col-md-5,
  .col-md-6,
  .col-md-7,
  .col-md-8,
  .col-md-9,
  .col-md-10,
  .col-md-11,
  .col-md-12 {
    float: left;
  }
  .col-md-12 {
    width: 100%;
  }
  .col-md-12 {
    width: 100%;
    -ms-flex-preferred-size: 100%;
        flex-basis: 100%;
    float: none;
  }
  .col-md-11 {
    width: 91.66666667%;
  }
  .col-md-11 {
    width: 91.66666667%;
    -ms-flex-preferred-size: 91.66666667%;
        flex-basis: 91.66666667%;
    float: none;
  }
  .col-md-10 {
    width: 83.33333333%;
  }
  .col-md-10 {
    width: 83.33333333%;
    -ms-flex-preferred-size: 83.33333333%;
        flex-basis: 83.33333333%;
    float: none;
  }
  .col-md-9 {
    width: 75%;
  }
  .col-md-9 {
    width: 75%;
    -ms-flex-preferred-size: 75%;
        flex-basis: 75%;
    float: none;
  }
  .col-md-8 {
    width: 66.66666667%;
  }
  .col-md-8 {
    width: 66.66666667%;
    -ms-flex-preferred-size: 66.66666667%;
        flex-basis: 66.66666667%;
    float: none;
  }
  .col-md-7 {
    width: 58.33333333%;
  }
  .col-md-7 {
    width: 58.33333333%;
    -ms-flex-preferred-size: 58.33333333%;
        flex-basis: 58.33333333%;
    float: none;
  }
  .col-md-6 {
    width: 50%;
  }
  .col-md-6 {
    width: 50%;
    -ms-flex-preferred-size: 50%;
        flex-basis: 50%;
    float: none;
  }
  .col-md-5 {
    width: 41.66666667%;
  }
  .col-md-5 {
    width: 41.66666667%;
    -ms-flex-preferred-size: 41.66666667%;
        flex-basis: 41.66666667%;
    float: none;
  }
  .col-md-4 {
    width: 33.33333333%;
  }
  .col-md-4 {
    width: 33.33333333%;
    -ms-flex-preferred-size: 33.33333333%;
        flex-basis: 33.33333333%;
    float: none;
  }
  .col-md-3 {
    width: 25%;
  }
  .col-md-3 {
    width: 25%;
    -ms-flex-preferred-size: 25%;
        flex-basis: 25%;
    float: none;
  }
  .col-md-2 {
    width: 16.66666667%;
  }
  .col-md-2 {
    width: 16.66666667%;
    -ms-flex-preferred-size: 16.66666667%;
        flex-basis: 16.66666667%;
    float: none;
  }
  .col-md-1 {
    width: 8.33333333%;
  }
  .col-md-1 {
    width: 8.33333333%;
    -ms-flex-preferred-size: 8.33333333%;
        flex-basis: 8.33333333%;
    float: none;
  }
  .col-md-pull-12 {
    right: 100%;
  }
  .col-md-pull-11 {
    right: 91.66666667%;
  }
  .col-md-pull-10 {
    right: 83.33333333%;
  }
  .col-md-pull-9 {
    right: 75%;
  }
  .col-md-pull-8 {
    right: 66.66666667%;
  }
  .col-md-pull-7 {
    right: 58.33333333%;
  }
  .col-md-pull-6 {
    right: 50%;
  }
  .col-md-pull-5 {
    right: 41.66666667%;
  }
  .col-md-pull-4 {
    right: 33.33333333%;
  }
  .col-md-pull-3 {
    right: 25%;
  }
  .col-md-pull-2 {
    right: 16.66666667%;
  }
  .col-md-pull-1 {
    right: 8.33333333%;
  }
  .col-md-pull-0 {
    right: auto;
  }
  .col-md-push-12 {
    left: 100%;
  }
  .col-md-push-11 {
    left: 91.66666667%;
  }
  .col-md-push-10 {
    left: 83.33333333%;
  }
  .col-md-push-9 {
    left: 75%;
  }
  .col-md-push-8 {
    left: 66.66666667%;
  }
  .col-md-push-7 {
    left: 58.33333333%;
  }
  .col-md-push-6 {
    left: 50%;
  }
  .col-md-push-5 {
    left: 41.66666667%;
  }
  .col-md-push-4 {
    left: 33.33333333%;
  }
  .col-md-push-3 {
    left: 25%;
  }
  .col-md-push-2 {
    left: 16.66666667%;
  }
  .col-md-push-1 {
    left: 8.33333333%;
  }
  .col-md-push-0 {
    left: auto;
  }
  .col-md-offset-12 {
    margin-left: 100%;
  }
  .col-md-offset-11 {
    margin-left: 91.66666667%;
  }
  .col-md-offset-10 {
    margin-left: 83.33333333%;
  }
  .col-md-offset-9 {
    margin-left: 75%;
  }
  .col-md-offset-8 {
    margin-left: 66.66666667%;
  }
  .col-md-offset-7 {
    margin-left: 58.33333333%;
  }
  .col-md-offset-6 {
    margin-left: 50%;
  }
  .col-md-offset-5 {
    margin-left: 41.66666667%;
  }
  .col-md-offset-4 {
    margin-left: 33.33333333%;
  }
  .col-md-offset-3 {
    margin-left: 25%;
  }
  .col-md-offset-2 {
    margin-left: 16.66666667%;
  }
  .col-md-offset-1 {
    margin-left: 8.33333333%;
  }
  .col-md-offset-0 {
    margin-left: 0%;
  }
}
@media (min-width: 992px) {
  .col-lg-1,
  .col-lg-2,
  .col-lg-3,
  .col-lg-4,
  .col-lg-5,
  .col-lg-6,
  .col-lg-7,
  .col-lg-8,
  .col-lg-9,
  .col-lg-10,
  .col-lg-11,
  .col-lg-12 {
    float: left;
  }
  .col-lg-12 {
    width: 100%;
  }
  .col-lg-12 {
    width: 100%;
    -ms-flex-preferred-size: 100%;
        flex-basis: 100%;
    float: none;
  }
  .col-lg-11 {
    width: 91.66666667%;
  }
  .col-lg-11 {
    width: 91.66666667%;
    -ms-flex-preferred-size: 91.66666667%;
        flex-basis: 91.66666667%;
    float: none;
  }
  .col-lg-10 {
    width: 83.33333333%;
  }
  .col-lg-10 {
    width: 83.33333333%;
    -ms-flex-preferred-size: 83.33333333%;
        flex-basis: 83.33333333%;
    float: none;
  }
  .col-lg-9 {
    width: 75%;
  }
  .col-lg-9 {
    width: 75%;
    -ms-flex-preferred-size: 75%;
        flex-basis: 75%;
    float: none;
  }
  .col-lg-8 {
    width: 66.66666667%;
  }
  .col-lg-8 {
    width: 66.66666667%;
    -ms-flex-preferred-size: 66.66666667%;
        flex-basis: 66.66666667%;
    float: none;
  }
  .col-lg-7 {
    width: 58.33333333%;
  }
  .col-lg-7 {
    width: 58.33333333%;
    -ms-flex-preferred-size: 58.33333333%;
        flex-basis: 58.33333333%;
    float: none;
  }
  .col-lg-6 {
    width: 50%;
  }
  .col-lg-6 {
    width: 50%;
    -ms-flex-preferred-size: 50%;
        flex-basis: 50%;
    float: none;
  }
  .col-lg-5 {
    width: 41.66666667%;
  }
  .col-lg-5 {
    width: 41.66666667%;
    -ms-flex-preferred-size: 41.66666667%;
        flex-basis: 41.66666667%;
    float: none;
  }
  .col-lg-4 {
    width: 33.33333333%;
  }
  .col-lg-4 {
    width: 33.33333333%;
    -ms-flex-preferred-size: 33.33333333%;
        flex-basis: 33.33333333%;
    float: none;
  }
  .col-lg-3 {
    width: 25%;
  }
  .col-lg-3 {
    width: 25%;
    -ms-flex-preferred-size: 25%;
        flex-basis: 25%;
    float: none;
  }
  .col-lg-2 {
    width: 16.66666667%;
  }
  .col-lg-2 {
    width: 16.66666667%;
    -ms-flex-preferred-size: 16.66666667%;
        flex-basis: 16.66666667%;
    float: none;
  }
  .col-lg-1 {
    width: 8.33333333%;
  }
  .col-lg-1 {
    width: 8.33333333%;
    -ms-flex-preferred-size: 8.33333333%;
        flex-basis: 8.33333333%;
    float: none;
  }
  .col-lg-pull-12 {
    right: 100%;
  }
  .col-lg-pull-11 {
    right: 91.66666667%;
  }
  .col-lg-pull-10 {
    right: 83.33333333%;
  }
  .col-lg-pull-9 {
    right: 75%;
  }
  .col-lg-pull-8 {
    right: 66.66666667%;
  }
  .col-lg-pull-7 {
    right: 58.33333333%;
  }
  .col-lg-pull-6 {
    right: 50%;
  }
  .col-lg-pull-5 {
    right: 41.66666667%;
  }
  .col-lg-pull-4 {
    right: 33.33333333%;
  }
  .col-lg-pull-3 {
    right: 25%;
  }
  .col-lg-pull-2 {
    right: 16.66666667%;
  }
  .col-lg-pull-1 {
    right: 8.33333333%;
  }
  .col-lg-pull-0 {
    right: auto;
  }
  .col-lg-push-12 {
    left: 100%;
  }
  .col-lg-push-11 {
    left: 91.66666667%;
  }
  .col-lg-push-10 {
    left: 83.33333333%;
  }
  .col-lg-push-9 {
    left: 75%;
  }
  .col-lg-push-8 {
    left: 66.66666667%;
  }
  .col-lg-push-7 {
    left: 58.33333333%;
  }
  .col-lg-push-6 {
    left: 50%;
  }
  .col-lg-push-5 {
    left: 41.66666667%;
  }
  .col-lg-push-4 {
    left: 33.33333333%;
  }
  .col-lg-push-3 {
    left: 25%;
  }
  .col-lg-push-2 {
    left: 16.66666667%;
  }
  .col-lg-push-1 {
    left: 8.33333333%;
  }
  .col-lg-push-0 {
    left: auto;
  }
  .col-lg-offset-12 {
    margin-left: 100%;
  }
  .col-lg-offset-11 {
    margin-left: 91.66666667%;
  }
  .col-lg-offset-10 {
    margin-left: 83.33333333%;
  }
  .col-lg-offset-9 {
    margin-left: 75%;
  }
  .col-lg-offset-8 {
    margin-left: 66.66666667%;
  }
  .col-lg-offset-7 {
    margin-left: 58.33333333%;
  }
  .col-lg-offset-6 {
    margin-left: 50%;
  }
  .col-lg-offset-5 {
    margin-left: 41.66666667%;
  }
  .col-lg-offset-4 {
    margin-left: 33.33333333%;
  }
  .col-lg-offset-3 {
    margin-left: 25%;
  }
  .col-lg-offset-2 {
    margin-left: 16.66666667%;
  }
  .col-lg-offset-1 {
    margin-left: 8.33333333%;
  }
  .col-lg-offset-0 {
    margin-left: 0%;
  }
}
table {
  background-color: transparent;
}
table col[class*="col-"] {
  position: static;
  display: table-column;
  float: none;
}
table td[class*="col-"],
table th[class*="col-"] {
  position: static;
  display: table-cell;
  float: none;
}
caption {
  padding-top: 8px;
  padding-bottom: 8px;
  color: rgba(47, 53, 56, 0.6);
  text-align: left;
}
th {
  text-align: left;
}
.table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 24px;
}
.table > thead > tr > th,
.table > tbody > tr > th,
.table > tfoot > tr > th,
.table > thead > tr > td,
.table > tbody > tr > td,
.table > tfoot > tr > td {
  padding: 8px;
  line-height: 1.5;
  vertical-align: top;
  border-top: 1px solid #EBEBEB;
}
.table > thead > tr > th {
  vertical-align: bottom;
  border-bottom: 2px solid #EBEBEB;
}
.table > caption + thead > tr:first-child > th,
.table > colgroup + thead > tr:first-child > th,
.table > thead:first-child > tr:first-child > th,
.table > caption + thead > tr:first-child > td,
.table > colgroup + thead > tr:first-child > td,
.table > thead:first-child > tr:first-child > td {
  border-top: 0;
}
.table > tbody + tbody {
  border-top: 2px solid #EBEBEB;
}
.table .table {
  background-color: #fff;
}
.table-condensed > thead > tr > th,
.table-condensed > tbody > tr > th,
.table-condensed > tfoot > tr > th,
.table-condensed > thead > tr > td,
.table-condensed > tbody > tr > td,
.table-condensed > tfoot > tr > td {
  padding: 5px;
}
.table-bordered {
  border: 1px solid #EBEBEB;
}
.table-bordered > thead > tr > th,
.table-bordered > tbody > tr > th,
.table-bordered > tfoot > tr > th,
.table-bordered > thead > tr > td,
.table-bordered > tbody > tr > td,
.table-bordered > tfoot > tr > td {
  border: 1px solid #EBEBEB;
}
.table-bordered > thead > tr > th,
.table-bordered > thead > tr > td {
  border-bottom-width: 2px;
}
.table-striped > tbody > tr:nth-of-type(odd) {
  background-color: #f9f9f9;
}
.table-hover > tbody > tr:hover {
  background-color: #f5f5f5;
}
.table > thead > tr > td.active,
.table > tbody > tr > td.active,
.table > tfoot > tr > td.active,
.table > thead > tr > th.active,
.table > tbody > tr > th.active,
.table > tfoot > tr > th.active,
.table > thead > tr.active > td,
.table > tbody > tr.active > td,
.table > tfoot > tr.active > td,
.table > thead > tr.active > th,
.table > tbody > tr.active > th,
.table > tfoot > tr.active > th {
  background-color: #f5f5f5;
}
.table-hover > tbody > tr > td.active:hover,
.table-hover > tbody > tr > th.active:hover,
.table-hover > tbody > tr.active:hover > td,
.table-hover > tbody > tr:hover > .active,
.table-hover > tbody > tr.active:hover > th {
  background-color: #e8e8e8;
}
.table > thead > tr > td.success,
.table > tbody > tr > td.success,
.table > tfoot > tr > td.success,
.table > thead > tr > th.success,
.table > tbody > tr > th.success,
.table > tfoot > tr > th.success,
.table > thead > tr.success > td,
.table > tbody > tr.success > td,
.table > tfoot > tr.success > td,
.table > thead > tr.success > th,
.table > tbody > tr.success > th,
.table > tfoot > tr.success > th {
  background-color: #dff0d8;
}
.table-hover > tbody > tr > td.success:hover,
.table-hover > tbody > tr > th.success:hover,
.table-hover > tbody > tr.success:hover > td,
.table-hover > tbody > tr:hover > .success,
.table-hover > tbody > tr.success:hover > th {
  background-color: #d0e9c6;
}
.table > thead > tr > td.info,
.table > tbody > tr > td.info,
.table > tfoot > tr > td.info,
.table > thead > tr > th.info,
.table > tbody > tr > th.info,
.table > tfoot > tr > th.info,
.table > thead > tr.info > td,
.table > tbody > tr.info > td,
.table > tfoot > tr.info > td,
.table > thead > tr.info > th,
.table > tbody > tr.info > th,
.table > tfoot > tr.info > th {
  background-color: #d9edf7;
}
.table-hover > tbody > tr > td.info:hover,
.table-hover > tbody > tr > th.info:hover,
.table-hover > tbody > tr.info:hover > td,
.table-hover > tbody > tr:hover > .info,
.table-hover > tbody > tr.info:hover > th {
  background-color: #c4e3f3;
}
.table > thead > tr > td.warning,
.table > tbody > tr > td.warning,
.table > tfoot > tr > td.warning,
.table > thead > tr > th.warning,
.table > tbody > tr > th.warning,
.table > tfoot > tr > th.warning,
.table > thead > tr.warning > td,
.table > tbody > tr.warning > td,
.table > tfoot > tr.warning > td,
.table > thead > tr.warning > th,
.table > tbody > tr.warning > th,
.table > tfoot > tr.warning > th {
  background-color: #fcf8e3;
}
.table-hover > tbody > tr > td.warning:hover,
.table-hover > tbody > tr > th.warning:hover,
.table-hover > tbody > tr.warning:hover > td,
.table-hover > tbody > tr:hover > .warning,
.table-hover > tbody > tr.warning:hover > th {
  background-color: #faf2cc;
}
.table > thead > tr > td.danger,
.table > tbody > tr > td.danger,
.table > tfoot > tr > td.danger,
.table > thead > tr > th.danger,
.table > tbody > tr > th.danger,
.table > tfoot > tr > th.danger,
.table > thead > tr.danger > td,
.table > tbody > tr.danger > td,
.table > tfoot > tr.danger > td,
.table > thead > tr.danger > th,
.table > tbody > tr.danger > th,
.table > tfoot > tr.danger > th {
  background-color: #f2dede;
}
.table-hover > tbody > tr > td.danger:hover,
.table-hover > tbody > tr > th.danger:hover,
.table-hover > tbody > tr.danger:hover > td,
.table-hover > tbody > tr:hover > .danger,
.table-hover > tbody > tr.danger:hover > th {
  background-color: #ebcccc;
}
.table-responsive {
  min-height: 0.01%;
  overflow-x: auto;
}
@media screen and (max-width: 575px) {
  .table-responsive {
    width: 100%;
    margin-bottom: 18px;
    overflow-y: hidden;
    -ms-overflow-style: -ms-autohiding-scrollbar;
    border: 1px solid #EBEBEB;
  }
  .table-responsive > .table {
    margin-bottom: 0;
  }
  .table-responsive > .table > thead > tr > th,
  .table-responsive > .table > tbody > tr > th,
  .table-responsive > .table > tfoot > tr > th,
  .table-responsive > .table > thead > tr > td,
  .table-responsive > .table > tbody > tr > td,
  .table-responsive > .table > tfoot > tr > td {
    white-space: nowrap;
  }
  .table-responsive > .table-bordered {
    border: 0;
  }
  .table-responsive > .table-bordered > thead > tr > th:first-child,
  .table-responsive > .table-bordered > tbody > tr > th:first-child,
  .table-responsive > .table-bordered > tfoot > tr > th:first-child,
  .table-responsive > .table-bordered > thead > tr > td:first-child,
  .table-responsive > .table-bordered > tbody > tr > td:first-child,
  .table-responsive > .table-bordered > tfoot > tr > td:first-child {
    border-left: 0;
  }
  .table-responsive > .table-bordered > thead > tr > th:last-child,
  .table-responsive > .table-bordered > tbody > tr > th:last-child,
  .table-responsive > .table-bordered > tfoot > tr > th:last-child,
  .table-responsive > .table-bordered > thead > tr > td:last-child,
  .table-responsive > .table-bordered > tbody > tr > td:last-child,
  .table-responsive > .table-bordered > tfoot > tr > td:last-child {
    border-right: 0;
  }
  .table-responsive > .table-bordered > tbody > tr:last-child > th,
  .table-responsive > .table-bordered > tfoot > tr:last-child > th,
  .table-responsive > .table-bordered > tbody > tr:last-child > td,
  .table-responsive > .table-bordered > tfoot > tr:last-child > td {
    border-bottom: 0;
  }
}
fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}
legend {
  display: block;
  width: 100%;
  padding: 0;
  margin-bottom: 24px;
  font-size: 24px;
  line-height: inherit;
  color: #2F3538;
  border: 0;
  border-bottom: 1px solid #e5e5e5;
}
label {
  display: inline-block;
  max-width: 100%;
  margin-bottom: 5px;
  font-weight: 700;
}
input[type="search"] {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-appearance: none;
  -moz-appearance: none;
       appearance: none;
}
input[type="radio"],
input[type="checkbox"] {
  margin: 4px 0 0;
  margin-top: 1px \9;
  line-height: normal;
}
input[type="radio"][disabled],
input[type="checkbox"][disabled],
input[type="radio"].disabled,
input[type="checkbox"].disabled,
fieldset[disabled] input[type="radio"],
fieldset[disabled] input[type="checkbox"] {
  cursor: not-allowed;
}
input[type="file"] {
  display: block;
}
input[type="range"] {
  display: block;
  width: 100%;
}
select[multiple],
select[size] {
  height: auto;
}
input[type="file"]:focus,
input[type="radio"]:focus,
input[type="checkbox"]:focus {
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
  outline: none;
}
output {
  display: block;
  padding-top: 7px;
  font-size: 16px;
  line-height: 1.5;
  color: #555555;
}
.form-control {
  display: block;
  width: 100%;
  height: 38px;
  padding: 6px 12px;
  font-size: 16px;
  line-height: 1.5;
  color: #555555;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ccc;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -webkit-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
  -webkit-transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
  transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
  transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
  transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
}
.form-control:focus {
  border-color: #66afe9;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, 0.6);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, 0.6);
}
.form-control::-moz-placeholder {
  color: rgba(47, 53, 56, 0.6);
  opacity: 1;
}
.form-control:-ms-input-placeholder {
  color: rgba(47, 53, 56, 0.6);
}
.form-control::-webkit-input-placeholder {
  color: rgba(47, 53, 56, 0.6);
}
.form-control::-ms-expand {
  background-color: transparent;
  border: 0;
}
.form-control[disabled],
.form-control[readonly],
fieldset[disabled] .form-control {
  background-color: #F7F7F7;
  opacity: 1;
}
.form-control[disabled],
fieldset[disabled] .form-control {
  cursor: not-allowed;
}
textarea.form-control {
  height: auto;
}
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  input[type="date"].form-control,
  input[type="time"].form-control,
  input[type="datetime-local"].form-control,
  input[type="month"].form-control {
    line-height: 38px;
  }
  input[type="date"].input-sm,
  input[type="time"].input-sm,
  input[type="datetime-local"].input-sm,
  input[type="month"].input-sm,
  .input-group-sm input[type="date"],
  .input-group-sm input[type="time"],
  .input-group-sm input[type="datetime-local"],
  .input-group-sm input[type="month"] {
    line-height: 33px;
  }
  input[type="date"].input-lg,
  input[type="time"].input-lg,
  input[type="datetime-local"].input-lg,
  input[type="month"].input-lg,
  .input-group-lg input[type="date"],
  .input-group-lg input[type="time"],
  .input-group-lg input[type="datetime-local"],
  .input-group-lg input[type="month"] {
    line-height: 49px;
  }
}
.form-group {
  margin-bottom: 15px;
}
.radio,
.checkbox {
  position: relative;
  display: block;
  margin-top: 10px;
  margin-bottom: 10px;
}
.radio.disabled label,
.checkbox.disabled label,
fieldset[disabled] .radio label,
fieldset[disabled] .checkbox label {
  cursor: not-allowed;
}
.radio label,
.checkbox label {
  min-height: 24px;
  padding-left: 20px;
  margin-bottom: 0;
  font-weight: 400;
  cursor: pointer;
}
.radio input[type="radio"],
.radio-inline input[type="radio"],
.checkbox input[type="checkbox"],
.checkbox-inline input[type="checkbox"] {
  position: absolute;
  margin-top: 4px \9;
  margin-left: -20px;
}
.radio + .radio,
.checkbox + .checkbox {
  margin-top: -5px;
}
.radio-inline,
.checkbox-inline {
  position: relative;
  display: inline-block;
  padding-left: 20px;
  margin-bottom: 0;
  font-weight: 400;
  vertical-align: middle;
  cursor: pointer;
}
.radio-inline.disabled,
.checkbox-inline.disabled,
fieldset[disabled] .radio-inline,
fieldset[disabled] .checkbox-inline {
  cursor: not-allowed;
}
.radio-inline + .radio-inline,
.checkbox-inline + .checkbox-inline {
  margin-top: 0;
  margin-left: 10px;
}
.form-control-static {
  min-height: 40px;
  padding-top: 7px;
  padding-bottom: 7px;
  margin-bottom: 0;
}
.form-control-static.input-lg,
.form-control-static.input-sm {
  padding-right: 0;
  padding-left: 0;
}
.input-sm {
  height: 33px;
  padding: 5px 10px;
  font-size: 14px;
  line-height: 1.5;
  border-radius: 3px;
}
select.input-sm {
  height: 33px;
  line-height: 33px;
}
textarea.input-sm,
select[multiple].input-sm {
  height: auto;
}
.form-group-sm .form-control {
  height: 33px;
  padding: 5px 10px;
  font-size: 14px;
  line-height: 1.5;
  border-radius: 3px;
}
.form-group-sm select.form-control {
  height: 33px;
  line-height: 33px;
}
.form-group-sm textarea.form-control,
.form-group-sm select[multiple].form-control {
  height: auto;
}
.form-group-sm .form-control-static {
  height: 33px;
  min-height: 38px;
  padding: 6px 10px;
  font-size: 14px;
  line-height: 1.5;
}
.input-lg {
  height: 49px;
  padding: 10px 16px;
  font-size: 20px;
  line-height: 1.3333333;
  border-radius: 6px;
}
select.input-lg {
  height: 49px;
  line-height: 49px;
}
textarea.input-lg,
select[multiple].input-lg {
  height: auto;
}
.form-group-lg .form-control {
  height: 49px;
  padding: 10px 16px;
  font-size: 20px;
  line-height: 1.3333333;
  border-radius: 6px;
}
.form-group-lg select.form-control {
  height: 49px;
  line-height: 49px;
}
.form-group-lg textarea.form-control,
.form-group-lg select[multiple].form-control {
  height: auto;
}
.form-group-lg .form-control-static {
  height: 49px;
  min-height: 44px;
  padding: 11px 16px;
  font-size: 20px;
  line-height: 1.3333333;
}
.has-feedback {
  position: relative;
}
.has-feedback .form-control {
  padding-right: 47.5px;
}
.form-control-feedback {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
  display: block;
  width: 38px;
  height: 38px;
  line-height: 38px;
  text-align: center;
  pointer-events: none;
}
.input-lg + .form-control-feedback,
.input-group-lg + .form-control-feedback,
.form-group-lg .form-control + .form-control-feedback {
  width: 49px;
  height: 49px;
  line-height: 49px;
}
.input-sm + .form-control-feedback,
.input-group-sm + .form-control-feedback,
.form-group-sm .form-control + .form-control-feedback {
  width: 33px;
  height: 33px;
  line-height: 33px;
}
.has-success .help-block,
.has-success .control-label,
.has-success .radio,
.has-success .checkbox,
.has-success .radio-inline,
.has-success .checkbox-inline,
.has-success.radio label,
.has-success.checkbox label,
.has-success.radio-inline label,
.has-success.checkbox-inline label {
  color: #3c763d;
}
.has-success .form-control {
  border-color: #3c763d;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.has-success .form-control:focus {
  border-color: #2b542c;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168;
}
.has-success .input-group-addon {
  color: #3c763d;
  background-color: #dff0d8;
  border-color: #3c763d;
}
.has-success .form-control-feedback {
  color: #3c763d;
}
.has-warning .help-block,
.has-warning .control-label,
.has-warning .radio,
.has-warning .checkbox,
.has-warning .radio-inline,
.has-warning .checkbox-inline,
.has-warning.radio label,
.has-warning.checkbox label,
.has-warning.radio-inline label,
.has-warning.checkbox-inline label {
  color: #8a6d3b;
}
.has-warning .form-control {
  border-color: #8a6d3b;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.has-warning .form-control:focus {
  border-color: #66512c;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b;
}
.has-warning .input-group-addon {
  color: #8a6d3b;
  background-color: #fcf8e3;
  border-color: #8a6d3b;
}
.has-warning .form-control-feedback {
  color: #8a6d3b;
}
.has-error .help-block,
.has-error .control-label,
.has-error .radio,
.has-error .checkbox,
.has-error .radio-inline,
.has-error .checkbox-inline,
.has-error.radio label,
.has-error.checkbox label,
.has-error.radio-inline label,
.has-error.checkbox-inline label {
  color: #a94442;
}
.has-error .form-control {
  border-color: #a94442;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.has-error .form-control:focus {
  border-color: #843534;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
}
.has-error .input-group-addon {
  color: #a94442;
  background-color: #f2dede;
  border-color: #a94442;
}
.has-error .form-control-feedback {
  color: #a94442;
}
.has-feedback label ~ .form-control-feedback {
  top: 29px;
}
.has-feedback label.sr-only ~ .form-control-feedback {
  top: 0;
}
.help-block {
  display: block;
  margin-top: 5px;
  margin-bottom: 10px;
  color: #69777d;
}
@media (min-width: 576px) {
  .form-inline .form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }
  .form-inline .form-control-static {
    display: inline-block;
  }
  .form-inline .input-group {
    display: inline-table;
    vertical-align: middle;
  }
  .form-inline .input-group .input-group-addon,
  .form-inline .input-group .input-group-btn,
  .form-inline .input-group .form-control {
    width: auto;
  }
  .form-inline .input-group > .form-control {
    width: 100%;
  }
  .form-inline .control-label {
    margin-bottom: 0;
    vertical-align: middle;
  }
  .form-inline .radio,
  .form-inline .checkbox {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .form-inline .radio label,
  .form-inline .checkbox label {
    padding-left: 0;
  }
  .form-inline .radio input[type="radio"],
  .form-inline .checkbox input[type="checkbox"] {
    position: relative;
    margin-left: 0;
  }
  .form-inline .has-feedback .form-control-feedback {
    top: 0;
  }
}
.form-horizontal .radio,
.form-horizontal .checkbox,
.form-horizontal .radio-inline,
.form-horizontal .checkbox-inline {
  padding-top: 7px;
  margin-top: 0;
  margin-bottom: 0;
}
.form-horizontal .radio,
.form-horizontal .checkbox {
  min-height: 31px;
}
.form-horizontal .form-group {
  margin-right: -15px;
  margin-left: -15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.form-horizontal .form-group::before,
.form-horizontal .form-group::after {
  display: none;
}
@media (min-width: 576px) {
  .form-horizontal .control-label {
    padding-top: 7px;
    margin-bottom: 0;
    text-align: right;
  }
}
.form-horizontal .has-feedback .form-control-feedback {
  right: 15px;
}
@media (min-width: 576px) {
  .form-horizontal .form-group-lg .control-label {
    padding-top: 11px;
    font-size: 20px;
  }
}
@media (min-width: 576px) {
  .form-horizontal .form-group-sm .control-label {
    padding-top: 6px;
    font-size: 14px;
  }
}
.btn {
  display: inline-block;
  margin-bottom: 0;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -ms-touch-action: manipulation;
      touch-action: manipulation;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  padding: 6px 12px;
  font-size: 16px;
  line-height: 1.5;
  border-radius: 4px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.btn:focus,
.btn:active:focus,
.btn.active:focus,
.btn.focus,
.btn:active.focus,
.btn.active.focus {
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
  outline: none;
}
.btn:hover,
.btn:focus,
.btn.focus {
  color: #333;
  text-decoration: none;
}
.btn:active,
.btn.active {
  background-image: none;
  outline: 0;
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.btn.disabled,
.btn[disabled],
fieldset[disabled] .btn {
  cursor: not-allowed;
  filter: alpha(opacity=65);
  opacity: 0.65;
  -webkit-box-shadow: none;
  box-shadow: none;
}
a.btn.disabled,
fieldset[disabled] a.btn {
  pointer-events: none;
}
.btn-default {
  color: #333;
  background-color: #fff;
  border-color: #ccc;
}
.btn-default:focus,
.btn-default.focus {
  color: #333;
  background-color: #e6e6e6;
  border-color: #8c8c8c;
}
.btn-default:hover {
  color: #333;
  background-color: #e6e6e6;
  border-color: #adadad;
}
.btn-default:active,
.btn-default.active,
.open > .dropdown-toggle.btn-default {
  color: #333;
  background-color: #e6e6e6;
  background-image: none;
  border-color: #adadad;
}
.btn-default:active:hover,
.btn-default.active:hover,
.open > .dropdown-toggle.btn-default:hover,
.btn-default:active:focus,
.btn-default.active:focus,
.open > .dropdown-toggle.btn-default:focus,
.btn-default:active.focus,
.btn-default.active.focus,
.open > .dropdown-toggle.btn-default.focus {
  color: #333;
  background-color: #d4d4d4;
  border-color: #8c8c8c;
}
.btn-default.disabled:hover,
.btn-default[disabled]:hover,
fieldset[disabled] .btn-default:hover,
.btn-default.disabled:focus,
.btn-default[disabled]:focus,
fieldset[disabled] .btn-default:focus,
.btn-default.disabled.focus,
.btn-default[disabled].focus,
fieldset[disabled] .btn-default.focus {
  background-color: #fff;
  border-color: #ccc;
}
.btn-default .badge {
  color: #fff;
  background-color: #333;
}
.btn-default:focus,
.btn-default.focus {
  color: #333;
  background-color: #ececec;
  border-color: transparent;
}
.btn-default:hover {
  color: #333;
  background-color: #ececec;
  border-color: transparent;
}
.btn-default:active,
.btn-default.active,
.open > .dropdown-toggle.btn-default {
  color: #333;
  background-color: #e6e6e6;
  background-image: none;
  border-color: transparent;
}
.btn-default:active:hover,
.btn-default.active:hover,
.open > .dropdown-toggle.btn-default:hover,
.btn-default:active:focus,
.btn-default.active:focus,
.open > .dropdown-toggle.btn-default:focus,
.btn-default:active.focus,
.btn-default.active.focus,
.open > .dropdown-toggle.btn-default.focus {
  color: #333;
  background-color: #dfdfdf;
  border-color: transparent;
}
.btn-default.disabled:hover,
.btn-default[disabled]:hover,
fieldset[disabled] .btn-default:hover,
.btn-default.disabled:focus,
.btn-default[disabled]:focus,
fieldset[disabled] .btn-default:focus,
.btn-default.disabled.focus,
.btn-default[disabled].focus,
fieldset[disabled] .btn-default.focus {
  background-color: #fff;
  border-color: #ccc;
}
.btn-default .badge {
  color: #fff;
  background-color: #333;
}
.btn-primary {
  color: #fff;
  background-color: #DF0000;
  border-color: #c60000;
}
.btn-primary:focus,
.btn-primary.focus {
  color: #fff;
  background-color: #ac0000;
  border-color: #460000;
}
.btn-primary:hover {
  color: #fff;
  background-color: #ac0000;
  border-color: #880000;
}
.btn-primary:active,
.btn-primary.active,
.open > .dropdown-toggle.btn-primary {
  color: #fff;
  background-color: #ac0000;
  background-image: none;
  border-color: #880000;
}
.btn-primary:active:hover,
.btn-primary.active:hover,
.open > .dropdown-toggle.btn-primary:hover,
.btn-primary:active:focus,
.btn-primary.active:focus,
.open > .dropdown-toggle.btn-primary:focus,
.btn-primary:active.focus,
.btn-primary.active.focus,
.open > .dropdown-toggle.btn-primary.focus {
  color: #fff;
  background-color: #880000;
  border-color: #460000;
}
.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled.focus,
.btn-primary[disabled].focus,
fieldset[disabled] .btn-primary.focus {
  background-color: #DF0000;
  border-color: #c60000;
}
.btn-primary .badge {
  color: #DF0000;
  background-color: #fff;
}
.btn-primary:focus,
.btn-primary.focus {
  color: #fff;
  background-color: #b90000;
  border-color: transparent;
}
.btn-primary:hover {
  color: #fff;
  background-color: #b90000;
  border-color: transparent;
}
.btn-primary:active,
.btn-primary.active,
.open > .dropdown-toggle.btn-primary {
  color: #fff;
  background-color: #ac0000;
  background-image: none;
  border-color: transparent;
}
.btn-primary:active:hover,
.btn-primary.active:hover,
.open > .dropdown-toggle.btn-primary:hover,
.btn-primary:active:focus,
.btn-primary.active:focus,
.open > .dropdown-toggle.btn-primary:focus,
.btn-primary:active.focus,
.btn-primary.active.focus,
.open > .dropdown-toggle.btn-primary.focus {
  color: #fff;
  background-color: #9f0000;
  border-color: transparent;
}
.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled.focus,
.btn-primary[disabled].focus,
fieldset[disabled] .btn-primary.focus {
  background-color: #DF0000;
  border-color: #c60000;
}
.btn-primary .badge {
  color: #DF0000;
  background-color: #fff;
}
.btn-success {
  color: #fff;
  background-color: #00B25B;
  border-color: #00994e;
}
.btn-success:focus,
.btn-success.focus {
  color: #fff;
  background-color: #007f41;
  border-color: #00190d;
}
.btn-success:hover {
  color: #fff;
  background-color: #007f41;
  border-color: #005b2f;
}
.btn-success:active,
.btn-success.active,
.open > .dropdown-toggle.btn-success {
  color: #fff;
  background-color: #007f41;
  background-image: none;
  border-color: #005b2f;
}
.btn-success:active:hover,
.btn-success.active:hover,
.open > .dropdown-toggle.btn-success:hover,
.btn-success:active:focus,
.btn-success.active:focus,
.open > .dropdown-toggle.btn-success:focus,
.btn-success:active.focus,
.btn-success.active.focus,
.open > .dropdown-toggle.btn-success.focus {
  color: #fff;
  background-color: #005b2f;
  border-color: #00190d;
}
.btn-success.disabled:hover,
.btn-success[disabled]:hover,
fieldset[disabled] .btn-success:hover,
.btn-success.disabled:focus,
.btn-success[disabled]:focus,
fieldset[disabled] .btn-success:focus,
.btn-success.disabled.focus,
.btn-success[disabled].focus,
fieldset[disabled] .btn-success.focus {
  background-color: #00B25B;
  border-color: #00994e;
}
.btn-success .badge {
  color: #00B25B;
  background-color: #fff;
}
.btn-success:focus,
.btn-success.focus {
  color: #fff;
  background-color: #008c47;
  border-color: transparent;
}
.btn-success:hover {
  color: #fff;
  background-color: #008c47;
  border-color: transparent;
}
.btn-success:active,
.btn-success.active,
.open > .dropdown-toggle.btn-success {
  color: #fff;
  background-color: #007f41;
  background-image: none;
  border-color: transparent;
}
.btn-success:active:hover,
.btn-success.active:hover,
.open > .dropdown-toggle.btn-success:hover,
.btn-success:active:focus,
.btn-success.active:focus,
.open > .dropdown-toggle.btn-success:focus,
.btn-success:active.focus,
.btn-success.active.focus,
.open > .dropdown-toggle.btn-success.focus {
  color: #fff;
  background-color: #00723a;
  border-color: transparent;
}
.btn-success.disabled:hover,
.btn-success[disabled]:hover,
fieldset[disabled] .btn-success:hover,
.btn-success.disabled:focus,
.btn-success[disabled]:focus,
fieldset[disabled] .btn-success:focus,
.btn-success.disabled.focus,
.btn-success[disabled].focus,
fieldset[disabled] .btn-success.focus {
  background-color: #00B25B;
  border-color: #00994e;
}
.btn-success .badge {
  color: #00B25B;
  background-color: #fff;
}
.btn-info {
  color: #fff;
  background-color: #5bc0de;
  border-color: #46b8da;
}
.btn-info:focus,
.btn-info.focus {
  color: #fff;
  background-color: #31b0d5;
  border-color: #1b6d85;
}
.btn-info:hover {
  color: #fff;
  background-color: #31b0d5;
  border-color: #269abc;
}
.btn-info:active,
.btn-info.active,
.open > .dropdown-toggle.btn-info {
  color: #fff;
  background-color: #31b0d5;
  background-image: none;
  border-color: #269abc;
}
.btn-info:active:hover,
.btn-info.active:hover,
.open > .dropdown-toggle.btn-info:hover,
.btn-info:active:focus,
.btn-info.active:focus,
.open > .dropdown-toggle.btn-info:focus,
.btn-info:active.focus,
.btn-info.active.focus,
.open > .dropdown-toggle.btn-info.focus {
  color: #fff;
  background-color: #269abc;
  border-color: #1b6d85;
}
.btn-info.disabled:hover,
.btn-info[disabled]:hover,
fieldset[disabled] .btn-info:hover,
.btn-info.disabled:focus,
.btn-info[disabled]:focus,
fieldset[disabled] .btn-info:focus,
.btn-info.disabled.focus,
.btn-info[disabled].focus,
fieldset[disabled] .btn-info.focus {
  background-color: #5bc0de;
  border-color: #46b8da;
}
.btn-info .badge {
  color: #5bc0de;
  background-color: #fff;
}
.btn-info:focus,
.btn-info.focus {
  color: #fff;
  background-color: #3bb4d8;
  border-color: transparent;
}
.btn-info:hover {
  color: #fff;
  background-color: #3bb4d8;
  border-color: transparent;
}
.btn-info:active,
.btn-info.active,
.open > .dropdown-toggle.btn-info {
  color: #fff;
  background-color: #31b0d5;
  background-image: none;
  border-color: transparent;
}
.btn-info:active:hover,
.btn-info.active:hover,
.open > .dropdown-toggle.btn-info:hover,
.btn-info:active:focus,
.btn-info.active:focus,
.open > .dropdown-toggle.btn-info:focus,
.btn-info:active.focus,
.btn-info.active.focus,
.open > .dropdown-toggle.btn-info.focus {
  color: #fff;
  background-color: #2aaacf;
  border-color: transparent;
}
.btn-info.disabled:hover,
.btn-info[disabled]:hover,
fieldset[disabled] .btn-info:hover,
.btn-info.disabled:focus,
.btn-info[disabled]:focus,
fieldset[disabled] .btn-info:focus,
.btn-info.disabled.focus,
.btn-info[disabled].focus,
fieldset[disabled] .btn-info.focus {
  background-color: #5bc0de;
  border-color: #46b8da;
}
.btn-info .badge {
  color: #5bc0de;
  background-color: #fff;
}
.btn-warning {
  color: #fff;
  background-color: #f0ad4e;
  border-color: #eea236;
}
.btn-warning:focus,
.btn-warning.focus {
  color: #fff;
  background-color: #ec971f;
  border-color: #985f0d;
}
.btn-warning:hover {
  color: #fff;
  background-color: #ec971f;
  border-color: #d58512;
}
.btn-warning:active,
.btn-warning.active,
.open > .dropdown-toggle.btn-warning {
  color: #fff;
  background-color: #ec971f;
  background-image: none;
  border-color: #d58512;
}
.btn-warning:active:hover,
.btn-warning.active:hover,
.open > .dropdown-toggle.btn-warning:hover,
.btn-warning:active:focus,
.btn-warning.active:focus,
.open > .dropdown-toggle.btn-warning:focus,
.btn-warning:active.focus,
.btn-warning.active.focus,
.open > .dropdown-toggle.btn-warning.focus {
  color: #fff;
  background-color: #d58512;
  border-color: #985f0d;
}
.btn-warning.disabled:hover,
.btn-warning[disabled]:hover,
fieldset[disabled] .btn-warning:hover,
.btn-warning.disabled:focus,
.btn-warning[disabled]:focus,
fieldset[disabled] .btn-warning:focus,
.btn-warning.disabled.focus,
.btn-warning[disabled].focus,
fieldset[disabled] .btn-warning.focus {
  background-color: #f0ad4e;
  border-color: #eea236;
}
.btn-warning .badge {
  color: #f0ad4e;
  background-color: #fff;
}
.btn-warning:focus,
.btn-warning.focus {
  color: #fff;
  background-color: #ed9d2b;
  border-color: transparent;
}
.btn-warning:hover {
  color: #fff;
  background-color: #ed9d2b;
  border-color: transparent;
}
.btn-warning:active,
.btn-warning.active,
.open > .dropdown-toggle.btn-warning {
  color: #fff;
  background-color: #ec971f;
  background-image: none;
  border-color: transparent;
}
.btn-warning:active:hover,
.btn-warning.active:hover,
.open > .dropdown-toggle.btn-warning:hover,
.btn-warning:active:focus,
.btn-warning.active:focus,
.open > .dropdown-toggle.btn-warning:focus,
.btn-warning:active.focus,
.btn-warning.active.focus,
.open > .dropdown-toggle.btn-warning.focus {
  color: #fff;
  background-color: #ea9214;
  border-color: transparent;
}
.btn-warning.disabled:hover,
.btn-warning[disabled]:hover,
fieldset[disabled] .btn-warning:hover,
.btn-warning.disabled:focus,
.btn-warning[disabled]:focus,
fieldset[disabled] .btn-warning:focus,
.btn-warning.disabled.focus,
.btn-warning[disabled].focus,
fieldset[disabled] .btn-warning.focus {
  background-color: #f0ad4e;
  border-color: #eea236;
}
.btn-warning .badge {
  color: #f0ad4e;
  background-color: #fff;
}
.btn-danger {
  color: #fff;
  background-color: #d9534f;
  border-color: #d43f3a;
}
.btn-danger:focus,
.btn-danger.focus {
  color: #fff;
  background-color: #c9302c;
  border-color: #761c19;
}
.btn-danger:hover {
  color: #fff;
  background-color: #c9302c;
  border-color: #ac2925;
}
.btn-danger:active,
.btn-danger.active,
.open > .dropdown-toggle.btn-danger {
  color: #fff;
  background-color: #c9302c;
  background-image: none;
  border-color: #ac2925;
}
.btn-danger:active:hover,
.btn-danger.active:hover,
.open > .dropdown-toggle.btn-danger:hover,
.btn-danger:active:focus,
.btn-danger.active:focus,
.open > .dropdown-toggle.btn-danger:focus,
.btn-danger:active.focus,
.btn-danger.active.focus,
.open > .dropdown-toggle.btn-danger.focus {
  color: #fff;
  background-color: #ac2925;
  border-color: #761c19;
}
.btn-danger.disabled:hover,
.btn-danger[disabled]:hover,
fieldset[disabled] .btn-danger:hover,
.btn-danger.disabled:focus,
.btn-danger[disabled]:focus,
fieldset[disabled] .btn-danger:focus,
.btn-danger.disabled.focus,
.btn-danger[disabled].focus,
fieldset[disabled] .btn-danger.focus {
  background-color: #d9534f;
  border-color: #d43f3a;
}
.btn-danger .badge {
  color: #d9534f;
  background-color: #fff;
}
.btn-danger:focus,
.btn-danger.focus {
  color: #fff;
  background-color: #d23430;
  border-color: transparent;
}
.btn-danger:hover {
  color: #fff;
  background-color: #d23430;
  border-color: transparent;
}
.btn-danger:active,
.btn-danger.active,
.open > .dropdown-toggle.btn-danger {
  color: #fff;
  background-color: #c9302c;
  background-image: none;
  border-color: transparent;
}
.btn-danger:active:hover,
.btn-danger.active:hover,
.open > .dropdown-toggle.btn-danger:hover,
.btn-danger:active:focus,
.btn-danger.active:focus,
.open > .dropdown-toggle.btn-danger:focus,
.btn-danger:active.focus,
.btn-danger.active.focus,
.open > .dropdown-toggle.btn-danger.focus {
  color: #fff;
  background-color: #bf2e29;
  border-color: transparent;
}
.btn-danger.disabled:hover,
.btn-danger[disabled]:hover,
fieldset[disabled] .btn-danger:hover,
.btn-danger.disabled:focus,
.btn-danger[disabled]:focus,
fieldset[disabled] .btn-danger:focus,
.btn-danger.disabled.focus,
.btn-danger[disabled].focus,
fieldset[disabled] .btn-danger.focus {
  background-color: #d9534f;
  border-color: #d43f3a;
}
.btn-danger .badge {
  color: #d9534f;
  background-color: #fff;
}
.btn-link {
  font-weight: 400;
  color: #DF0000;
  border-radius: 0;
}
.btn-link,
.btn-link:active,
.btn-link.active,
.btn-link[disabled],
fieldset[disabled] .btn-link {
  background-color: transparent;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.btn-link,
.btn-link:hover,
.btn-link:focus,
.btn-link:active {
  border-color: transparent;
}
.btn-link:hover,
.btn-link:focus {
  color: #930000;
  text-decoration: underline;
  background-color: transparent;
}
.btn-link[disabled]:hover,
fieldset[disabled] .btn-link:hover,
.btn-link[disabled]:focus,
fieldset[disabled] .btn-link:focus {
  color: #EBEBEB;
  text-decoration: none;
}
.btn-lg,
.btn-group-lg > .btn {
  padding: 10px 16px;
  font-size: 20px;
  line-height: 1.3333333;
  border-radius: 6px;
}
.btn-sm,
.btn-group-sm > .btn {
  padding: 5px 10px;
  font-size: 14px;
  line-height: 1.5;
  border-radius: 3px;
}
.btn-xs,
.btn-group-xs > .btn {
  padding: 1px 5px;
  font-size: 14px;
  line-height: 1.5;
  border-radius: 3px;
}
.btn-block {
  display: block;
  width: 100%;
}
.btn-block + .btn-block {
  margin-top: 5px;
}
input[type="submit"].btn-block,
input[type="reset"].btn-block,
input[type="button"].btn-block {
  width: 100%;
}
.fade {
  opacity: 0;
  -webkit-transition: opacity 0.15s linear;
  transition: opacity 0.15s linear;
}
.fade.in {
  opacity: 1;
}
.collapse {
  display: none;
}
.collapse.in {
  display: block;
}
tr.collapse.in {
  display: table-row;
}
tbody.collapse.in {
  display: table-row-group;
}
.collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  -webkit-transition-property: height, visibility;
  transition-property: height, visibility;
  -webkit-transition-duration: 0.35s;
  transition-duration: 0.35s;
  -webkit-transition-timing-function: ease;
  transition-timing-function: ease;
}
.caret {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 2px;
  vertical-align: middle;
  border-top: 4px dashed;
  border-top: 4px solid \9;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
}
.dropup,
.dropdown {
  position: relative;
}
.dropdown-toggle:focus {
  outline: 0;
}
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 160px;
  padding: 5px 0;
  margin: 2px 0 0;
  font-size: 16px;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
}
.dropdown-menu.pull-right {
  right: 0;
  left: auto;
}
.dropdown-menu .divider {
  height: 1px;
  margin: 11px 0;
  overflow: hidden;
  background-color: #e5e5e5;
}
.dropdown-menu > li > a {
  display: block;
  padding: 3px 20px;
  clear: both;
  font-weight: 400;
  line-height: 1.5;
  color: #2F3538;
  white-space: nowrap;
}
.dropdown-menu > li > a:hover,
.dropdown-menu > li > a:focus {
  color: #DF0000;
  text-decoration: none;
  background-color: #eeeeee;
}
.dropdown-menu > .active > a,
.dropdown-menu > .active > a:hover,
.dropdown-menu > .active > a:focus {
  color: #DF0000;
  text-decoration: none;
  background-color: #F7F7F7;
  outline: 0;
}
.dropdown-menu > .disabled > a,
.dropdown-menu > .disabled > a:hover,
.dropdown-menu > .disabled > a:focus {
  color: #EBEBEB;
}
.dropdown-menu > .disabled > a:hover,
.dropdown-menu > .disabled > a:focus {
  text-decoration: none;
  cursor: not-allowed;
  background-color: transparent;
  background-image: none;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
.open > .dropdown-menu {
  display: block;
}
.open > a {
  outline: 0;
}
.dropdown-menu-right {
  right: 0;
  left: auto;
}
.dropdown-menu-left {
  right: auto;
  left: 0;
}
.dropdown-header {
  display: block;
  padding: 3px 20px;
  font-size: 14px;
  line-height: 1.5;
  color: #EBEBEB;
  white-space: nowrap;
}
.dropdown-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 990;
}
.pull-right > .dropdown-menu {
  right: 0;
  left: auto;
}
.dropup .caret,
.navbar-fixed-bottom .dropdown .caret {
  content: "";
  border-top: 0;
  border-bottom: 4px dashed;
  border-bottom: 4px solid \9;
}
.dropup .dropdown-menu,
.navbar-fixed-bottom .dropdown .dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-bottom: 2px;
}
@media (min-width: 576px) {
  .navbar-right .dropdown-menu {
    right: 0;
    left: auto;
  }
  .navbar-right .dropdown-menu-left {
    right: auto;
    left: 0;
  }
}
.btn-group,
.btn-group-vertical {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}
.btn-group > .btn,
.btn-group-vertical > .btn {
  position: relative;
  float: left;
}
.btn-group > .btn:hover,
.btn-group-vertical > .btn:hover,
.btn-group > .btn:focus,
.btn-group-vertical > .btn:focus,
.btn-group > .btn:active,
.btn-group-vertical > .btn:active,
.btn-group > .btn.active,
.btn-group-vertical > .btn.active {
  z-index: 2;
}
.btn-group .btn + .btn,
.btn-group .btn + .btn-group,
.btn-group .btn-group + .btn,
.btn-group .btn-group + .btn-group {
  margin-left: -1px;
}
.btn-toolbar {
  margin-left: -5px;
}
.btn-toolbar .btn,
.btn-toolbar .btn-group,
.btn-toolbar .input-group {
  float: left;
}
.btn-toolbar > .btn,
.btn-toolbar > .btn-group,
.btn-toolbar > .input-group {
  margin-left: 5px;
}
.btn-group > .btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {
  border-radius: 0;
}
.btn-group > .btn:first-child {
  margin-left: 0;
}
.btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.btn-group > .btn:last-child:not(:first-child),
.btn-group > .dropdown-toggle:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.btn-group > .btn-group {
  float: left;
}
.btn-group > .btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0;
}
.btn-group > .btn-group:first-child:not(:last-child) > .btn:last-child,
.btn-group > .btn-group:first-child:not(:last-child) > .dropdown-toggle {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.btn-group > .btn-group:last-child:not(:first-child) > .btn:first-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.btn-group .dropdown-toggle:active,
.btn-group.open .dropdown-toggle {
  outline: 0;
}
.btn-group > .btn + .dropdown-toggle {
  padding-right: 8px;
  padding-left: 8px;
}
.btn-group > .btn-lg + .dropdown-toggle {
  padding-right: 12px;
  padding-left: 12px;
}
.btn-group.open .dropdown-toggle {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.btn-group.open .dropdown-toggle.btn-link {
  -webkit-box-shadow: none;
  box-shadow: none;
}
.btn .caret {
  margin-left: 0;
}
.btn-lg .caret {
  border-width: 5px 5px 0;
  border-bottom-width: 0;
}
.dropup .btn-lg .caret {
  border-width: 0 5px 5px;
}
.btn-group-vertical > .btn,
.btn-group-vertical > .btn-group,
.btn-group-vertical > .btn-group > .btn {
  display: block;
  float: none;
  width: 100%;
  max-width: 100%;
}
.btn-group-vertical > .btn-group > .btn {
  float: none;
}
.btn-group-vertical > .btn + .btn,
.btn-group-vertical > .btn + .btn-group,
.btn-group-vertical > .btn-group + .btn,
.btn-group-vertical > .btn-group + .btn-group {
  margin-top: -1px;
  margin-left: 0;
}
.btn-group-vertical > .btn:not(:first-child):not(:last-child) {
  border-radius: 0;
}
.btn-group-vertical > .btn:first-child:not(:last-child) {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.btn-group-vertical > .btn:last-child:not(:first-child) {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
}
.btn-group-vertical > .btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0;
}
.btn-group-vertical > .btn-group:first-child:not(:last-child) > .btn:last-child,
.btn-group-vertical > .btn-group:first-child:not(:last-child) > .dropdown-toggle {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.btn-group-vertical > .btn-group:last-child:not(:first-child) > .btn:first-child {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.btn-group-justified {
  display: table;
  width: 100%;
  table-layout: fixed;
  border-collapse: separate;
}
.btn-group-justified > .btn,
.btn-group-justified > .btn-group {
  display: table-cell;
  float: none;
  width: 1%;
}
.btn-group-justified > .btn-group .btn {
  width: 100%;
}
.btn-group-justified > .btn-group .dropdown-menu {
  left: auto;
}
[data-toggle="buttons"] > .btn input[type="radio"],
[data-toggle="buttons"] > .btn-group > .btn input[type="radio"],
[data-toggle="buttons"] > .btn input[type="checkbox"],
[data-toggle="buttons"] > .btn-group > .btn input[type="checkbox"] {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}
.input-group {
  position: relative;
  display: table;
  border-collapse: separate;
}
.input-group[class*="col-"] {
  float: none;
  padding-right: 0;
  padding-left: 0;
}
.input-group .form-control {
  position: relative;
  z-index: 2;
  float: left;
  width: 100%;
  margin-bottom: 0;
}
.input-group .form-control:focus {
  z-index: 3;
}
.input-group-lg > .form-control,
.input-group-lg > .input-group-addon,
.input-group-lg > .input-group-btn > .btn {
  height: 49px;
  padding: 10px 16px;
  font-size: 20px;
  line-height: 1.3333333;
  border-radius: 6px;
}
select.input-group-lg > .form-control,
select.input-group-lg > .input-group-addon,
select.input-group-lg > .input-group-btn > .btn {
  height: 49px;
  line-height: 49px;
}
textarea.input-group-lg > .form-control,
textarea.input-group-lg > .input-group-addon,
textarea.input-group-lg > .input-group-btn > .btn,
select[multiple].input-group-lg > .form-control,
select[multiple].input-group-lg > .input-group-addon,
select[multiple].input-group-lg > .input-group-btn > .btn {
  height: auto;
}
.input-group-sm > .form-control,
.input-group-sm > .input-group-addon,
.input-group-sm > .input-group-btn > .btn {
  height: 33px;
  padding: 5px 10px;
  font-size: 14px;
  line-height: 1.5;
  border-radius: 3px;
}
select.input-group-sm > .form-control,
select.input-group-sm > .input-group-addon,
select.input-group-sm > .input-group-btn > .btn {
  height: 33px;
  line-height: 33px;
}
textarea.input-group-sm > .form-control,
textarea.input-group-sm > .input-group-addon,
textarea.input-group-sm > .input-group-btn > .btn,
select[multiple].input-group-sm > .form-control,
select[multiple].input-group-sm > .input-group-addon,
select[multiple].input-group-sm > .input-group-btn > .btn {
  height: auto;
}
.input-group-addon,
.input-group-btn,
.input-group .form-control {
  display: table-cell;
}
.input-group-addon:not(:first-child):not(:last-child),
.input-group-btn:not(:first-child):not(:last-child),
.input-group .form-control:not(:first-child):not(:last-child) {
  border-radius: 0;
}
.input-group-addon,
.input-group-btn {
  width: 1%;
  white-space: nowrap;
  vertical-align: middle;
}
.input-group-addon {
  padding: 6px 12px;
  font-size: 16px;
  font-weight: 400;
  line-height: 1;
  color: #555555;
  text-align: center;
  background-color: #F7F7F7;
  border: 1px solid #ccc;
  border-radius: 4px;
}
.input-group-addon.input-sm {
  padding: 5px 10px;
  font-size: 14px;
  border-radius: 3px;
}
.input-group-addon.input-lg {
  padding: 10px 16px;
  font-size: 20px;
  border-radius: 6px;
}
.input-group-addon input[type="radio"],
.input-group-addon input[type="checkbox"] {
  margin-top: 0;
}
.input-group .form-control:first-child,
.input-group-addon:first-child,
.input-group-btn:first-child > .btn,
.input-group-btn:first-child > .btn-group > .btn,
.input-group-btn:first-child > .dropdown-toggle,
.input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle),
.input-group-btn:last-child > .btn-group:not(:last-child) > .btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group-addon:first-child {
  border-right: 0;
}
.input-group .form-control:last-child,
.input-group-addon:last-child,
.input-group-btn:last-child > .btn,
.input-group-btn:last-child > .btn-group > .btn,
.input-group-btn:last-child > .dropdown-toggle,
.input-group-btn:first-child > .btn:not(:first-child),
.input-group-btn:first-child > .btn-group:not(:first-child) > .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.input-group-addon:last-child {
  border-left: 0;
}
.input-group-btn {
  position: relative;
  font-size: 0;
  white-space: nowrap;
}
.input-group-btn > .btn {
  position: relative;
}
.input-group-btn > .btn + .btn {
  margin-left: -1px;
}
.input-group-btn > .btn:hover,
.input-group-btn > .btn:focus,
.input-group-btn > .btn:active {
  z-index: 2;
}
.input-group-btn:first-child > .btn,
.input-group-btn:first-child > .btn-group {
  margin-right: -1px;
}
.input-group-btn:last-child > .btn,
.input-group-btn:last-child > .btn-group {
  z-index: 2;
  margin-left: -1px;
}
.nav {
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}
.nav > li {
  position: relative;
  display: block;
}
.nav > li > a {
  position: relative;
  display: block;
  padding: 10px 15px;
}
.nav > li > a:hover,
.nav > li > a:focus {
  text-decoration: none;
  background-color: #F7F7F7;
}
.nav > li.disabled > a {
  color: #EBEBEB;
}
.nav > li.disabled > a:hover,
.nav > li.disabled > a:focus {
  color: #EBEBEB;
  text-decoration: none;
  cursor: not-allowed;
  background-color: transparent;
}
.nav .open > a,
.nav .open > a:hover,
.nav .open > a:focus {
  background-color: #F7F7F7;
  border-color: #DF0000;
}
.nav .nav-divider {
  height: 1px;
  margin: 11px 0;
  overflow: hidden;
  background-color: #e5e5e5;
}
.nav > li > a > img {
  max-width: none;
}
.nav-tabs {
  border-bottom: 1px solid #ddd;
}
.nav-tabs > li {
  float: left;
  margin-bottom: -1px;
}
.nav-tabs > li > a {
  margin-right: 2px;
  line-height: 1.5;
  border: 1px solid transparent;
  border-radius: 4px 4px 0 0;
}
.nav-tabs > li > a:hover {
  border-color: #F7F7F7 #F7F7F7 #ddd;
}
.nav-tabs > li.active > a,
.nav-tabs > li.active > a:hover,
.nav-tabs > li.active > a:focus {
  color: #555555;
  cursor: default;
  background-color: #fff;
  border: 1px solid #ddd;
  border-bottom-color: transparent;
}
.nav-tabs.nav-justified {
  width: 100%;
  border-bottom: 0;
}
.nav-tabs.nav-justified > li {
  float: none;
}
.nav-tabs.nav-justified > li > a {
  margin-bottom: 5px;
  text-align: center;
}
.nav-tabs.nav-justified > .dropdown .dropdown-menu {
  top: auto;
  left: auto;
}
@media (min-width: 576px) {
  .nav-tabs.nav-justified > li {
    display: table-cell;
    width: 1%;
  }
  .nav-tabs.nav-justified > li > a {
    margin-bottom: 0;
  }
}
.nav-tabs.nav-justified > li > a {
  margin-right: 0;
  border-radius: 4px;
}
.nav-tabs.nav-justified > .active > a,
.nav-tabs.nav-justified > .active > a:hover,
.nav-tabs.nav-justified > .active > a:focus {
  border: 1px solid #ddd;
}
@media (min-width: 576px) {
  .nav-tabs.nav-justified > li > a {
    border-bottom: 1px solid #ddd;
    border-radius: 4px 4px 0 0;
  }
  .nav-tabs.nav-justified > .active > a,
  .nav-tabs.nav-justified > .active > a:hover,
  .nav-tabs.nav-justified > .active > a:focus {
    border-bottom-color: #fff;
  }
}
.nav-pills > li {
  float: left;
}
.nav-pills > li > a {
  border-radius: 4px;
}
.nav-pills > li + li {
  margin-left: 2px;
}
.nav-pills > li.active > a,
.nav-pills > li.active > a:hover,
.nav-pills > li.active > a:focus {
  color: #DF0000;
  background-color: #F7F7F7;
}
.nav-stacked > li {
  float: none;
}
.nav-stacked > li + li {
  margin-top: 2px;
  margin-left: 0;
}
.nav-justified {
  width: 100%;
}
.nav-justified > li {
  float: none;
}
.nav-justified > li > a {
  margin-bottom: 5px;
  text-align: center;
}
.nav-justified > .dropdown .dropdown-menu {
  top: auto;
  left: auto;
}
@media (min-width: 576px) {
  .nav-justified > li {
    display: table-cell;
    width: 1%;
  }
  .nav-justified > li > a {
    margin-bottom: 0;
  }
}
.nav-tabs-justified {
  border-bottom: 0;
}
.nav-tabs-justified > li > a {
  margin-right: 0;
  border-radius: 4px;
}
.nav-tabs-justified > .active > a,
.nav-tabs-justified > .active > a:hover,
.nav-tabs-justified > .active > a:focus {
  border: 1px solid #ddd;
}
@media (min-width: 576px) {
  .nav-tabs-justified > li > a {
    border-bottom: 1px solid #ddd;
    border-radius: 4px 4px 0 0;
  }
  .nav-tabs-justified > .active > a,
  .nav-tabs-justified > .active > a:hover,
  .nav-tabs-justified > .active > a:focus {
    border-bottom-color: #fff;
  }
}
.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}
.nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.navbar {
  position: relative;
  min-height: 50px;
  margin-bottom: 24px;
  border: 1px solid transparent;
}
@media (min-width: 576px) {
  .navbar {
    border-radius: 4px;
  }
}
@media (min-width: 576px) {
  .navbar-header {
    float: left;
  }
}
.navbar-collapse {
  padding-right: 15px;
  padding-left: 15px;
  overflow-x: visible;
  border-top: 1px solid transparent;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
  -webkit-overflow-scrolling: touch;
}
.navbar-collapse.in {
  overflow-y: auto;
}
@media (min-width: 576px) {
  .navbar-collapse {
    width: auto;
    border-top: 0;
    -webkit-box-shadow: none;
            box-shadow: none;
  }
  .navbar-collapse.collapse {
    display: block !important;
    height: auto !important;
    padding-bottom: 0;
    overflow: visible !important;
  }
  .navbar-collapse.in {
    overflow-y: visible;
  }
  .navbar-fixed-top .navbar-collapse,
  .navbar-static-top .navbar-collapse,
  .navbar-fixed-bottom .navbar-collapse {
    padding-right: 0;
    padding-left: 0;
  }
}
.navbar-fixed-top,
.navbar-fixed-bottom {
  position: fixed;
  right: 0;
  left: 0;
  z-index: 1030;
}
.navbar-fixed-top .navbar-collapse,
.navbar-fixed-bottom .navbar-collapse {
  max-height: 340px;
}
@media (max-device-width: 0px) and (orientation: landscape) {
  .navbar-fixed-top .navbar-collapse,
  .navbar-fixed-bottom .navbar-collapse {
    max-height: 200px;
  }
}
@media (min-width: 576px) {
  .navbar-fixed-top,
  .navbar-fixed-bottom {
    border-radius: 0;
  }
}
.navbar-fixed-top {
  top: 0;
  border-width: 0 0 1px;
}
.navbar-fixed-bottom {
  bottom: 0;
  margin-bottom: 0;
  border-width: 1px 0 0;
}
.container > .navbar-header,
.container-fluid > .navbar-header,
.container > .navbar-collapse,
.container-fluid > .navbar-collapse {
  margin-right: -15px;
  margin-left: -15px;
}
@media (min-width: 576px) {
  .container > .navbar-header,
  .container-fluid > .navbar-header,
  .container > .navbar-collapse,
  .container-fluid > .navbar-collapse {
    margin-right: 0;
    margin-left: 0;
  }
}
.navbar-static-top {
  z-index: 1000;
  border-width: 0 0 1px;
}
@media (min-width: 576px) {
  .navbar-static-top {
    border-radius: 0;
  }
}
.navbar-brand {
  float: left;
  height: 50px;
  padding: 13px 15px;
  font-size: 20px;
  line-height: 24px;
}
.navbar-brand:hover,
.navbar-brand:focus {
  text-decoration: none;
}
.navbar-brand > img {
  display: block;
}
@media (min-width: 576px) {
  .navbar > .container .navbar-brand,
  .navbar > .container-fluid .navbar-brand {
    margin-left: -15px;
  }
}
.navbar-toggle {
  position: relative;
  float: right;
  padding: 9px 10px;
  margin-right: 15px;
  margin-top: 8px;
  margin-bottom: 8px;
  background-color: transparent;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 4px;
}
.navbar-toggle:focus {
  outline: 0;
}
.navbar-toggle .icon-bar {
  display: block;
  width: 22px;
  height: 2px;
  border-radius: 1px;
}
.navbar-toggle .icon-bar + .icon-bar {
  margin-top: 4px;
}
@media (min-width: 576px) {
  .navbar-toggle {
    display: none;
  }
}
.navbar-nav {
  margin: 6.5px -15px;
}
.navbar-nav > li > a {
  padding-top: 10px;
  padding-bottom: 10px;
  line-height: 24px;
}
@media (max-width: 575px) {
  .navbar-nav .open .dropdown-menu {
    position: static;
    float: none;
    width: auto;
    margin-top: 0;
    background-color: transparent;
    border: 0;
    -webkit-box-shadow: none;
            box-shadow: none;
  }
  .navbar-nav .open .dropdown-menu > li > a,
  .navbar-nav .open .dropdown-menu .dropdown-header {
    padding: 5px 15px 5px 25px;
  }
  .navbar-nav .open .dropdown-menu > li > a {
    line-height: 24px;
  }
  .navbar-nav .open .dropdown-menu > li > a:hover,
  .navbar-nav .open .dropdown-menu > li > a:focus {
    background-image: none;
  }
}
@media (min-width: 576px) {
  .navbar-nav {
    float: left;
    margin: 0;
  }
  .navbar-nav > li {
    float: left;
  }
  .navbar-nav > li > a {
    padding-top: 13px;
    padding-bottom: 13px;
  }
}
.navbar-form {
  padding: 10px 15px;
  margin-right: -15px;
  margin-left: -15px;
  border-top: 1px solid transparent;
  border-bottom: 1px solid transparent;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
  margin-top: 6px;
  margin-bottom: 6px;
}
@media (min-width: 576px) {
  .navbar-form .form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .navbar-form .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }
  .navbar-form .form-control-static {
    display: inline-block;
  }
  .navbar-form .input-group {
    display: inline-table;
    vertical-align: middle;
  }
  .navbar-form .input-group .input-group-addon,
  .navbar-form .input-group .input-group-btn,
  .navbar-form .input-group .form-control {
    width: auto;
  }
  .navbar-form .input-group > .form-control {
    width: 100%;
  }
  .navbar-form .control-label {
    margin-bottom: 0;
    vertical-align: middle;
  }
  .navbar-form .radio,
  .navbar-form .checkbox {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .navbar-form .radio label,
  .navbar-form .checkbox label {
    padding-left: 0;
  }
  .navbar-form .radio input[type="radio"],
  .navbar-form .checkbox input[type="checkbox"] {
    position: relative;
    margin-left: 0;
  }
  .navbar-form .has-feedback .form-control-feedback {
    top: 0;
  }
}
@media (max-width: 575px) {
  .navbar-form .form-group {
    margin-bottom: 5px;
  }
  .navbar-form .form-group:last-child {
    margin-bottom: 0;
  }
}
@media (min-width: 576px) {
  .navbar-form {
    width: auto;
    padding-top: 0;
    padding-bottom: 0;
    margin-right: 0;
    margin-left: 0;
    border: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
  }
}
.navbar-nav > li > .dropdown-menu {
  margin-top: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.navbar-fixed-bottom .navbar-nav > li > .dropdown-menu {
  margin-bottom: 0;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.navbar-btn {
  margin-top: 6px;
  margin-bottom: 6px;
}
.navbar-btn.btn-sm {
  margin-top: 8.5px;
  margin-bottom: 8.5px;
}
.navbar-btn.btn-xs {
  margin-top: 14px;
  margin-bottom: 14px;
}
.navbar-text {
  margin-top: 13px;
  margin-bottom: 13px;
}
@media (min-width: 576px) {
  .navbar-text {
    float: left;
    margin-right: 15px;
    margin-left: 15px;
  }
}
@media (min-width: 576px) {
  .navbar-left {
    float: left !important;
  }
  .navbar-right {
    float: right !important;
    margin-right: -15px;
  }
  .navbar-right ~ .navbar-right {
    margin-right: 0;
  }
}
.navbar-default {
  background-color: #f8f8f8;
  border-color: #e7e7e7;
}
.navbar-default .navbar-brand {
  color: #777;
}
.navbar-default .navbar-brand:hover,
.navbar-default .navbar-brand:focus {
  color: #5e5e5e;
  background-color: transparent;
}
.navbar-default .navbar-text {
  color: #777;
}
.navbar-default .navbar-nav > li > a {
  color: #777;
}
.navbar-default .navbar-nav > li > a:hover,
.navbar-default .navbar-nav > li > a:focus {
  color: #333;
  background-color: transparent;
}
.navbar-default .navbar-nav > .active > a,
.navbar-default .navbar-nav > .active > a:hover,
.navbar-default .navbar-nav > .active > a:focus {
  color: #555;
  background-color: #e7e7e7;
}
.navbar-default .navbar-nav > .disabled > a,
.navbar-default .navbar-nav > .disabled > a:hover,
.navbar-default .navbar-nav > .disabled > a:focus {
  color: #ccc;
  background-color: transparent;
}
.navbar-default .navbar-nav > .open > a,
.navbar-default .navbar-nav > .open > a:hover,
.navbar-default .navbar-nav > .open > a:focus {
  color: #555;
  background-color: #e7e7e7;
}
@media (max-width: 575px) {
  .navbar-default .navbar-nav .open .dropdown-menu > li > a {
    color: #777;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > li > a:hover,
  .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus {
    color: #333;
    background-color: transparent;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > .active > a,
  .navbar-default .navbar-nav .open .dropdown-menu > .active > a:hover,
  .navbar-default .navbar-nav .open .dropdown-menu > .active > a:focus {
    color: #555;
    background-color: #e7e7e7;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a,
  .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:hover,
  .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:focus {
    color: #ccc;
    background-color: transparent;
  }
}
.navbar-default .navbar-toggle {
  border-color: #ddd;
}
.navbar-default .navbar-toggle:hover,
.navbar-default .navbar-toggle:focus {
  background-color: #ddd;
}
.navbar-default .navbar-toggle .icon-bar {
  background-color: #888;
}
.navbar-default .navbar-collapse,
.navbar-default .navbar-form {
  border-color: #e7e7e7;
}
.navbar-default .navbar-link {
  color: #777;
}
.navbar-default .navbar-link:hover {
  color: #333;
}
.navbar-default .btn-link {
  color: #777;
}
.navbar-default .btn-link:hover,
.navbar-default .btn-link:focus {
  color: #333;
}
.navbar-default .btn-link[disabled]:hover,
fieldset[disabled] .navbar-default .btn-link:hover,
.navbar-default .btn-link[disabled]:focus,
fieldset[disabled] .navbar-default .btn-link:focus {
  color: #ccc;
}
.navbar-inverse {
  background-color: #222;
  border-color: #080808;
}
.navbar-inverse .navbar-brand {
  color: #ffffff;
}
.navbar-inverse .navbar-brand:hover,
.navbar-inverse .navbar-brand:focus {
  color: #fff;
  background-color: transparent;
}
.navbar-inverse .navbar-text {
  color: #ffffff;
}
.navbar-inverse .navbar-nav > li > a {
  color: #ffffff;
}
.navbar-inverse .navbar-nav > li > a:hover,
.navbar-inverse .navbar-nav > li > a:focus {
  color: #fff;
  background-color: transparent;
}
.navbar-inverse .navbar-nav > .active > a,
.navbar-inverse .navbar-nav > .active > a:hover,
.navbar-inverse .navbar-nav > .active > a:focus {
  color: #fff;
  background-color: #080808;
}
.navbar-inverse .navbar-nav > .disabled > a,
.navbar-inverse .navbar-nav > .disabled > a:hover,
.navbar-inverse .navbar-nav > .disabled > a:focus {
  color: #444;
  background-color: transparent;
}
.navbar-inverse .navbar-nav > .open > a,
.navbar-inverse .navbar-nav > .open > a:hover,
.navbar-inverse .navbar-nav > .open > a:focus {
  color: #fff;
  background-color: #080808;
}
@media (max-width: 575px) {
  .navbar-inverse .navbar-nav .open .dropdown-menu > .dropdown-header {
    border-color: #080808;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu .divider {
    background-color: #080808;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {
    color: #ffffff;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:hover,
  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:focus {
    color: #fff;
    background-color: transparent;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a,
  .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:hover,
  .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:focus {
    color: #fff;
    background-color: #080808;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a,
  .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:hover,
  .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:focus {
    color: #444;
    background-color: transparent;
  }
}
.navbar-inverse .navbar-toggle {
  border-color: #333;
}
.navbar-inverse .navbar-toggle:hover,
.navbar-inverse .navbar-toggle:focus {
  background-color: #333;
}
.navbar-inverse .navbar-toggle .icon-bar {
  background-color: #fff;
}
.navbar-inverse .navbar-collapse,
.navbar-inverse .navbar-form {
  border-color: #101010;
}
.navbar-inverse .navbar-link {
  color: #ffffff;
}
.navbar-inverse .navbar-link:hover {
  color: #fff;
}
.navbar-inverse .btn-link {
  color: #ffffff;
}
.navbar-inverse .btn-link:hover,
.navbar-inverse .btn-link:focus {
  color: #fff;
}
.navbar-inverse .btn-link[disabled]:hover,
fieldset[disabled] .navbar-inverse .btn-link:hover,
.navbar-inverse .btn-link[disabled]:focus,
fieldset[disabled] .navbar-inverse .btn-link:focus {
  color: #444;
}
.breadcrumb {
  padding: 0 0;
  margin-bottom: 24px;
  list-style: none;
  background-color: transparent;
  border-radius: 4px;
}
.breadcrumb > li {
  display: inline-block;
}
.breadcrumb > li + li:before {
  padding: 0 5px;
  color: rgba(47, 53, 56, 0.6);
  content: "\00a0";
}
.breadcrumb > .active {
  color: rgba(47, 53, 56, 0.6);
}
.pagination {
  display: inline-block;
  padding-left: 0;
  margin: 24px 0;
  border-radius: 4px;
}
.pagination > li {
  display: inline;
}
.pagination > li > a,
.pagination > li > span {
  position: relative;
  float: left;
  padding: 6px 12px;
  margin-left: -1px;
  line-height: 1.5;
  color: #DF0000;
  text-decoration: none;
  background-color: #fff;
  border: 1px solid #ddd;
}
.pagination > li > a:hover,
.pagination > li > span:hover,
.pagination > li > a:focus,
.pagination > li > span:focus {
  z-index: 2;
  color: #930000;
  background-color: #F7F7F7;
  border-color: #ddd;
}
.pagination > li:first-child > a,
.pagination > li:first-child > span {
  margin-left: 0;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
.pagination > li:last-child > a,
.pagination > li:last-child > span {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.pagination > .active > a,
.pagination > .active > span,
.pagination > .active > a:hover,
.pagination > .active > span:hover,
.pagination > .active > a:focus,
.pagination > .active > span:focus {
  z-index: 3;
  color: #fff;
  cursor: default;
  background-color: #DF0000;
  border-color: #DF0000;
}
.pagination > .disabled > span,
.pagination > .disabled > span:hover,
.pagination > .disabled > span:focus,
.pagination > .disabled > a,
.pagination > .disabled > a:hover,
.pagination > .disabled > a:focus {
  color: #EBEBEB;
  cursor: not-allowed;
  background-color: #fff;
  border-color: #ddd;
}
.pagination-lg > li > a,
.pagination-lg > li > span {
  padding: 10px 16px;
  font-size: 20px;
  line-height: 1.3333333;
}
.pagination-lg > li:first-child > a,
.pagination-lg > li:first-child > span {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}
.pagination-lg > li:last-child > a,
.pagination-lg > li:last-child > span {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}
.pagination-sm > li > a,
.pagination-sm > li > span {
  padding: 5px 10px;
  font-size: 14px;
  line-height: 1.5;
}
.pagination-sm > li:first-child > a,
.pagination-sm > li:first-child > span {
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}
.pagination-sm > li:last-child > a,
.pagination-sm > li:last-child > span {
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}
.pager {
  padding-left: 0;
  margin: 24px 0;
  text-align: center;
  list-style: none;
}
.pager li {
  display: inline;
}
.pager li > a,
.pager li > span {
  display: inline-block;
  padding: 5px 14px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 15px;
}
.pager li > a:hover,
.pager li > a:focus {
  text-decoration: none;
  background-color: #F7F7F7;
}
.pager .next > a,
.pager .next > span {
  float: right;
}
.pager .previous > a,
.pager .previous > span {
  float: left;
}
.pager .disabled > a,
.pager .disabled > a:hover,
.pager .disabled > a:focus,
.pager .disabled > span {
  color: #EBEBEB;
  cursor: not-allowed;
  background-color: #fff;
}
.label {
  display: inline;
  padding: 0.2em 0.6em 0.3em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25em;
}
a.label:hover,
a.label:focus {
  color: #fff;
  text-decoration: none;
  cursor: pointer;
}
.label:empty {
  display: none;
}
.btn .label {
  position: relative;
  top: -1px;
}
.label-default {
  background-color: #EBEBEB;
}
.label-default[href]:hover,
.label-default[href]:focus {
  background-color: #d2d2d2;
}
.label-primary {
  background-color: #DF0000;
}
.label-primary[href]:hover,
.label-primary[href]:focus {
  background-color: #ac0000;
}
.label-success {
  background-color: #00B25B;
}
.label-success[href]:hover,
.label-success[href]:focus {
  background-color: #007f41;
}
.label-info {
  background-color: #5bc0de;
}
.label-info[href]:hover,
.label-info[href]:focus {
  background-color: #31b0d5;
}
.label-warning {
  background-color: #f0ad4e;
}
.label-warning[href]:hover,
.label-warning[href]:focus {
  background-color: #ec971f;
}
.label-danger {
  background-color: #d9534f;
}
.label-danger[href]:hover,
.label-danger[href]:focus {
  background-color: #c9302c;
}
.badge {
  display: inline-block;
  min-width: 10px;
  padding: 3px 7px;
  font-size: 14px;
  font-weight: bold;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  background-color: #EBEBEB;
  border-radius: 10px;
}
.badge:empty {
  display: none;
}
.btn .badge {
  position: relative;
  top: -1px;
}
.btn-xs .badge,
.btn-group-xs > .btn .badge {
  top: 0;
  padding: 1px 5px;
}
a.badge:hover,
a.badge:focus {
  color: #fff;
  text-decoration: none;
  cursor: pointer;
}
.list-group-item.active > .badge,
.nav-pills > .active > a > .badge {
  color: #DF0000;
  background-color: #fff;
}
.list-group-item > .badge {
  float: right;
}
.list-group-item > .badge + .badge {
  margin-right: 5px;
}
.nav-pills > li > a > .badge {
  margin-left: 3px;
}
.jumbotron {
  padding-top: 30px;
  padding-bottom: 30px;
  margin-bottom: 30px;
  color: inherit;
  background-color: #F7F7F7;
}
.jumbotron h1,
.jumbotron .h1 {
  color: inherit;
}
.jumbotron p {
  margin-bottom: 15px;
  font-size: 24px;
  font-weight: 200;
}
.jumbotron > hr {
  border-top-color: #dedede;
}
.container .jumbotron,
.container-fluid .jumbotron {
  padding-right: 15px;
  padding-left: 15px;
  border-radius: 6px;
}
.jumbotron .container {
  max-width: 100%;
}
@media screen and (min-width: 576px) {
  .jumbotron {
    padding-top: 48px;
    padding-bottom: 48px;
  }
  .container .jumbotron,
  .container-fluid .jumbotron {
    padding-right: 60px;
    padding-left: 60px;
  }
  .jumbotron h1,
  .jumbotron .h1 {
    font-size: 72px;
  }
}
.thumbnail {
  display: block;
  padding: 4px;
  margin-bottom: 24px;
  line-height: 1.5;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  -webkit-transition: border 0.2s ease-in-out;
  transition: border 0.2s ease-in-out;
}
.thumbnail > img,
.thumbnail a > img {
  margin-right: auto;
  margin-left: auto;
}
a.thumbnail:hover,
a.thumbnail:focus,
a.thumbnail.active {
  border-color: #DF0000;
}
.thumbnail .caption {
  padding: 9px;
  color: #2F3538;
}
.alert {
  padding: 15px;
  margin-bottom: 24px;
  border: 1px solid transparent;
  border-radius: 4px;
}
.alert h4 {
  margin-top: 0;
  color: inherit;
}
.alert .alert-link {
  font-weight: bold;
}
.alert > p,
.alert > ul {
  margin-bottom: 0;
}
.alert > p + p {
  margin-top: 5px;
}
.alert-dismissable,
.alert-dismissible {
  padding-right: 35px;
}
.alert-dismissable .close,
.alert-dismissible .close {
  position: relative;
  top: -2px;
  right: -21px;
  color: inherit;
}
.alert-success {
  color: #3c763d;
  background-color: #dff0d8;
  border-color: #d6e9c6;
}
.alert-success hr {
  border-top-color: #c9e2b3;
}
.alert-success .alert-link {
  color: #2b542c;
}
.alert-info {
  color: #31708f;
  background-color: #d9edf7;
  border-color: #bce8f1;
}
.alert-info hr {
  border-top-color: #a6e1ec;
}
.alert-info .alert-link {
  color: #245269;
}
.alert-warning {
  color: #8a6d3b;
  background-color: #fcf8e3;
  border-color: #faebcc;
}
.alert-warning hr {
  border-top-color: #f7e1b5;
}
.alert-warning .alert-link {
  color: #66512c;
}
.alert-danger {
  color: #a94442;
  background-color: #f2dede;
  border-color: #ebccd1;
}
.alert-danger hr {
  border-top-color: #e4b9c0;
}
.alert-danger .alert-link {
  color: #843534;
}
@-webkit-keyframes progress-bar-stripes {
  from {
    background-position: 40px 0;
  }
  to {
    background-position: 0 0;
  }
}
@keyframes progress-bar-stripes {
  from {
    background-position: 40px 0;
  }
  to {
    background-position: 0 0;
  }
}
.progress {
  height: 24px;
  margin-bottom: 24px;
  overflow: hidden;
  background-color: #f5f5f5;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}
.progress-bar {
  float: left;
  width: 0%;
  height: 100%;
  font-size: 14px;
  line-height: 24px;
  color: #fff;
  text-align: center;
  background-color: #DF0000;
  -webkit-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  -webkit-transition: width 0.6s ease;
  transition: width 0.6s ease;
}
.progress-striped .progress-bar,
.progress-bar-striped {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.progress.active .progress-bar,
.progress-bar.active {
  -webkit-animation: progress-bar-stripes 2s linear infinite;
  animation: progress-bar-stripes 2s linear infinite;
}
.progress-bar-success {
  background-color: #00B25B;
}
.progress-striped .progress-bar-success {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.progress-bar-info {
  background-color: #5bc0de;
}
.progress-striped .progress-bar-info {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.progress-bar-warning {
  background-color: #f0ad4e;
}
.progress-striped .progress-bar-warning {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.progress-bar-danger {
  background-color: #d9534f;
}
.progress-striped .progress-bar-danger {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.media {
  margin-top: 15px;
}
.media:first-child {
  margin-top: 0;
}
.media,
.media-body {
  overflow: hidden;
  zoom: 1;
}
.media-body {
  width: 10000px;
}
.media-object {
  display: block;
}
.media-object.img-thumbnail {
  max-width: none;
}
.media-right,
.media > .pull-right {
  padding-left: 10px;
}
.media-left,
.media > .pull-left {
  padding-right: 10px;
}
.media-left,
.media-right,
.media-body {
  display: table-cell;
  vertical-align: top;
}
.media-middle {
  vertical-align: middle;
}
.media-bottom {
  vertical-align: bottom;
}
.media-heading {
  margin-top: 0;
  margin-bottom: 5px;
}
.media-list {
  padding-left: 0;
  list-style: none;
}
.list-group {
  padding-left: 0;
  margin-bottom: 20px;
}
.list-group-item {
  position: relative;
  display: block;
  padding: 10px 15px;
  margin-bottom: -1px;
  background-color: #fff;
  border: 1px solid #ddd;
}
.list-group-item:first-child {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}
.list-group-item:last-child {
  margin-bottom: 0;
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
}
.list-group-item.disabled,
.list-group-item.disabled:hover,
.list-group-item.disabled:focus {
  color: #EBEBEB;
  cursor: not-allowed;
  background-color: #F7F7F7;
}
.list-group-item.disabled .list-group-item-heading,
.list-group-item.disabled:hover .list-group-item-heading,
.list-group-item.disabled:focus .list-group-item-heading {
  color: inherit;
}
.list-group-item.disabled .list-group-item-text,
.list-group-item.disabled:hover .list-group-item-text,
.list-group-item.disabled:focus .list-group-item-text {
  color: #EBEBEB;
}
.list-group-item.active,
.list-group-item.active:hover,
.list-group-item.active:focus {
  z-index: 2;
  color: #DF0000;
  background-color: #F7F7F7;
  border-color: #F7F7F7;
}
.list-group-item.active .list-group-item-heading,
.list-group-item.active:hover .list-group-item-heading,
.list-group-item.active:focus .list-group-item-heading,
.list-group-item.active .list-group-item-heading > small,
.list-group-item.active:hover .list-group-item-heading > small,
.list-group-item.active:focus .list-group-item-heading > small,
.list-group-item.active .list-group-item-heading > .small,
.list-group-item.active:hover .list-group-item-heading > .small,
.list-group-item.active:focus .list-group-item-heading > .small {
  color: inherit;
}
.list-group-item.active .list-group-item-text,
.list-group-item.active:hover .list-group-item-text,
.list-group-item.active:focus .list-group-item-text {
  color: #ffffff;
}
a.list-group-item,
button.list-group-item {
  color: #555;
}
a.list-group-item .list-group-item-heading,
button.list-group-item .list-group-item-heading {
  color: #333;
}
a.list-group-item:hover,
button.list-group-item:hover,
a.list-group-item:focus,
button.list-group-item:focus {
  color: #555;
  text-decoration: none;
  background-color: #f5f5f5;
}
button.list-group-item {
  width: 100%;
  text-align: left;
}
.list-group-item-success {
  color: #3c763d;
  background-color: #dff0d8;
}
a.list-group-item-success,
button.list-group-item-success {
  color: #3c763d;
}
a.list-group-item-success .list-group-item-heading,
button.list-group-item-success .list-group-item-heading {
  color: inherit;
}
a.list-group-item-success:hover,
button.list-group-item-success:hover,
a.list-group-item-success:focus,
button.list-group-item-success:focus {
  color: #3c763d;
  background-color: #d0e9c6;
}
a.list-group-item-success.active,
button.list-group-item-success.active,
a.list-group-item-success.active:hover,
button.list-group-item-success.active:hover,
a.list-group-item-success.active:focus,
button.list-group-item-success.active:focus {
  color: #fff;
  background-color: #3c763d;
  border-color: #3c763d;
}
.list-group-item-info {
  color: #31708f;
  background-color: #d9edf7;
}
a.list-group-item-info,
button.list-group-item-info {
  color: #31708f;
}
a.list-group-item-info .list-group-item-heading,
button.list-group-item-info .list-group-item-heading {
  color: inherit;
}
a.list-group-item-info:hover,
button.list-group-item-info:hover,
a.list-group-item-info:focus,
button.list-group-item-info:focus {
  color: #31708f;
  background-color: #c4e3f3;
}
a.list-group-item-info.active,
button.list-group-item-info.active,
a.list-group-item-info.active:hover,
button.list-group-item-info.active:hover,
a.list-group-item-info.active:focus,
button.list-group-item-info.active:focus {
  color: #fff;
  background-color: #31708f;
  border-color: #31708f;
}
.list-group-item-warning {
  color: #8a6d3b;
  background-color: #fcf8e3;
}
a.list-group-item-warning,
button.list-group-item-warning {
  color: #8a6d3b;
}
a.list-group-item-warning .list-group-item-heading,
button.list-group-item-warning .list-group-item-heading {
  color: inherit;
}
a.list-group-item-warning:hover,
button.list-group-item-warning:hover,
a.list-group-item-warning:focus,
button.list-group-item-warning:focus {
  color: #8a6d3b;
  background-color: #faf2cc;
}
a.list-group-item-warning.active,
button.list-group-item-warning.active,
a.list-group-item-warning.active:hover,
button.list-group-item-warning.active:hover,
a.list-group-item-warning.active:focus,
button.list-group-item-warning.active:focus {
  color: #fff;
  background-color: #8a6d3b;
  border-color: #8a6d3b;
}
.list-group-item-danger {
  color: #a94442;
  background-color: #f2dede;
}
a.list-group-item-danger,
button.list-group-item-danger {
  color: #a94442;
}
a.list-group-item-danger .list-group-item-heading,
button.list-group-item-danger .list-group-item-heading {
  color: inherit;
}
a.list-group-item-danger:hover,
button.list-group-item-danger:hover,
a.list-group-item-danger:focus,
button.list-group-item-danger:focus {
  color: #a94442;
  background-color: #ebcccc;
}
a.list-group-item-danger.active,
button.list-group-item-danger.active,
a.list-group-item-danger.active:hover,
button.list-group-item-danger.active:hover,
a.list-group-item-danger.active:focus,
button.list-group-item-danger.active:focus {
  color: #fff;
  background-color: #a94442;
  border-color: #a94442;
}
.list-group-item-heading {
  margin-top: 0;
  margin-bottom: 5px;
}
.list-group-item-text {
  margin-bottom: 0;
  line-height: 1.3;
}
.panel {
  margin-bottom: 24px;
  background-color: #fff;
  border: 1px solid transparent;
  border-radius: 4px;
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}
.panel-body {
  padding: 15px;
}
.panel-heading {
  padding: 10px 15px;
  border-bottom: 1px solid transparent;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}
.panel-heading > .dropdown .dropdown-toggle {
  color: inherit;
}
.panel-title {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 18px;
  color: inherit;
}
.panel-title > a,
.panel-title > small,
.panel-title > .small,
.panel-title > small > a,
.panel-title > .small > a {
  color: inherit;
}
.panel-footer {
  padding: 10px 15px;
  background-color: #f5f5f5;
  border-top: 1px solid #ddd;
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px;
}
.panel > .list-group,
.panel > .panel-collapse > .list-group {
  margin-bottom: 0;
}
.panel > .list-group .list-group-item,
.panel > .panel-collapse > .list-group .list-group-item {
  border-width: 1px 0;
  border-radius: 0;
}
.panel > .list-group:first-child .list-group-item:first-child,
.panel > .panel-collapse > .list-group:first-child .list-group-item:first-child {
  border-top: 0;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}
.panel > .list-group:last-child .list-group-item:last-child,
.panel > .panel-collapse > .list-group:last-child .list-group-item:last-child {
  border-bottom: 0;
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px;
}
.panel > .panel-heading + .panel-collapse > .list-group .list-group-item:first-child {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.panel-heading + .list-group .list-group-item:first-child {
  border-top-width: 0;
}
.list-group + .panel-footer {
  border-top-width: 0;
}
.panel > .table,
.panel > .table-responsive > .table,
.panel > .panel-collapse > .table {
  margin-bottom: 0;
}
.panel > .table caption,
.panel > .table-responsive > .table caption,
.panel > .panel-collapse > .table caption {
  padding-right: 15px;
  padding-left: 15px;
}
.panel > .table:first-child,
.panel > .table-responsive:first-child > .table:first-child {
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}
.panel > .table:first-child > thead:first-child > tr:first-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child,
.panel > .table:first-child > tbody:first-child > tr:first-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child {
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}
.panel > .table:first-child > thead:first-child > tr:first-child td:first-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:first-child,
.panel > .table:first-child > tbody:first-child > tr:first-child td:first-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:first-child,
.panel > .table:first-child > thead:first-child > tr:first-child th:first-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:first-child,
.panel > .table:first-child > tbody:first-child > tr:first-child th:first-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:first-child {
  border-top-left-radius: 3px;
}
.panel > .table:first-child > thead:first-child > tr:first-child td:last-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:last-child,
.panel > .table:first-child > tbody:first-child > tr:first-child td:last-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:last-child,
.panel > .table:first-child > thead:first-child > tr:first-child th:last-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:last-child,
.panel > .table:first-child > tbody:first-child > tr:first-child th:last-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:last-child {
  border-top-right-radius: 3px;
}
.panel > .table:last-child,
.panel > .table-responsive:last-child > .table:last-child {
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px;
}
.panel > .table:last-child > tbody:last-child > tr:last-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child {
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px;
}
.panel > .table:last-child > tbody:last-child > tr:last-child td:first-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:first-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child td:first-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:first-child,
.panel > .table:last-child > tbody:last-child > tr:last-child th:first-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:first-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child th:first-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:first-child {
  border-bottom-left-radius: 3px;
}
.panel > .table:last-child > tbody:last-child > tr:last-child td:last-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:last-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child td:last-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:last-child,
.panel > .table:last-child > tbody:last-child > tr:last-child th:last-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:last-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child th:last-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:last-child {
  border-bottom-right-radius: 3px;
}
.panel > .panel-body + .table,
.panel > .panel-body + .table-responsive,
.panel > .table + .panel-body,
.panel > .table-responsive + .panel-body {
  border-top: 1px solid #EBEBEB;
}
.panel > .table > tbody:first-child > tr:first-child th,
.panel > .table > tbody:first-child > tr:first-child td {
  border-top: 0;
}
.panel > .table-bordered,
.panel > .table-responsive > .table-bordered {
  border: 0;
}
.panel > .table-bordered > thead > tr > th:first-child,
.panel > .table-responsive > .table-bordered > thead > tr > th:first-child,
.panel > .table-bordered > tbody > tr > th:first-child,
.panel > .table-responsive > .table-bordered > tbody > tr > th:first-child,
.panel > .table-bordered > tfoot > tr > th:first-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > th:first-child,
.panel > .table-bordered > thead > tr > td:first-child,
.panel > .table-responsive > .table-bordered > thead > tr > td:first-child,
.panel > .table-bordered > tbody > tr > td:first-child,
.panel > .table-responsive > .table-bordered > tbody > tr > td:first-child,
.panel > .table-bordered > tfoot > tr > td:first-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > td:first-child {
  border-left: 0;
}
.panel > .table-bordered > thead > tr > th:last-child,
.panel > .table-responsive > .table-bordered > thead > tr > th:last-child,
.panel > .table-bordered > tbody > tr > th:last-child,
.panel > .table-responsive > .table-bordered > tbody > tr > th:last-child,
.panel > .table-bordered > tfoot > tr > th:last-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > th:last-child,
.panel > .table-bordered > thead > tr > td:last-child,
.panel > .table-responsive > .table-bordered > thead > tr > td:last-child,
.panel > .table-bordered > tbody > tr > td:last-child,
.panel > .table-responsive > .table-bordered > tbody > tr > td:last-child,
.panel > .table-bordered > tfoot > tr > td:last-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > td:last-child {
  border-right: 0;
}
.panel > .table-bordered > thead > tr:first-child > td,
.panel > .table-responsive > .table-bordered > thead > tr:first-child > td,
.panel > .table-bordered > tbody > tr:first-child > td,
.panel > .table-responsive > .table-bordered > tbody > tr:first-child > td,
.panel > .table-bordered > thead > tr:first-child > th,
.panel > .table-responsive > .table-bordered > thead > tr:first-child > th,
.panel > .table-bordered > tbody > tr:first-child > th,
.panel > .table-responsive > .table-bordered > tbody > tr:first-child > th {
  border-bottom: 0;
}
.panel > .table-bordered > tbody > tr:last-child > td,
.panel > .table-responsive > .table-bordered > tbody > tr:last-child > td,
.panel > .table-bordered > tfoot > tr:last-child > td,
.panel > .table-responsive > .table-bordered > tfoot > tr:last-child > td,
.panel > .table-bordered > tbody > tr:last-child > th,
.panel > .table-responsive > .table-bordered > tbody > tr:last-child > th,
.panel > .table-bordered > tfoot > tr:last-child > th,
.panel > .table-responsive > .table-bordered > tfoot > tr:last-child > th {
  border-bottom: 0;
}
.panel > .table-responsive {
  margin-bottom: 0;
  border: 0;
}
.panel-group {
  margin-bottom: 24px;
}
.panel-group .panel {
  margin-bottom: 0;
  border-radius: 4px;
}
.panel-group .panel + .panel {
  margin-top: 5px;
}
.panel-group .panel-heading {
  border-bottom: 0;
}
.panel-group .panel-heading + .panel-collapse > .panel-body,
.panel-group .panel-heading + .panel-collapse > .list-group {
  border-top: 1px solid #ddd;
}
.panel-group .panel-footer {
  border-top: 0;
}
.panel-group .panel-footer + .panel-collapse .panel-body {
  border-bottom: 1px solid #ddd;
}
.panel-default {
  border-color: #ddd;
}
.panel-default > .panel-heading {
  color: #2F3538;
  background-color: #f5f5f5;
  border-color: #ddd;
}
.panel-default > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #ddd;
}
.panel-default > .panel-heading .badge {
  color: #f5f5f5;
  background-color: #2F3538;
}
.panel-default > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #ddd;
}
.panel-primary {
  border-color: #DF0000;
}
.panel-primary > .panel-heading {
  color: #fff;
  background-color: #DF0000;
  border-color: #DF0000;
}
.panel-primary > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #DF0000;
}
.panel-primary > .panel-heading .badge {
  color: #DF0000;
  background-color: #fff;
}
.panel-primary > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #DF0000;
}
.panel-success {
  border-color: #d6e9c6;
}
.panel-success > .panel-heading {
  color: #3c763d;
  background-color: #dff0d8;
  border-color: #d6e9c6;
}
.panel-success > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #d6e9c6;
}
.panel-success > .panel-heading .badge {
  color: #dff0d8;
  background-color: #3c763d;
}
.panel-success > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #d6e9c6;
}
.panel-info {
  border-color: #bce8f1;
}
.panel-info > .panel-heading {
  color: #31708f;
  background-color: #d9edf7;
  border-color: #bce8f1;
}
.panel-info > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #bce8f1;
}
.panel-info > .panel-heading .badge {
  color: #d9edf7;
  background-color: #31708f;
}
.panel-info > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #bce8f1;
}
.panel-warning {
  border-color: #faebcc;
}
.panel-warning > .panel-heading {
  color: #8a6d3b;
  background-color: #fcf8e3;
  border-color: #faebcc;
}
.panel-warning > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #faebcc;
}
.panel-warning > .panel-heading .badge {
  color: #fcf8e3;
  background-color: #8a6d3b;
}
.panel-warning > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #faebcc;
}
.panel-danger {
  border-color: #ebccd1;
}
.panel-danger > .panel-heading {
  color: #a94442;
  background-color: #f2dede;
  border-color: #ebccd1;
}
.panel-danger > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #ebccd1;
}
.panel-danger > .panel-heading .badge {
  color: #f2dede;
  background-color: #a94442;
}
.panel-danger > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #ebccd1;
}
.embed-responsive {
  position: relative;
  display: block;
  height: 0;
  padding: 0;
  overflow: hidden;
}
.embed-responsive .embed-responsive-item,
.embed-responsive iframe,
.embed-responsive embed,
.embed-responsive object,
.embed-responsive video {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}
.embed-responsive-16by9 {
  padding-bottom: 56.25%;
}
.embed-responsive-4by3 {
  padding-bottom: 75%;
}
.well {
  min-height: 20px;
  padding: 19px;
  margin-bottom: 20px;
  background-color: #f5f5f5;
  border: 1px solid #e3e3e3;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
}
.well blockquote {
  border-color: #ddd;
  border-color: rgba(0, 0, 0, 0.15);
}
.well-lg {
  padding: 24px;
  border-radius: 6px;
}
.well-sm {
  padding: 9px;
  border-radius: 3px;
}
.close {
  float: right;
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  filter: alpha(opacity=20);
  opacity: 0.2;
}
.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
  filter: alpha(opacity=50);
  opacity: 0.5;
}
button.close {
  padding: 0;
  cursor: pointer;
  background: transparent;
  border: 0;
  -webkit-appearance: none;
  -moz-appearance: none;
       appearance: none;
}
.modal-open {
  overflow: hidden;
}
.modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1050;
  display: none;
  overflow: hidden;
  -webkit-overflow-scrolling: touch;
  outline: 0;
}
.modal.fade .modal-dialog {
  -webkit-transform: translate(0, -25%);
  transform: translate(0, -25%);
  -webkit-transition: -webkit-transform 0.3s ease-out;
  transition: -webkit-transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
  transition: transform 0.3s ease-out, -webkit-transform 0.3s ease-out;
}
.modal.in .modal-dialog {
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
}
.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto;
}
.modal-dialog {
  position: relative;
  width: auto;
  margin: 10px;
}
.modal-content {
  position: relative;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #999;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  outline: 0;
}
.modal-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1040;
  background-color: #000;
}
.modal-backdrop.fade {
  filter: alpha(opacity=0);
  opacity: 0;
}
.modal-backdrop.in {
  filter: alpha(opacity=50);
  opacity: 0.5;
}
.modal-header {
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
}
.modal-header .close {
  margin-top: -2px;
}
.modal-title {
  margin: 0;
  line-height: 1.5;
}
.modal-body {
  position: relative;
  padding: 15px;
}
.modal-footer {
  padding: 15px;
  text-align: right;
  border-top: 1px solid #e5e5e5;
}
.modal-footer .btn + .btn {
  margin-bottom: 0;
  margin-left: 5px;
}
.modal-footer .btn-group .btn + .btn {
  margin-left: -1px;
}
.modal-footer .btn-block + .btn-block {
  margin-left: 0;
}
.modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll;
}
@media (min-width: 576px) {
  .modal-dialog {
    width: 600px;
    margin: 30px auto;
  }
  .modal-content {
    -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  }
  .modal-sm {
    width: 300px;
  }
}
@media (min-width: 768px) {
  .modal-lg {
    width: 900px;
  }
}
.tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  font-family: 'Bai Jamjuree', sans-serif;
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  line-break: auto;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  word-wrap: normal;
  white-space: normal;
  font-size: 14px;
  filter: alpha(opacity=0);
  opacity: 0;
}
.tooltip.in {
  filter: alpha(opacity=90);
  opacity: 0.9;
}
.tooltip.top {
  padding: 5px 0;
  margin-top: -3px;
}
.tooltip.right {
  padding: 0 5px;
  margin-left: 3px;
}
.tooltip.bottom {
  padding: 5px 0;
  margin-top: 3px;
}
.tooltip.left {
  padding: 0 5px;
  margin-left: -3px;
}
.tooltip.top .tooltip-arrow {
  bottom: 0;
  left: 50%;
  margin-left: -5px;
  border-width: 5px 5px 0;
  border-top-color: #000;
}
.tooltip.top-left .tooltip-arrow {
  right: 5px;
  bottom: 0;
  margin-bottom: -5px;
  border-width: 5px 5px 0;
  border-top-color: #000;
}
.tooltip.top-right .tooltip-arrow {
  bottom: 0;
  left: 5px;
  margin-bottom: -5px;
  border-width: 5px 5px 0;
  border-top-color: #000;
}
.tooltip.right .tooltip-arrow {
  top: 50%;
  left: 0;
  margin-top: -5px;
  border-width: 5px 5px 5px 0;
  border-right-color: #000;
}
.tooltip.left .tooltip-arrow {
  top: 50%;
  right: 0;
  margin-top: -5px;
  border-width: 5px 0 5px 5px;
  border-left-color: #000;
}
.tooltip.bottom .tooltip-arrow {
  top: 0;
  left: 50%;
  margin-left: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000;
}
.tooltip.bottom-left .tooltip-arrow {
  top: 0;
  right: 5px;
  margin-top: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000;
}
.tooltip.bottom-right .tooltip-arrow {
  top: 0;
  left: 5px;
  margin-top: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000;
}
.tooltip-inner {
  max-width: 200px;
  padding: 3px 8px;
  color: #fff;
  text-align: center;
  background-color: #000;
  border-radius: 4px;
}
.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1060;
  display: none;
  max-width: 276px;
  padding: 1px;
  font-family: 'Bai Jamjuree', sans-serif;
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  line-break: auto;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  word-wrap: normal;
  white-space: normal;
  font-size: 16px;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}
.popover.top {
  margin-top: -10px;
}
.popover.right {
  margin-left: 10px;
}
.popover.bottom {
  margin-top: 10px;
}
.popover.left {
  margin-left: -10px;
}
.popover > .arrow {
  border-width: 11px;
}
.popover > .arrow,
.popover > .arrow:after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.popover > .arrow:after {
  content: "";
  border-width: 10px;
}
.popover.top > .arrow {
  bottom: -11px;
  left: 50%;
  margin-left: -11px;
  border-top-color: #999999;
  border-top-color: rgba(0, 0, 0, 0.25);
  border-bottom-width: 0;
}
.popover.top > .arrow:after {
  bottom: 1px;
  margin-left: -10px;
  content: " ";
  border-top-color: #fff;
  border-bottom-width: 0;
}
.popover.right > .arrow {
  top: 50%;
  left: -11px;
  margin-top: -11px;
  border-right-color: #999999;
  border-right-color: rgba(0, 0, 0, 0.25);
  border-left-width: 0;
}
.popover.right > .arrow:after {
  bottom: -10px;
  left: 1px;
  content: " ";
  border-right-color: #fff;
  border-left-width: 0;
}
.popover.bottom > .arrow {
  top: -11px;
  left: 50%;
  margin-left: -11px;
  border-top-width: 0;
  border-bottom-color: #999999;
  border-bottom-color: rgba(0, 0, 0, 0.25);
}
.popover.bottom > .arrow:after {
  top: 1px;
  margin-left: -10px;
  content: " ";
  border-top-width: 0;
  border-bottom-color: #fff;
}
.popover.left > .arrow {
  top: 50%;
  right: -11px;
  margin-top: -11px;
  border-right-width: 0;
  border-left-color: #999999;
  border-left-color: rgba(0, 0, 0, 0.25);
}
.popover.left > .arrow:after {
  right: 1px;
  bottom: -10px;
  content: " ";
  border-right-width: 0;
  border-left-color: #fff;
}
.popover-title {
  padding: 8px 14px;
  margin: 0;
  font-size: 16px;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  border-radius: 5px 5px 0 0;
}
.popover-content {
  padding: 9px 14px;
}
.carousel {
  position: relative;
}
.carousel-inner {
  position: relative;
  width: 100%;
  overflow: hidden;
}
.carousel-inner > .item {
  position: relative;
  display: none;
  -webkit-transition: 0.6s ease-in-out left;
  transition: 0.6s ease-in-out left;
}
.carousel-inner > .item > img,
.carousel-inner > .item > a > img {
  line-height: 1;
}
@media all and (transform-3d), (-webkit-transform-3d) {
  .carousel-inner > .item {
    -webkit-transition: -webkit-transform 0.6s ease-in-out;
    transition: -webkit-transform 0.6s ease-in-out;
    transition: transform 0.6s ease-in-out;
    transition: transform 0.6s ease-in-out, -webkit-transform 0.6s ease-in-out;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-perspective: 1000px;
    perspective: 1000px;
  }
  .carousel-inner > .item.next,
  .carousel-inner > .item.active.right {
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
    left: 0;
  }
  .carousel-inner > .item.prev,
  .carousel-inner > .item.active.left {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
    left: 0;
  }
  .carousel-inner > .item.next.left,
  .carousel-inner > .item.prev.right,
  .carousel-inner > .item.active {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    left: 0;
  }
}
.carousel-inner > .active,
.carousel-inner > .next,
.carousel-inner > .prev {
  display: block;
}
.carousel-inner > .active {
  left: 0;
}
.carousel-inner > .next,
.carousel-inner > .prev {
  position: absolute;
  top: 0;
  width: 100%;
}
.carousel-inner > .next {
  left: 100%;
}
.carousel-inner > .prev {
  left: -100%;
}
.carousel-inner > .next.left,
.carousel-inner > .prev.right {
  left: 0;
}
.carousel-inner > .active.left {
  left: -100%;
}
.carousel-inner > .active.right {
  left: 100%;
}
.carousel-control {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 15%;
  font-size: 20px;
  color: #fff;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
  background-color: rgba(0, 0, 0, 0);
  filter: alpha(opacity=50);
  opacity: 0.5;
}
.carousel-control.left {
  background-image: -webkit-gradient(linear, left top, right top, from(rgba(0, 0, 0, 0.5)), to(rgba(0, 0, 0, 0.0001)));
  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#80000000', endColorstr='#00000000', GradientType=1);
  background-repeat: repeat-x;
}
.carousel-control.right {
  right: 0;
  left: auto;
  background-image: -webkit-gradient(linear, left top, right top, from(rgba(0, 0, 0, 0.0001)), to(rgba(0, 0, 0, 0.5)));
  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00000000', endColorstr='#80000000', GradientType=1);
  background-repeat: repeat-x;
}
.carousel-control:hover,
.carousel-control:focus {
  color: #fff;
  text-decoration: none;
  outline: 0;
  filter: alpha(opacity=90);
  opacity: 0.9;
}
.carousel-control .icon-prev,
.carousel-control .icon-next,
.carousel-control .glyphicon-chevron-left,
.carousel-control .glyphicon-chevron-right {
  position: absolute;
  top: 50%;
  z-index: 5;
  display: inline-block;
  margin-top: -10px;
}
.carousel-control .icon-prev,
.carousel-control .glyphicon-chevron-left {
  left: 50%;
  margin-left: -10px;
}
.carousel-control .icon-next,
.carousel-control .glyphicon-chevron-right {
  right: 50%;
  margin-right: -10px;
}
.carousel-control .icon-prev,
.carousel-control .icon-next {
  width: 20px;
  height: 20px;
  font-family: serif;
  line-height: 1;
}
.carousel-control .icon-prev:before {
  content: "\2039";
}
.carousel-control .icon-next:before {
  content: "\203a";
}
.carousel-indicators {
  position: absolute;
  bottom: 10px;
  left: 50%;
  z-index: 15;
  width: 60%;
  padding-left: 0;
  margin-left: -30%;
  text-align: center;
  list-style: none;
}
.carousel-indicators li {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin: 1px;
  text-indent: -999px;
  cursor: pointer;
  background-color: #000 \9;
  background-color: rgba(0, 0, 0, 0);
  border: 1px solid #fff;
  border-radius: 10px;
}
.carousel-indicators .active {
  width: 12px;
  height: 12px;
  margin: 0;
  background-color: #fff;
}
.carousel-caption {
  position: absolute;
  right: 15%;
  bottom: 20px;
  left: 15%;
  z-index: 10;
  padding-top: 20px;
  padding-bottom: 20px;
  color: #fff;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
}
.carousel-caption .btn {
  text-shadow: none;
}
@media screen and (min-width: 576px) {
  .carousel-control .glyphicon-chevron-left,
  .carousel-control .glyphicon-chevron-right,
  .carousel-control .icon-prev,
  .carousel-control .icon-next {
    width: 30px;
    height: 30px;
    margin-top: -10px;
    font-size: 30px;
  }
  .carousel-control .glyphicon-chevron-left,
  .carousel-control .icon-prev {
    margin-left: -10px;
  }
  .carousel-control .glyphicon-chevron-right,
  .carousel-control .icon-next {
    margin-right: -10px;
  }
  .carousel-caption {
    right: 20%;
    left: 20%;
    padding-bottom: 30px;
  }
  .carousel-indicators {
    bottom: 20px;
  }
}
.clearfix:before,
.clearfix:after,
.dl-horizontal dd:before,
.dl-horizontal dd:after,
.container:before,
.container:after,
.container-fluid:before,
.container-fluid:after,
.row:before,
.row:after,
.form-horizontal .form-group:before,
.form-horizontal .form-group:after,
.btn-toolbar:before,
.btn-toolbar:after,
.btn-group-vertical > .btn-group:before,
.btn-group-vertical > .btn-group:after,
.nav:before,
.nav:after,
.navbar:before,
.navbar:after,
.navbar-header:before,
.navbar-header:after,
.navbar-collapse:before,
.navbar-collapse:after,
.pager:before,
.pager:after,
.panel-body:before,
.panel-body:after,
.modal-header:before,
.modal-header:after,
.modal-footer:before,
.modal-footer:after,
.container:before,
.container:after,
.partner-logo-row:before,
.partner-logo-row:after,
.category-banner-row:before,
.category-banner-row:after {
  display: table;
  content: " ";
}
.clearfix:after,
.dl-horizontal dd:after,
.container:after,
.container-fluid:after,
.row:after,
.form-horizontal .form-group:after,
.btn-toolbar:after,
.btn-group-vertical > .btn-group:after,
.nav:after,
.navbar:after,
.navbar-header:after,
.navbar-collapse:after,
.pager:after,
.panel-body:after,
.modal-header:after,
.modal-footer:after,
.container:after,
.partner-logo-row:after,
.category-banner-row:after {
  clear: both;
}
.center-block {
  display: block;
  margin-right: auto;
  margin-left: auto;
}
.pull-right {
  float: right !important;
}
.pull-left {
  float: left !important;
}
.hide {
  display: none !important;
}
.show {
  display: block !important;
}
.invisible {
  visibility: hidden;
}
.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}
.hidden {
  display: none !important;
}
.affix {
  position: fixed;
}
@-ms-viewport {
  width: device-width;
}
.visible-xs,
.visible-sm,
.visible-md,
.visible-lg {
  display: none !important;
}
.visible-xs-block,
.visible-xs-inline,
.visible-xs-inline-block,
.visible-sm-block,
.visible-sm-inline,
.visible-sm-inline-block,
.visible-md-block,
.visible-md-inline,
.visible-md-inline-block,
.visible-lg-block,
.visible-lg-inline,
.visible-lg-inline-block {
  display: none !important;
}
@media (max-width: 575px) {
  .visible-xs {
    display: block !important;
  }
  table.visible-xs {
    display: table !important;
  }
  tr.visible-xs {
    display: table-row !important;
  }
  th.visible-xs,
  td.visible-xs {
    display: table-cell !important;
  }
}
@media (max-width: 575px) {
  .visible-xs-block {
    display: block !important;
  }
}
@media (max-width: 575px) {
  .visible-xs-inline {
    display: inline !important;
  }
}
@media (max-width: 575px) {
  .visible-xs-inline-block {
    display: inline-block !important;
  }
}
@media (min-width: 576px) and (max-width: 575px) {
  .visible-sm {
    display: block !important;
  }
  table.visible-sm {
    display: table !important;
  }
  tr.visible-sm {
    display: table-row !important;
  }
  th.visible-sm,
  td.visible-sm {
    display: table-cell !important;
  }
}
@media (min-width: 576px) and (max-width: 575px) {
  .visible-sm-block {
    display: block !important;
  }
}
@media (min-width: 576px) and (max-width: 575px) {
  .visible-sm-inline {
    display: inline !important;
  }
}
@media (min-width: 576px) and (max-width: 575px) {
  .visible-sm-inline-block {
    display: inline-block !important;
  }
}
@media (min-width: 768px) and (max-width: 767px) {
  .visible-md {
    display: block !important;
  }
  table.visible-md {
    display: table !important;
  }
  tr.visible-md {
    display: table-row !important;
  }
  th.visible-md,
  td.visible-md {
    display: table-cell !important;
  }
}
@media (min-width: 768px) and (max-width: 767px) {
  .visible-md-block {
    display: block !important;
  }
}
@media (min-width: 768px) and (max-width: 767px) {
  .visible-md-inline {
    display: inline !important;
  }
}
@media (min-width: 768px) and (max-width: 767px) {
  .visible-md-inline-block {
    display: inline-block !important;
  }
}
@media (min-width: 992px) {
  .visible-lg {
    display: block !important;
  }
  table.visible-lg {
    display: table !important;
  }
  tr.visible-lg {
    display: table-row !important;
  }
  th.visible-lg,
  td.visible-lg {
    display: table-cell !important;
  }
}
@media (min-width: 992px) {
  .visible-lg-block {
    display: block !important;
  }
}
@media (min-width: 992px) {
  .visible-lg-inline {
    display: inline !important;
  }
}
@media (min-width: 992px) {
  .visible-lg-inline-block {
    display: inline-block !important;
  }
}
@media (max-width: 575px) {
  .hidden-xs {
    display: none !important;
  }
}
@media (min-width: 576px) and (max-width: 575px) {
  .hidden-sm {
    display: none !important;
  }
}
@media (min-width: 768px) and (max-width: 767px) {
  .hidden-md {
    display: none !important;
  }
}
@media (min-width: 992px) {
  .hidden-lg {
    display: none !important;
  }
}
.visible-print {
  display: none !important;
}
@media print {
  .visible-print {
    display: block !important;
  }
  table.visible-print {
    display: table !important;
  }
  tr.visible-print {
    display: table-row !important;
  }
  th.visible-print,
  td.visible-print {
    display: table-cell !important;
  }
}
.visible-print-block {
  display: none !important;
}
@media print {
  .visible-print-block {
    display: block !important;
  }
}
.visible-print-inline {
  display: none !important;
}
@media print {
  .visible-print-inline {
    display: inline !important;
  }
}
.visible-print-inline-block {
  display: none !important;
}
@media print {
  .visible-print-inline-block {
    display: inline-block !important;
  }
}
@media print {
  .hidden-print {
    display: none !important;
  }
}
/* Slider */
.slick-slider {
  position: relative;
  display: block;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -ms-touch-action: pan-y;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
}
.slick-list {
  position: relative;
  overflow: hidden;
  display: block;
  margin: 0;
  padding: 0;
}
.slick-list:focus {
  outline: none;
}
.slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}
.slick-slider .slick-track,
.slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}
.slick-track {
  position: relative;
  left: 0;
  top: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}
.slick-track:before,
.slick-track:after {
  content: "";
  display: table;
}
.slick-track:after {
  clear: both;
}
.slick-loading .slick-track {
  visibility: hidden;
}
.slick-slide {
  float: left;
  height: 100%;
  min-height: 1px;
  display: none;
}
[dir="rtl"] .slick-slide {
  float: right;
}
.slick-slide img {
  display: block;
}
.slick-slide.slick-loading img {
  display: none;
}
.slick-slide.dragging img {
  pointer-events: none;
}
.slick-initialized .slick-slide {
  display: block;
}
.slick-loading .slick-slide {
  visibility: hidden;
}
.slick-vertical .slick-slide {
  display: block;
  height: auto;
  border: 1px solid transparent;
}
.slick-arrow.slick-hidden {
  display: none;
}
/* Slider */
.slick-loading .slick-list {
  background: #fff url('../../node_modules/slick-carousel/slick/ajax-loader.gif') center center no-repeat;
}
/* Arrows */
.slick-prev,
.slick-next {
  position: absolute;
  display: block;
  height: 20px;
  width: 20px;
  line-height: 0px;
  font-size: 0px;
  cursor: pointer;
  background: transparent;
  color: transparent;
  top: 50%;
  -webkit-transform: translate(0, -50%);
  transform: translate(0, -50%);
  padding: 0;
  border: none;
  outline: none;
}
.slick-prev:hover,
.slick-next:hover,
.slick-prev:focus,
.slick-next:focus {
  outline: none;
  background: transparent;
  color: transparent;
}
.slick-prev:hover:before,
.slick-next:hover:before,
.slick-prev:focus:before,
.slick-next:focus:before {
  opacity: 1;
}
.slick-prev.slick-disabled:before,
.slick-next.slick-disabled:before {
  opacity: 0.25;
}
.slick-prev:before,
.slick-next:before {
  font-family: "slick";
  font-size: 20px;
  line-height: 1;
  color: white;
  opacity: 0.75;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Icons */
}
@font-face {
  font-family: 'slick';
  font-weight: normal;
  font-style: normal;
  src: url('../../node_modules/slick-carousel/slick/fonts/slick.eot');
  src: url('../../node_modules/slick-carousel/slick/fonts/slick.eot?#iefix') format('embedded-opentype'), url('../../node_modules/slick-carousel/slick/fonts/slick.woff') format('woff'), url('../../node_modules/slick-carousel/slick/fonts/slick.ttf') format('truetype'), url('../../node_modules/slick-carousel/slick/fonts/slick.svg#slick') format('svg');
}
.slick-prev {
  left: -25px;
}
[dir="rtl"] .slick-prev {
  left: auto;
  right: -25px;
}
.slick-prev:before {
  content: "←";
}
[dir="rtl"] .slick-prev:before {
  content: "→";
}
.slick-next {
  right: -25px;
}
[dir="rtl"] .slick-next {
  left: -25px;
  right: auto;
}
.slick-next:before {
  content: "→";
}
[dir="rtl"] .slick-next:before {
  content: "←";
}
/* Dots */
.slick-dotted .slick-slider {
  margin-bottom: 30px;
}
.slick-dots {
  position: absolute;
  bottom: -25px;
  list-style: none;
  display: block;
  text-align: center;
  padding: 0;
  margin: 0;
  width: 100%;
}
.slick-dots li {
  position: relative;
  display: inline-block;
  height: 20px;
  width: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}
.slick-dots li button {
  border: 0;
  background: transparent;
  display: block;
  height: 20px;
  width: 20px;
  outline: none;
  line-height: 0px;
  font-size: 0px;
  color: transparent;
  padding: 5px;
  cursor: pointer;
}
.slick-dots li button:hover,
.slick-dots li button:focus {
  outline: none;
}
.slick-dots li button:hover:before,
.slick-dots li button:focus:before {
  opacity: 1;
}
.slick-dots li button:before {
  position: absolute;
  top: 0;
  left: 0;
  content: "•";
  width: 20px;
  height: 20px;
  font-family: "slick";
  font-size: 6px;
  line-height: 20px;
  text-align: center;
  color: black;
  opacity: 0.25;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.slick-dots li.slick-active button:before {
  color: black;
  opacity: 0.75;
}
.row::before,
.row::after {
  display: none;
}
.row-align-start {
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}
@media (min-width: 1200px) {
  .col-xl-1,
  .col-xl-2,
  .col-xl-3,
  .col-xl-4,
  .col-xl-5,
  .col-xl-6,
  .col-xl-7,
  .col-xl-8,
  .col-xl-9,
  .col-xl-10,
  .col-xl-11,
  .col-xl-12 {
    float: left;
  }
  .col-xl-12 {
    width: 100%;
  }
  .col-xl-12 {
    width: 100%;
    -ms-flex-preferred-size: 100%;
        flex-basis: 100%;
    float: none;
  }
  .col-xl-11 {
    width: 91.66666667%;
  }
  .col-xl-11 {
    width: 91.66666667%;
    -ms-flex-preferred-size: 91.66666667%;
        flex-basis: 91.66666667%;
    float: none;
  }
  .col-xl-10 {
    width: 83.33333333%;
  }
  .col-xl-10 {
    width: 83.33333333%;
    -ms-flex-preferred-size: 83.33333333%;
        flex-basis: 83.33333333%;
    float: none;
  }
  .col-xl-9 {
    width: 75%;
  }
  .col-xl-9 {
    width: 75%;
    -ms-flex-preferred-size: 75%;
        flex-basis: 75%;
    float: none;
  }
  .col-xl-8 {
    width: 66.66666667%;
  }
  .col-xl-8 {
    width: 66.66666667%;
    -ms-flex-preferred-size: 66.66666667%;
        flex-basis: 66.66666667%;
    float: none;
  }
  .col-xl-7 {
    width: 58.33333333%;
  }
  .col-xl-7 {
    width: 58.33333333%;
    -ms-flex-preferred-size: 58.33333333%;
        flex-basis: 58.33333333%;
    float: none;
  }
  .col-xl-6 {
    width: 50%;
  }
  .col-xl-6 {
    width: 50%;
    -ms-flex-preferred-size: 50%;
        flex-basis: 50%;
    float: none;
  }
  .col-xl-5 {
    width: 41.66666667%;
  }
  .col-xl-5 {
    width: 41.66666667%;
    -ms-flex-preferred-size: 41.66666667%;
        flex-basis: 41.66666667%;
    float: none;
  }
  .col-xl-4 {
    width: 33.33333333%;
  }
  .col-xl-4 {
    width: 33.33333333%;
    -ms-flex-preferred-size: 33.33333333%;
        flex-basis: 33.33333333%;
    float: none;
  }
  .col-xl-3 {
    width: 25%;
  }
  .col-xl-3 {
    width: 25%;
    -ms-flex-preferred-size: 25%;
        flex-basis: 25%;
    float: none;
  }
  .col-xl-2 {
    width: 16.66666667%;
  }
  .col-xl-2 {
    width: 16.66666667%;
    -ms-flex-preferred-size: 16.66666667%;
        flex-basis: 16.66666667%;
    float: none;
  }
  .col-xl-1 {
    width: 8.33333333%;
  }
  .col-xl-1 {
    width: 8.33333333%;
    -ms-flex-preferred-size: 8.33333333%;
        flex-basis: 8.33333333%;
    float: none;
  }
  .col-xl-pull-12 {
    right: 100%;
  }
  .col-xl-pull-11 {
    right: 91.66666667%;
  }
  .col-xl-pull-10 {
    right: 83.33333333%;
  }
  .col-xl-pull-9 {
    right: 75%;
  }
  .col-xl-pull-8 {
    right: 66.66666667%;
  }
  .col-xl-pull-7 {
    right: 58.33333333%;
  }
  .col-xl-pull-6 {
    right: 50%;
  }
  .col-xl-pull-5 {
    right: 41.66666667%;
  }
  .col-xl-pull-4 {
    right: 33.33333333%;
  }
  .col-xl-pull-3 {
    right: 25%;
  }
  .col-xl-pull-2 {
    right: 16.66666667%;
  }
  .col-xl-pull-1 {
    right: 8.33333333%;
  }
  .col-xl-pull-0 {
    right: auto;
  }
  .col-xl-push-12 {
    left: 100%;
  }
  .col-xl-push-11 {
    left: 91.66666667%;
  }
  .col-xl-push-10 {
    left: 83.33333333%;
  }
  .col-xl-push-9 {
    left: 75%;
  }
  .col-xl-push-8 {
    left: 66.66666667%;
  }
  .col-xl-push-7 {
    left: 58.33333333%;
  }
  .col-xl-push-6 {
    left: 50%;
  }
  .col-xl-push-5 {
    left: 41.66666667%;
  }
  .col-xl-push-4 {
    left: 33.33333333%;
  }
  .col-xl-push-3 {
    left: 25%;
  }
  .col-xl-push-2 {
    left: 16.66666667%;
  }
  .col-xl-push-1 {
    left: 8.33333333%;
  }
  .col-xl-push-0 {
    left: auto;
  }
  .col-xl-offset-12 {
    margin-left: 100%;
  }
  .col-xl-offset-11 {
    margin-left: 91.66666667%;
  }
  .col-xl-offset-10 {
    margin-left: 83.33333333%;
  }
  .col-xl-offset-9 {
    margin-left: 75%;
  }
  .col-xl-offset-8 {
    margin-left: 66.66666667%;
  }
  .col-xl-offset-7 {
    margin-left: 58.33333333%;
  }
  .col-xl-offset-6 {
    margin-left: 50%;
  }
  .col-xl-offset-5 {
    margin-left: 41.66666667%;
  }
  .col-xl-offset-4 {
    margin-left: 33.33333333%;
  }
  .col-xl-offset-3 {
    margin-left: 25%;
  }
  .col-xl-offset-2 {
    margin-left: 16.66666667%;
  }
  .col-xl-offset-1 {
    margin-left: 8.33333333%;
  }
  .col-xl-offset-0 {
    margin-left: 0%;
  }
  .row > *[class*="col-xl-"] {
    position: relative;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
  }
}
.dropdown-toggle .caret {
  border: none;
  width: 0.75rem;
  height: 0.4375rem;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  background-image: url('../img/dropdown-chevron-down.svg');
  margin-left: 0.5rem;
  margin-top: 1px;
}
.dropdown-menu {
  -webkit-box-shadow: 0 12px 24px 0 rgba(0, 0, 0, 0.1);
          box-shadow: 0 12px 24px 0 rgba(0, 0, 0, 0.1);
  border: none;
}
.dropdown-menu .dropdown-item {
  font-weight: 500;
  font-size: 0.875rem;
  padding: 0.3125rem 1.125rem 0.25rem 1.125rem;
  cursor: pointer;
}
.btn {
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  -webkit-transition-property: background, background-color, border, outline, color;
  transition-property: background, background-color, border, outline, color;
}
.btn:active,
.btn.active {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.05);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.05);
}
.btn-bordered {
  display: inline-block;
  margin-bottom: 0;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -ms-touch-action: manipulation;
      touch-action: manipulation;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  padding: 6px 12px;
  font-size: 16px;
  line-height: 1.5;
  border-radius: 4px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  -webkit-transition-property: background, background-color, border, outline, color;
  transition-property: background, background-color, border, outline, color;
  color: #2F3538;
  background-color: #F7F7F7;
  border-color: #F7F7F7;
  background-color: transparent;
}
.btn-bordered:focus,
.btn-bordered:active:focus,
.btn-bordered.active:focus,
.btn-bordered.focus,
.btn-bordered:active.focus,
.btn-bordered.active.focus {
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
  outline: none;
}
.btn-bordered:hover,
.btn-bordered:focus,
.btn-bordered.focus {
  color: #333;
  text-decoration: none;
}
.btn-bordered:active,
.btn-bordered.active {
  background-image: none;
  outline: 0;
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.btn-bordered.disabled,
.btn-bordered[disabled],
fieldset[disabled] .btn-bordered {
  cursor: not-allowed;
  filter: alpha(opacity=65);
  opacity: 0.65;
  -webkit-box-shadow: none;
  box-shadow: none;
}
a.btn-bordered.disabled,
fieldset[disabled] a.btn-bordered {
  pointer-events: none;
}
.btn-bordered:active,
.btn-bordered.active {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.05);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.05);
}
.btn-bordered:focus,
.btn-bordered.focus {
  color: #2F3538;
  background-color: #dedede;
  border-color: #b7b7b7;
}
.btn-bordered:hover {
  color: #2F3538;
  background-color: #dedede;
  border-color: #d8d8d8;
}
.btn-bordered:active,
.btn-bordered.active,
.open > .dropdown-toggle.btn-bordered {
  color: #2F3538;
  background-color: #dedede;
  background-image: none;
  border-color: #d8d8d8;
}
.btn-bordered:active:hover,
.btn-bordered.active:hover,
.open > .dropdown-toggle.btn-bordered:hover,
.btn-bordered:active:focus,
.btn-bordered.active:focus,
.open > .dropdown-toggle.btn-bordered:focus,
.btn-bordered:active.focus,
.btn-bordered.active.focus,
.open > .dropdown-toggle.btn-bordered.focus {
  color: #2F3538;
  background-color: #cccccc;
  border-color: #b7b7b7;
}
.btn-bordered.disabled:hover,
.btn-bordered[disabled]:hover,
fieldset[disabled] .btn-bordered:hover,
.btn-bordered.disabled:focus,
.btn-bordered[disabled]:focus,
fieldset[disabled] .btn-bordered:focus,
.btn-bordered.disabled.focus,
.btn-bordered[disabled].focus,
fieldset[disabled] .btn-bordered.focus {
  background-color: #F7F7F7;
  border-color: #F7F7F7;
}
.btn-bordered .badge {
  color: #F7F7F7;
  background-color: #2F3538;
}
.btn-bordered:focus,
.btn-bordered.focus {
  color: #2F3538;
  background-color: #e4e4e4;
  border-color: transparent;
}
.btn-bordered:hover {
  color: #2F3538;
  background-color: #e4e4e4;
  border-color: transparent;
}
.btn-bordered:active,
.btn-bordered.active,
.open > .dropdown-toggle.btn-bordered {
  color: #2F3538;
  background-color: #dedede;
  background-image: none;
  border-color: transparent;
}
.btn-bordered:active:hover,
.btn-bordered.active:hover,
.open > .dropdown-toggle.btn-bordered:hover,
.btn-bordered:active:focus,
.btn-bordered.active:focus,
.open > .dropdown-toggle.btn-bordered:focus,
.btn-bordered:active.focus,
.btn-bordered.active.focus,
.open > .dropdown-toggle.btn-bordered.focus {
  color: #2F3538;
  background-color: #d7d7d7;
  border-color: transparent;
}
.btn-bordered.disabled:hover,
.btn-bordered[disabled]:hover,
fieldset[disabled] .btn-bordered:hover,
.btn-bordered.disabled:focus,
.btn-bordered[disabled]:focus,
fieldset[disabled] .btn-bordered:focus,
.btn-bordered.disabled.focus,
.btn-bordered[disabled].focus,
fieldset[disabled] .btn-bordered.focus {
  background-color: #F7F7F7;
  border-color: #F7F7F7;
}
.btn-bordered .badge {
  color: #F7F7F7;
  background-color: #2F3538;
}
.btn-icon {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.btn-icon svg,
.btn-icon .icon {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  vertical-align: middle;
  margin-right: 0.625rem;
}
.btn-icon-right {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.btn-icon-right svg,
.btn-icon-right .icon {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  vertical-align: middle;
  margin-right: 0.625rem;
}
.btn-icon-right svg,
.btn-icon-right .icon {
  margin-right: 0;
  margin-left: 0.625rem;
}
.btn-icon-only {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.btn-icon-only svg,
.btn-icon-only .icon {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  vertical-align: middle;
  margin-right: 0.625rem;
}
.btn-icon-only svg,
.btn-icon-only .icon {
  margin-right: 0;
  margin-left: 0;
}
.btn-white {
  color: #2F3538;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}
.btn-white:focus,
.btn-white.focus {
  color: #2F3538;
  background-color: #e6e6e6;
  border-color: #bfbfbf;
}
.btn-white:hover {
  color: #2F3538;
  background-color: #e6e6e6;
  border-color: #e0e0e0;
}
.btn-white:active,
.btn-white.active,
.open > .dropdown-toggle.btn-white {
  color: #2F3538;
  background-color: #e6e6e6;
  background-image: none;
  border-color: #e0e0e0;
}
.btn-white:active:hover,
.btn-white.active:hover,
.open > .dropdown-toggle.btn-white:hover,
.btn-white:active:focus,
.btn-white.active:focus,
.open > .dropdown-toggle.btn-white:focus,
.btn-white:active.focus,
.btn-white.active.focus,
.open > .dropdown-toggle.btn-white.focus {
  color: #2F3538;
  background-color: #d4d4d4;
  border-color: #bfbfbf;
}
.btn-white.disabled:hover,
.btn-white[disabled]:hover,
fieldset[disabled] .btn-white:hover,
.btn-white.disabled:focus,
.btn-white[disabled]:focus,
fieldset[disabled] .btn-white:focus,
.btn-white.disabled.focus,
.btn-white[disabled].focus,
fieldset[disabled] .btn-white.focus {
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}
.btn-white .badge {
  color: #FFFFFF;
  background-color: #2F3538;
}
.btn-white:focus,
.btn-white.focus {
  color: #2F3538;
  background-color: #ececec;
  border-color: transparent;
}
.btn-white:hover {
  color: #2F3538;
  background-color: #ececec;
  border-color: transparent;
}
.btn-white:active,
.btn-white.active,
.open > .dropdown-toggle.btn-white {
  color: #2F3538;
  background-color: #e6e6e6;
  background-image: none;
  border-color: transparent;
}
.btn-white:active:hover,
.btn-white.active:hover,
.open > .dropdown-toggle.btn-white:hover,
.btn-white:active:focus,
.btn-white.active:focus,
.open > .dropdown-toggle.btn-white:focus,
.btn-white:active.focus,
.btn-white.active.focus,
.open > .dropdown-toggle.btn-white.focus {
  color: #2F3538;
  background-color: #dfdfdf;
  border-color: transparent;
}
.btn-white.disabled:hover,
.btn-white[disabled]:hover,
fieldset[disabled] .btn-white:hover,
.btn-white.disabled:focus,
.btn-white[disabled]:focus,
fieldset[disabled] .btn-white:focus,
.btn-white.disabled.focus,
.btn-white[disabled].focus,
fieldset[disabled] .btn-white.focus {
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}
.btn-white .badge {
  color: #FFFFFF;
  background-color: #2F3538;
}
.btn-light {
  color: #2F3538;
  background-color: #EBEBEB;
  border-color: #EBEBEB;
}
.btn-light:focus,
.btn-light.focus {
  color: #2F3538;
  background-color: #d2d2d2;
  border-color: #ababab;
}
.btn-light:hover {
  color: #2F3538;
  background-color: #d2d2d2;
  border-color: #cccccc;
}
.btn-light:active,
.btn-light.active,
.open > .dropdown-toggle.btn-light {
  color: #2F3538;
  background-color: #d2d2d2;
  background-image: none;
  border-color: #cccccc;
}
.btn-light:active:hover,
.btn-light.active:hover,
.open > .dropdown-toggle.btn-light:hover,
.btn-light:active:focus,
.btn-light.active:focus,
.open > .dropdown-toggle.btn-light:focus,
.btn-light:active.focus,
.btn-light.active.focus,
.open > .dropdown-toggle.btn-light.focus {
  color: #2F3538;
  background-color: #c0c0c0;
  border-color: #ababab;
}
.btn-light.disabled:hover,
.btn-light[disabled]:hover,
fieldset[disabled] .btn-light:hover,
.btn-light.disabled:focus,
.btn-light[disabled]:focus,
fieldset[disabled] .btn-light:focus,
.btn-light.disabled.focus,
.btn-light[disabled].focus,
fieldset[disabled] .btn-light.focus {
  background-color: #EBEBEB;
  border-color: #EBEBEB;
}
.btn-light .badge {
  color: #EBEBEB;
  background-color: #2F3538;
}
.btn-light:focus,
.btn-light.focus {
  color: #2F3538;
  background-color: #d8d8d8;
  border-color: transparent;
}
.btn-light:hover {
  color: #2F3538;
  background-color: #d8d8d8;
  border-color: transparent;
}
.btn-light:active,
.btn-light.active,
.open > .dropdown-toggle.btn-light {
  color: #2F3538;
  background-color: #d2d2d2;
  background-image: none;
  border-color: transparent;
}
.btn-light:active:hover,
.btn-light.active:hover,
.open > .dropdown-toggle.btn-light:hover,
.btn-light:active:focus,
.btn-light.active:focus,
.open > .dropdown-toggle.btn-light:focus,
.btn-light:active.focus,
.btn-light.active.focus,
.open > .dropdown-toggle.btn-light.focus {
  color: #2F3538;
  background-color: #cbcbcb;
  border-color: transparent;
}
.btn-light.disabled:hover,
.btn-light[disabled]:hover,
fieldset[disabled] .btn-light:hover,
.btn-light.disabled:focus,
.btn-light[disabled]:focus,
fieldset[disabled] .btn-light:focus,
.btn-light.disabled.focus,
.btn-light[disabled].focus,
fieldset[disabled] .btn-light.focus {
  background-color: #EBEBEB;
  border-color: #EBEBEB;
}
.btn-light .badge {
  color: #EBEBEB;
  background-color: #2F3538;
}
.btn-lighter {
  color: #2F3538;
  background-color: #F7F7F7;
  border-color: #F7F7F7;
}
.btn-lighter:focus,
.btn-lighter.focus {
  color: #2F3538;
  background-color: #dedede;
  border-color: #b7b7b7;
}
.btn-lighter:hover {
  color: #2F3538;
  background-color: #dedede;
  border-color: #d8d8d8;
}
.btn-lighter:active,
.btn-lighter.active,
.open > .dropdown-toggle.btn-lighter {
  color: #2F3538;
  background-color: #dedede;
  background-image: none;
  border-color: #d8d8d8;
}
.btn-lighter:active:hover,
.btn-lighter.active:hover,
.open > .dropdown-toggle.btn-lighter:hover,
.btn-lighter:active:focus,
.btn-lighter.active:focus,
.open > .dropdown-toggle.btn-lighter:focus,
.btn-lighter:active.focus,
.btn-lighter.active.focus,
.open > .dropdown-toggle.btn-lighter.focus {
  color: #2F3538;
  background-color: #cccccc;
  border-color: #b7b7b7;
}
.btn-lighter.disabled:hover,
.btn-lighter[disabled]:hover,
fieldset[disabled] .btn-lighter:hover,
.btn-lighter.disabled:focus,
.btn-lighter[disabled]:focus,
fieldset[disabled] .btn-lighter:focus,
.btn-lighter.disabled.focus,
.btn-lighter[disabled].focus,
fieldset[disabled] .btn-lighter.focus {
  background-color: #F7F7F7;
  border-color: #F7F7F7;
}
.btn-lighter .badge {
  color: #F7F7F7;
  background-color: #2F3538;
}
.btn-lighter:focus,
.btn-lighter.focus {
  color: #2F3538;
  background-color: #e4e4e4;
  border-color: transparent;
}
.btn-lighter:hover {
  color: #2F3538;
  background-color: #e4e4e4;
  border-color: transparent;
}
.btn-lighter:active,
.btn-lighter.active,
.open > .dropdown-toggle.btn-lighter {
  color: #2F3538;
  background-color: #dedede;
  background-image: none;
  border-color: transparent;
}
.btn-lighter:active:hover,
.btn-lighter.active:hover,
.open > .dropdown-toggle.btn-lighter:hover,
.btn-lighter:active:focus,
.btn-lighter.active:focus,
.open > .dropdown-toggle.btn-lighter:focus,
.btn-lighter:active.focus,
.btn-lighter.active.focus,
.open > .dropdown-toggle.btn-lighter.focus {
  color: #2F3538;
  background-color: #d7d7d7;
  border-color: transparent;
}
.btn-lighter.disabled:hover,
.btn-lighter[disabled]:hover,
fieldset[disabled] .btn-lighter:hover,
.btn-lighter.disabled:focus,
.btn-lighter[disabled]:focus,
fieldset[disabled] .btn-lighter:focus,
.btn-lighter.disabled.focus,
.btn-lighter[disabled].focus,
fieldset[disabled] .btn-lighter.focus {
  background-color: #F7F7F7;
  border-color: #F7F7F7;
}
.btn-lighter .badge {
  color: #F7F7F7;
  background-color: #2F3538;
}
html,
body {
  font-size: 16px;
}
svg {
  vertical-align: middle;
}
address {
  margin-bottom: 0;
}
.container {
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
  width: 100%;
}
@media (min-width: 576px) {
  .container {
    width: 100%;
    max-width: 570px;
  }
}
@media (min-width: 768px) {
  .container {
    width: 100%;
    max-width: 750px;
  }
}
@media (min-width: 992px) {
  .container {
    width: 100%;
    max-width: 990px;
  }
}
@media (min-width: 1200px) {
  .container {
    width: 100%;
    max-width: 1230px;
  }
}
.text-double-col {
  -webkit-column-count: 2;
     -moz-column-count: 2;
          column-count: 2;
}
.list {
  padding-left: 0;
  list-style: none;
}
.text-default-links a {
  color: #2F3538;
}
.section {
  padding: 2rem 0;
}
@media (min-width: 576px) {
  .section {
    padding: 3.5rem 0;
  }
}
.section .section-header {
  margin-bottom: 1rem;
}
@media (min-width: 576px) {
  .section .section-header {
    margin-bottom: 2rem;
  }
}
.section .section-title {
  font-size: 1.25rem;
  text-align: center;
  margin-bottom: 0;
}
@media (min-width: 576px) {
  .section .section-title {
    font-size: 1.5rem;
  }
}
.section .section-description {
  font-size: 1rem;
  text-align: center;
  margin-bottom: 0;
}
@media (min-width: 576px) {
  .section .section-description {
    font-size: 1.125rem;
  }
}
.no-more-section {
  position: relative;
  margin-top: -29rem;
  padding-top: 20rem;
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(255, 255, 255, 0)), color-stop(17rem, #FFFFFF));
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #FFFFFF 17rem);
  z-index: 10;
}
@media (min-width: 576px) {
  .no-more-section {
    padding-top: 18rem;
    margin-top: -32rem;
  }
}
img {
  max-width: 100%;
}
.fill-primary {
  fill: #DF0000;
}
.fill-secondary {
  fill: #0094D2;
}
.fill-gray-light {
  fill: #EBEBEB;
}
.fill-gray-dark {
  fill: #2F3538;
}
.fill-success {
  fill: #00B25B;
}
.fill-danger {
  fill: #d9534f;
}
.stroke-primary {
  stroke: #DF0000;
}
.stroke-secondary {
  stroke: #0094D2;
}
.stroke-gray-light {
  fill: #EBEBEB;
}
.stroke-gray-dark {
  fill: #2F3538;
}
h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4,
h5,
.h5,
h6,
.h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  line-height: 1.5;
}
.font-weight-bold {
  font-weight: 700;
}
.font-weight-semibold {
  font-weight: 600;
}
.font-weight-medium {
  font-weight: 500;
}
.font-weight-normal {
  font-weight: 400;
}
.font-weight-light {
  font-weight: 300;
}
.font-italic {
  font-style: italic;
}
.text-secondary {
  color: #0094D2;
}
.line-height-large {
  line-height: 1.75;
}
.mt-5 {
  margin-top: 2rem;
}
.mr-5 {
  margin-right: 2rem;
}
.mb-5 {
  margin-bottom: 2rem;
}
.ml-5 {
  margin-left: 2rem;
}
.pt-5 {
  padding-top: 2rem;
}
.pr-5 {
  padding-right: 2rem;
}
.pb-5 {
  padding-bottom: 2rem;
}
.pl-5 {
  padding-left: 2rem;
}
.mt-4 {
  margin-top: 1.5rem;
}
.mr-4 {
  margin-right: 1.5rem;
}
.mb-4 {
  margin-bottom: 1.5rem;
}
.ml-4 {
  margin-left: 1.5rem;
}
.pt-4 {
  padding-top: 1.5rem;
}
.pr-4 {
  padding-right: 1.5rem;
}
.pb-4 {
  padding-bottom: 1.5rem;
}
.pl-4 {
  padding-left: 1.5rem;
}
.mt-3 {
  margin-top: 1rem;
}
.mr-3 {
  margin-right: 1rem;
}
.mb-3 {
  margin-bottom: 1rem;
}
.ml-3 {
  margin-left: 1rem;
}
.pt-3 {
  padding-top: 1rem;
}
.pr-3 {
  padding-right: 1rem;
}
.pb-3 {
  padding-bottom: 1rem;
}
.pl-3 {
  padding-left: 1rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.pt-2 {
  padding-top: 0.5rem;
}
.pr-2 {
  padding-right: 0.5rem;
}
.pb-2 {
  padding-bottom: 0.5rem;
}
.pl-2 {
  padding-left: 0.5rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mr-1 {
  margin-right: 0.25rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.ml-1 {
  margin-left: 0.25rem;
}
.pt-1 {
  padding-top: 0.25rem;
}
.pr-1 {
  padding-right: 0.25rem;
}
.pb-1 {
  padding-bottom: 0.25rem;
}
.pl-1 {
  padding-left: 0.25rem;
}
.mt-0 {
  margin-top: 0;
}
.mr-0 {
  margin-right: 0;
}
.mb-0 {
  margin-bottom: 0;
}
.ml-0 {
  margin-left: 0;
}
.pt-0 {
  padding-top: 0;
}
.pr-0 {
  padding-right: 0;
}
.pb-0 {
  padding-bottom: 0;
}
.pl-0 {
  padding-left: 0;
}
.search {
  position: relative;
  width: 100%;
  max-width: 28.75rem;
  margin: 0 auto;
  -ms-flex-negative: 1;
      flex-shrink: 1;
}
@media (max-width: 991px) {
  .search {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
}
.search input {
  width: 100%;
  outline: none;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  color: #2F3538;
  font-weight: 600;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  -webkit-transition-property: padding-right;
  transition-property: padding-right;
  -webkit-transition-delay: 0.2s;
          transition-delay: 0.2s;
  border-radius: 4px;
  height: 3rem;
  font-size: 1rem;
  padding: 0.4375rem 2.75rem 0.4375rem 1.125rem;
  border: 2px solid #DF0000;
}
.search input:focus {
  padding-right: 10rem;
}
@media (max-width: 991px) {
  .search input:focus {
    border-color: #0094D2;
  }
}
.search input::-webkit-input-placeholder {
  font-size: 1rem;
  color: #2F3538;
  font-weight: 600;
  opacity: 1;
}
.search input::-moz-placeholder {
  font-size: 1rem;
  color: #2F3538;
  font-weight: 600;
  opacity: 1;
}
.search input:-ms-input-placeholder {
  font-size: 1rem;
  color: #2F3538;
  font-weight: 600;
  opacity: 1;
}
.search input::-ms-input-placeholder {
  font-size: 1rem;
  color: #2F3538;
  font-weight: 600;
  opacity: 1;
}
.search input::placeholder {
  font-size: 1rem;
  color: #2F3538;
  font-weight: 600;
  opacity: 1;
}
@media (min-width: 992px) {
  .search input:focus {
    -webkit-transition-delay: 0s;
            transition-delay: 0s;
  }
  .search input:focus ~ button {
    color: #fff;
    background-color: #DF0000;
    border-color: #c60000;
    font-weight: 600;
    padding-right: 0.75rem;
    -webkit-transition-delay: 0s;
            transition-delay: 0s;
  }
  .search input:focus ~ button:focus,
  .search input:focus ~ button.focus {
    color: #fff;
    background-color: #ac0000;
    border-color: #460000;
  }
  .search input:focus ~ button:hover {
    color: #fff;
    background-color: #ac0000;
    border-color: #880000;
  }
  .search input:focus ~ button:active,
  .search input:focus ~ button.active,
  .open > .dropdown-toggle.search input:focus ~ button {
    color: #fff;
    background-color: #ac0000;
    background-image: none;
    border-color: #880000;
  }
  .search input:focus ~ button:active:hover,
  .search input:focus ~ button.active:hover,
  .open > .dropdown-toggle.search input:focus ~ button:hover,
  .search input:focus ~ button:active:focus,
  .search input:focus ~ button.active:focus,
  .open > .dropdown-toggle.search input:focus ~ button:focus,
  .search input:focus ~ button:active.focus,
  .search input:focus ~ button.active.focus,
  .open > .dropdown-toggle.search input:focus ~ button.focus {
    color: #fff;
    background-color: #880000;
    border-color: #460000;
  }
  .search input:focus ~ button.disabled:hover,
  .search input:focus ~ button[disabled]:hover,
  fieldset[disabled] .search input:focus ~ button:hover,
  .search input:focus ~ button.disabled:focus,
  .search input:focus ~ button[disabled]:focus,
  fieldset[disabled] .search input:focus ~ button:focus,
  .search input:focus ~ button.disabled.focus,
  .search input:focus ~ button[disabled].focus,
  fieldset[disabled] .search input:focus ~ button.focus {
    background-color: #DF0000;
    border-color: #c60000;
  }
  .search input:focus ~ button .badge {
    color: #DF0000;
    background-color: #fff;
  }
  .search input:focus ~ button:focus,
  .search input:focus ~ button.focus {
    color: #fff;
    background-color: #b90000;
    border-color: transparent;
  }
  .search input:focus ~ button:hover {
    color: #fff;
    background-color: #b90000;
    border-color: transparent;
  }
  .search input:focus ~ button:active,
  .search input:focus ~ button.active,
  .open > .dropdown-toggle.search input:focus ~ button {
    color: #fff;
    background-color: #ac0000;
    background-image: none;
    border-color: transparent;
  }
  .search input:focus ~ button:active:hover,
  .search input:focus ~ button.active:hover,
  .open > .dropdown-toggle.search input:focus ~ button:hover,
  .search input:focus ~ button:active:focus,
  .search input:focus ~ button.active:focus,
  .open > .dropdown-toggle.search input:focus ~ button:focus,
  .search input:focus ~ button:active.focus,
  .search input:focus ~ button.active.focus,
  .open > .dropdown-toggle.search input:focus ~ button.focus {
    color: #fff;
    background-color: #9f0000;
    border-color: transparent;
  }
  .search input:focus ~ button.disabled:hover,
  .search input:focus ~ button[disabled]:hover,
  fieldset[disabled] .search input:focus ~ button:hover,
  .search input:focus ~ button.disabled:focus,
  .search input:focus ~ button[disabled]:focus,
  fieldset[disabled] .search input:focus ~ button:focus,
  .search input:focus ~ button.disabled.focus,
  .search input:focus ~ button[disabled].focus,
  fieldset[disabled] .search input:focus ~ button.focus {
    background-color: #DF0000;
    border-color: #c60000;
  }
  .search input:focus ~ button .badge {
    color: #DF0000;
    background-color: #fff;
  }
  .search input:focus ~ button .button-label {
    max-width: 6.25rem;
    -webkit-transition-delay: 0s;
            transition-delay: 0s;
  }
  .search input:focus ~ button svg {
    fill: #FFFFFF;
    -webkit-transition-delay: 0s;
            transition-delay: 0s;
  }
  .search input:focus ~ .search-cancel {
    -webkit-transition-delay: 0s;
            transition-delay: 0s;
    width: 1.25rem;
    height: 1.25rem;
  }
  .search input:focus:-moz-placeholder-shown ~ .search-cancel {
    width: 0;
    height: 0;
    margin-left: 0;
  }
  .search input:focus:-ms-input-placeholder ~ .search-cancel {
    width: 0;
    height: 0;
    margin-left: 0;
  }
  .search input:focus:placeholder-shown ~ .search-cancel {
    width: 0;
    height: 0;
    margin-left: 0;
  }
}
@media (max-width: 991px) {
  .search input ~ .search-cancel {
    width: auto;
    height: auto;
    -ms-flex-negative: 0;
        flex-shrink: 0;
    -webkit-transition-delay: 0s;
            transition-delay: 0s;
    margin-left: 1.35rem;
  }
  .search input:-moz-placeholder-shown ~ .search-cancel {
    width: 0;
    height: 0;
    margin-left: 0;
  }
  .search input:-ms-input-placeholder ~ .search-cancel {
    width: 0;
    height: 0;
    margin-left: 0;
  }
  .search input:placeholder-shown ~ .search-cancel {
    width: 0;
    height: 0;
    margin-left: 0;
  }
}
.search button {
  display: inline-block;
  margin-bottom: 0;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -ms-touch-action: manipulation;
      touch-action: manipulation;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  padding: 6px 12px;
  font-size: 16px;
  line-height: 1.5;
  border-radius: 4px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-transition-property: background, background-color, border, outline, color;
  transition-property: background, background-color, border, outline, color;
  background-color: transparent;
  position: absolute;
  right: 0.25rem;
  top: 0.25rem;
  overflow: hidden;
  padding-right: 0;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  -webkit-transition-property: background, background-color, border, outline, color, padding-right;
  transition-property: background, background-color, border, outline, color, padding-right;
  -webkit-transition-delay: 0.2s;
          transition-delay: 0.2s;
}
.search button:focus,
.search button:active:focus,
.search button.active:focus,
.search button.focus,
.search button:active.focus,
.search button.active.focus {
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
  outline: none;
}
.search button:hover,
.search button:focus,
.search button.focus {
  color: #333;
  text-decoration: none;
}
.search button:active,
.search button.active {
  background-image: none;
  outline: 0;
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.search button.disabled,
.search button[disabled],
fieldset[disabled] .search button {
  cursor: not-allowed;
  filter: alpha(opacity=65);
  opacity: 0.65;
  -webkit-box-shadow: none;
  box-shadow: none;
}
a.search button.disabled,
fieldset[disabled] a.search button {
  pointer-events: none;
}
.search button:active,
.search button.active {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.05);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.05);
}
@media (max-width: 991px) {
  .search button {
    position: static;
    margin-left: -3.125rem;
  }
}
.search button .button-label {
  display: inline-block;
  max-width: 0;
  color: #FFFFFF;
  vertical-align: middle;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  -webkit-transition-delay: 0.2s;
          transition-delay: 0.2s;
}
.search button svg {
  width: 1.5rem;
  height: 1.5rem;
  margin-right: 0.25rem;
  -webkit-transition-delay: 0.2s;
          transition-delay: 0.2s;
}
.search .search-cancel {
  width: 0;
  height: 0;
  cursor: pointer;
  overflow: hidden;
  -webkit-transition-delay: 0.2s;
          transition-delay: 0.2s;
}
@media (min-width: 992px) {
  .search .search-cancel {
    position: absolute;
    top: 50%;
    right: 8.25rem;
    opacity: 0.4;
    -webkit-transform: translate(0, -50%);
            transform: translate(0, -50%);
  }
}
.search .search-cancel .icon {
  width: 100%;
  height: 100%;
}
@media (max-width: 991px) {
  .search .search-cancel .icon {
    display: none;
  }
}
.search .search-cancel .search-cancel-label {
  color: #0094D2;
  font-weight: 600;
}
@media (min-width: 992px) {
  .search .search-cancel .search-cancel-label {
    display: none;
  }
}
/**
    Body styles when header is fixed or mobile navigation is visivle
 */
body.main-header-fixed  {
  padding-top: 6.25rem;
}
@media (max-width: 991px) {
  body.main-header-fixed  {
    padding-top: 4.125rem;
  }
}
@media (max-width: 991px) {
  body.navigation-visible  {
    overflow: hidden;
  }
}
/**
    End of Body styles
 */
.main-header {
  background: #FFFFFF;
  height: 6.25rem;
  border-bottom: 1px solid #EBEBEB;
}
@media (max-width: 991px) {
  .main-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 4.125rem;
    z-index: 300;
    position: static;
  }
  body.main-header-fixed .main-header {
    position: fixed;
  }
  body.navigation-visible .main-header {
    background-color: #F7F7F7;
  }
}
body.main-header-fixed .main-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 4.125rem;
  z-index: 300;
}
.main-header .container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  height: 100%;
}
@media (min-width: 992px) {
  .main-header .container {
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
  }
}
.main-header .container::before,
.main-header .container::after {
  display: none;
}
.main-header .mobile-navigation-wrap {
  display: none;
}
@media (min-width: 992px) {
  .main-header .mobile-navigation-wrap {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    height: 100%;
    width: 100%;
  }
}
body.main-header-fixed .main-header .mobile-navigation-wrap {
  top: 4.125rem;
}
@media (max-width: 991px) {
  body.navigation-visible .main-header .mobile-navigation-wrap {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    position: fixed;
    top: 4.125rem;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 1rem;
    background-color: #FFFFFF;
    z-index: 300;
    overflow: auto;
  }
}
.main-header .mobile-navigation-wrap > * {
  -ms-flex-negative: 0;
      flex-shrink: 0;
}
@media (max-width: 991px) {
  .main-header .mobile-navigation-wrap > * {
    width: 100%;
    max-width: 720px !important;
    margin-left: auto !important;
    margin-right: auto !important;
  }
}
@media (max-width: 767px) {
  .main-header .mobile-navigation-wrap > * {
    max-width: 540px !important;
  }
}
.main-header .logo {
  width: 10.4rem;
  height: 2.875rem;
  margin-right: 1rem;
  -ms-flex-negative: 0;
      flex-shrink: 0;
}
@media (min-width: 1200px) {
  .main-header .logo {
    margin-right: 1.75rem;
  }
}
.main-header .logo .icon {
  max-width: 100%;
  max-height: 100%;
}
.main-header .main-nav-wrap {
  position: relative;
}
@media (max-width: 991px) {
  .main-header .main-nav-wrap {
    overflow-x: hidden;
    margin-right: 0;
  }
}
.main-header .menu-button {
  display: inline-block;
  margin-bottom: 0;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -ms-touch-action: manipulation;
      touch-action: manipulation;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  padding: 6px 12px;
  font-size: 16px;
  line-height: 1.5;
  border-radius: 4px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  -webkit-transition-property: background, background-color, border, outline, color;
  transition-property: background, background-color, border, outline, color;
  color: #2F3538;
  background-color: #F7F7F7;
  border-color: #F7F7F7;
  font-size: 1.125rem;
  display: none;
  position: relative;
  font-weight: 600;
  padding: 1.1rem 1.1rem 1.1rem 3.375rem;
  border-bottom-width: 2px;
}
.main-header .menu-button:focus,
.main-header .menu-button:active:focus,
.main-header .menu-button.active:focus,
.main-header .menu-button.focus,
.main-header .menu-button:active.focus,
.main-header .menu-button.active.focus {
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
  outline: none;
}
.main-header .menu-button:hover,
.main-header .menu-button:focus,
.main-header .menu-button.focus {
  color: #333;
  text-decoration: none;
}
.main-header .menu-button:active,
.main-header .menu-button.active {
  background-image: none;
  outline: 0;
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.main-header .menu-button.disabled,
.main-header .menu-button[disabled],
fieldset[disabled] .main-header .menu-button {
  cursor: not-allowed;
  filter: alpha(opacity=65);
  opacity: 0.65;
  -webkit-box-shadow: none;
  box-shadow: none;
}
a.main-header .menu-button.disabled,
fieldset[disabled] a.main-header .menu-button {
  pointer-events: none;
}
.main-header .menu-button:active,
.main-header .menu-button.active {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.05);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.05);
}
.main-header .menu-button:focus,
.main-header .menu-button.focus {
  color: #2F3538;
  background-color: #dedede;
  border-color: #b7b7b7;
}
.main-header .menu-button:hover {
  color: #2F3538;
  background-color: #dedede;
  border-color: #d8d8d8;
}
.main-header .menu-button:active,
.main-header .menu-button.active,
.open > .dropdown-toggle.main-header .menu-button {
  color: #2F3538;
  background-color: #dedede;
  background-image: none;
  border-color: #d8d8d8;
}
.main-header .menu-button:active:hover,
.main-header .menu-button.active:hover,
.open > .dropdown-toggle.main-header .menu-button:hover,
.main-header .menu-button:active:focus,
.main-header .menu-button.active:focus,
.open > .dropdown-toggle.main-header .menu-button:focus,
.main-header .menu-button:active.focus,
.main-header .menu-button.active.focus,
.open > .dropdown-toggle.main-header .menu-button.focus {
  color: #2F3538;
  background-color: #cccccc;
  border-color: #b7b7b7;
}
.main-header .menu-button.disabled:hover,
.main-header .menu-button[disabled]:hover,
fieldset[disabled] .main-header .menu-button:hover,
.main-header .menu-button.disabled:focus,
.main-header .menu-button[disabled]:focus,
fieldset[disabled] .main-header .menu-button:focus,
.main-header .menu-button.disabled.focus,
.main-header .menu-button[disabled].focus,
fieldset[disabled] .main-header .menu-button.focus {
  background-color: #F7F7F7;
  border-color: #F7F7F7;
}
.main-header .menu-button .badge {
  color: #F7F7F7;
  background-color: #2F3538;
}
.main-header .menu-button:focus,
.main-header .menu-button.focus {
  color: #2F3538;
  background-color: #e4e4e4;
  border-color: transparent;
}
.main-header .menu-button:hover {
  color: #2F3538;
  background-color: #e4e4e4;
  border-color: transparent;
}
.main-header .menu-button:active,
.main-header .menu-button.active,
.open > .dropdown-toggle.main-header .menu-button {
  color: #2F3538;
  background-color: #dedede;
  background-image: none;
  border-color: transparent;
}
.main-header .menu-button:active:hover,
.main-header .menu-button.active:hover,
.open > .dropdown-toggle.main-header .menu-button:hover,
.main-header .menu-button:active:focus,
.main-header .menu-button.active:focus,
.open > .dropdown-toggle.main-header .menu-button:focus,
.main-header .menu-button:active.focus,
.main-header .menu-button.active.focus,
.open > .dropdown-toggle.main-header .menu-button.focus {
  color: #2F3538;
  background-color: #d7d7d7;
  border-color: transparent;
}
.main-header .menu-button.disabled:hover,
.main-header .menu-button[disabled]:hover,
fieldset[disabled] .main-header .menu-button:hover,
.main-header .menu-button.disabled:focus,
.main-header .menu-button[disabled]:focus,
fieldset[disabled] .main-header .menu-button:focus,
.main-header .menu-button.disabled.focus,
.main-header .menu-button[disabled].focus,
fieldset[disabled] .main-header .menu-button.focus {
  background-color: #F7F7F7;
  border-color: #F7F7F7;
}
.main-header .menu-button .badge {
  color: #F7F7F7;
  background-color: #2F3538;
}
body.main-header-fixed .main-header .menu-button {
  padding: 0.45rem 1rem 0.45rem 3.25rem;
}
@media (max-width: 991px) {
  .main-header .menu-button {
    padding: 0.45rem 0 !important;
    background-color: transparent;
    border: transparent;
  }
  .main-header .menu-button#main-nav-toggle-mobile {
    display: inline-block;
  }
}
@media (min-width: 992px) {
  .main-header .menu-button#main-nav-toggle-desktop {
    display: inline-block;
  }
}
.main-header .menu-button .content {
  display: none;
}
@media (min-width: 992px) {
  .main-header .menu-button .content {
    display: inline-block;
  }
}
.main-header .menu-button svg {
  position: absolute;
  top: 1.4rem;
  left: 1.1rem;
  width: 1.75rem;
  height: 1.25rem;
  margin-right: 0.375rem;
  fill: #2F3538;
}
body.main-header-fixed .main-header .menu-button svg {
  top: 0.7rem;
  left: 1rem;
}
@media (max-width: 991px) {
  .main-header .menu-button svg {
    top: -0.1rem !important;
    left: 0 !important;
    margin-left: -1.75rem;
  }
}
.main-header .menu-button .icon-close {
  -webkit-transform: scale(0);
          transform: scale(0);
}
.main-header .menu-button .icon-open {
  -webkit-transform: scale(1);
          transform: scale(1);
}
.main-header .menu-button.active,
.main-header .menu-button.active:focus {
  border-bottom: 2px solid #DF0000 !important;
}
.main-header .menu-button.active .icon-open {
  -webkit-animation: 0.2s menuIconClose forwards;
          animation: 0.2s menuIconClose forwards;
}
.main-header .menu-button.active .icon-close {
  -webkit-animation: 0.2s menuIconOpen forwards 0.25s;
          animation: 0.2s menuIconOpen forwards 0.25s;
}
.main-header .menu-button.inactive .icon-open {
  -webkit-transform: scale(0);
          transform: scale(0);
  -webkit-animation: 0.2s menuIconOpen forwards 0.25s;
          animation: 0.2s menuIconOpen forwards 0.25s;
}
.main-header .menu-button.inactive .icon-close {
  -webkit-animation: 0.2s menuIconClose forwards;
          animation: 0.2s menuIconClose forwards;
}
@-webkit-keyframes menuIconClose {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  30% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
  }
  100% {
    -webkit-transform: scale(0);
            transform: scale(0);
  }
}
@keyframes menuIconClose {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  30% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
  }
  100% {
    -webkit-transform: scale(0);
            transform: scale(0);
  }
}
@-webkit-keyframes menuIconOpen {
  0% {
    -webkit-transform: scale(0);
            transform: scale(0);
  }
  70% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}
@keyframes menuIconOpen {
  0% {
    -webkit-transform: scale(0);
            transform: scale(0);
  }
  70% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}
.main-header .mobile-header-icon-buttons {
  display: none;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  margin-left: auto;
  margin-right: 3rem;
}
@media (max-width: 991px) {
  .main-header .mobile-header-icon-buttons {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }
  body.navigation-visible .main-header .mobile-header-icon-buttons {
    display: none;
  }
}
.main-header .mobile-header-icon-buttons .icon-button-mobile {
  display: inline-block;
  margin-bottom: 0;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -ms-touch-action: manipulation;
      touch-action: manipulation;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  padding: 6px 12px;
  font-size: 16px;
  line-height: 1.5;
  border-radius: 4px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  -webkit-transition-property: background, background-color, border, outline, color;
  transition-property: background, background-color, border, outline, color;
  background-color: transparent;
  border: transparent;
  padding: 0.375rem;
  margin-left: 1rem;
}
.main-header .mobile-header-icon-buttons .icon-button-mobile:focus,
.main-header .mobile-header-icon-buttons .icon-button-mobile:active:focus,
.main-header .mobile-header-icon-buttons .icon-button-mobile.active:focus,
.main-header .mobile-header-icon-buttons .icon-button-mobile.focus,
.main-header .mobile-header-icon-buttons .icon-button-mobile:active.focus,
.main-header .mobile-header-icon-buttons .icon-button-mobile.active.focus {
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
  outline: none;
}
.main-header .mobile-header-icon-buttons .icon-button-mobile:hover,
.main-header .mobile-header-icon-buttons .icon-button-mobile:focus,
.main-header .mobile-header-icon-buttons .icon-button-mobile.focus {
  color: #333;
  text-decoration: none;
}
.main-header .mobile-header-icon-buttons .icon-button-mobile:active,
.main-header .mobile-header-icon-buttons .icon-button-mobile.active {
  background-image: none;
  outline: 0;
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.main-header .mobile-header-icon-buttons .icon-button-mobile.disabled,
.main-header .mobile-header-icon-buttons .icon-button-mobile[disabled],
fieldset[disabled] .main-header .mobile-header-icon-buttons .icon-button-mobile {
  cursor: not-allowed;
  filter: alpha(opacity=65);
  opacity: 0.65;
  -webkit-box-shadow: none;
  box-shadow: none;
}
a.main-header .mobile-header-icon-buttons .icon-button-mobile.disabled,
fieldset[disabled] a.main-header .mobile-header-icon-buttons .icon-button-mobile {
  pointer-events: none;
}
.main-header .mobile-header-icon-buttons .icon-button-mobile:active,
.main-header .mobile-header-icon-buttons .icon-button-mobile.active {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.05);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.05);
}
.main-header .mobile-header-icon-buttons .icon-button-mobile:first-child {
  margin-left: 0;
}
.main-header .mobile-header-icon-buttons .icon-button-mobile .icon {
  display: block;
  width: 1.5rem;
  height: 1.5rem;
}
.main-header .search {
  margin: 0 1rem;
  -ms-flex-negative: 1;
      flex-shrink: 1;
}
@media (max-width: 991px) {
  .main-header .search {
    margin: 0 0 1.375rem;
    -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
            order: -1;
  }
}
@media (min-width: 1200px) {
  .main-header .search {
    margin: 0 2rem 0 2rem;
  }
}
.main-header .search input {
  border: none;
  height: 3.625rem;
  padding: 1rem 2.75rem 1rem 0;
  border-bottom: 2px solid #2F3538;
  font-size: 1.125rem;
  border-radius: 0;
}
body.main-header-fixed .main-header .search input {
  border-radius: 4px;
  height: 3rem;
  font-size: 1rem;
  padding: 0.4375rem 2.75rem 0.4375rem 1.125rem;
  border: 2px solid #DF0000;
}
body.main-header-fixed .main-header .search input:focus {
  padding-right: 10rem;
}
@media (max-width: 991px) {
  .main-header .search input:focus {
    border-color: #0094D2;
  }
}
@media (max-width: 991px) {
  .main-header .search input {
    border-radius: 4px;
    height: 3rem;
    font-size: 1rem;
    padding: 0.4375rem 2.75rem 0.4375rem 1.125rem;
    border: 2px solid #DF0000;
  }
}
.main-header .search input::-webkit-input-placeholder {
  font-size: 1.125rem;
}
body.main-header-fixed .main-header .search input::-webkit-input-placeholder {
  font-size: 1rem;
}
@media (max-width: 991px) {
  .main-header .search input::-webkit-input-placeholder {
    font-size: 1rem;
  }
}
.main-header .search input::-moz-placeholder {
  font-size: 1.125rem;
}
body.main-header-fixed .main-header .search input::-moz-placeholder {
  font-size: 1rem;
}
@media (max-width: 991px) {
  .main-header .search input::-moz-placeholder {
    font-size: 1rem;
  }
}
.main-header .search input:-ms-input-placeholder {
  font-size: 1.125rem;
}
body.main-header-fixed .main-header .search input:-ms-input-placeholder {
  font-size: 1rem;
}
@media (max-width: 991px) {
  .main-header .search input:-ms-input-placeholder {
    font-size: 1rem;
  }
}
.main-header .search input::-webkit-input-placeholder {
  font-size: 1.125rem;
}
.main-header .search input::-moz-placeholder {
  font-size: 1.125rem;
}
.main-header .search input:-ms-input-placeholder {
  font-size: 1.125rem;
}
.main-header .search input::-ms-input-placeholder {
  font-size: 1.125rem;
}
.main-header .search input::placeholder {
  font-size: 1.125rem;
}
body.main-header-fixed .main-header .search input::-webkit-input-placeholder {
  font-size: 1rem;
}
body.main-header-fixed .main-header .search input::-moz-placeholder {
  font-size: 1rem;
}
body.main-header-fixed .main-header .search input:-ms-input-placeholder {
  font-size: 1rem;
}
body.main-header-fixed .main-header .search input::-ms-input-placeholder {
  font-size: 1rem;
}
body.main-header-fixed .main-header .search input::placeholder {
  font-size: 1rem;
}
@media (max-width: 991px) {
  .main-header .search input::-webkit-input-placeholder {
    font-size: 1rem;
  }
  .main-header .search input::-moz-placeholder {
    font-size: 1rem;
  }
  .main-header .search input:-ms-input-placeholder {
    font-size: 1rem;
  }
  .main-header .search input::-ms-input-placeholder {
    font-size: 1rem;
  }
  .main-header .search input::placeholder {
    font-size: 1rem;
  }
}
@media (min-width: 992px) {
  .main-header .search input:focus {
    border-color: #0094D2;
    padding: 1rem 10rem 1rem 0;
  }
}
body .main-header .search button {
  right: 0;
  top: 0.625rem;
}
body.main-header-fixed .main-header .search button {
  right: 0.25rem;
  top: 0.25rem;
}
@media (max-width: 991px) {
  .main-header .search button {
    position: static;
    margin-left: -3.125rem;
  }
}
@media (min-width: 992px) {
  .main-header .search .search-cancel {
    right: 8rem;
  }
}
body.main-header-fixed .main-header .search .search-cancel {
  right: 8.25rem;
}
.main-header .site-settings {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}
@media (max-width: 991px) {
  .main-header .site-settings {
    display: none;
  }
}
.main-header .header-dropdown .btn {
  border: 1px solid #EBEBEB;
  padding: 0.375rem 0.625rem 0.375rem;
  border-radius: 1.25rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-weight: 500;
}
.main-header .header-dropdown .icon {
  width: 1.5rem;
  margin-right: 0.375rem;
  border-radius: 50%;
}
.main-header .header-dropdown .dropdown-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.main-header .language {
  margin-right: 0.625rem;
}
.main-header .language .list-of-languages .dropdown-item .icon {
  width: 1.5rem;
  margin-right: 0.375rem;
}
.main-header .mobile-language {
  display: none;
}
@media (max-width: 991px) {
  .main-header .mobile-language {
    margin-right: 0.625rem;
    display: block;
  }
  .main-header .mobile-language .list-of-languages .dropdown-item .icon {
    width: 1.5rem;
    margin-right: 0.375rem;
  }
}
.main-header .mobile-language,
.main-header .mobile-language button {
  width: 100%;
}
.main-header .mobile-language .mobile-language-open-button {
  margin-left: auto;
  color: #0094D2;
}
.main-header .mobile-account {
  display: none;
}
@media (max-width: 991px) {
  .main-header .mobile-account {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    margin-bottom: 1rem;
  }
}
@media (max-width: 991px) {
  .main-header .mobile-account .login-button {
    height: 2.25rem;
    width: calc(50% - 0.375rem);
    -ms-flex-preferred-size: calc(50% - 0.375rem);
        flex-basis: calc(50% - 0.375rem);
    -ms-flex-negative: 0;
        flex-shrink: 0;
    padding: 0.5625rem 0.75rem 0.5625rem;
  }
}
.main-header .login-button {
  line-height: 1rem;
  font-weight: 600;
  padding: 0.375rem 0.625rem 0.375rem;
  border-radius: 1.25rem;
}
.main-header .login-button .icon {
  width: 1.5rem;
  height: 1.5rem;
  margin-right: 0.375rem;
}
.main-header .login-button span {
  vertical-align: middle;
}
.main-header .shopping-cart {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 2.375rem;
  border-radius: 2rem;
  margin-left: 0.625rem;
}
.main-header .shopping-cart .icon {
  width: 1.5rem;
  height: 1.5rem;
  margin-right: 0.5rem;
}
@media (min-width: 992px) {
  body.main-header-fixed .main-header .quick-menu {
    display: none;
  }
}
.main-header .quick-menu ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  margin-top: 0.5rem;
  margin-bottom: 0;
  padding: 0;
  list-style-type: none;
}
@media (max-width: 991px) {
  .main-header .quick-menu ul {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    margin-bottom: 1.5rem;
  }
}
.main-header .quick-menu ul li {
  margin-right: 0.5rem;
}
@media (max-width: 991px) {
  .main-header .quick-menu ul li {
    margin-right: 0;
    margin-bottom: 0.5rem;
  }
}
.main-header .quick-menu ul li:last-child {
  margin-right: 0;
}
.main-header .quick-menu ul a {
  padding: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(47, 53, 56, 0.6);
}
.main-footer {
  border-top: 1px solid #EBEBEB;
  margin-top: 2rem;
  padding: 2.5rem 0;
  font-weight: 500;
}
@media (min-width: 992px) {
  .main-footer {
    padding: 3.75rem 0;
  }
}
.main-footer .logo {
  display: block;
  margin-bottom: 1rem;
  margin-top: -1rem;
}
@media (min-width: 992px) {
  .main-footer .logo {
    margin-bottom: 2rem;
  }
}
.main-footer .logo .icon {
  width: 10.5rem;
  height: 3.5rem;
}
@media (max-width: 991px) {
  .main-footer .footer-collapse-col {
    border-top: 1px solid #EBEBEB;
    border-bottom: 1px solid #EBEBEB;
    margin: -1px 0.5rem 0;
  }
}
.main-footer .footer-collapse-col .footer-heading {
  position: relative;
  padding-top: 0.6875rem;
  padding-bottom: 0.6875rem;
  margin-bottom: 0;
}
@media (min-width: 992px) {
  .main-footer .footer-collapse-col .footer-heading {
    margin-bottom: 1rem;
    padding: 0;
  }
  .main-footer .footer-collapse-col .footer-heading .icon-active,
  .main-footer .footer-collapse-col .footer-heading .icon-inactive {
    display: none !important;
  }
}
.main-footer .footer-collapse-col .footer-heading[aria-expanded="false"] .icon-active {
  display: none;
}
.main-footer .footer-collapse-col .footer-heading[aria-expanded="false"] .icon-inactive {
  display: block;
}
.main-footer .footer-collapse-col .footer-heading[aria-expanded="true"] .icon-active {
  display: block;
}
.main-footer .footer-collapse-col .footer-heading[aria-expanded="true"] .icon-inactive {
  display: none;
}
.main-footer .footer-collapse-col .footer-collapse-content {
  padding: 0.5rem 0 1rem;
}
@media (min-width: 992px) {
  .main-footer .footer-collapse-col .footer-collapse-content {
    padding: 0;
  }
}
.main-footer .footer-collapse-col .footer-collapse-icon {
  display: block;
  position: absolute;
  top: 50%;
  right: 0;
  -webkit-transform: translate(0, -50%);
          transform: translate(0, -50%);
  width: 1rem;
  height: 1rem;
  fill: #DF0000;
}
.main-footer .footer-heading {
  margin-bottom: 1rem;
}
.main-footer .footer-list-of-links li {
  margin-bottom: 0.65rem;
}
@media (min-width: 992px) {
  .main-footer .footer-list-of-links li {
    margin-bottom: 0.35rem;
  }
}
.main-footer address {
  color: rgba(47, 53, 56, 0.6);
  line-height: 1.875rem;
}
.main-footer .socials-list {
  padding-left: 0;
  list-style: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-bottom: 0;
  margin-top: 1rem;
}
@media (min-width: 992px) {
  .main-footer .socials-list {
    margin-top: 0;
  }
}
@media (min-width: 992px) {
  .main-footer .socials-list {
    -webkit-box-pack: end;
        -ms-flex-pack: end;
            justify-content: flex-end;
  }
}
.main-footer .socials-list li {
  margin-right: 0.95rem;
}
.main-footer .socials-list li:last-child {
  margin-right: 0;
}
.main-footer .socials-list svg {
  width: 1.25rem;
  height: 1.25rem;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
.main-footer .socials-list svg:hover {
  fill: #DF0000;
}
.main-footer .copyright {
  padding-top: 1.5rem;
  font-size: 0.875rem;
  color: rgba(47, 53, 56, 0.6);
}
@media (min-width: 576px) {
  .main-footer .copyright {
    padding-top: 2.5rem;
  }
  .main-footer .copyright .copyright-powered-by {
    text-align: right;
  }
}
@media (min-width: 992px) {
  .main-footer .footer-collapse-col .footer-collapse-content {
    display: block !important;
    height: auto !important;
  }
}
input::-webkit-input-placeholder {
  font-style: italic;
}
input::-moz-placeholder {
  font-style: italic;
}
input:-ms-input-placeholder {
  font-style: italic;
}
input::-ms-input-placeholder {
  font-style: italic;
}
input::placeholder {
  font-style: italic;
}
.input-number-with-controls {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.input-number-with-controls input {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  width: 3.33333333em;
  height: 2.77777778em;
  border: 1px solid #EBEBEB;
  font-size: 1rem;
  text-align: center;
  font-weight: 600;
  border-radius: 0;
}
@media (min-width: 992px) {
  .input-number-with-controls input {
    font-size: 1.125rem;
  }
}
.input-number-with-controls input::-webkit-inner-spin-button,
.input-number-with-controls input::-webkit-outer-spin-button {
  -webkit-appearance: none;
          appearance: none;
  margin: 0;
}
.input-number-with-controls button {
  display: inline-block;
  margin-bottom: 0;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -ms-touch-action: manipulation;
      touch-action: manipulation;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  padding: 6px 12px;
  font-size: 16px;
  line-height: 1.5;
  border-radius: 4px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  -webkit-transition-property: background, background-color, border, outline, color;
  transition-property: background, background-color, border, outline, color;
  background-color: transparent;
  width: 2.77777778em;
  height: 2.77777778em;
  font-size: 1rem;
  padding: 1rem;
}
.input-number-with-controls button:focus,
.input-number-with-controls button:active:focus,
.input-number-with-controls button.active:focus,
.input-number-with-controls button.focus,
.input-number-with-controls button:active.focus,
.input-number-with-controls button.active.focus {
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
  outline: none;
}
.input-number-with-controls button:hover,
.input-number-with-controls button:focus,
.input-number-with-controls button.focus {
  color: #333;
  text-decoration: none;
}
.input-number-with-controls button:active,
.input-number-with-controls button.active {
  background-image: none;
  outline: 0;
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.input-number-with-controls button.disabled,
.input-number-with-controls button[disabled],
fieldset[disabled] .input-number-with-controls button {
  cursor: not-allowed;
  filter: alpha(opacity=65);
  opacity: 0.65;
  -webkit-box-shadow: none;
  box-shadow: none;
}
a.input-number-with-controls button.disabled,
fieldset[disabled] a.input-number-with-controls button {
  pointer-events: none;
}
.input-number-with-controls button:active,
.input-number-with-controls button.active {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.05);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.05);
}
@media (min-width: 992px) {
  .input-number-with-controls button {
    font-size: 1.125rem;
  }
}
.input-number-with-controls button:hover:not(:disabled) {
  background-color: #F7F7F7;
}
.input-number-with-controls button svg {
  width: 100%;
  height: 100%;
  fill: #2F3538;
}
.input-number-with-controls button:disabled svg {
  fill: rgba(47, 53, 56, 0.3);
}
.input-number-with-controls button.button-plus {
  border: 1px solid #EBEBEB;
  border-left: none;
  border-radius: 0 4px 4px 0;
}
.input-number-with-controls button.button-minus {
  border: 1px solid #EBEBEB;
  border-right: none;
  border-radius: 4px 0 0 4px;
}
.main-nav {
  display: none;
}
@media (min-width: 992px) {
  .main-nav {
    position: absolute;
    top: 100%;
    -webkit-transform: translate(0, 0.5rem);
            transform: translate(0, 0.5rem);
    z-index: 1000;
  }
}
@media (max-width: 991px) {
  .main-nav {
    margin-bottom: 1.15rem;
    -webkit-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
  }
}
body.navigation-visible .main-nav {
  display: block;
}
.main-nav .main-nav-level,
.main-nav .main-nav-sub-level {
  padding: 0.625rem 0;
  margin: 0;
  list-style-type: none;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  background-color: #FFFFFF;
  min-width: 13.125rem;
  max-width: 18.75rem;
  border-radius: 4px;
}
@media (max-width: 991px) {
  .main-nav .main-nav-level,
  .main-nav .main-nav-sub-level {
    width: 100%;
    max-width: none;
  }
}
.main-nav .main-nav-level::after,
.main-nav .main-nav-sub-level::after {
  display: block;
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: -1;
}
@media (min-width: 992px) {
  .main-nav .main-nav-level::after,
  .main-nav .main-nav-sub-level::after {
    -webkit-box-shadow: 0 12px 24px 0 rgba(0, 0, 0, 0.1);
            box-shadow: 0 12px 24px 0 rgba(0, 0, 0, 0.1);
  }
}
.main-nav .main-nav-sub-level {
  display: none;
  position: absolute;
  left: 100%;
  top: 0;
  bottom: 0;
}
.main-nav .main-nav-sub-level::before {
  display: none;
  content: '';
  width: 1px;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  background-color: #EBEBEB;
}
@media (min-width: 992px) {
  .main-nav .main-nav-sub-level::before {
    display: block;
  }
}
@media (min-width: 992px) {
  .main-nav .main-nav-menu-item:hover {
    background-color: #eeeeee;
  }
  .main-nav .main-nav-menu-item:hover > .main-nav-sub-level {
    display: block;
  }
  .main-nav .main-nav-menu-item:hover > .main-nav-menu-item-link {
    color: #DF0000;
    text-decoration: none;
  }
}
@media (max-width: 991px) {
  .main-nav .main-nav-menu-item.active > .main-nav-sub-level {
    display: block;
  }
}
.main-nav .main-nav-menu-item.main-nav-menu-item-current-level,
.main-nav .main-nav-menu-item.main-nav-menu-item-back {
  display: none;
}
@media (max-width: 991px) {
  .main-nav .main-nav-menu-item.main-nav-menu-item-current-level,
  .main-nav .main-nav-menu-item.main-nav-menu-item-back {
    display: block;
  }
}
.main-nav .main-nav-menu-item.main-nav-menu-item-current-level .icon,
.main-nav .main-nav-menu-item.main-nav-menu-item-back .icon {
  vertical-align: -0.1rem;
  fill: #0094D2;
  width: 1rem;
  height: 1rem;
  margin-right: 0.5rem;
}
.main-nav .main-nav-menu-item.main-nav-menu-item-back .main-nav-menu-item-link {
  font-size: 1.25rem;
  font-weight: 700;
  background-image: none;
}
.main-nav .main-nav-menu-item.main-nav-menu-item-back .icon {
  fill: rgba(47, 53, 56, 0.6);
}
.main-nav .main-nav-menu-item-link {
  display: block;
  padding: 0.3125rem 2.5rem 0.25rem 1.125rem;
  font-weight: 600;
  font-size: 0.875rem;
  color: #2F3538;
  background-image: url('../img/menu-chevron-right.svg');
  background-repeat: no-repeat;
  background-position: calc(100% - 0.75rem) center;
  text-decoration: none;
}
@media (max-width: 991px) {
  .main-nav .main-nav-menu-item-link {
    padding: 0.375rem 0.5625rem 0.3125rem;
    border-bottom: 1px solid #EBEBEB;
  }
}
.cover {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  text-align: center;
  background-position: center;
  background-repeat: no-repeat;
  background-size: auto 100%;
  background-color: #101010;
  overflow: hidden;
}
.cover .container {
  position: relative;
}
.cover .cover-title {
  font-size: 1.25rem;
  margin-bottom: 0.25rem;
  color: #FFFFFF;
}
@media (min-width: 576px) {
  .cover .cover-title {
    font-size: 1.5rem;
  }
}
.cover .cover-sub-title {
  font-size: 1rem;
  color: #FFFFFF;
}
@media (min-width: 576px) {
  .cover .cover-sub-title {
    font-size: 1.125rem;
  }
}
.home-cover {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  text-align: center;
  background-position: center;
  background-repeat: no-repeat;
  background-size: auto 100%;
  background-color: #101010;
  overflow: hidden;
  height: 26.25rem;
  padding: 3.5rem 0;
}
.home-cover .container {
  position: relative;
}
.home-cover .cover-title {
  font-size: 1.25rem;
  margin-bottom: 0.25rem;
  color: #FFFFFF;
}
@media (min-width: 576px) {
  .home-cover .cover-title {
    font-size: 1.5rem;
  }
}
.home-cover .cover-sub-title {
  font-size: 1rem;
  color: #FFFFFF;
}
@media (min-width: 576px) {
  .home-cover .cover-sub-title {
    font-size: 1.125rem;
  }
}
@media (max-width: 575px) {
  .home-cover {
    height: 27.5rem;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
}
@media (min-width: 768px) {
  .home-cover .container {
    margin-top: -4rem;
  }
}
.home-cover .home-cover-image-wrap {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  max-width: 100rem;
  z-index: 0;
}
.home-cover .homepage-cover-image {
  display: none;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
  max-height: none;
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}
.home-cover .home-cover-image-desktop {
  display: none;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
  max-height: none;
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  left: 60%;
  margin-left: -10rem;
}
@media (min-width: 576px) {
  .home-cover .home-cover-image-desktop {
    display: block;
  }
}
@media (min-width: 992px) {
  .home-cover .home-cover-image-desktop {
    margin-left: 0;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
  }
}
.home-cover .home-cover-image-mobile {
  display: none;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
  max-height: none;
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  top: auto;
  bottom: -3%;
  -webkit-transform: translate(-50%, 0);
          transform: translate(-50%, 0);
}
@media (max-width: 575px) {
  .home-cover .home-cover-image-mobile {
    display: block;
  }
}
.home-cover .cover-sub-title {
  margin-bottom: 1.125rem;
}
@media (min-width: 576px) {
  .home-cover .cover-sub-title {
    margin-bottom: 2rem;
  }
}
.home-cover .cover-search {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  max-width: 28.75rem;
  width: 100%;
  height: 3.125rem;
  margin: 0 auto;
  -webkit-box-shadow: 0 0.375rem 0.75rem 0 rgba(223, 0, 0, 0.2);
          box-shadow: 0 0.375rem 0.75rem 0 rgba(223, 0, 0, 0.2);
}
.home-cover .cover-search label {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 1.5rem;
  padding: 0 0.75rem;
  margin-bottom: 0;
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  cursor: text;
}
.home-cover .cover-search label .icon {
  height: 100%;
  width: 100%;
}
.home-cover .cover-search input {
  display: inline-block;
  width: 100%;
  height: auto;
  padding: 0.75rem 0.75rem 0.75rem 3.25rem;
  border-radius: 4px 0 0 4px;
  border: none;
  font-size: 1.125rem;
  font-weight: 600;
}
.home-cover .cover-search button {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding-left: 1rem;
  padding-right: 1rem;
  border-radius: 0 4px 4px 0;
  -ms-flex-negative: 0;
      flex-shrink: 0;
}
.home-cover .cover-search button svg,
.home-cover .cover-search button .icon {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  vertical-align: middle;
  margin-right: 0.625rem;
}
.home-cover .cover-search button svg,
.home-cover .cover-search button .icon {
  margin-right: 0;
  margin-left: 0.625rem;
}
@media (max-width: 575px) {
  .home-cover .cover-search button .home-cover-search-submit-text {
    display: none;
  }
}
.home-cover .cover-search button .icon {
  width: 1.1875rem;
  fill: #FFFFFF;
}
@media (max-width: 575px) {
  .home-cover .cover-search button .icon {
    margin-left: 0;
  }
}
.subpage-cover {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  text-align: center;
  background-position: center;
  background-repeat: no-repeat;
  background-size: auto 100%;
  background-color: #101010;
  overflow: hidden;
  padding: 2rem 0;
  background-color: #111111;
  height: 10rem;
}
.subpage-cover .container {
  position: relative;
}
.subpage-cover .cover-title {
  font-size: 1.25rem;
  margin-bottom: 0.25rem;
  color: #FFFFFF;
}
@media (min-width: 576px) {
  .subpage-cover .cover-title {
    font-size: 1.5rem;
  }
}
.subpage-cover .cover-sub-title {
  font-size: 1rem;
  color: #FFFFFF;
}
@media (min-width: 576px) {
  .subpage-cover .cover-sub-title {
    font-size: 1.125rem;
  }
}
@media (min-width: 576px) {
  .subpage-cover {
    height: 12.5rem;
  }
}
.subpage-cover .cover-title {
  font-style: italic;
  font-weight: 700;
  font-size: 1.375rem;
}
@media (min-width: 576px) {
  .subpage-cover .cover-title {
    font-size: 1.75rem;
  }
}
.subpage-cover .cover-title em {
  display: inline-block;
  position: relative;
  z-index: 10;
}
.subpage-cover .cover-title em::after {
  content: '';
  display: block;
  position: absolute;
  top: -0.3rem;
  right: -0.35rem;
  bottom: -0.3rem;
  left: -0.35rem;
  z-index: -1;
  background-color: rgba(223, 0, 0, 0.6);
  -webkit-transform: skew(-12deg, 0);
          transform: skew(-12deg, 0);
}
@media (min-width: 576px) {
  .subpage-cover .cover-title em::after {
    top: -0.4rem;
    bottom: -0.4rem;
  }
}
.partner-logo-list {
  max-width: 46.625rem;
  margin: 0 auto;
}
.partner-logo-row {
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-right: -12px;
  margin-left: -12px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-bottom: -1.5rem;
}
.partner-logo-row::before,
.partner-logo-row::after {
  display: none;
}
.partner-logo-col {
  position: relative;
  float: left;
  width: 33.33333333%;
  min-height: 1px;
  padding-right: 4px;
  padding-left: 4px;
  float: none;
  -ms-flex-preferred-size: 33.33333333%;
      flex-basis: 33.33333333%;
  margin-bottom: 0.5rem;
}
@media (min-width: 576px) {
  .partner-logo-col {
    position: relative;
    float: left;
    width: 33.33333333%;
    min-height: 1px;
    padding-right: 12px;
    padding-left: 12px;
    float: none;
    -ms-flex-preferred-size: 33.33333333%;
        flex-basis: 33.33333333%;
    margin-bottom: 1.5rem;
  }
}
@media (min-width: 768px) {
  .partner-logo-col {
    position: relative;
    float: left;
    width: 20%;
    min-height: 1px;
    padding-right: 12px;
    padding-left: 12px;
    float: none;
    -ms-flex-preferred-size: 20%;
        flex-basis: 20%;
  }
}
.partner-logo {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  height: 4.375rem;
  padding: 0.375rem;
  -webkit-box-shadow: 0 12px 24px 0 rgba(0, 0, 0, 0.1);
          box-shadow: 0 12px 24px 0 rgba(0, 0, 0, 0.1);
  background-color: #FFFFFF;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 4px;
}
@media (min-width: 576px) {
  .partner-logo {
    height: 5.125rem;
  }
}
.partner-logo img {
  max-width: 100%;
  max-height: 100%;
}
.category-banner-row {
  margin-right: -1px;
  margin-left: -1px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.category-banner-row::before,
.category-banner-row::after {
  display: none;
}
.category-banner-col {
  position: relative;
  float: left;
  width: 100%;
  min-height: 1px;
  padding-right: 1px;
  padding-left: 1px;
  float: none;
  -ms-flex-preferred-size: 100%;
      flex-basis: 100%;
  margin-bottom: 2px;
}
@media (min-width: 992px) {
  .category-banner-col {
    position: relative;
    float: left;
    width: 33.33333333%;
    min-height: 1px;
    padding-right: 1px;
    padding-left: 1px;
    float: none;
    -ms-flex-preferred-size: 33.33333333%;
        flex-basis: 33.33333333%;
    margin-bottom: 0.125rem;
  }
}
.category-banner {
  position: relative;
  padding-top: 130%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 1;
  border-color: transparent;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
.category-banner:hover {
  border-left: 1px solid #FFFFFF;
  border-right: 1px solid #FFFFFF;
  -webkit-transform: scale(1.033);
          transform: scale(1.033);
  -webkit-box-shadow: 0 0.5rem 2.125rem 0.125rem rgba(0, 0, 0, 0.2);
          box-shadow: 0 0.5rem 2.125rem 0.125rem rgba(0, 0, 0, 0.2);
  z-index: 10;
}
@media (min-width: 576px) {
  .category-banner {
    padding-top: 110%;
  }
}
@media (min-width: 768px) {
  .category-banner {
    padding-top: 80%;
  }
}
@media (min-width: 992px) {
  .category-banner {
    padding-top: 110%;
  }
}
.category-banner a {
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  color: #FFFFFF;
  text-decoration: none;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(50%, rgba(0, 0, 0, 0)), to(#000000));
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 50%, #000000 100%);
  z-index: 10;
}
.category-banner .content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: absolute;
  left: 1rem;
  bottom: 1rem;
  right: 1rem;
}
@media (min-width: 992px) {
  .category-banner .content {
    left: 1.5rem;
    bottom: 1.5rem;
    right: 1.5rem;
  }
}
.category-banner .title {
  color: #FFFFFF;
  font-size: 1.25rem;
  margin-bottom: 0.25rem;
}
@media (min-width: 768px) {
}
@media (min-width: 992px) {
  .category-banner .title {
    font-size: 1.5rem;
  }
}
.category-banner .description {
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 0;
}
.category-banner .link {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  width: 3.75rem;
  height: 3.75rem;
  margin-left: 1rem;
}
.category-banner .link .icon {
  width: 1.1875rem;
  height: 0.75rem;
  fill: #FFFFFF;
}
.card {
  display: block;
  padding: 1.5rem;
  -webkit-box-shadow: 0 12px 24px 6px rgba(14, 25, 67, 0.08);
          box-shadow: 0 12px 24px 6px rgba(14, 25, 67, 0.08);
  border-radius: 1rem;
  margin-bottom: 1rem;
  background-color: #fff;
}
@media (min-width: 992px) {
  .card {
    padding: 2rem;
  }
}
.table > tbody > tr > th {
  font-weight: 600;
}
.table > tbody > tr > th,
.table > tbody > tr > td {
  font-size: 0.875rem;
  padding: 0.6875rem;
}
.table > tbody > tr > th:first-child,
.table > tbody > tr > td:first-child {
  padding-left: 0;
}
.table > tbody > tr > th:last-child,
.table > tbody > tr > td:last-child {
  padding-right: 0;
}
.table > tbody > tr:last-child > th,
.table > tbody > tr:last-child > td {
  border-bottom: 1px solid #EBEBEB;
}
.suggestions-list .suggestion {
  margin-bottom: 0;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -ms-touch-action: manipulation;
      touch-action: manipulation;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  padding: 6px 12px;
  font-size: 16px;
  line-height: 1.5;
  border-radius: 4px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  -webkit-transition-property: background, background-color, border, outline, color;
  transition-property: background, background-color, border, outline, color;
  color: #2F3538;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  display: inline-block;
  -webkit-box-shadow: 0 12px 24px 0 rgba(0, 0, 0, 0.08);
          box-shadow: 0 12px 24px 0 rgba(0, 0, 0, 0.08);
  font-weight: 600;
  margin-right: 0.5rem;
  margin-bottom: 0.7rem;
  padding: 0.5rem;
  font-size: 0.875rem;
}
.suggestions-list .suggestion:focus,
.suggestions-list .suggestion:active:focus,
.suggestions-list .suggestion.active:focus,
.suggestions-list .suggestion.focus,
.suggestions-list .suggestion:active.focus,
.suggestions-list .suggestion.active.focus {
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
  outline: none;
}
.suggestions-list .suggestion:hover,
.suggestions-list .suggestion:focus,
.suggestions-list .suggestion.focus {
  color: #333;
  text-decoration: none;
}
.suggestions-list .suggestion:active,
.suggestions-list .suggestion.active {
  background-image: none;
  outline: 0;
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.suggestions-list .suggestion.disabled,
.suggestions-list .suggestion[disabled],
fieldset[disabled] .suggestions-list .suggestion {
  cursor: not-allowed;
  filter: alpha(opacity=65);
  opacity: 0.65;
  -webkit-box-shadow: none;
  box-shadow: none;
}
a.suggestions-list .suggestion.disabled,
fieldset[disabled] a.suggestions-list .suggestion {
  pointer-events: none;
}
.suggestions-list .suggestion:active,
.suggestions-list .suggestion.active {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.05);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.05);
}
.suggestions-list .suggestion:focus,
.suggestions-list .suggestion.focus {
  color: #2F3538;
  background-color: #e6e6e6;
  border-color: #bfbfbf;
}
.suggestions-list .suggestion:hover {
  color: #2F3538;
  background-color: #e6e6e6;
  border-color: #e0e0e0;
}
.suggestions-list .suggestion:active,
.suggestions-list .suggestion.active,
.open > .dropdown-toggle.suggestions-list .suggestion {
  color: #2F3538;
  background-color: #e6e6e6;
  background-image: none;
  border-color: #e0e0e0;
}
.suggestions-list .suggestion:active:hover,
.suggestions-list .suggestion.active:hover,
.open > .dropdown-toggle.suggestions-list .suggestion:hover,
.suggestions-list .suggestion:active:focus,
.suggestions-list .suggestion.active:focus,
.open > .dropdown-toggle.suggestions-list .suggestion:focus,
.suggestions-list .suggestion:active.focus,
.suggestions-list .suggestion.active.focus,
.open > .dropdown-toggle.suggestions-list .suggestion.focus {
  color: #2F3538;
  background-color: #d4d4d4;
  border-color: #bfbfbf;
}
.suggestions-list .suggestion.disabled:hover,
.suggestions-list .suggestion[disabled]:hover,
fieldset[disabled] .suggestions-list .suggestion:hover,
.suggestions-list .suggestion.disabled:focus,
.suggestions-list .suggestion[disabled]:focus,
fieldset[disabled] .suggestions-list .suggestion:focus,
.suggestions-list .suggestion.disabled.focus,
.suggestions-list .suggestion[disabled].focus,
fieldset[disabled] .suggestions-list .suggestion.focus {
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}
.suggestions-list .suggestion .badge {
  color: #FFFFFF;
  background-color: #2F3538;
}
.suggestions-list .suggestion:focus,
.suggestions-list .suggestion.focus {
  color: #2F3538;
  background-color: #ececec;
  border-color: transparent;
}
.suggestions-list .suggestion:hover {
  color: #2F3538;
  background-color: #ececec;
  border-color: transparent;
}
.suggestions-list .suggestion:active,
.suggestions-list .suggestion.active,
.open > .dropdown-toggle.suggestions-list .suggestion {
  color: #2F3538;
  background-color: #e6e6e6;
  background-image: none;
  border-color: transparent;
}
.suggestions-list .suggestion:active:hover,
.suggestions-list .suggestion.active:hover,
.open > .dropdown-toggle.suggestions-list .suggestion:hover,
.suggestions-list .suggestion:active:focus,
.suggestions-list .suggestion.active:focus,
.open > .dropdown-toggle.suggestions-list .suggestion:focus,
.suggestions-list .suggestion:active.focus,
.suggestions-list .suggestion.active.focus,
.open > .dropdown-toggle.suggestions-list .suggestion.focus {
  color: #2F3538;
  background-color: #dfdfdf;
  border-color: transparent;
}
.suggestions-list .suggestion.disabled:hover,
.suggestions-list .suggestion[disabled]:hover,
fieldset[disabled] .suggestions-list .suggestion:hover,
.suggestions-list .suggestion.disabled:focus,
.suggestions-list .suggestion[disabled]:focus,
fieldset[disabled] .suggestions-list .suggestion:focus,
.suggestions-list .suggestion.disabled.focus,
.suggestions-list .suggestion[disabled].focus,
fieldset[disabled] .suggestions-list .suggestion.focus {
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}
.suggestions-list .suggestion .badge {
  color: #FFFFFF;
  background-color: #2F3538;
}
.suggestions-list .suggestion:hover {
  border-color: #0094D2;
  background-color: rgba(0, 148, 210, 0.03);
}
@media (min-width: 576px) {
  .suggestions-list .suggestion {
    font-size: 1rem;
    margin-right: 1rem;
    margin-bottom: 0.75rem;
    padding: 0.75rem;
  }
}
.breadcrumb {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-bottom: 0.75rem;
}
.breadcrumb .breadcrumb-item {
  position: relative;
  font-weight: 700;
  padding-left: 1.35rem;
  font-size: 0.875rem;
}
.breadcrumb .breadcrumb-item:first-of-type {
  padding-left: 0;
}
.breadcrumb .breadcrumb-item::before {
  position: absolute;
  top: 50%;
  left: 0.5rem;
  -webkit-transform: translate(0, -50%);
          transform: translate(0, -50%);
  display: inline-block;
  background-image: url('../img/breadcrumb-separator.svg');
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  width: 0.3125rem;
  height: 0.5625rem;
  padding: 0;
}
.breadcrumb .breadcrumb-item .icon {
  width: 0.9375rem;
  height: 0.8125rem;
  fill: #0094D2;
}
.breadcrumb a {
  display: inline-block;
  color: #0094D2;
}
.product-list .product-list-header {
  background-color: #F4FAFB;
  padding-bottom: 1.5rem;
}
.product-list .product-list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 1.625rem;
}
.product-list .product-list-item .product-list-item-link {
  display: block;
  width: 100%;
  padding: 1.4rem 1.3rem;
  background-color: #FFFFFF;
  -webkit-box-shadow: 0 12px 24px 6px rgba(14, 25, 67, 0.08);
          box-shadow: 0 12px 24px 6px rgba(14, 25, 67, 0.08);
  border-radius: 1rem;
  margin: 0 -3px;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  color: #2F3538;
  text-decoration: none;
}
.product-list .product-list-item .product-list-item-link:hover {
  -webkit-transform: scale(1.033);
          transform: scale(1.033);
  -webkit-box-shadow: 0 12px 24px 6px rgba(14, 25, 67, 0.1);
          box-shadow: 0 12px 24px 6px rgba(14, 25, 67, 0.1);
}
.product-list .product-list-item .product-list-item-badge {
  display: inline-block;
  font-size: 0.875rem;
  font-style: italic;
  font-weight: 600;
  padding: 0.35rem 0.8rem;
  background-color: #F7F7F7;
  border-radius: 10rem;
  margin-bottom: 0.2rem;
}
.product-list .product-list-item img {
  display: block;
  margin: 0 auto 1.25rem;
}
.product-list .product-list-item .product-list-item-category {
  font-size: 0.75rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}
.product-list .product-list-item .product-list-item-title {
  margin-bottom: 0.75rem;
}
.product-list .product-list-item .product-list-item-price-title {
  font-size: 0.875rem;
  font-style: italic;
  color: rgba(47, 53, 56, 0.6);
  font-weight: 600;
  margin-bottom: 0.5rem;
}
.product-list .product-list-item .product-list-item-price {
  display: block;
  margin-bottom: 0.625rem;
}
.product-list .product-list-item .product-list-item-current-price {
  font-size: 1.125rem;
  margin-right: 0.25rem;
  font-weight: 700;
}
.product-list .product-list-item .product-list-item-original-price {
  font-size: 0.875rem;
  color: rgba(47, 53, 56, 0.6);
  font-weight: 500;
}
.product-list .product-list-item .product-list-item-availability {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-weight: 500;
}
.product-list .product-list-item .product-list-item-availability .icon {
  width: 1.5rem;
  height: 1.5rem;
  margin-right: 0.625rem;
}
.product-list .product-list-item .product-list-button {
  display: inline-block;
  margin-bottom: 0;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -ms-touch-action: manipulation;
      touch-action: manipulation;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  padding: 6px 12px;
  font-size: 16px;
  line-height: 1.5;
  border-radius: 4px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  -webkit-transition-property: background, background-color, border, outline, color;
  transition-property: background, background-color, border, outline, color;
  width: 100%;
  color: #2F3538;
  font-weight: 600;
  border-radius: 10rem;
  border: 1px solid #EBEBEB;
  background-color: transparent;
}
.product-list .product-list-item .product-list-button:focus,
.product-list .product-list-item .product-list-button:active:focus,
.product-list .product-list-item .product-list-button.active:focus,
.product-list .product-list-item .product-list-button.focus,
.product-list .product-list-item .product-list-button:active.focus,
.product-list .product-list-item .product-list-button.active.focus {
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
  outline: none;
}
.product-list .product-list-item .product-list-button:hover,
.product-list .product-list-item .product-list-button:focus,
.product-list .product-list-item .product-list-button.focus {
  color: #333;
  text-decoration: none;
}
.product-list .product-list-item .product-list-button:active,
.product-list .product-list-item .product-list-button.active {
  background-image: none;
  outline: 0;
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.product-list .product-list-item .product-list-button.disabled,
.product-list .product-list-item .product-list-button[disabled],
fieldset[disabled] .product-list .product-list-item .product-list-button {
  cursor: not-allowed;
  filter: alpha(opacity=65);
  opacity: 0.65;
  -webkit-box-shadow: none;
  box-shadow: none;
}
a.product-list .product-list-item .product-list-button.disabled,
fieldset[disabled] a.product-list .product-list-item .product-list-button {
  pointer-events: none;
}
.product-list .product-list-item .product-list-button:active,
.product-list .product-list-item .product-list-button.active {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.05);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.05);
}
.product-list .product-list-item .product-list-button:hover {
  background-color: #EBEBEB;
}
.product-detail {
  padding: 0.875rem 0 6rem;
  margin-bottom: -2rem;
}
@media (max-width: 991px) {
  .product-detail {
    background: linear-gradient(180deg, #FFFFFF 56%, rgba(255, 255, 255, 0) 100%) no-repeat right, linear-gradient(320deg, rgba(255, 255, 255, 0), #FFFFFF 35%) no-repeat right, repeating-linear-gradient(320deg, rgba(0, 0, 0, 0.04), rgba(0, 0, 0, 0.04) 13px, rgba(0, 0, 0, 0) 3px, rgba(0, 0, 0, 0) 15px) no-repeat right;
    background-size: 100% 100%,
                100% 100%,
                100% 100%;
  }
}
@media (min-width: 992px) {
  .product-detail {
    background: linear-gradient(180deg, #FFFFFF 56%, rgba(255, 255, 255, 0) 100%) no-repeat right, linear-gradient(320deg, rgba(255, 255, 255, 0), #FFFFFF 35%) no-repeat right, repeating-linear-gradient(320deg, rgba(0, 0, 0, 0.04), rgba(0, 0, 0, 0.04) 13px, rgba(0, 0, 0, 0) 3px, rgba(0, 0, 0, 0) 15px) no-repeat right, linear-gradient(320deg, #FFFFFF 32%, rgba(255, 255, 255, 0) 40%) no-repeat left, linear-gradient(45deg, rgba(255, 255, 255, 0), #FFFFFF 35%) no-repeat left, repeating-linear-gradient(320deg, rgba(0, 0, 0, 0.04), rgba(0, 0, 0, 0.04) 13px, rgba(0, 0, 0, 0) 3px, rgba(0, 0, 0, 0) 15px) no-repeat left;
    background-size: 100% 100%,
                50% 100%,
                50% 100%,
                50% 100%,
                50% 100%,
                50% 100%;
  }
}
.product-detail a:not(.btn) {
  color: #0094D2;
}
@media (min-width: 768px) {
  .product-detail .product-detail-main-content {
    max-width: 30.625rem;
  }
}
@media (min-width: 992px) {
  .product-detail .product-detail-main-content {
    margin-left: auto;
  }
}
@media (min-width: 1200px) {
  .product-detail .product-detail-main-content {
    margin-left: 10%;
  }
}
.product-detail .product-detail-title {
  font-size: 1.25rem;
  margin-bottom: 0.375rem;
}
@media (min-width: 992px) {
  .product-detail .product-detail-title {
    font-size: 1.5rem;
  }
}
.product-detail .product-detail-description {
  font-size: 0.875rem;
  margin-bottom: 0.875rem;
}
.product-detail .product-detail-price-table .product-detail-price-row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.product-detail .product-detail-price-table .product-detail-price-col {
  width: 50%;
}
.product-detail .product-detail-text-larger {
  font-size: 1rem;
}
@media (min-width: 992px) {
  .product-detail .product-detail-text-larger {
    font-size: 1.125rem;
  }
}
.product-detail .product-detail-add-to-cart-section {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
@media (min-width: 992px) {
  .product-detail .product-detail-add-to-cart-section {
    -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
  }
}
.product-detail .product-detail-add-to-cart-section > * {
  -ms-flex-negative: 0;
      flex-shrink: 0;
}
.product-detail .product-detail-add-to-cart-section .product-detail-button {
  -ms-flex-negative: 1;
      flex-shrink: 1;
  width: 100%;
  height: 2.77777778em;
  font-size: 1rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
@media (max-width: 991px) {
  .product-detail .product-detail-add-to-cart-section .product-detail-button:last-child {
    margin-top: 1.5rem;
  }
}
.product-detail .product-detail-add-to-cart-section .product-detail-button.background-less:not(:hover) {
  background-color: transparent;
}
@media (min-width: 992px) {
  .product-detail .product-detail-add-to-cart-section .product-detail-button {
    font-size: 1.125rem;
    margin-top: 0;
  }
  .product-detail .product-detail-add-to-cart-section .product-detail-button:last-child {
    margin-left: 1.125rem;
  }
}
.product-detail .product-detail-back {
  display: inline-block;
  margin-bottom: 0;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -ms-touch-action: manipulation;
      touch-action: manipulation;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  padding: 6px 12px;
  font-size: 16px;
  line-height: 1.5;
  border-radius: 4px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  -webkit-transition-property: background, background-color, border, outline, color;
  transition-property: background, background-color, border, outline, color;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  color: #2F3538;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  font-size: 0.875rem;
  color: #0094D2 !important;
  font-weight: 700;
  margin-bottom: 1rem;
}
.product-detail .product-detail-back:focus,
.product-detail .product-detail-back:active:focus,
.product-detail .product-detail-back.active:focus,
.product-detail .product-detail-back.focus,
.product-detail .product-detail-back:active.focus,
.product-detail .product-detail-back.active.focus {
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
  outline: none;
}
.product-detail .product-detail-back:hover,
.product-detail .product-detail-back:focus,
.product-detail .product-detail-back.focus {
  color: #333;
  text-decoration: none;
}
.product-detail .product-detail-back:active,
.product-detail .product-detail-back.active {
  background-image: none;
  outline: 0;
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.product-detail .product-detail-back.disabled,
.product-detail .product-detail-back[disabled],
fieldset[disabled] .product-detail .product-detail-back {
  cursor: not-allowed;
  filter: alpha(opacity=65);
  opacity: 0.65;
  -webkit-box-shadow: none;
  box-shadow: none;
}
a.product-detail .product-detail-back.disabled,
fieldset[disabled] a.product-detail .product-detail-back {
  pointer-events: none;
}
.product-detail .product-detail-back:active,
.product-detail .product-detail-back.active {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.05);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.05);
}
.product-detail .product-detail-back svg,
.product-detail .product-detail-back .icon {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  vertical-align: middle;
  margin-right: 0.625rem;
}
.product-detail .product-detail-back:focus,
.product-detail .product-detail-back.focus {
  color: #2F3538;
  background-color: #e6e6e6;
  border-color: #bfbfbf;
}
.product-detail .product-detail-back:hover {
  color: #2F3538;
  background-color: #e6e6e6;
  border-color: #e0e0e0;
}
.product-detail .product-detail-back:active,
.product-detail .product-detail-back.active,
.open > .dropdown-toggle.product-detail .product-detail-back {
  color: #2F3538;
  background-color: #e6e6e6;
  background-image: none;
  border-color: #e0e0e0;
}
.product-detail .product-detail-back:active:hover,
.product-detail .product-detail-back.active:hover,
.open > .dropdown-toggle.product-detail .product-detail-back:hover,
.product-detail .product-detail-back:active:focus,
.product-detail .product-detail-back.active:focus,
.open > .dropdown-toggle.product-detail .product-detail-back:focus,
.product-detail .product-detail-back:active.focus,
.product-detail .product-detail-back.active.focus,
.open > .dropdown-toggle.product-detail .product-detail-back.focus {
  color: #2F3538;
  background-color: #d4d4d4;
  border-color: #bfbfbf;
}
.product-detail .product-detail-back.disabled:hover,
.product-detail .product-detail-back[disabled]:hover,
fieldset[disabled] .product-detail .product-detail-back:hover,
.product-detail .product-detail-back.disabled:focus,
.product-detail .product-detail-back[disabled]:focus,
fieldset[disabled] .product-detail .product-detail-back:focus,
.product-detail .product-detail-back.disabled.focus,
.product-detail .product-detail-back[disabled].focus,
fieldset[disabled] .product-detail .product-detail-back.focus {
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}
.product-detail .product-detail-back .badge {
  color: #FFFFFF;
  background-color: #2F3538;
}
.product-detail .product-detail-back:focus,
.product-detail .product-detail-back.focus {
  color: #2F3538;
  background-color: #ececec;
  border-color: transparent;
}
.product-detail .product-detail-back:hover {
  color: #2F3538;
  background-color: #ececec;
  border-color: transparent;
}
.product-detail .product-detail-back:active,
.product-detail .product-detail-back.active,
.open > .dropdown-toggle.product-detail .product-detail-back {
  color: #2F3538;
  background-color: #e6e6e6;
  background-image: none;
  border-color: transparent;
}
.product-detail .product-detail-back:active:hover,
.product-detail .product-detail-back.active:hover,
.open > .dropdown-toggle.product-detail .product-detail-back:hover,
.product-detail .product-detail-back:active:focus,
.product-detail .product-detail-back.active:focus,
.open > .dropdown-toggle.product-detail .product-detail-back:focus,
.product-detail .product-detail-back:active.focus,
.product-detail .product-detail-back.active.focus,
.open > .dropdown-toggle.product-detail .product-detail-back.focus {
  color: #2F3538;
  background-color: #dfdfdf;
  border-color: transparent;
}
.product-detail .product-detail-back.disabled:hover,
.product-detail .product-detail-back[disabled]:hover,
fieldset[disabled] .product-detail .product-detail-back:hover,
.product-detail .product-detail-back.disabled:focus,
.product-detail .product-detail-back[disabled]:focus,
fieldset[disabled] .product-detail .product-detail-back:focus,
.product-detail .product-detail-back.disabled.focus,
.product-detail .product-detail-back[disabled].focus,
fieldset[disabled] .product-detail .product-detail-back.focus {
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}
.product-detail .product-detail-back .badge {
  color: #FFFFFF;
  background-color: #2F3538;
}
.product-detail .product-detail-back .icon {
  fill: rgba(47, 53, 56, 0.6);
}
.product-detail .product-detail-gallery {
  padding: 0 3rem;
  margin-bottom: 1.5rem;
}
@media (min-width: 1200px) {
  .product-detail .product-detail-gallery {
    width: 110%;
  }
}
.product-detail .product-detail-gallery .slick-next,
.product-detail .product-detail-gallery .slick-prev {
  overflow: visible;
  width: 1.25rem;
  height: 2.5rem;
}
.product-detail .product-detail-gallery .slick-next::before,
.product-detail .product-detail-gallery .slick-prev::before {
  display: none;
}
.product-detail .product-detail-gallery .slick-next .icon,
.product-detail .product-detail-gallery .slick-prev .icon {
  width: 100%;
  height: 100%;
  stroke: rgba(47, 53, 56, 0.6);
  stroke-width: 1px;
}
.product-detail .product-detail-gallery .slick-next:hover .icon,
.product-detail .product-detail-gallery .slick-prev:hover .icon {
  stroke: #2F3538;
}
.product-detail .product-detail-gallery .slick-next {
  right: 0;
}
.product-detail .product-detail-gallery .slick-prev {
  left: 0;
}
.product-detail .product-detail-gallery .slick-track {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.product-detail .product-detail-gallery .slick-track .slick-slide {
  height: auto;
}
.product-detail .product-detail-gallery .product-detail-gallery-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 25rem;
}
.product-detail .product-detail-gallery-thumbnails {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.product-detail .product-detail-gallery-thumbnails .product-detail-gallery-thumbnail-item {
  display: block;
  width: 3.125rem;
  height: 3.125rem;
  padding: 0.3125rem;
  background-color: #FFFFFF;
  border: 1px solid #F7F7F7;
  border-radius: 2px;
  margin-right: 1rem;
}
.product-detail .product-detail-gallery-thumbnails .product-detail-gallery-thumbnail-item:last-child {
  margin-right: 0;
}
.product-detail .product-detail-gallery-thumbnails .product-detail-gallery-thumbnail-item:hover {
  border-color: #EBEBEB;
}
.product-detail .product-detail-gallery-thumbnails .product-detail-gallery-thumbnail-item.active {
  border-color: #0094D2;
}

