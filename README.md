BUXUS 7
=======

Buxus 7 package for basic Buxus installation. 

Installation
----------

Please use composer for Buxus installation.

1. `composer create-project buxus/buxus`
2. `npm install` - installs node dependencies
3. `composer require <theme-balicek>` - install initial Buxus theme (for example `krabica-theme/basic`)
4. Create local database (collation:  `utf8-slovak-ci`)
5. Edit file `.env` and fill `HOST`, `DEFAULT_HOSTNAME` a `DB_*` 
6. `php artisan buxus:post-install` - sets up required buxus permissions
7. `php artisan krabica:post-install` - copy demo content (for example product image files)
8. `php artisan vendor:publish` - publish assets, config files and database migrations
9. `php artisan migrate` - run database migrations
10. `php artisan fs:generate fsi` - regenerate faceted search cache
11. `gulp build` - build assets

If you need to purge database, run `php command krabica:purge-db`

Composer inštaluje z ui42 privátnej repository packagist.ui42.sk, preto si je potrebné nastaviť do svojho user profilu súbor
~/.composer/config.json

```json
{
    "repositories": [
        {"type": "composer", "url": "https://packagist.ui42.sk/"}
    ]
}
```

Set up Capistrano deployment
--------------------
We use Capistrano with Jenkins user interface for Buxus projects deployment. Deployment configuration is located in `deploy` directory and is written in Ruby.
Deploy tasks are defined in `deploy/lib/capistrano/tasks` directory. Deploy events binding is defined in `deploy/config/deploy.rb`. 
Capistrano uses deploy profiles which are located in `deploy/config/deploy`. We typically use `generic` profile which loads the most of variables
from Jenkins environment variables configuration. Please, add the following configuration to your Jenkins Build environment (check `Inject environment variables to the build process` and fill this to `	Properties Content`):
```
deploy_to=/home/<USER>/test
app_environment=test
branch=master
server_name=SERVER_NAME
server_username=SERVER_USERNAME
application=PROJECT_NAME
tmp_dir=/home/<USER>/tmp
php_fpm_path=/etc/init.d/php7.2-fpm
```
Please note that you have to replace variables in uppercase with concrete values. Please also check php version for your project and adjust the `php_fpm_path` variable value according to that. If your project doesn't run on php fpm, you only need to remove `after 'deploy', 'buxus:restart_fpm'` line from `deploy/config/deploy.rb`

You can customize the deploy flow and add custom configuration in these files.  

Default users 
--------------------

Default admin je `admin/krabicaadmin` a default user je `user/krabicauser`

### Vytvorenie hostu
Pridať `127.0.0.1   buxus.localhost` do `/etc/hosts`
a vytvoriť virtual host:

```
ln -s /path/to/buxus /var/www/buxus-dev
vim /etc/apache2/sites-enabled/buxus.localhost
        ServerName buxus.localhost
        ServerAlias www.buxus.localhost
        ServerAdmin webmaster@localhost
        DocumentRoot /var/www/buxus-dev/public
service apache2 restart
```

### Fixnutie "červených problémov":
Prihlásiť sa do Buxusu [http://buxus.localhost/buxus/system](http://buxus.localhost/buxus/system) (prihlasovacie údaje: `admin` `krabicaadmin`)

```
chmod 777 /path/to/buxus/storage/buxus/log/log
chmod 777 -R /path/to/buxus/public/buxus/tmp/ /path/to/buxus/public/buxus/docs/ /path/to/buxus/public/buxus/images/
```

### Ak nejde pregenerovať FS

Precondition: V Buxuse sme v nástrojoch.
Nejde spustiť pregenerovanie FS, čo je potrebné.

Riešenie: `sudo apt-get install php5-curl`
