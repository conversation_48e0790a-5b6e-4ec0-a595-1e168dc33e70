# This task will notify <PERSON><PERSON> via webhook of a new release
#
# It requires the SENTRY_RELEASE_WEBHOOK env variable containing the webhook
# from sentry
#
#   ENV['SENTRY_RELEASE_WEBHOOK'] : release webhook relative url (/api/hooks/release/builtin/x/6c04fb719393a04abc084829e0fd4baa0f4dd48fd24e4dd566b26b2f154bf123/)
#

namespace :sentry do
  task :notify_deployment do
    run_locally do
      require 'uri'
      require 'net/https'

      if !ENV['SENTRY_RELEASE_WEBHOOK'].nil? && !ENV['SENTRY_RELEASE_WEBHOOK'].empty?
          puts "Notifying Sentry of release..."
          uri = URI.parse("https://sentry.ui42.sk")
          http = Net::HTTP.new(uri.host, uri.port)
          http.use_ssl = true

          req = Net::HTTP::Post.new(ENV['SENTRY_RELEASE_WEBHOOK'], initheader={'Content-Type' =>'application/json'})
          req.body = %Q[{"version":"#{fetch(:release_timestamp)}","ref":"#{fetch(:current_revision)}"}]

          response = http.start { |h| h.request(req) }
          puts "Sentry response: #{response.body}"
      else
          puts "Sentry release not created. Please set the SENTRY_RELEASE_WEBHOOK environment variable !!!"
      end
    end
  end
end


