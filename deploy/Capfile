# Load DSL and set up stages
require 'capistrano/setup'

# Include default deployment tasks
require 'capistrano/deploy'

require "capistrano/scm/git"
install_plugin Capistrano::SCM::Git

require 'capistrano/npm'
require 'capistrano/composer'
# require 'capistrano/bower'
require 'capistrano/gulp'

if !ENV['PHP_PATH'].nil? && !ENV['PHP_PATH'].empty?
    SSHKit.config.command_map[:php] = ENV['PHP_PATH']
end

if !ENV['COMPOSER_PATH'].nil? && !ENV['COMPOSER_PATH'].empty?
    SSHKit.config.command_map[:composer] = ENV['COMPOSER_PATH']
end

set :composer_flags, '--no-interaction --prefer-dist --quiet --optimize-autoloader'

# Load custom tasks from `lib/capistrano/tasks' if you have any defined
Dir.glob('lib/capistrano/tasks/*.rake').each { |r| import r }
