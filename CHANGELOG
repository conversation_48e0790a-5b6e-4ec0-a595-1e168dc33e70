##7.3.7
- [feature] add `APP_PROTOCOL` to `.env` file and set `https://` as default value

##7.3.6
- [bugfix] fix CSRF token handling due to changes on laravel side

##7.3.5
- [developer][feature] allow queueing cookies in the `buxus_cms` middleware group

##7.3.4
- [developer] allow to set P<PERSON>_PATH, COMPOSER_PATH, GULP_TASK env variables for deploy
- [developer] allow to set ENV variable `BUXUS_CACHE_DRIVER` to change the default BUXUS cache driver
- [developer][bugfix] fix various deployment tasks

##7.3.3
- [bugfix][developer] fix resolving CSRF token from the request input if the input contained an XSRF token

##7.3.2
- [bugfix] fix missing `end` statement for the `buxus:restart_fpm` rake task, fix indendation for `end` statements in the task
- [feature][developer] add `pda/pheanstalk` as a default dependency

##7.3.1
- [feature][developer] added possibility to run actions after deploy, that are tagged by hashtag in commit message

##7.3.0
- [feature] updated version of `krabica/krabica-core` to 7.0 which by default uses the laravel blade layout for rendering ZF actions

##7.2.3
- [feature][developer] Fix resolving xsrf token for alternative sites, add support for csrf placeholders & refactor `csrf.js`
- [bugfix][developer] Fix publishing assets with `buxus:krabica_vendor_publish` deploy task
- [bugfix] made `XSRF-TOKEN` cookie always encrypted and failproofed the `csrf.js` when it is not

##7.2.2
- [feature][developer] Refactor `fs_title_generator()` to use the new `\FS\Seo\FSTitleGenerator` from FS version `5.1.`, which generates page title from current page, active filters & page number

##7.2.1
- [feature][developer] Add `buxus` filesystems storage disk
- [feature] added support for csrf token validation
- [developer] changed source of the csrf cookie for ajax request to be resolved from cookie

##7.2.0
- [feature] support for laravel 6
- [feature] added `SEARCH_HOST` & `SEARCH_PORT` env variables for setting up solr search to the default `fs.php` config

##7.1.6
- [bugfix] changed default `fs.php` config, method `fs_title_generator`, to properly generate FS titles for different languages

##7.1.5
- [developer] added dependency on `krabica/krabica-core` version `^6.0.0`
- [developer] removed unused tests and synced tests with default laravel installation

##7.1.4
- [developer] removed the usage of custom bower repository
- [developer] removed NPM module `bower-requirejs` as it is deprecated
- [developer] default encoding for database is now `utf8mb4`

##7.1.3
- [developer][bugfix] fixed CSS file generation timestamp
- [developer] removed deprecated rake tasks from deployment
- [developer] added the `force_protocol` option to default `app.php` config

##7.1.2
- [developer][feature] moved all model files to the `app` directory
- [developer][feature] the contents of `IndexController` is now taken from the `\Krabica\Controllers\IndexController` class
- [developer][bugfix] fixed the `gulpfile.js` to correctly compile AMD layers and also to uglify ES6 code

##7.1.1
- [developer] removed theme package from `composer.json`

##7.1.0
- [developer] support for laravel 5.8
- [developer] changed versioning scheme to copy the `buxus/core` versioning

##1.1.1
- [bugfix] fixed loading of developer dependencies in `composer.json`

##1.1.0
- [feature] usage of refactored `buxus-libs/email` package version 2.0
- [bugfix] pagination to first page no longer removes all the GET params
- [feature] set default database engine for new tables to `MyISAM`
- [feature] set default database charset to `utf8`

##1.0.6
- [bugfix] fixed default eshop config for variants

##1.0.5
- [bugfix] fixed the default laravel 404 action `\App\Http\Controllers\IndexController@error404` to properly set response headers
- [developer] disabled the config option `app.default_laravel_action` by default to render 404 pages with ZF

##1.0.4
- [bugfix] removed installing of dev composer packages
- [developer] fixed 500 and 503 error pages to show actual date as copyright

##1.0.3
- [developer] changed the SQL mode to not strict by default to make BUXUS work with MySQL5.7

##1.0.2
- [bugfix] fixed page redirection for page with number `1` in `\IndexController::setPage()` to redirect only if page equals exactly the string `"1"`
- [feature] changed the default fs config to use the load more button for paging results

##1.0.1
- [developer] first usable version

##1.0.0
- initial release
