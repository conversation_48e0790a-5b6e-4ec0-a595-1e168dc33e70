<?php

namespace App\Invoice;

use Carbon\Carbon;
use Illuminate\Database\Query\Builder;

class InvoiceFactory
{
    public function getInvoiceForId($enclosureId): ?Invoice
    {
        $invoice = new Invoice($enclosureId);

        if ($invoice->isValid()) {
            return $invoice;
        }

        return null;
    }

    public function builder(): Builder
    {
        return \DB::table('onix_enclosures');
    }

    protected function getInvoicesArray($invoices)
    {
        $invoiceArr = [];

        foreach ($invoices as $invoice) {
            $invoiceArr[] = Invoice::factoryFromDbObject($invoice);
        }

        $invoiceArr = array_filter($invoiceArr);

        return $invoiceArr;
    }

    public function getToBePaidInDays($days, $partnerId = null)
    {
        $invoices = $this->builder()
            ->where('payment_remaining', '>', 0)
            ->whereDate('due_date', '=', Carbon::now()->addDays($days)->toDateString())
            ->when(!empty($partnerId), function ($q) use ($partnerId) {
                $q->where('partner_id', $partnerId);
            })->get();

        return $this->getInvoicesArray($invoices);
    }

    public function getUnpaidInDays($days, $partnerId = null)
    {
        $invoices = $this->builder()
            ->where('payment_remaining', '>', 0)
            ->whereDate('due_date', '=', Carbon::now()->subDays($days)->toDateString())
            ->when(!empty($partnerId), function ($q) use ($partnerId) {
                $q->where('partner_id', $partnerId);
            })->get();

        return $this->getInvoicesArray($invoices);
    }

    public function getUnpaidInMoreThan($days, $partnerId = null)
    {
        $invoices = $this->builder()
            ->where('payment_remaining', '>', 0)
            ->whereDate('due_date', '<=', Carbon::now()->subDays($days)->toDateString())
            ->when(!empty($partnerId), function ($q) use ($partnerId) {
                $q->where('partner_id', $partnerId);
            })->get();

        return $this->getInvoicesArray($invoices);
    }

    public function getUnpaid($partnerId = null): array
    {
        $invoices = $this->builder()
            ->where('payment_remaining', '>', 0)
            ->whereDate('due_date', '<', Carbon::now()->toDateString())
            ->when(!empty($partnerId), function ($q) use ($partnerId) {
                $q->where('partner_id', $partnerId);
            })->get();

        return $this->getInvoicesArray($invoices);
    }

    public function getAllUnpaid(): array
    {
        return $this->getUnpaid();
    }

    public function getUnpaidForUser($userId): array
    {
        $user = \WebUserFactory::getById($userId);

        if (empty($user->getOnixPartnerId())) {
            return [];
        }

        return $this->getUnpaid($user->getOnixPartnerId());
    }
}
