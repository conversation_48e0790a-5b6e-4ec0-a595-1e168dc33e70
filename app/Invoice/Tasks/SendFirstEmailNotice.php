<?php

namespace App\Invoice\Tasks;

use App\Invoice\Facades\InvoiceFactory;
use Buxus\Logger\Logger;
use Buxus\Util\PageIds;

class SendFirstEmailNotice
{
    protected $logger;

    public function __construct()
    {
        $this->logger = new Logger('invoice/send-first-email-notice');
    }

    public function handle()
    {
        $invoices = InvoiceFactory::getToBePaidInDays(5);
        $mailPageId = PageIds::getDueDateIn_5Days();

        $this->logger->info('Starting SendFirstEmailNotice task', ['invoices_count' => count($invoices)]);

        $emailsSentCount = 0;
        foreach ($invoices as $invoice) {
            $this->logger->info('Processing invoice ' . $invoice->vs);
            $users = $invoice->getUsers();
            if (!$invoice->wasEmailSent($mailPageId)) {
                $emails = [];

                foreach ($users as $user) {
                    if (!empty(filter_var($user->getEmail(), FILTER_VALIDATE_EMAIL))) {
                        $emails[] = $user->getEmail();
                    }
                    if (!empty(filter_var($user->getUsername(), FILTER_VALIDATE_EMAIL))) {
                        $emails[] = $user->getUsername();
                    }
                }

                $emails = array_unique($emails);
                if (!empty($emails)) {
                    $email = \Email::get($mailPageId);
                    $email->setRecipientsAddresses($emails);
                    $email->setDataTag('VARIABLE_SYMBOL', $invoice->vs);
                    $email->addAttachment($invoice->getPathForEmail(), $invoice->getFilenameForEmail());
                    $email->send();

                    $emailsSentCount++;
                    $this->logger->info('First notice email sent', [
                        'invoice_vs' => $invoice->vs,
                        'recipients_count' => count($emails)
                    ]);
                }

                $invoice->logEmail($mailPageId);
            }
        }

        $this->logger->info('SendFirstEmailNotice task completed', ['emails_sent' => $emailsSentCount]);
    }
}
