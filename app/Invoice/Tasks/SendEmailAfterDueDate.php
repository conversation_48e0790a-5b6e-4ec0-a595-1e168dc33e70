<?php

namespace App\Invoice\Tasks;

use App\Invoice\Facades\InvoiceFactory;
use Buxus\Logger\Logger;
use Buxus\Util\PageIds;
use BuxusSite;

class SendEmailAfterDueDate
{
    protected $logger;

    public function __construct()
    {
        $this->logger = new Logger('invoice/send-email-after-due-date');
    }

    public function handle()
    {
        $invoices = InvoiceFactory::getUnpaidInDays(1);
        $mailPageId = PageIds::getInvoiceAfterDueDateMail();

        $this->logger->info('Starting SendEmailAfterDueDate task', ['invoices_count' => count($invoices)]);

        $emailsSentCount = 0;
        foreach ($invoices as $invoice) {
            $this->logger->info('Processing invoice ' . $invoice->vs);
            if (!$invoice->wasEmailSent($mailPageId)) {
                $emails = [];
                $users = $invoice->getUsers();

                foreach ($users as $user) {
                    if ($user->shouldInvoiceEmailBeSent()) {
                        $emails[$user->getSite()] = $user->getInvoiceEmailAddress();
                    }
                }

                $emails = array_unique($emails);

                if (!$invoice->wasEmailSent($mailPageId)) {
                    foreach ($emails as $site => $address) {
                        BuxusSite::executeInSiteContext($site, function () use ($address, $mailPageId, $invoice) {
                            if (!empty($address)) {
                                $email = \Email::get($mailPageId);
                                $email->setRecipientsAddresses([$address]);
                                $email->setDataTag('VARIABLE_SYMBOL', $invoice->vs);
                                $email->addAttachment($invoice->getPathForEmail(), $invoice->getFilenameForEmail());
                                $email->send();

                                $invoice->logEmail($mailPageId);
                            }
                        });
                    }

                    $emailsSentCount++;
                    $this->logger->info('Email sent for invoice after due date', [
                        'invoice_vs' => $invoice->vs,
                        'recipients_count' => count($emails)
                    ]);
                }
            }
        }

        $this->logger->info('SendEmailAfterDueDate task completed', ['emails_sent' => $emailsSentCount]);
    }
}
