<?php

namespace App\Invoice;

use App\Models\EnclosureTransaction;
use App\Onix\Onix;
use Buxus\WebUser\Contracts\WebUser;

class CreditNote
{
    public $creditNote;
    protected $invoicesThatCanBePaid;

    public function __construct($enclosureId)
    {
        $this->creditNote = \DB::table('onix_enclosures')
            ->where('enclosure_record_id', $enclosureId)
            ->where('enclosure_type_id', Onix::CREDIT_NOTE_TYPE_ID)
            ->first();
    }

    public function getInvoicesThatCanBePaid(): \Illuminate\Support\Collection
    {
        if ($this->creditNote->payment_remaining <= 0) {
            return collect();
        }

        if ($this->hasOngoingTransaction()) {
            return collect();
        }

        if ($this->hasDeniedTransaction()) {
            return collect();
        }

        if (is_null($this->invoicesThatCanBePaid)) {
            $this->invoicesThatCanBePaid = collect();

            $invoicesToBePaid = \DB::table('onix_enclosures')
                ->where('partner_id', $this->creditNote->partner_id)
                ->where('enclosure_type_id', Onix::INVOICE_TYPE_ID)
                ->whereRaw('0 + payment_remaining >= ' . floatval($this->creditNote->payment_remaining))
                ->orderByDesc('onix_date_document')
                ->get();

            foreach ($invoicesToBePaid as $invoice) {
                $invoice = new Invoice($invoice->enclosure_record_id);

                if ($invoice->hasOngoingTransaction()) {
                    continue;
                }

                $this->invoicesThatCanBePaid->push($invoice);
            }
        }


        return $this->invoicesThatCanBePaid;
    }

    public function hasOngoingTransaction(): bool
    {
        return EnclosureTransaction::where('credit_note_enclosure_record_id', $this->creditNote->enclosure_record_id)
            ->where('status', EnclosureTransaction::STATUS_REQUESTED)
            ->exists();
    }

    public function hasDeniedTransaction(): bool
    {
        return EnclosureTransaction::where('credit_note_enclosure_record_id', $this->creditNote->enclosure_record_id)
            ->where('status', EnclosureTransaction::STATUS_DENIED)
            ->exists();
    }

    public function finishOngoingTransactions(): void
    {
        EnclosureTransaction::where('credit_note_enclosure_record_id', $this->creditNote->enclosure_record_id)
            ->where('status', EnclosureTransaction::STATUS_REQUESTED)
            ->update(['status' => EnclosureTransaction::STATUS_DONE]);
    }
}