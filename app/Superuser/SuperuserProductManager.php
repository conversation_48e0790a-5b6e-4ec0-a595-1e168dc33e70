<?php

namespace App\Superuser;

use App\Eshop\CategoryManager;
use Buxus\Util\PageTypeID;
use Buxus\Util\PropertyTag;
use Buxus\Util\Url;
use Illuminate\Http\Request;

class SuperuserProductManager
{
    public static function render()
    {
        $fakeAuth = new \App\Authentication\FakeAuthentication();

        if ((!\WebUserAuthentication::isAuthenticated()) || (!\WebUserAuthentication::getUser()->isSalesRepresentant())) {
            if (empty($superuser = $fakeAuth->getSuperuser())) {
                return;
            }
        }

        $producersTmp = \Ciselniky::get('product_catalog.producer')->getAllValues();

        $producers = [];

        foreach ($producersTmp as $producer) {
            $producers[$producer->getId()] = $producer->getName();
        }

        $manager = new CategoryManager();
        $category_1 = $manager->getMainCategories();

        return view('partials.superuser.product-create', [
            'producers' => $producers,
            'category_1' => $category_1
        ]);
    }

    public function create(Request $request)
    {
        $request = $request->toArray();

        $categoryId = 0;

        for ($i = 1; $i < 5; $i++) {
            if (!empty($request['product_category_' . $i])) {
                $categorization[] = $request['product_category_' . $i];
                $categoryId = $request['product_category_' . $i];
            } else {
                break;
            }
        }

        $ciselnikProducerId = \Ciselniky::get('product_catalog.producer')->getValueByName($request['product_producer'])->getId();

        if (!empty($categoryId) && is_numeric($categoryId)) {
            $page = \PageFactory::create($categoryId, PageTypeID::ESHOP_PRODUCT_ID());
            $page->setPageName($request['product_title']);
            $page->setValue(PropertyTag::TITLE_TAG(), $request['product_title']);
            $page->setValue(PropertyTag::ESHOP_ROLLER_PRODUCER_TAG(), $ciselnikProducerId);
            $page->setValue(PropertyTag::ESHOP_EUR_FIXED_PRICE_WITHOUT_VAT_TAG(), $request['product_price']);
            $page->setValue(PropertyTag::ESHOP_CZK_FIXED_PRICE_WITHOUT_VAT_TAG(), $request['product_price_cz']);
            $page->setValue(PropertyTag::ONIX_MAIN_CODE_TAG(), $request['product_main_code']);
            $page->setValue(PropertyTag::ONIX_CODES_TAG(), $request['product_main_code']);
            $page->setValue(PropertyTag::ONIX_SEARCH_KEYWORDS_TAG(), $request['product_main_code']);
            $page->setValue(PropertyTag::CATEGORIZATION_TAG(), $categorization);
            $page->save();

            return redirect(Url::page($page->getPageId()));
        }

        return redirect()->back();
    }
}
