<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class IndexLevel extends Model
{
    public const IVECO_STOCK_TYPE_TAG = 'iveco_stock';

    protected $fillable = [
        'index_level_from',
        'additional_price_increase',
        'additional_price_increase_eu',
        'type_tag',
        'index_level_enabled'
    ];

    protected $casts = [
        'index_level_enabled' => 'boolean',
    ];
}
