<?php

namespace App\Models;

use App\Invoice\CreditNote;
use App\OnixLib\Onix;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Model for web user notifications
 *
 * This model represents notifications for web users about new documents
 */
class WebUserNotification extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'tblWebUserNotifications';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'webuser_id',
        'notification_type',
        'notification_item_id',
    ];

    /**
     * Static property to store all notifications for the current user
     * Used for optimization to avoid multiple database queries
     *
     * @var array|null
     */
    protected static $userNotifications = [];

    static public function addOnixNotification($onixPartnerId, $onixNotificationType, $itemId)
    {
        $userIds = \DB::table('tblWebUserOptions')
            ->where('user_option_tag', 'onix_partner_id')
            ->where('user_option_value', $onixPartnerId)
            ->get()->pluck('user_id');

        $notificationType = 0;
        if($onixNotificationType == 1) {
            $notificationType = config('notifications.types.invoice');
        }
        if($onixNotificationType == 2) {
            $notificationType = config('notifications.types.credit_note');
        }
        if($onixNotificationType == 3) {
            $notificationType = config('notifications.types.delivery_note');
        }

        if($notificationType == 0) {
            return;
        }

        foreach ($userIds as $userId) {
            self::addNotification($userId, $notificationType, $itemId);
        }
    }

    /**
     * Add a new notification for a user
     *
     * @param int $userId User ID
     * @param int $notificationType Notification type ID
     * @param int $itemId ID of the document
     * @return WebUserNotification
     */
    static public function addNotification($userId, $notificationType, $itemId)
    {
        $notification = self::where('webuser_id', $userId)
            ->where('notification_type', $notificationType)
            ->where('notification_item_id', $itemId)
            ->first();
        if ($notification) {
            $notification->update(['updated_at' => now()]);
            return $notification;
        }

        $notification = WebUserNotification::create([
            'webuser_id' => $userId,
            'notification_type' => $notificationType,
            'notification_item_id' => $itemId,
            'created_at' => now(),
        ]);

        self::invalidateCache($userId);

        return $notification;
    }

    static private function invalidateCache($userId)
    {
        unset(self::$userNotifications[$userId]);
    }

    static private function loadUserNotifications($userId) : Collection
    {
        if (!isset(self::$userNotifications[$userId])) {
            self::$userNotifications[$userId] = self::where('webuser_id', $userId)
                ->get();
            self::addUnpaidCreditNoteNotifications($userId);
        }
        return self::$userNotifications[$userId];
    }

    static private function addUnpaidCreditNoteNotifications($userId)
    {
        $creditNoteEnclosures = \DB::table('onix_enclosures')
            ->where('partner_id', \WebUserFactory::getById($userId)->getOnixPartnerId())
            ->where('enclosure_type_id', Onix::CREDIT_NOTE_TYPE_ID)
            ->where('payment_remaining', '>', 0)
            ->get();

        foreach ($creditNoteEnclosures as $creditNoteEnclosure) {
            $creditNote = new CreditNote($creditNoteEnclosure->enclosure_record_id);
            if($creditNote->getInvoicesThatCanBePaid()->isEmpty()) {
                continue;
            }
            if(self::hasNotification(config('notifications.types.credit_note'), $creditNoteEnclosure->doc_record_id)) {
                continue;
            }
            self::$userNotifications[$userId]->push(new WebUserNotification([
                'webuser_id' => $userId,
                'notification_type' => config('notifications.types.credit_note'),
                'notification_item_id' => $creditNoteEnclosure->doc_record_id,
                'created_at' => now(),
            ]));
        }
    }

    /**
     * Get count of notifications for a specific type
     *
     * @param int $userId User ID
     * @param int $notificationType Notification type ID
     * @return int
     */
    static public function getNotificationCountByType($userId, $notificationType)
    {
        $notifications = self::loadUserNotifications($userId);

        return count(array_filter($notifications, function($notification) use ($notificationType) {
            return $notification->notification_type == $notificationType;
        }));
    }

    static public function getAllNotificationCounts($userId)
    {
        $notifications = self::loadUserNotifications($userId);

        $counts = [
            'all' => count($notifications),
        ];
        foreach (config('notifications.types') as $notificationType => $notificationId) {
            $counts[$notificationType] = count($notifications->filter(function($notification) use ($notificationType, $notificationId) {
                return $notification->notification_type == $notificationId;
            }));
        }

        return $counts;
    }

    static public function hasNotification($notificationType = null, $itemId = null)
    {
        $notifications = self::loadUserNotifications(\WebUserAuthentication::getUserId());

        if($notificationType === null) {
            return count($notifications) > 0;
        }

        foreach ($notifications as $notification) {
            if($notification->notification_type != $notificationType) {
                continue;
            }
            if($itemId === null) {
                return true;
            }
            if($notification->notification_item_id == $itemId) {
                return true;
            }
        }

        return false;
    }

    static public function getNotificationsCount($notificationType = null)
    {
        $notifications = self::loadUserNotifications(\WebUserAuthentication::getUserId());

        if($notificationType === null) {
            return count($notifications);
        }

        return count(array_filter($notifications->toArray(), function($notification) use ($notificationType) {
            return $notification['notification_type'] == $notificationType;
        }));
    }

}
