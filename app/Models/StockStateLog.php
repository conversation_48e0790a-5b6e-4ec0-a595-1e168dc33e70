<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StockStateLog extends Model
{
    use HasFactory;
    protected $table = 'stock_state_log';

    protected $fillable = [
        'buxus_product_id',
        'stock_value',
    ];

    static public function logCurrentValueIfChanged($buxus_product_id, $stock_value)
    {
        $lastLoggedValue = StockStateLog::where('buxus_product_id', $buxus_product_id)
            ->orderBy('created_at', 'desc')
            ->first();

        if ($lastLoggedValue === null || $lastLoggedValue->stock_value != $stock_value) {
            StockStateLog::create([
                'buxus_product_id' => $buxus_product_id,
                'stock_value' => $stock_value,
            ]);
        }
    }


}
