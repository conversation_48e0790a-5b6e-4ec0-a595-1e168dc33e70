<?php

namespace App\OnixLib;


class OnixHighPriority extends \App\Onix\Onix
{

    public function getOnixUsername()
    {
        return $this->getConfigValue('onix_user_high_priority');
    }

    static function getQueueName()
    {
        if(empty(config('onix.bearer_token_high_priority'))) {
            return 'rinoparts_onix_';
        }
        return 'rinoparts_onix_high_';
    }


    function getConfigValue($tag)
    {
        if($tag === 'bearer_token') {
            $tag = 'bearer_token_high_priority';
            $value = config('onix.' . $tag);
            if(!empty($value)) {
                return $value;
            }
            $tag = 'bearer_token';
        }
        return config('onix.' . $tag);
    }


}
