<?php

namespace App\OnixLib\Libs;

use Illuminate\Support\Facades\DB;

class Reservations
{
    static function reserveProduct($buxusProductId, $orderId, $amount)
    {
        DB::table('onix_reserved_stock_products')
            ->insert([
                'inserted' => date('Y-m-d H:i:s'),
                'buxus_order_id' => $orderId,
                'buxus_product_id' => $buxusProductId,
                'reserved_amount' => $amount
            ]);
    }

    static function reserveProductForWarehouse($buxusProductId, $orderId, $amount, $warehouse)
    {
        DB::table('onix_reserved_stock_products')
            ->insert([
                'inserted' => date('Y-m-d H:i:s'),
                'buxus_order_id' => $orderId,
                'buxus_product_id' => $buxusProductId,
                'reserved_amount' => $amount,
                'warehouse' => $warehouse
            ]);
    }

    static function confirmReservationsForOrder(\Buxus\Eshop\Order\OrderInterface $order)
    {
        $orderItems = $order->getItems();
        foreach ($orderItems as $orderItem) {
            if ($orderItem->getPageId()) {
                self::confirmReservation($orderItem->getPageId(), $order->getOrderId(), $orderItem->getAmount());
            }
        }
    }

    static function confirmReservation($buxusProductId, $orderId, $amount)
    {
        DB::table('onix_reserved_stock_products')
            ->where('buxus_order_id', '=', $orderId)
            ->where('buxus_product_id', '=', $buxusProductId)
            ->whereNull('onix_timestamp')
            ->update([
                'onix_timestamp' => date('Y-m-d H:i:s')
            ]);
    }

    static function cleanReservations($onixTimestamp = null)
    {
        DB::table('onix_reserved_stock_products')
            ->where('reserved_amount', '<=', 0)
            ->delete();
        DB::table('onix_reserved_stock_products')
            ->where('inserted', '<', date('Y-m-d H:i:s', time() - 3600))
            ->delete();

        if ($onixTimestamp) {
            DB::table('onix_reserved_stock_products')
                ->whereNotNull('onix_timestamp')
                ->where('onix_timestamp', '<', $onixTimestamp)
                ->delete();
        }
    }

    static function getReservedAmount($buxusProductId, $afterTimestamp = null)
    {
        $query = DB::table('onix_reserved_stock_products')
            ->where('buxus_product_id', '=', $buxusProductId);
        if ($afterTimestamp) {
            $query->where('onix_timestamp', '>=', date('Y-m-d H:i:s', $afterTimestamp));
        }
        return $query->sum('reserved_amount');
    }

    static function getReservedAmountForWarehouse($buxusProductId, $warehouse, $afterTimestamp = null)
    {
        $query = DB::table('onix_reserved_stock_products')
            ->where('buxus_product_id', '=', $buxusProductId)
            ->where('warehouse', '=', $warehouse);
        if ($afterTimestamp) {
            $query->where('onix_timestamp', '>=', date('Y-m-d H:i:s', $afterTimestamp));
        }
        return $query->sum('reserved_amount');
    }
}
