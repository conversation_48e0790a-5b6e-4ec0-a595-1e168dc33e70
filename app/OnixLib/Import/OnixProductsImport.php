<?php

namespace App\OnixLib\Import;

use App\Onix\Onix;
use App\OnixLib\Jobs\OnixProductItemImportJob;
use App\OnixLib\Jobs\OnixProductsImportJob;
use App\OnixLib\Libs\Reservations;
use App\OnixLib\Loggers\OnixProductsImportLogger;
use Buxus\Core\Constants;
use Buxus\Error\ErrorReporter;
use Buxus\Page\PageInterface;
use Buxus\Util\PageIds;
use Buxus\Util\PageTypeID;
use Buxus\Util\PropertyID;
use Buxus\Util\PropertyTag;

class OnixProductsImport
{

    /** @var OnixProductsImportLogger */
    protected $logger;

    protected $hourly_import = false;

    protected $importTimestamp;

    protected $onix_buxus_categories_map;
    protected $buxus_categories_map_states;

    protected $logNsNumbers;


    public function __construct(OnixProductsImportLogger $logger, $hourly_import = false)
    {
        $this->hourly_import = $hourly_import;
        $this->logger = $logger;
        $this->onix_buxus_categories_map = [];
        $this->buxus_categories_map_states = [];

        $this->logNsNumbers = explode(',', env('ONIX_LOG_NS_NUMBERS', ''));
    }

    public function logItem($onix_item, $message)
    {
        if (in_array($onix_item->Ns_Number, $this->logNsNumbers)) {
            $this->logger->info('@' . $onix_item->Ns_Number . ': ' . $message);
        }
    }

    public function setCategoriesMap($onix_buxus_categories_map, $buxus_categories_map_states)
    {
        $this->onix_buxus_categories_map = $onix_buxus_categories_map;
        $this->buxus_categories_map_states = $buxus_categories_map_states;
    }

    public function getLogger()
    {
        return ($this->logger);
    }

    public function run()
    {
        //dd($this->cleanDeletedProducts());

        Reservations::cleanReservations();

        try {
            $this->logger->info('Starting import job');
            $job = new OnixProductsImportJob($this);

            if (Onix::isSync()) {
                $job->handle();
            } else {
                dispatch($job);
            }
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            echo $e->getMessage();
            ErrorReporter::reportSilent($e);
        }
    }

    /**
     * Zmaze vsetky kategorie/podkategorie a ich produkty ktore nie su v strome
     *
     * @return int
     * @throws \Buxus\Page\Exception\InvalidPageTagException
     */
    protected function cleanDeletedProducts()
    {
        $n = 0;
        $products = \PageFactory::builder()->wherePageType([
            //PageTypeID::ESHOP_PRODUCT_ID(),
            PageTypeID::ESHOP_SUBCATEGORY_ID(),
            PageTypeID::ESHOP_CATEGORY_ID()
        ])->get();
        foreach ($products as $product) {
            if (!$this->getRootPageId($product)) {
                $n++;
                //$product->delete();
                echo $n . ' delete product ' . $product->getPageId() . "\n";
            }
        }

        return $n;
    }

    /**
     * @param PageInterface $page
     */
    protected function getRootPageId($page)
    {
        $parent_page_id = $page->getParentPageId();
        if (!$parent_page_id) {
            return null;
        }
        if ($parent_page_id === PageIds::getEshopCatalog()) {
            return $parent_page_id;
        }

        $parent = \PageFactory::get($parent_page_id);
        if (!$parent) {
            return null;
        }

        return ($this->getRootPageId($parent));
    }

    protected function fetchItems()
    {
        $onix = app(Onix::class, [
            'config' => ['logger' => $this->logger]
        ]);

        $this->logger->info('Process import' . ($onix->isOnline() ? '' : ' OFFLINE'));
        return $onix->getStockItems();
    }

    protected function getItemIds($item)
    {
        return implode('/', [$item->IdRecord, $item->Ns_Code . '/' . $item->Ns_Number, $item->Product_Code]);
    }

    protected function getProductTypes()
    {
        return [
            PageTypeID::ESHOP_PRODUCT_ID()
        ];
    }

    protected function initBeforeLoop()
    {

    }

    protected function isImportable($onix_item)
    {
        // import only stock configured in onix.stock_code
        $inMyStock = false;
        foreach ($onix_item->StockItemBalance as $StockItemBalance) {
            if (in_array($StockItemBalance->StockCode, config('onix.stock_codes'))) {
                $inMyStock = true;
            }
        }
        if (!$inMyStock) {
            $this->logger->info('Product ' . $this->getItemIds($onix_item) . ' is not from stock ' . config('onix.stock_code') . '. Item stock is ' . $StockItemBalance->StockCode . ' Product skipped.');
            return false;
        }
        return true;
    }

    protected function getStartTimestamp()
    {
        return $this->importTimestamp;
    }

    public function setStartTimestamp($timestamp = null)
    {
        $this->importTimestamp = $timestamp;
        if (!$this->importTimestamp) {
            $this->importTimestamp = time();
        }
    }

    protected function initInLoop($item)
    {

    }

    protected function getPages(array $pageIds): array
    {
        $pages = [];
        foreach ($pageIds as $pageId) {
            $pages[] = \PageFactory::get($pageId);
        }

        return $pages;
    }

    protected function findPagesByCode($item): array
    {
        // $codes = $this->getCodes($item);
        return [];
    }

    protected function createNewPage($item, $parent_for_import = null)
    {
        $this->logItem($item, 'Create new product ' . __METHOD__ . ':' . __LINE__ . '=' . $parent_for_import . '/');
        if ($parent_for_import === null) {
            $this->logItem($item, 'Create new product ' . __METHOD__ . ':' . __LINE__ . '=' . $parent_for_import . '/');
            $parent_for_import = $this->getDefaultCategoryId($item);
            $this->logItem($item, 'Create new product ' . __METHOD__ . ':' . __LINE__ . '=' . $parent_for_import . '/');
        }
        $this->logItem($item, 'Create new product ' . __METHOD__ . ':' . __LINE__ . '=' . $parent_for_import . '/');
        $this->logItem($item, 'Create new product in category ' . $parent_for_import);
        $this->logger->info('Create product ' . $this->getItemIds($item) . ' (category=' . $parent_for_import . ')');
        if (!$parent_for_import) {
            throw new \Exception('$parent_for_import is null ' . __LINE__);
        }
        $page = \PageFactory::create($parent_for_import, PageTypeID::ESHOP_PRODUCT_ID());

        return $page;
    }

    protected function getCodes($onixItem)
    {
        $main_code = '';
        $codes = [];
        $keywords = [];
        if (is_array($onixItem->StockItemCodes)) {
            foreach ($onixItem->StockItemCodes as $code) {
                $codes[] = $code->Code;
                if (ltrim($code->Code, '0') !== $code->Code) {
                    $keywords[] = ltrim($code->Code, '0');
                }
                if ($code->Is_Default === -1) {
                    $main_code = $code->Code;
                }
            }
        }

        return [
            'main_code' => $main_code,
            'codes' => $codes,
            'keywords' => $keywords
        ];
    }


    protected function parseCustomColumns($item)
    {
        $lng_map = config('onix.lng_map');
        $custom_columns = [];
        if (is_array($item->CustomColumns)) {
            foreach ($item->CustomColumns as $customColumn) {
                $column_name = trim($customColumn->Name);
                $column_value = trim($customColumn->Value);

                // language names
                if (is_array($lng_map)) {
                    if (substr($column_name, 0, 6) === 'Nazov_') {
                        $lng = substr($column_name, 6);
                        $lng = $lng_map[$lng];
                        if ($lng) {
                            $column_name = 'title_' . $lng;
                        }
                    }
                }

                $custom_columns[$column_name] = $column_value;
            }
        }
        return $custom_columns;
    }

    protected function getDefaultCategoryId($item)
    {
        return PageIds::getEshopCatalog();
    }

    /**
     * Process the import of items.
     *
     * @param array|null $onix_items The items to be imported. Default is null.
     *
     * @return void
     */
    public function processImport($onix_items = null)
    {
        $this->logger->info('PROCESS PRODUCT IMPORT START');
        $this->logger->info('Import type: ' . ($this->hourly_import ? 'hourly' : 'daily'));

        $this->setStartTimestamp();
        if ($onix_items === null) {
            $onix_items = $this->fetchItems();
        }

        if (!is_array($onix_items)) {
            $this->logger->error('Items not an array.');
            return;
        }
        $this->logger->info('Total onix items = ' . count($onix_items));

        // fetch existing onix_ids from buxus
        $onix_ns_records_pages = \PageFactory::builder()
            ->wherePageType($this->getProductTypes())
            ->whereNotEmptyPropertyValue(PropertyID::ONIX_NS_NUMBER_ID())
            ->getRaw(['page.page_id', 'property_value']);
        $onix_ns_records = [];
        $products_for_delete = [];
        foreach ($onix_ns_records_pages as $p) {
            if (!isset($onix_ns_records[$p->property_value])) {
                $onix_ns_records[$p->property_value] = [];
            }
            $onix_ns_records[$p->property_value][] = $p->page_id;
            $products_for_delete[(int)$p->page_id] = true;
        }

        $this->initBeforeLoop();

        $async = true;

        $n = 0;
        foreach ($onix_items as $onix_item) {
            $n++;
            if (!($n % 100)) {
                echo $n . "\n";
                $this->logger->info('Processed ' . $n . ' items');
            }

            $this->logItem($onix_item, 'Start import');

            if (!$this->isImportable($onix_item)) {
                $this->logItem($onix_item, 'Product skipped because is not importable');
                continue;
            }

            $this->initInLoop($onix_item);

            if (isset($onix_ns_records[$onix_item->Ns_Number])) {
                $this->logItem($onix_item, 'Product exists in buxus by onix_ns_number page_ids=' . implode(',', $onix_ns_records[$onix_item->Ns_Number]));
                /**
                 * product exists in buxus by onix_ns_number
                 */
                foreach ($onix_ns_records[$onix_item->Ns_Number] as $onixPageId) {
                    unset($products_for_delete[(int)$onixPageId]);
                }

                if ($async) {
                    $this->logItem($onix_item, 'Dispatch async job ');

                    dispatch(new OnixProductItemImportJob(
                        $onix_ns_records[$onix_item->Ns_Number],
                        $onix_item,
                        $this->hourly_import,
                        $this->onix_buxus_categories_map,
                        $this->buxus_categories_map_states,
                        $this->importTimestamp
                    ));
                } else {
                    $this->logItem($onix_item, 'Update existing products');
                    $pages = $this->getPages($onix_ns_records[$onix_item->Ns_Number]);
                    foreach ($pages as $page) {
                        $this->beforeUpdatePage($page, $onix_item);
                        $this->updatePage($page, $onix_item, false);
                        $this->afterUpdateExistingPage($page, $onix_item);
                    }
                }
            } else {
                $this->logItem($onix_item, 'Find pages by code');
                $pages = $this->findPagesByCode($onix_item);
                if (!count($pages)) {
                    $this->logItem($onix_item, 'No pages found by code');
                    if ($async) {
                        $this->logItem($onix_item, 'Dispatch async job ');
                        dispatch(
                            new OnixProductItemImportJob([],
                                $onix_item,
                                $this->hourly_import,
                                $this->onix_buxus_categories_map,
                                $this->buxus_categories_map_states,
                                $this->importTimestamp
                            ));
                    } else {
                        $this->logItem($onix_item, 'Create new product');
                        $page = $this->createNewPage($onix_item);
                        if ($page) {
                            $this->beforeUpdatePage($page, $onix_item);
                            $this->updatePage($page, $onix_item, true);
                            $this->afterCreateNewPage($page, $onix_item);
                            $pages = [$page];
                        }
                    }
                } else {
                    $pageIds = [];
                    foreach ($pages as $page) {
                        if ($page) {
                            $pageIds[] = $page->getPageId();
                        }
                    }
                    $this->logItem($onix_item, 'Pages found by code (' . implode(',', $pageIds) . ')');

                    if ($async) {
                        $this->logItem($onix_item, 'Dispatch async job ');
                        dispatch(new OnixProductItemImportJob(
                            $pageIds,
                            $onix_item,
                            $this->hourly_import,
                            $this->onix_buxus_categories_map,
                            $this->buxus_categories_map_states,
                            $this->importTimestamp
                        ));
                    } else {
                        foreach ($pages as $page) {
                            if ($page) {
                                $this->logItem($onix_item, 'New Onix product ' . $this->getItemIds($onix_item) . '. Pairing to ' . $page->getPageId());

                                $this->beforeUpdatePage($page, $onix_item);
                                $this->updatePage($page, $onix_item, false);
                                $this->afterUpdateExistingPage($page, $onix_item);
                            }
                        }
                    }


                    foreach ($pages as $page) {
                        if ($page) {
                            if (!isset($onix_ns_records[$onix_item->Ns_Number])) {
                                $onix_ns_records[$onix_item->Ns_Number] = [];
                            }
                            $onix_ns_records[$onix_item->Ns_Number][] = $page->getPageId();
                        }
                    }
                }
            }
        }
        $this->unusedProductsSetAsPassive($products_for_delete);
        $this->afterLoop();
        $this->logger->info('IMPORT DONE. Total ' . count($onix_items) . '. Updated/Created/Deleted ' . implode('/', [$this->logger->getUpdatedProductsCount(), $this->logger->getCreatedProductsCount(), $this->logger->getDeletedProductsCount()]) . ' products.');
    }

    public function updatePagesAsync(array $page_ids, object $onix_item): void
    {

        echo '=';
        if (!count($page_ids)) {
            $this->logItem($onix_item, 'Create new product');
            $page = $this->createNewPage($onix_item);
            $this->logItem($onix_item, 'Create new product');
            $this->beforeUpdatePage($page, $onix_item);
            $this->updatePage($page, $onix_item, true);
            $this->afterCreateNewPage($page, $onix_item);
        } else {
            $this->logItem($onix_item, 'Update existing products ' . implode(',', $page_ids));
            $pages = $this->getPages($page_ids);
            foreach ($pages as $page) {
                $this->beforeUpdatePage($page, $onix_item);
                $this->updatePage($page, $onix_item, false);
                $this->afterUpdateExistingPage($page, $onix_item);
            }
        }
    }


    protected function unusedProductsSetAsPassive($products_for_delete = [])
    {
        // set passive for deleted products
        if (count($products_for_delete)) {
            $products_for_delete_active = \PageFactory::builder()->wherePage(array_keys($products_for_delete))->whereActive()->getPageIds()->toArray();
            foreach ($products_for_delete_active as $page_id) {
                $page = \PageFactory::get($page_id);
                if ($page) {
                    $this->setPageStateForDeletedProduct($page);
                    $page->save();
                    $this->logger->incrementDeletedProductCount();
                }
            }
            $this->logger->info('Set to passive products =' . implode(',', $products_for_delete_active));
            $this->logger->info('Passive products =' . implode(',', array_keys($products_for_delete)));
        }
    }

    protected function afterLoop()
    {

    }

    public function beforeUpdatePage(PageInterface $page, $onix_item): void
    {

    }

    /**
     * @param \Buxus\Page\PageInterface $page
     * @param $item
     */
    private function updatePage($page, $item, $created = false)
    {
        $old = [];
        $new = [];

        $this->logItem($item, 'Update page ' . $page->getPageId());

        // base properties
        $old['parent_page_id'] = $page->getParentPageId();
        $old['page_name'] = $page->getPageName();
        $old['page_state'] = $page->getPageStateId();
        $old['page_class'] = $page->getPageClassId();

        $page->setPageName($item->Name);
        $page->setPageClassId('1');

        $props = array_keys($page->getProperties());
        foreach ($props as $prop) {
            $old[$prop] = $this->getPropertyValue($page, $prop);
        }

        // extended properties
        $page->setValue(PropertyTag::ONIX_ID_RECORD_TAG(), $item->IdRecord);
        $page->setValue(PropertyTag::ONIX_NS_NUMBER_TAG(), $item->Ns_Number);
        $page->setValue(PropertyTag::ONIX_NS_CODE_TAG(), $item->Ns_Code);

        // props
        $save_props = [];
        $lng_map = config('onix.lng_map');
        if (is_array($lng_map)) {
            foreach ($lng_map as $lng) {
                if (isset($custom_columns['title_' . $lng])) {
                    $save_props['title_' . $lng] = $custom_columns['title_' . $lng];
                }
            }
        }
        foreach ($save_props as $prop_name => $prop_value) {
            $page->setValue($prop_name, $prop_value);
        }

        $this->updateCustomProperties($page, $item);
        if ($created) {
            $this->setPageStateForNewProduct($page);
        } else {
            $this->setPageStateForUpdatedProduct($page);
        }
        $this->updateStockAndPrices($page, $item);

        $new['parent_page_id'] = $page->getParentPageId();
        $new['page_name'] = $page->getPageName();
        $new['page_state'] = $page->getPageStateId();
        $new['page_class'] = $page->getPageClassId();
        foreach ($props as $prop) {
            $new[$prop] = $this->getPropertyValue($page, $prop);;
        }

        if ($this->isPageModified($page, $old, $new)) {
            $this->logItem($item, 'Product modified');
            $this->savePage($page);
            $this->logItem($item, 'Product saved as page_id=' . $page->getPageId());


            if ($created) {
                $this->logger->incrementCreatedProductCount();
                $this->logItem($item, 'Create product page_id=' . $page->getPageId() . ' ' . $this->getItemIds($item) . ' STATE=' . $page->getPageStateId());
            } else {
                $this->logger->incrementUpdatedProductCount();
                $this->logItem($item, 'Update product page_id=' . $page->getPageId() . ' ' . $this->getItemIds($item) . ' STATE=' . $page->getPageStateId());
            }
        } else {
            $this->logItem($item, 'Product not modified');
        }
    }

    protected function afterCreateNewPage(\Buxus\Page\PageInterface $page, $onix_item): void
    {

    }

    protected function afterUpdateExistingPage(\Buxus\Page\PageInterface $page, $onix_item): void
    {

    }

    protected function setPageStateForNewProduct(PageInterface $page)
    {
        $page->setPageStateId(Constants::C_active_page_state_id);
    }

    protected function setPageStateForUpdatedProduct(PageInterface $page)
    {
        $page->setPageStateId(Constants::C_active_page_state_id);
    }

    protected function setPageStateForDeletedProduct(PageInterface $page)
    {
        $page->setPageStateId(Constants::C_passive_page_state_id);
    }

    /**
     * @param \Buxus\Page\PageInterface $page
     * @param array $oldValues
     * @param array $newValues
     * @return boolean
     */
    protected function isPageModified($page, array $oldValues, array $newValues)
    {
        if (implode('|', $oldValues) === implode('|', $newValues)) {
            return false;
        }

//        $diff = [];
//        foreach ($oldValues as $key => $value) {
//            if(!isset($newValues[$key])) {
//                $diff[$key] = [ $value, null ];
//                continue;
//            }
//            if($value != $newValues[$key]) {
//                $diff[$key] = [ $value, $newValues[$key] ];
//                continue;
//            }
//        }
//        foreach ($newValues as $key => $value) {
//            if(!isset($oldValues[$key])) {
//                $diff[$key] = [ null, $value ];
//            }
//        }
//        $this->logger->info('DIFF: ' . print_r($diff, 1));

        return true;
    }

    protected function savePage($page)
    {
        $page->save();
    }

    protected function updateStockAndPrices($page, $item)
    {
        // stock
        $stock_state = 2;
        $balance = 0;
        $price = 0;
        $reservated = 0;

        foreach ($item->StockItemBalance as $stock) {
            if ($stock->StockCode == config('onix.stock_code')) {
                if ($stock->Available > 0) {
                    $stock_state = 1;
                }
                $price = max($price, (float)$stock->StockPrice);
                $balance += (float)$stock->Balance;
                $reservated += (float)$stock->Reservated;
            }
        }

        if ($page->getPageId()) {
            $reservated += Reservations::getReservedAmount($page->getPageId());
        }

        if ($balance - $reservated <= 0) {
            $stock_state = 2;
        }

        $page->setValue(PropertyTag::ESHOP_EUR_PRICE_WITHOUT_VAT_TAG(), $price);
        $page->setValue(PropertyTag::AVAILABILITY_TAG(), $stock_state);

        // $balance
        // $reservated
    }

    protected function updateCustomProperties(PageInterface $page, $item)
    {

    }

    protected function getPropertyValue($page, $tag)
    {
        return $page->getValue($tag);
    }

    public function loadCategories(): void
    {

    }

}
