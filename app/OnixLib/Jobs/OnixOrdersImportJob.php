<?php
namespace App\OnixLib\Jobs;

use App\Onix\Import\OnixOrdersImport;
use App\Onix\OnixHighPriority;
use App\OnixLib\Loggers\OnixOrdersImportLogger;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;

class OnixOrdersImportJob implements ShouldQueue
{
    use Queueable;


    /**
     * @var OnixOrdersImportLogger
     */
    protected $logger;

    /**
     * OnixOrdersImportJob constructor.
     * @param OnixOrdersImportLogger $logger
     */
    public function __construct(OnixOrdersImportLogger $logger)
    {
        $this->logger = $logger;

        $this->onQueue(OnixHighPriority::getQueueName() . env('DB_DATABASE'));
    }

    public function handle()
    {
        $import = new OnixOrdersImport($this->logger);
        try {
            $import->processImport();
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            echo $e->getMessage();
            exit;
        }
    }
}
