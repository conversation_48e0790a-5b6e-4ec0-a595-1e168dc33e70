<?php

namespace App\OnixLib\Jobs;

use App\Onix\Import\OnixProductsImport;
use App\OnixLib\Loggers\OnixProductsImportLogger;
use Buxus\Error\ErrorReporter;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;

class OnixProductItemImportJob implements ShouldQueue
{
    use Queueable;
    public $timeout = 60 * 60;

    protected array $page_ids;
    protected object $onixItemData;
    protected $hourly_import;

    protected $importTimestamp;

    protected $onix_buxus_categories_map;
    protected $buxus_categories_map_states;

    public function __construct(array  $page_ids,
                                object $onixItemData,
                                       $hourly_import = true,
                                       $onix_buxus_categories_map = [],
                                       $buxus_categories_map_states = [],
                                       $importTimestamp = null
    )
    {
        $this->page_ids = $page_ids;
        $this->onixItemData = $onixItemData;
        $this->hourly_import = $hourly_import;
        $this->onix_buxus_categories_map = $onix_buxus_categories_map;
        $this->buxus_categories_map_states = $buxus_categories_map_states;
        $this->importTimestamp = $importTimestamp;
        if(!$this->importTimestamp) {
            $this->importTimestamp = time();
        }

        $this->onQueue('rinoparts_onix_products_' . env('DB_DATABASE'));
    }

    public function handle()
    {
        ini_set('memory_limit', '4608M');

        $logger = new OnixProductsImportLogger();
        $import = new OnixProductsImport($logger, $this->hourly_import);
        $import->logItem($this->onixItemData, 'Starting asynx job for page ids: ' . implode(',', $this->page_ids));
        $import->setStartTimestamp($this->importTimestamp);

        try {
            $import->setCategoriesMap($this->onix_buxus_categories_map, $this->buxus_categories_map_states);
            $import->updatePagesAsync($this->page_ids, $this->onixItemData);
        } catch (\Exception $e) {
            $import->getLogger()->error($e->getMessage());
            echo $e->getMessage();
            ErrorReporter::reportSilent($e);
            exit;
        }
    }
}
