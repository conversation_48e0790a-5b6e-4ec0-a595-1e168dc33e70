<?php

namespace App\OnixLib\Jobs;

use App\Onix\Import\OnixInvoicesImport;
use App\OnixLib\Loggers\OnixInvoicesImportLogger;
use Buxus\Error\ErrorReporter;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;

class OnixInvoicesImportJob implements ShouldQueue
{
    use Queueable;


    /**
     * @var OnixInvoicesImportLogger
     */
    protected $logger;
    public $timeout = 30 * 60;

    /**
     * OnixInvoicesImportJob constructor.
     * @param OnixInvoicesImportLogger $logger
     */
    public function __construct(OnixInvoicesImportLogger $logger)
    {
        $this->logger = $logger;

        $this->onQueue('rinoparts_onix_' . env('DB_DATABASE'));
    }

    public function handle()
    {
        $import = new OnixInvoicesImport($this->logger);
        try {
            $import->processImport();
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            ErrorReporter::reportSilent($e);
            echo $e->getMessage();
            exit;
        }
    }
}
