<?php

namespace App\OnixLib\Jobs;

use App\Onix\Import\OnixProductsImport;
use Buxus\Error\ErrorReporter;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;

class OnixProductsImportJob implements ShouldQueue
{
    use Queueable;

    public $timeout = 60 * 60;
    /**
     * @var OnixProductsImport
     */
    protected $import;

    /**
     * OnixProductsImportJob constructor.
     * @param OnixProductsImport $import
     */
    public function __construct(
        OnixProductsImport $import
    )
    {
        $this->import = $import;

        $this->onQueue('rinoparts_onix_' . env('DB_DATABASE'));
    }

    public function handle()
    {
        try {
            $this->import->processImport();
        } catch (\Exception $e) {
            $this->import->getLogger()->error($e->getMessage());
            echo $e->getMessage();
            ErrorReporter::reportSilent($e);
            exit;
        }
    }
}
