<?php

namespace App\OnixLib\WatchDog;

use App\Onix\Export\OnixOrdersExport;
use App\OnixLib\Jobs\OnixWatchDogJob;
use App\OnixLib\Loggers\OnixWatchDogLogger;
use Buxus\Util\PageIds;

class OnixWatchDog
{
    /** @var OnixWatchDogLogger */
    protected $logger;

    /** @var bool */
    protected $sync = true;

    /**
     * OnixWatchDog constructor.
     * @param OnixWatchDogLogger $logger
     */
    public function __construct(OnixWatchDogLogger $logger)
    {
        $this->logger = $logger;
    }

    function getLogger()
    {
        return $this->logger;
    }


    public function run()
    {
        try {
            $this->logger->info('Starting watching...');
            $job = new OnixWatchDogJob($this);

            if ($this->sync) {
                $job->handle();
            }
            else {
                dispatch($job);
            }
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            echo $e->getMessage();
        }
    }

    public function processWatching()
    {
        $message = '';

        // objednavky ktore nie su odoslane, a su starsie ako x, ale nie starsie ako y
        $order_ids = OnixOrdersExport::getOrderIdsForProcessing(true);
        $count = 0;
        if(count($order_ids)) {
            $order_ids = $order_ids->toArray();

            $this->logger->info('Orders: ' . implode(', ', $order_ids));
            $order_ids_str = '';
            $order_ids_str_without_onix_user_id = '';
            if(is_array($order_ids)) {
                foreach ($order_ids as $order_id) {
                    $order = \OrderFactory::getById($order_id);
                    $lastNotif = $order->getData('error_last_notified');
                    if(empty($lastNotif) || strtotime($lastNotif) < (time() - 3600*12)) { // repeat after 12 hours
                        $onix_partner_id = null;
                        $user_id = $order->getUserId();
                        if($user_id) {
                            $webUser = \WebUserFactory::getById($user_id);
                            if ($webUser && !empty($webUser->getCustomOption('onix_partner_id'))) {
                                $onix_partner_id = $webUser->getCustomOption('onix_partner_id');
                            }
                        }

                        if(empty($onix_partner_id)) {
                            $order_ids_str_without_onix_user_id .= ' ' . $order->getVariableSymbol();
                        }
                        else {
                            $order_ids_str .= ' ' . $order->getVariableSymbol();
                        }

                        $order->setData('error_last_notified', date('Y-m-d H:i:s'));
                        $order->save(false);
                        $count++;
                    }
                }
            }
            $message .= "Niektoré objednávky neboli prenesené do Onixu. Počet objednávok " . $count . ". ";
            if(strlen($order_ids_str)) {
                $message .= "Objednávky: " . $order_ids_str;
            }
            if(strlen($order_ids_str_without_onix_user_id)) {
                $message .= "Objednávky bez PartnerID: " . $order_ids_str_without_onix_user_id;
            }
        }
        else {
            $this->logger->info('Orders: -');
        }

        if(strlen($message) && $count) {
            $mail = $this->getNotificationEmail($message);
            if(app()->environment() !== 'test') {
                $mail->send();
            }
        }

        $this->logger->info('DONE.');
    }

    /**
     * @param $message
     * @return mixed
     */
    protected function getNotificationEmail($message)
    {
        $buxusEmail = \Email\Facade\Email::get(PageIds::getOnixNotificationEmail());

        $buxusEmail->setDataTag('MESSAGE', $message);

        return $buxusEmail;
    }

}
