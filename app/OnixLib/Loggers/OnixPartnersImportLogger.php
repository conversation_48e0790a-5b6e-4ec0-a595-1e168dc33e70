<?php
namespace App\OnixLib\Loggers;


class OnixPartnersImportLogger extends \Buxus\Logger\Logger
{
    /** @var int */
    protected $updatedPartnersCount = 0;

    public function __construct()
    {
        parent::__construct('onix/import-import-partners');
    }

    /**
     * @return int
     */
    public function getUpdatedPartnersCount()
    {
        return $this->updatedPartnersCount;
    }

    /**
     * @param int $updatedPartnersCount
     */
    public function setUpdatedPartnersCount($updatedPartnersCount)
    {
        $this->updatedPartnersCount = $updatedPartnersCount;
    }

    public function incrementUpdatedPartnerCount()
    {
        $this->setUpdatedPartnersCount($this->getUpdatedPartnersCount() + 1);
    }
}
