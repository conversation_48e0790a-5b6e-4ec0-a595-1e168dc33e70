<?php
namespace App\OnixLib\Commands;

use App\Onix\WatchDog\OnixWatchDog;
use App\OnixLib\Loggers\OnixWatchDogLogger;
use Illuminate\Console\Command;

class OnixWatchDogCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'onix:watch-dog';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Report errors.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $logger = new OnixWatchDogLogger();
        $dog = new OnixWatchDog($logger);
        $dog->run();
    }
}
