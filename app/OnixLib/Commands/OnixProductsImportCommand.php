<?php
namespace App\OnixLib\Commands;

use App\Onix\Import\OnixProductsImport;
use App\OnixLib\Loggers\OnixProductsImportLogger;
use Illuminate\Console\Command;

class OnixProductsImportCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'onix:import-products';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import Products from Onix API.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $hourly_import = true;

        if((date('H')) <= 2) {
            $hourly_import = false;
        }
        $hourly_import = false;
        $logger = new OnixProductsImportLogger();
        $import = new OnixProductsImport($logger, $hourly_import);
        $import->run();
    }
}
