<?php

namespace App\Authentication\Form;

use Authentication\AuthenticationPages;
use Authentication\Form\ResetPasswordForm;
use Buxus\Util\Url;
use Illuminate\Http\Request;

class ExtendedResetPasswordForm extends ResetPasswordForm
{
    protected function submitHandler(Request $request)
    {
        $password = $this->getValue('password');

        /**
         * Ensure login is enabled after password reset
         * this also works as a simple registration form for silently registered web users
         *
         * Then update the account with the newly provided password
         *
         * @note Web user is saved in the `resetPassword` method!
         */
        $this->user->resetPassword($password);

        if (!$this->user->canLogin()) {
            $this->redirect(Url::taggedPage(AuthenticationPages::FORGOTTEN_PASSWORD_CHANGED));
        }


        \WebUserAuthentication::login($this->user->getUsername(), $password);

        $this->redirect(Url::taggedPage(AuthenticationPages::CLIENT_ZONE));
    }
}