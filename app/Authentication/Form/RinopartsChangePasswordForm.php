<?php

namespace App\Authentication\Form;

use Authentication\Form\ChangePasswordForm;
use Authentication\Form\Validator\ValidateConfirmPasswordConfirmation;
use Buxus\Captcha\Form\Element\CaptchaFormElement;
use Buxus\Captcha\Form\Element\HoneypotFormElement;
use FormBase\Element\PasswordElement;
use FormBase\Element\SubmitElement;

class RinopartsChangePasswordForm extends ChangePasswordForm
{
    public function init()
    {
        // Password
        $password = new PasswordElement('password');
        $password->setLabel(\Trans::raw(lang('user', 'Heslo')));
        $password->addValidator(new \Zend_Validate_StringLength(6, null, 'utf-8'));
        $password->addLabelClass('col-xs-12');
        $password->setValueClass('col-xs-9');
        $password->setRequired(true);
        $this->addElement($password);

        // Confirm password
        $confirm_password = new PasswordElement('confirm_password');
        $confirm_password->setLabel(\Trans::raw(lang('user', 'Potvrdenie hesla')));
        $confirm_password->addValidator(new ValidateConfirmPasswordConfirmation());
        $confirm_password->addLabelClass('col-xs-12');
        $confirm_password->setValueClass('col-xs-9');
        $confirm_password->setRequired(true);
        $this->addElement($confirm_password);

        // Fake field for antispam protection
        $honeypotElement = new HoneypotFormElement();
        $this->addElement($honeypotElement);

        $captchaElement = new CaptchaFormElement();
        $this->addElement($captchaElement);

        // Submit
        $submit = new SubmitElement($this->getFormTag());
        $submit->setLabel(\Trans::raw(lang('user', 'Odoslať')));
        $this->addElement($submit);
    }
}