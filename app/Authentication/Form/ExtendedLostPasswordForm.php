<?php

namespace App\Authentication\Form;

use Authentication\AuthenticationPages;
use Authentication\Form\LostPasswordForm;
use Buxus\Util\Url;
use Illuminate\Http\Request;

class ExtendedLostPasswordForm extends LostPasswordForm
{
    protected function submitHandler(Request $request)
    {
        $email = $request->get('e_mail');

        $user = \WebUserFactory::getByEmail($email);

        if (is_null($user)) {
            $user = \WebUserFactory::getByUsername($email);
        }

        if (!is_null($user)) {
            $token = $user->generateLostPasswordToken();
            // Send email to user
            $buxus_email = \Email::get(AuthenticationPages::FORGOTTEN_PASSWORD_EMAIL);

            // Add recipient
            $buxus_email->addRecipientAddress($email);

            $buxus_email->setDataTag('HOSTNAME', \BuxusSite::getHostname());
            $buxus_email->setDataTag(
                'LINK',
                Url::staticUrl(
                    Url::taggedPage(
                        AuthenticationPages::FORGOTTEN_PASSWORD_CHANGE_PASSWORD,
                        '&token=' . urlencode($token)
                    )
                )
            );

            //Send email
            $buxus_email->send();
        }

        $this->redirect(Url::taggedPage(AuthenticationPages::FORGOTTEN_PASSWORD_SENT));
    }
}