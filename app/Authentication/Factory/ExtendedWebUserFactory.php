<?php

namespace App\Authentication\Factory;

use Buxus\WebUser\Contracts\WebUser as WebUserContract;
use Buxus\WebUser\WebUserFactory;

class ExtendedWebUserFactory extends WebUserFactory
{
    public function getByUsername($email, $site = null)
    {
        if (is_null($site)) {
            $site = \BuxusSite::site();
        }

        $email = trim($email);

        $query = \DB::table('tblWebUsers')
            ->select('user_id')
            ->where('username', '=', $email);

        $isLoginSiteDependent = config('buxus_authentication.is_site_dependent', true);
        if (true == $isLoginSiteDependent) {
            $query = $query->where('site', '=', $site);
        }
        $result = collect($query->get());
        $userId = $result->pluck('user_id')->first();

        if (empty($userId)) {
            return null;
        }

        /**
         * @var WebUserContract $user
         */
        $user = $this->app->make(WebUserContract::class);
        $user->setUserId($userId);

        return $user;
    }
}