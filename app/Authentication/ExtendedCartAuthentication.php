<?php

namespace App\Authentication;

use Buxus\Email\KeyValueDataProviderInterface;
use Buxus\Error\ErrorReporter;
use Buxus\Eshop\Event\OrderPreCreateEvent;
use Eshop\ShoppingCart\Authentication\CartAuthentication;

class ExtendedCartAuthentication extends CartAuthentication
{
    public function handle(OrderPreCreateEvent $event)
    {
        $order = $event->getOrder();
        $dataWasChanged = false;
        $userWasJustCreated = false;

        try {
            if ($order instanceof KeyValueDataProviderInterface) {
                $orderData = $order->getKeyValueData();
            } else {
                $orderData = [];
            }

            $fullyRegisteredDuringCheckout = false;
            $webUser = \WebUserFactory::getById($order->getUserId());
            if ($webUser === null) {
                $webUser = \WebUserFactory::getUser($order->getEmail(), $orderData, false, false);
                $userWasJustCreated = true;
                $fullyRegisteredDuringCheckout = $this->processFullWebUserRegistration($order, $orderData, $webUser);
                if (config('buxus_shopping_cart.automatic_registration_send_password_enabled', false) &&
                    !$webUser->isFullyActivated() &&
                    !$fullyRegisteredDuringCheckout) {
                    $webUser->sendAutomaticLoginData(true);
                }
            } else {
                if (!$webUser->isFullyActivated()) {
                    $dataWasChanged = $webUser->fillData($orderData);
                    $fullyRegisteredDuringCheckout = $this->processFullWebUserRegistration($order, $orderData, $webUser);
                    if (config('buxus_shopping_cart.automatic_registration_send_password_enabled', false) && !$fullyRegisteredDuringCheckout) {
                        $webUser->sendAutomaticLoginData(true);
                    }
                }
            }

            $saveWebUser = $fullyRegisteredDuringCheckout || $dataWasChanged || $userWasJustCreated;

            if (!$webUser->isFullyActivated() ||
                (isset($orderData['mailinglist_sign_up']) && $orderData['mailinglist_sign_up'] === 'T')) {
                $automaticNewsletters = config('buxus_shopping_cart.automatic_newsletters_to_register_after_shopping', []);

                if (is_array($automaticNewsletters) && !empty($automaticNewsletters)) {
                    $saveWebUser = true;
                    $webUser->setEnableMailingLists(true);
                    foreach ($automaticNewsletters as $automaticNewsletter) {
                        $webUser->setMailing($automaticNewsletter);
                    }
                }
            }

            if ($saveWebUser) {
                $webUser->save();
            }

            $order->setUserId($webUser->getUserId());
        } catch (\Exception $e) {
            ErrorReporter::reportSilent($e, __METHOD__, 'unable to get user for order. email %s', $order->getEmail());
        }
    }
}
