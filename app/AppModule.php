<?php

namespace App;

use App\Authentication\ExtendedCartAuthentication;
use App\Authentication\Factory\ExtendedWebUserFactory;
use App\Authentication\Form\EditProfileForm;
use App\Authentication\Form\ExtendedLostPasswordForm;
use App\Authentication\Form\ExtendedResetPasswordForm;
use App\Authentication\Form\RegistrationForm;
use App\Authentication\Form\RinopartsChangePasswordForm;
use App\Blade\ClientZoneTitleBladeDirective;
use App\Email\ExtendedBaseEmail;
use App\Eshop\Cart\CartCommandProcessor;
use App\Eshop\Checkout;
use App\Eshop\Delivery\CarrierDeliveryType;
use App\Eshop\Delivery\RinopartsGenericDeliveryType;
use App\Eshop\Delivery\MMTransportDeliveryType;
use App\Eshop\Order\RinopartsOrder;
use App\Eshop\Order\RinopartsOrderItem;
use App\Eshop\Payment\AdvancePaymentType;
use App\Eshop\Payment\CashOnDeliveryPaymentType;
use App\Eshop\Payment\InvoicePaymentType;
use App\Eshop\Payment\RinopartsGenericPaymentType;
use App\Eshop\Price\Event\ActionPriceChangedEvent;
use App\Eshop\Price\Event\ActionPriceChangedEventHandler;
use App\Eshop\RinopartsFreeDeliveryManager;
use App\Eshop\ShoppingCart;
use App\Eshop\ShoppingCartFactory;
use App\Events\AppSiteSwitchedEventHandler;
use App\Events\CartChangeEventHandler;
use App\Events\CartRemoveEvent;
use App\Events\Order\AddSuperuserOnOrderCreated;
use App\Events\Order\SendEmailOnOrderCreated;
use App\Form\ContactForm;
use App\Form\Events\FormUpdatedEvent;
use App\Form\Events\FormUpdatedEventHandler;
use App\Form\Menu\ExtendedFormMenu;
use App\FulltextSearch\Event\WebUserSearchEvent;
use App\FulltextSearch\Event\WebUserSearchEventHandler;
use App\FulltextSearch\ProductSearch\Event\ProductImpressionEvent;
use App\FulltextSearch\ProductSearch\Event\ProductImpressionEventHandler;
use App\FulltextSearch\ProductSearch\Event\ProductSearchEmptyResultsEvent;
use App\FulltextSearch\ProductSearch\Event\ProductSearchEmptyResultsEventHandler;
use App\Invoice\Events\AddNotificationOnLogin;
use App\Invoice\ExtendedPdfInvoiceGenerator;
use App\Invoice\InvoiceFactory;
use App\Invoice\InvoiceManager;
use App\Mailinglist\Form\RinopartsMailinglistForm;
use App\Page\AppPageFactory;
use App\Page\EventHandlers\GroupedProductsHandler;
use App\Persistence\RinopartsPersistence;
use App\Persistence\RinopartsDatabasePersistenceAdapter;
use App\Product\Codes\ProductCodesChangedEvent;
use App\Product\Codes\ProductCodesChangedEventHandler;
use App\RelatedProducts\RelatedProductsStrategy;
use App\SEO\RinopartsAlternateLinksGenerator;
use App\SEO\Robots\DisallowImagesWithoutWatermarkRule;
use App\Stock\OrderCreatedEventHandler;
use App\User\Permissions\StatisticsBuxusUserPermission;
use App\WebUser\Event\SendEmailOnUserLoginEnabled;
use App\WebUser\Event\WebUserEnabledLoginEvent;
use App\WebUser\RinopartsWebUserAuthentication;
use App\WebUser\WebUser;
use App\WebUser\WebUserUpdatedEventHandler;
use Blade;
use Buxus\BuxusBladeComponents\Forms\Number;
use Buxus\Core\Menu\BuxusMenuManager;
use Buxus\Eshop\Contracts\PaymentTypeFactory;
use Buxus\Eshop\Event\CartAddProductEvent;
use Buxus\Eshop\Event\CartChangeProductAmountEvent;
use Buxus\Eshop\Event\CartPurchaseFinishedEvent;
use Buxus\Eshop\Event\CartRemoveProductEvent;
use Buxus\Eshop\Event\OrderCreatedEvent;
use Buxus\Eshop\FreeDelivery\Event\CartAmountChangeEvent;
use Buxus\Eshop\Item\Delivery\DeliveryTypeFactory;
use Buxus\Event\BuxusEvent;
use Buxus\Event\PagePreSubmitEvent;
use Buxus\Module\BuxusModule;
use Buxus\Site\Event\SiteSwitchedEvent;
use Buxus\User\UserManager;
use Buxus\WebUser\Event\WebUserLoginEvent;
use Buxus\WebUser\Event\WebUserUpdateEvent;
use Illuminate\Container\Container;
use Illuminate\Pagination\Paginator;

class AppModule extends BuxusModule
{
    public function register()
    {
        error_reporting(E_ALL - E_NOTICE - E_STRICT - E_DEPRECATED);

        // set custom eshop classes
        $this->app->bind('buxus.eshop.checkout-object', Checkout::class);

        $this->app->bind('buxus.eshop.cart-object', ShoppingCart::class);
        $this->app->bind('buxus.eshop.command-processor-object', CartCommandProcessor::class);

        $this->app->bind('buxus.eshop.order-item-object', RinopartsOrderItem::class);
        $this->app->bind('buxus.eshop.order-object', RinopartsOrder::class);

        $this->app->bind('buxus:authentication:mailinglist-form', RinopartsMailinglistForm::class);
        $this->app->bind('buxus:authentication:registration-form', RegistrationForm::class);
        $this->app->bind('buxus:authentication:lost-password-form', ExtendedLostPasswordForm::class);
        $this->app->bind('buxus:authentication:reset-password-form', ExtendedResetPasswordForm::class);
        $this->app->bind('buxus.webuser.factory', ExtendedWebUserFactory::class);
        $this->app->bind('buxus:authentication:change-password-form', RinopartsChangePasswordForm::class);
        $this->app->bind('buxus.webuser.authentication', RinopartsWebUserAuthentication::class, true);

        $this->app->bind('buxus.page-factory', AppPageFactory::class, true);
        $this->app->bind('buxus.webuser.webuser-object', WebUser::class);

        $this->app->bind('buxus.eshop.delivery-factory', \Buxus\Eshop\FreeDelivery\Item\Delivery\FreeDeliveryTypeFactory::class, true);
        $this->app->bind('buxus:free-delivery:manager', RinopartsFreeDeliveryManager::class, true);
        $this->app->bind('buxus.eshop.default-delivery-type', RinopartsGenericDeliveryType::class);
        $this->app->bind('buxus.eshop.default-payment-type', RinopartsGenericPaymentType::class);
        $this->app->bind('buxus:eshop:invoice-generator:order_summary', ExtendedPdfInvoiceGenerator::class);
        $this->app->bind('invoice-factory', InvoiceFactory::class);
        $this->app->bind('invoice-manager', InvoiceManager::class);

        $this->app->bind('buxus.eshop.shopping-cart-authentication', ExtendedCartAuthentication::class);

        $this->app->bind('buxus.persistence', static function (Container $app) {
            return new RinopartsPersistence($app, $app->make(RinopartsDatabasePersistenceAdapter::class));
        }, true);

        $this->app->bind('buxus:authentication:edit-profile-form', EditProfileForm::class);
        $this->app->bind(Number::class, \App\BuxusBladeComponents\Number::class);
        $this->app->bind('buxus:seo:alternate-links-generator', RinopartsAlternateLinksGenerator::class);

        $this->app->bind('buxus:related-products:strategy', RelatedProductsStrategy::class);

        $this->app->bind('buxus:email:base-email-object', ExtendedBaseEmail::class);

        $this->app->resolving(PaymentTypeFactory::class, static function (PaymentTypeFactory $factory) {
            $factory->registerType(CashOnDeliveryPaymentType::TAG, CashOnDeliveryPaymentType::class);
            $factory->registerType(InvoicePaymentType::TAG, InvoicePaymentType::class);
            $factory->registerType(AdvancePaymentType::TAG, AdvancePaymentType::class);
        });

        $this->app->resolving(DeliveryTypeFactory::class, static function (DeliveryTypeFactory $factory) {
            $factory->registerType(CarrierDeliveryType::TAG, CarrierDeliveryType::class);
            $factory->registerType(MMTransportDeliveryType::TAG, MMTransportDeliveryType::class);
        });

        $this->app->bind('buxus:contact-form:form', function () {
            return new ContactForm(config('contact-form.use_captcha'));
        });

        $this->app->bind('buxus.eshop.active-cart', function () {
            return ShoppingCartFactory::resolveActiveCart();
        });

        $this->app->tag(StatisticsBuxusUserPermission::class, [
            UserManager::PERMISSION_CONTAINER_TAG,
        ]);

        $this->app->tag(DisallowImagesWithoutWatermarkRule::class, [DisallowImagesWithoutWatermarkRule::SERVICE_PROVIDER_TAG]);

        $this->app->tag(
            ExtendedFormMenu::class,
            [BuxusMenuManager::MENU_PROVIDER_TAG]
        );

        $this->registerTools();

        Blade::directive('clientZoneTitle', function () {
            return '<?= (new Buxus\View\View())->clientZoneTitle() ?>';
        });

        Paginator::useBootstrap();
    }

    protected function registerTools()
    {
        $this->app->resolving(BuxusMenuManager::class, function () {
            if (\BuxusUserManager::hasPermission(StatisticsBuxusUserPermission::TAG)) {
                $this->tool('statistics', 'show', null, 'Štatistiky', 'Štatistiky');
                $this->tool('extended-statistics', 'show', null, 'Rozšírené štatistiky', 'Štatistiky');

                $this->tool('sales-statistics', 'show', null, 'Štatistiky predajcov', 'Štatistiky');
                $this->tool('slow-moving-stock-statistics', 'show', null, 'Ležiaky', 'Štatistiky');
            }
        });

        $this->tool('invoice-tool', 'show', null, 'Aktualizácia faktúr', 'Faktúry');

        $this->tool('margin-tool', 'show', null, 'Marža', 'Marža');
        $this->tool('margin-tool-small-db', 'show', null, 'Marža - IVECO malá DB', 'Marža');
        $this->tool('margin-tool-big-db', 'show', null, 'Marža - IVECO veľká DB', 'Marža');
        $this->tool('margin-tool-customers', 'show', null, 'Koeficienty marže', 'Marža');

        $this->tool('alternative-producers-prices-import', 'show', null, 'Import cenníku výrobcov', 'Import');
        $this->tool('iveco-purchase-prices-import', 'show', null, 'Import IVECO originál nákupné ceny', 'Import');
        $this->tool('iveco-big-db-import', 'show', null, 'Import IVECO originál veľká DB', 'Import');

        $this->tool('iveco-stock-prices', 'prices', null, 'Import IVECO Stock ceny', 'Iveco Stock');
        $this->tool('iveco-stock-prices-margin', 'margin', null, 'Marža IVECO Stock', 'Iveco Stock');

        $this->tool('augustin-group-import', 'show', null, 'Import - Augustin Group', 'Augustin Group');
        $this->tool('augustin-group-margin', 'margin', null, 'Marža - Augustin Group', 'Augustin Group');

        $this->tool('augustin-group-margin-bulky-parts', 'margin-bulky-parts', null, 'Prirážka - nadrozmery - Augustin Group', 'Augustin Group');

        $this->tool('mec-diesel-import', 'show', null, 'Import - Mec-Diesel', 'Mec-Diesel');
        $this->tool('mec-diesel-margin', 'margin', null, 'Marža - Mec-Diesel', 'Mec-Diesel');

        $this->tool('nrf-import', 'show', null, 'Import - NRF', 'NRF');
        $this->tool('nrf-margin', 'margin', null, 'Marža - NRF', 'NRF');

        $this->tool('febi-import', 'show', null, 'Import - FEBI Bilstein', 'FEBI Bilstein');
        $this->tool('febi-margin', 'margin', null, 'Marža - FEBI Bilstein', 'FEBI Bilstein');

        $this->tool('ms-import', 'show', null, 'Import - MS Motorservice', 'MS Motorservice');
        $this->tool('ms-margin', 'margin', null, 'Marža - MS Motorservice', 'MS Motorservice');
        $this->tool('ms-import-availability', 'availability', null, 'Dostupnosť - MS Motorservice', 'MS Motorservice');

        $this->tool('eminia-import', 'show', null, 'Import - Eminia', 'Eminia');
        $this->tool('eminia-margin', 'margin', null, 'Marža - Eminia', 'Eminia');

        $this->tool('casco-import', 'show', null, 'Import - CASCO', 'CASCO');
        $this->tool('casco-margin', 'margin', null, 'Marža - CASCO', 'CASCO');

        $this->tool('special-turbo-import', 'show', null, 'Import - SPECIAL TURBO', 'SPECIAL TURBO');
        $this->tool('special-turbo-margin', 'margin', null, 'Marža - SPECIAL TURBO', 'SPECIAL TURBO');

        $this->tool('martex-import', 'show', null, 'Import - MARTEX', 'MARTEX');
        $this->tool('martex-margin', 'margin', null, 'Marža - MARTEX', 'MARTEX');

        $this->tool('sabo-import', 'show', null, 'Import - SABO', 'SABO');
        $this->tool('sabo-margin', 'margin', null, 'Marža - SABO', 'SABO');
        $this->tool('sabo-availability', 'availability', null, 'Dostupnosť - SABO', 'SABO');

        $this->tool('remante-import', 'show', null, 'Import - Remante', 'Remante');
        $this->tool('remante-margin', 'margin', null, 'Marža - Remante', 'Remante');

        $this->tool('oe-germany-import', 'show', null, 'Import - OE Germany', 'OE Germany');
        $this->tool('oe-germany-margin', 'margin', null, 'Marža - OE Germany', 'OE Germany');

        $this->tool('meat-doria-import', 'show', null, 'Import - Meat Doria', 'Meat Doria');
        $this->tool('meat-doria-margin', 'margin', null, 'Marža - Meat Doria', 'Meat Doria');

        $this->tool('abakus-import', 'show', null, 'Import - Abakus', 'Abakus');
        $this->tool('abakus-margin', 'margin', null, 'Marža - Abakus', 'Abakus');

        $this->tool('weight-import', 'show', null, 'Import váh a rozmerov', 'weight-import');

        $this->tool('cei-import-availability', 'availability', null, 'Dostupnosť - CEI', 'CEI');

    }

    public function boot()
    {
        BuxusEvent::listen(SiteSwitchedEvent::class, AppSiteSwitchedEventHandler::class);
        BuxusEvent::listen(WebUserUpdateEvent::class, WebUserUpdatedEventHandler::class);
        BuxusEvent::listen(WebUserEnabledLoginEvent::class, SendEmailOnUserLoginEnabled::class);

        BuxusEvent::listen(OrderCreatedEvent::class, OrderCreatedEventHandler::class);
        BuxusEvent::listen(OrderCreatedEvent::class, SendEmailOnOrderCreated::class);
        BuxusEvent::listen(OrderCreatedEvent::class, CartChangeEventHandler::class);
        BuxusEvent::listen(OrderCreatedEvent::class, AddSuperuserOnOrderCreated::class);

        BuxusEvent::listen(CartRemoveProductEvent::class, CartChangeEventHandler::class);
        BuxusEvent::listen(CartAddProductEvent::class, CartChangeEventHandler::class);
        BuxusEvent::listen(CartAmountChangeEvent::class, CartChangeEventHandler::class);
        BuxusEvent::listen(CartChangeProductAmountEvent::class, CartChangeEventHandler::class);
        BuxusEvent::listen(CartPurchaseFinishedEvent::class, CartChangeEventHandler::class);
        BuxusEvent::listen(CartRemoveEvent::class, CartChangeEventHandler::class);

        BuxusEvent::listen(WebUserSearchEvent::class, WebUserSearchEventHandler::class);
        BuxusEvent::listen(WebUserLoginEvent::class, AddNotificationOnLogin::class);

        BuxusEvent::listen(FormUpdatedEvent::class, FormUpdatedEventHandler::class);

        BuxusEvent::listen(ProductCodesChangedEvent::class, ProductCodesChangedEventHandler::class);

        BuxusEvent::listen(ActionPriceChangedEvent::class, ActionPriceChangedEventHandler::class);

        BuxusEvent::listen(ProductImpressionEvent::class, ProductImpressionEventHandler::class);
        BuxusEvent::listen(ProductSearchEmptyResultsEvent::class, ProductSearchEmptyResultsEventHandler::class);

        BuxusEvent::listen(PagePreSubmitEvent::class, GroupedProductsHandler::class);

        \Widget::addWidgetNamespace('App\Widget');
    }
}
