<?php

namespace App\Notifier;

use App;
use App\Mail\TooManySearchNotification;
use App\Models\NotificationLog;
use Buxus\WebUser\Contracts\WebUser;
use Carbon\Carbon;
use Mail;

class TooManySearchesNotifier
{
    public function notify(WebUser $user, $searches)
    {
        $notificationType = $this->getNotificationType($searches);

        if (!$notificationType) {
            return;
        }

        if (!$this->shouldSendNotification($user, $notificationType)) {
            return;
        }

        $searches = [
            $notificationType => $searches,
        ];

        $mail = new TooManySearchNotification([
            $notificationType => $searches
        ]);

        if (App::environment('live')) {
            Mail::to(['<EMAIL>'])
                ->send($mail);
        }

        $this->logNotification($user, $notificationType);
    }

    protected function getNotificationType($searches)
    {
        $count = $searches->searches;

        if ($count > 40 && $count <= 80) {
            return NotificationLog::NOTIFICATION_TYPE_40;
        }

        if ($count > 80 && $count <= 120) {
            return NotificationLog::NOTIFICATION_TYPE_80;
        }

        if ($count > 120) {
            return NotificationLog::NOTIFICATION_TYPE_120;
        }

        return false;
    }

    protected function shouldSendNotification(WebUser $user, int $notificationType)
    {
        return !NotificationLog::where('webuser_id', $user->getUserId())
            ->where('notification_type', $notificationType)
            ->whereDate('notification_time', Carbon::now()->toDateString())
            ->exists();
    }

    protected function logNotification(WebUser $user, int $notificationType)
    {
        NotificationLog::create([
            'webuser_id' => $user->getUserId(),
            'notification_type' => $notificationType,
            'notification_time' => Carbon::now(),
        ]);
    }
}