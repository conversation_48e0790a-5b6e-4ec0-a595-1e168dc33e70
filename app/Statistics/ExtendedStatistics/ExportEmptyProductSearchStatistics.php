<?php

namespace App\Statistics\ExtendedStatistics;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ExportEmptyProductSearchStatistics implements FromCollection, WithHeadings
{
    protected $collection;

    public function __construct(Collection $collection)
    {
        $this->collection = $collection;
    }

    public function collection(): Collection
    {
        return $this->collection->map(function ($log) {
            return collect($log)->only(['search_term', 'sum']);
        });
    }

    public function headings(): array
    {
        return [
            'Výraz',
            'Počet',
        ];
    }
}
