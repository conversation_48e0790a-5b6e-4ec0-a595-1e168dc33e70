<?php

namespace App\Statistics;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ExportTermStatistics implements FromCollection, WithHeadings
{
    protected $collection;
    protected $dateFrom;
    protected $dateTo;

    public function __construct(Collection $collection, $dateFrom, $dateTo)
    {
        $this->collection = $collection;
        $this->dateFrom = $dateFrom;
        $this->dateTo = $dateTo;
    }

    public function collection(): Collection
    {
        return $this->collection->map(function ($log) {
            return collect($log)->only(['search_term', 'count']);
        });
    }

    public function headings(): array
    {
        return [
            'Vyhľadávaný výraz',
            'Počet',
            $this->dateFrom . ' - ' . $this->dateTo
        ];
    }
}
