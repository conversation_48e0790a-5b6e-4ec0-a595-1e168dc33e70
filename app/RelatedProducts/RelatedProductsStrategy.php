<?php

namespace App\RelatedProducts;

use Buxus\Eshop\Product\Product;
use Buxus\Eshop\RelatedProducts\RelatedStrategyInterface;
use Buxus\Util\PropertyID;
use Illuminate\Support\Collection;

class RelatedProductsStrategy implements RelatedStrategyInterface
{
    public function getRelatedProductList(Product $product, $limit = null): Collection
    {
        /** @var \App\Eshop\Product $product */
        return $product->getRelatedProducts($limit);
    }
}