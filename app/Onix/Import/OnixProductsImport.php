<?php

namespace App\Onix\Import;

use App\Eshop\Product\ProductExternalResources;
use App\Models\StockStateLog;
use App\Onix\OnixNotifier;
use App\OnixLib\Libs\Reservations;
use App\OnixLib\Loggers\OnixProductsImportLogger;
use App\Product\CleanupManager;
use App\Product\Codes\ProductCodesChangedEvent;
use App\Warehouse\Warehouse;
use Buxus\Ciselniky\GenericCiselnik;
use Buxus\Core\Constants;
use Buxus\Page\PageInterface;
use Buxus\Util\PageIds;
use Buxus\Util\PageTypeID;
use Buxus\Util\PropertyID;
use Buxus\Util\PropertyTag;
use BuxusEvent;
use Illuminate\Support\Facades\DB;

class OnixProductsImport extends \App\OnixLib\Import\OnixProductsImport
{

    protected $categories_nezaradene_cache;

    protected $buxus_categories_used;


    /**
     * OnixProductsImpor constructor.
     * @param OnixProductsImportLogger $logger
     */
    public function __construct(OnixProductsImportLogger $logger, $hourly_import = false)
    {
        parent::__construct($logger, $hourly_import);

        $this->categories_nezaradene_cache = null;
        $this->onix_buxus_categories_map = [];
        $this->buxus_categories_map_states = [];
        $this->onix_buxus_categories_map[0] = PageIds::getEshopCatalog();
        $this->buxus_categories_map_states[PageIds::getEshopCatalog()] = Constants::C_active_page_state_id;
        $this->buxus_categories_used = [];
        $this->buxus_categories_used[PageIds::getEshopCatalog()] = true;
    }

    protected function initBeforeLoop()
    {
        // load categories
        $this->loadCategories();
        OnixNotifier::clearGroupsLinksMessages();
    }

    public function loadCategories(): void
    {
        $categories = \PageFactory::builder()
            ->wherePageType([PageTypeID::ESHOP_SUBCATEGORY_ID(), PageTypeID::ESHOP_CATEGORY_ID()])
            ->wherePropertyValue(PropertyID::ONIX_ID_RECORD_ID(), '>', 0)
            ->get();
        foreach ($categories as $category) {
            $this->onix_buxus_categories_map[(int)$category->getValue(PropertyTag::ONIX_ID_RECORD_TAG())] = $category->getPageId();
            $this->buxus_categories_map_states[$category->getPageId()] = $category->getPageStateId();
        }
    }

    protected function initInLoop($item)
    {
        $this->updateParentPages($item);
        $this->updateCategorization(null, $item);
    }

    protected function isImportable($onix_item)
    {
        if (!parent::isImportable($onix_item)) {
            return false;
        }
        if (substr($onix_item->Ns_Number, 0, 1) === 'D') {
            return false;
        }

        $custom_columns = $this->parseCustomColumns($onix_item);
        if (!isset($custom_columns['Vyrobca']) || !strlen($custom_columns['Vyrobca'])) {
            return false;
        }
        $ciselnik = \Buxus\Ciselniky\Facades\Ciselniky::get('producer');
        /** @var GenericCiselnik $ciselnik */
        $producer_value = $ciselnik->getValueByName($custom_columns['Vyrobca']);
        if (!$producer_value || !$producer_value->getId()) {
            return false;
        }


        return true;
    }

    protected function getPages(array $pageIds): array
    {
        $pages = CleanupManager::removeProductsPairing($pageIds, $this->logger);
        if ($pages === null) {
            return [];
        }
        return $pages;
    }

    protected function findPagesByCode($item): array
    {
        $codes = $this->getCodes($item);

        $custom_columns = $this->parseCustomColumns($item);
        $ciselnik = \Buxus\Ciselniky\Facades\Ciselniky::get('producer');
        /** @var GenericCiselnik $ciselnik */
        $producer_value = $ciselnik->getValueByName($custom_columns['Vyrobca']);
        if (!$producer_value || !$producer_value->getId()) {
            return [];
        }

        $producer_id = $producer_value->getId();
        $page = \PageFactory::builder()
            ->wherePropertyValue(PropertyID::IVECO_BIG_DB_IMPORT_CODE_ID(), '=', $codes['main_code'])
            ->wherePropertyValue(PropertyID::ESHOP_ROLLER_PRODUCER_ID(), '=', $producer_id)
            ->whereEmptyPropertyValue(PropertyID::ONIX_NS_NUMBER_ID())
            ->first();
        return [$page];
    }


    protected function getDefaultCategoryId($item)
    {
        $this->logItem($item, 'getDefaultCategoryId');
        $defaultGroup = (int)$item->Id_Stock_Items_Group_Default;
        if ($defaultGroup > 0) {
            if (isset($this->onix_buxus_categories_map[$defaultGroup])) {
                $this->logItem($item, 'getDefaultCategoryId return Id_Stock_Items_Group_Default');
                return $this->onix_buxus_categories_map[$defaultGroup];
            }
        }

        $nezaradenePageId = PageIds::getNezaradene();
        $nezaradenePage = \PageFactory::get($nezaradenePageId);
        if (!$nezaradenePage) {
            throw new \Exception('Parent page don\'t exists. (' . $nezaradenePageId . ')');
        }

        // load categories into cache
        if ($this->categories_nezaradene_cache === null) {
            $this->categories_nezaradene_cache = [];
            $categories = \PageFactory::builder()
                ->whereParent($nezaradenePageId)
                ->wherePageType(PageTypeID::FOLDER_ID())
                ->get();
            if (count($categories)) {
                foreach ($categories as $category) {
                    $this->categories_nezaradene_cache[$category->getPageId()] = $category->getPageName();
                }
            }
        }

        $categoryName = $this->generateShortCategoryName($item->Name);
        $this->logItem($item, 'short category name: ' . $item->Name . ' -> ' . $categoryName);
        if (strlen($categoryName)) {
            $category_id = array_search($categoryName, $this->categories_nezaradene_cache);
            if ($category_id === false) {
                $this->logger->info('Create \'nezaradene\' category ' . $categoryName);
                $category = \PageFactory::create($nezaradenePageId, PageTypeID::FOLDER_ID());
                $category->setPageStateId(1);
                $category->setPageName($categoryName);
                $category->save();
                $category_id = $category->getPageId();
                $this->categories_nezaradene_cache[$category_id] = $categoryName;
            }
        } else {
            $category_id = $nezaradenePageId;
        }

        return $category_id;
    }


    protected function parseCustomColumns($item)
    {
        $lng_map = config('onix.lng_map');
        $custom_columns = [];
        if (is_array($item->CustomColumns)) {
            foreach ($item->CustomColumns as $customColumn) {
                $column_name = trim($customColumn->Name);
                $column_value = trim($customColumn->Value);
                $column_name = str_replace('STOCK_ITEMS_Z_RINO_00001_', '', $column_name);
                $column_name = str_replace('STOCK_ITEMS_U_STOI_00014_', '', $column_name);

                // language names
                if (is_array($lng_map)) {
                    if (substr($column_name, 0, 6) === 'Nazov_') {
                        $lng = substr($column_name, 6);
                        $lng = $lng_map[$lng];
                        if ($lng) {
                            $column_name = 'title_' . $lng;
                        }
                    }
                }

                $custom_columns[$column_name] = $column_value;
            }
        }
        return $custom_columns;
    }

    protected function afterLoop()
    {
        // set passive for unused categories
        foreach ($this->buxus_categories_map_states as $category_id => $state_id) {
            if ($state_id && $category_id) {
                if (!isset($this->buxus_categories_used[$category_id])) {
                    $category = \PageFactory::get($category_id);
                    $category->setPageStateId(Constants::C_passive_page_state_id);
                    $category->save();
                };
            }
        }
        OnixNotifier::sendGroupLinkMessages();

    }

    private function generateShortCategoryName($name)
    {
        $title_parts = explode(' ', \Buxus\Util\StringUtil::removeDiacritics($name));
        $categoryName = '';
        $i = 0;
        while (strlen(trim($categoryName)) < 2 && isset($title_parts[$i])) {
            if ($categoryName !== '') {
                $categoryName .= ' ';
            } else {
                if (is_numeric(trim($title_parts[$i], '() '))) {
                    $i++;
                    continue;
                }
                if ($title_parts === '-') {
                    $i++;
                    continue;
                }
            }
            $categoryName .= $title_parts[$i];
            $i++;
        }

        $categoryName = trim(strtolower(substr(\Buxus::removeDiacritic($categoryName), 0, 2)));

        return $categoryName;
    }

    protected function getPropertyValue($page, $tag)
    {
        if ($tag === PropertyTag::CATEGORIZATION_TAG()) {
            $categorization_ids = [];
            if (is_array($page->getValue(PropertyTag::CATEGORIZATION_TAG()))) {
                foreach ($page->getValue(PropertyTag::CATEGORIZATION_TAG()) as $categorization) {
                    $categorization_ids[] = (int)(string)$categorization;
                }
                sort($categorization_ids);
            }
            return implode(',', $categorization_ids);
        }

        return $page->getValue($tag);
    }


    protected function updateCustomProperties($page, $item)
    {
        $page->setValue(PropertyTag::ONIX_PRODUCT_CODE_TAG(), $item->Product_Code);

        $supplierCodes = explode(',', $item->Supplier_Codes);
        foreach ($supplierCodes as $k => $v) {
            $supplierCodes[$k] = trim($v);
        }

        if (!empty($supplierCodes) && !$page->getPageId()) {
            $page->save(); // generate pageId before set supplier codes, because GroupedProductsHandler need pageId for construct product object (if onix_supplier_codes is not empty)
        }
        $page->setValue(PropertyTag::ONIX_SUPPLIER_CODES_TAG(), implode(',', $supplierCodes));

        $codes = $this->getCodes($item);
        $main_code_prefix = '';
        if (isset($codes['main_code']) && strlen($codes['main_code'])) {
            $main_code_prefix = $codes['main_code'] . ' ';
        }
        $page->setValue('title', $main_code_prefix . $item->Name);

        $custom_columns = $this->parseCustomColumns($item);
        $ciselnik = \Buxus\Ciselniky\Facades\Ciselniky::get('producer');
        /** @var GenericCiselnik $ciselnik */
        $producer_value = $ciselnik->getValueByName($custom_columns['Vyrobca']);
        $page->setValue(PropertyTag::ESHOP_ROLLER_PRODUCER_TAG(), $producer_value->getId());


        $lng_map = config('onix.lng_map');
        // props
        $save_props = [
            PropertyTag::ONIX_LOKACIA_TAG() => $custom_columns['Lokacia'],
        ];
        if (is_array($lng_map)) {
            foreach ($lng_map as $lng) {
                if (isset($custom_columns['title_' . $lng])) {
                    $save_props['title_' . $lng] = $main_code_prefix . $custom_columns['title_' . $lng];
                }
            }
        }
        foreach ($save_props as $prop_name => $prop_value) {
            $page->setValue($prop_name, $prop_value);
        }

        $this->updateCategorization($page, $item);

        $this->updateCodes($page, $item);

    }

    protected function savePage($page)
    {
        $page->save();
        $event = new ProductCodesChangedEvent($page);
        BuxusEvent::fire($event);
    }


    private function updateCodes($page, $item)
    {
        $codes = $this->getCodes($item);
        $page->setValue(PropertyTag::ONIX_MAIN_CODE_TAG(), $codes['main_code']);
        $page->setValue(PropertyTag::ONIX_CODES_TAG(), implode(', ', $codes['codes']));
        $page->setValue(PropertyTag::ONIX_SEARCH_KEYWORDS_TAG(), implode(', ', $codes['keywords']));
        $page->setValue(PropertyTag::SUPPLIER_EAN_TAG(), $codes['supplier_ean']);

        $page->setValue(PropertyTag::ONIX_SUPPLIER_CODES_DATA_TAG(), json_encode($item->StockItemPartners));
    }

    protected function updateStockAndPrices($page, $item)
    {
        $stockCodes = config('onix.stock_codes');
        $stock_state = 2;
        $balance = [];
        $reservated = [];

        foreach ($stockCodes as $stockCode) {
            $balance[$stockCode] = 0;
            $reservated[$stockCode] = 0;
        }

        $price = 0;

        foreach ($item->StockItemBalance as $stock) {
            if (in_array($stock->StockCode, $stockCodes)) {
                $price = max($price, (float)$stock->StockPrice);
                $balance[$stock->StockCode] += (float)$stock->Balance;
                $reservated[$stock->StockCode] += (float)$stock->Reservated;
            }
        }

        if ($page->getPageId()) {
            foreach ($stockCodes as $stockCode) {
                $reservated[$stockCode] += Reservations::getReservedAmountForWarehouse($page->getPageId(), $stockCode, $this->getStartTimestamp());
            }
        }

        $totalBalance = array_sum($balance);

        if ($totalBalance <= 0) {
            $stock_state = 2;
        }

        if ($totalBalance > 0 || $price > 0) {
            $page->setValue(PropertyTag::ESHOP_EUR_PRICE_WITHOUT_VAT_TAG(), $price);
        }
        if ($totalBalance <= 0 && $page->getValue(PropertyTag::ESHOP_EUR_ACTION_PRICE_WITHOUT_VAT_TAG())) {
            $page->setValue(PropertyTag::ESHOP_EUR_ACTION_PRICE_WITHOUT_VAT_TAG(), null);
        }
        $page->setValue(PropertyTag::AVAILABILITY_TAG(), $stock_state);

        foreach ($stockCodes as $stockCode) {
            $page->setValue(Warehouse::getStockPropertyForWarehouse($stockCode), $balance[$stockCode]);
            $page->setValue(Warehouse::getReservationsPropertyForWarehouse($stockCode), $reservated[$stockCode]);
        }

        $page->setValue(PropertyTag::ONIX_STOCK_ITEM_BALANCE_JSON_DATA_TAG(), json_encode($item->StockItemBalance));

        if($page->getPageId()) {
            StockStateLog::logCurrentValueIfChanged($page->getPageId(), $totalBalance);
        }
    }

    protected function updateCategorization(?PageInterface $page, $item)
    {
        $set_parent_page_id = null;
        if ($page && $page->getPageId()) {
            $set_parent_page_id = $page->getParentPageId();
        }

        $categorizationPageIds = [];
        if (is_array($item->StockItemGroups) && count($item->StockItemGroups)) {
            foreach ($item->StockItemGroups as $group) {
                if (isset($this->onix_buxus_categories_map[$group->StockItems_IdRecord])) {
                    $categorizationPageIds[] = $this->onix_buxus_categories_map[$group->StockItems_IdRecord];
                }
            }
        }

        $defaultGroup = (int)$item->Id_Stock_Items_Group_Default;
        if ($defaultGroup > 0) {
            if (isset($this->onix_buxus_categories_map[$defaultGroup])) {
                $set_parent_page_id = $this->onix_buxus_categories_map[$defaultGroup];
            }
        }

        if (count($categorizationPageIds)) {
            $default_category_id = last($categorizationPageIds);
            if ($default_category_id) {
                $set_parent_page_id = $default_category_id;
            }
        }

        if (!$set_parent_page_id) {
            $set_parent_page_id = $this->getDefaultCategoryId($item);
            if (!$set_parent_page_id) {
                throw new \Exception('$set_parent_page_id is null ' . __LINE__);
            }
            $categorizationPageIds[] = $set_parent_page_id;
        }

        if (!$set_parent_page_id) {
            $set_parent_page_id = PageIds::getNezaradene();
            $categorizationPageIds[] = PageIds::getNezaradene();
        }


        if ($page) {
            $page->setParentPageId($set_parent_page_id);
        }

        $category = \PageFactory::get($set_parent_page_id);
        if ($category->getParentPageId() === PageIds::getNezaradene()) {
            $categorizationPageIds[] = PageIds::getNezaradene();
        }
        $categorizationPageIds = array_unique($categorizationPageIds);

        if ($page) {
            $page->setValue(PropertyTag::CATEGORIZATION_TAG(), $categorizationPageIds);
        }


        foreach ($categorizationPageIds as $category_id) {
            if (!isset($this->buxus_categories_map_states[$category_id]) || $this->buxus_categories_map_states[$category_id] != Constants::C_active_page_state_id) {
                $category = \PageFactory::get($category_id);
                $category->setPageStateId(Constants::C_active_page_state_id);
                $category->save();
                $this->buxus_categories_map_states[$category_id] = Constants::C_active_page_state_id;
            }
            $this->buxus_categories_used[$category_id] = true;
        }
    }

    private function updateParentPages($onixItem)
    {
        /**
         *  dd($onixItem->StockItemGroups);
         *     0 => {#440317
         * +"StockItems_IdRecord": 81214
         * +"Ns_Number": "000126"
         * +"Ns_Code": "SSK"
         * +"Name": "DAF"
         * +"IdParent": 0
         * }
         * 1 => {#440318
         * +"StockItems_IdRecord": 81217
         * +"Ns_Number": "000129"
         * +"Ns_Code": "SSK"
         * +"Name": "Podvozok"
         * +"IdParent": 81214
         * }
         */
        if (is_array($onixItem->StockItemGroups) && count($onixItem->StockItemGroups)) {
            $this->logItem($onixItem, 'updateParentPages');
            foreach ($onixItem->StockItemGroups as $group) {
                $onix_id = $group->StockItems_IdRecord;

                // find in cached map
                if (isset($this->onix_buxus_categories_map[$onix_id])) {
                    $this->logItem($onixItem, 'updateParentPages category found in cache [' . $onix_id . ' => ' . $this->onix_buxus_categories_map[$onix_id] . ']');
                    continue;
                }

                // find in buxus
                /** @var \Buxus\Page\PageInterface $category */
                $category = \PageFactory::builder()
                    ->wherePageType([
                        PageTypeID::ESHOP_CATEGORY_ID(),
                        PageTypeID::ESHOP_SUBCATEGORY_ID(),
                    ])
                    ->wherePropertyValue(PropertyID::ONIX_ID_RECORD_ID(), $onix_id)
                    ->first();
                //\PageFactory::get(101480)->delete(); exit;

                if ($category) {
                    if (!\PageFactory::get($category->getParentPageId())) {
                        throw new \Exception('parent of ' . $category->getPageId() . ' is null');
                    }
                    $this->onix_buxus_categories_map[$onix_id] = $category->getPageId();

                    $this->logItem($onixItem, 'updateParentPages category add to cache [' . $onix_id . ' => ' . $this->onix_buxus_categories_map[$onix_id] . ']');

                    // update parent_page_id, if needed
                    if ($this->onix_buxus_categories_map[$group->IdParent] != $category->getParentPageId()
                        || $category->getValue(PropertyTag::TITLE_TAG()) != $group->Name
                    ) {

                        $oldName = $category->getValue(PropertyTag::TITLE_TAG());
                        if (!$this->onix_buxus_categories_map[$group->IdParent]) {
                            throw new \Exception('$this->onix_buxus_categories_map[$group->IdParent] is null ' . __LINE__);
                        }
                        $category->setParentPageId($this->onix_buxus_categories_map[$group->IdParent]);
                        $category->setPageName($group->Name);
                        $category->setValue(PropertyTag::TITLE_TAG(), $group->Name);
                        $category->save();
                        $this->logger->info('Update category ' . $category->getPageId() . ' ' . $oldName . ' -> ' . $group->Name);
                        $this->buxus_categories_map_states[$category->getPageId()] = $category->getPageStateId();
                    }
                } else {
                    // create category
                    $parent_page_id = $this->onix_buxus_categories_map[$group->IdParent];
                    if (!$parent_page_id) {
                        throw new \Exception('$parent_page_id = null ' . __LINE__);
                    }
                    $category = \PageFactory::create($parent_page_id, $group->IdParent ? PageTypeID::ESHOP_SUBCATEGORY_ID() : PageTypeID::ESHOP_CATEGORY_ID());
                    $category->setPageStateId(Constants::C_passive_page_state_id);
                    $category->setPageName($group->Name);
                    $category->setValue(PropertyTag::TITLE_TAG(), $group->Name);
                    $category->setValue(PropertyTag::ONIX_ID_RECORD_TAG(), $onix_id);
                    $category->save();
                    $this->onix_buxus_categories_map[$onix_id] = $category->getPageId();
                    $this->buxus_categories_map_states[$category->getPageId()] = $category->getPageStateId();
                    $this->logger->info('Create category ' . $category->getPageId() . ' ' . $group->Name);
                    $this->logItem($onixItem, 'updateParentPages category created and added to cache [' . $onix_id . ' => ' . $this->onix_buxus_categories_map[$onix_id] . ']');
                }
            }
        }
    }

    /**
     * @param \Buxus\Page\PageInterface $page
     * @param array $oldValues
     * @param array $newValues
     * @return boolean
     */
    protected function isPageModified($page, array $oldValues, array $newValues)
    {
        if (!empty($page->getValue(PropertyTag::ONIX_SUPPLIER_CODES_TAG))) {
            // $this->logger->info('SUPPLIER: ' . $page->getValue(PropertyTag::ONIX_SUPPLIER_CODES_TAG));
            return true;
        }
        return parent::isPageModified($page, $oldValues, $newValues);
    }

    protected function getCodes($onixItem)
    {
        $main_code = '';
        $codes = [];
        $keywords = [];
        if (is_array($onixItem->StockItemCodes)) {
            foreach ($onixItem->StockItemCodes as $code) {
                if ($code->Type_Name == 'DODAVATELSKY EAN') {
                    $supplierEan = $code->Code;
                    continue;
                }
                $codes[] = $code->Code;
                if (ltrim($code->Code, '0') !== $code->Code) {
                    $keywords[] = ltrim($code->Code, '0');
                }
                if ($code->Is_Default === -1) {
                    $main_code = $code->Code;
                }
            }
        }

        return [
            'main_code' => $main_code,
            'codes' => $codes,
            'keywords' => $keywords,
            'supplier_ean' => $supplierEan ?? '',
        ];
    }

    public function beforeUpdatePage(PageInterface $page, $onix_item): void
    {

    }

}

