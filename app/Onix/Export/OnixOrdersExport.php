<?php

namespace App\Onix\Export;

use App\Country;
use App\Eshop\Delivery\CarrierDeliveryType;
use App\Eshop\Delivery\MMTransportDeliveryType;
use App\Eshop\OrderState;
use App\Eshop\Product\ProductExternalResources;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;
use Illuminate\Support\Facades\DB;

class OnixOrdersExport extends \App\OnixLib\Export\OnixOrdersExport
{
    static public function getOrderStatesForProcessing()
    {
        return [
            OrderState::STATE_NOVA,
            OrderState::STATE_RESEND_TO_ONIX,
            OrderState::STATE_WAITING_FOR_ONIX
        ];
    }

    static public function getOrderStateWaitingForOnix()
    {
        return OrderState::STATE_WAITING_FOR_ONIX;
    }

    static public function getOrderStateNew()
    {
        return OrderState::STATE_NOVA;
    }

    static public function getOrderStateResendToOnix()
    {
        return OrderState::STATE_RESEND_TO_ONIX;
    }


    public function getEmaiForOrder($order, $webUser)
    {
        $email = trim($order->getEmail());
        $invoice_email = trim($webUser->getCustomOption('email_invoice_address'));
        if (strlen($invoice_email) && $email != $invoice_email) {
            $email .= '; ' . $invoice_email;
        }
        return $email;
    }

    protected function customUpdate($buxusOrder, $onixPartnerId)
    {
        $orderDataUpdated = false;

        // find order with caka na dl
        $partner_order_ids = DB::table('tblShopOrderOptions')
            ->where('order_tag', '=', 'onix_partner_id')
            ->where('order_value', '=', $onixPartnerId)
            ->pluck('order_id');
        $dl_za_orders = DB::table('tblShopOrderOptions')
            ->where('order_tag', '=', 'onix_cakajuci_dl_za')
            ->whereIn('order_id', $partner_order_ids)
            ->where('order_value', '=', 1)
            ->count();
        $dl_ba_orders = DB::table('tblShopOrderOptions')
            ->where('order_tag', '=', 'onix_cakajuci_dl_ba')
            ->whereIn('order_id', $partner_order_ids)
            ->where('order_value', '=', 1)
            ->count();

        if ($dl_za_orders || $dl_ba_orders) {
            if ($dl_ba_orders) {
                $buxusOrder->setData('onix_cakajuci_dl_ba', 1);
            }
            if ($dl_za_orders) {
                $buxusOrder->setData('onix_cakajuci_dl_za', 1);
            }
            $orderDataUpdated = true;
        }

        if ($buxusOrder->getCurrency() === 'CZK') {
            $exchangeRate = (float)$buxusOrder->getData('exchange_rate');
            if (!$exchangeRate) {
                $eshopPropertiesPage = \PageFactory::get(PageIds::getEshopPropertiesSettings());
                $exchangeRate = (float)$eshopPropertiesPage->getValue(PropertyTag::EXCHANGE_RATE_CZ_TAG());
                $buxusOrder->setData('exchange_rate', $exchangeRate);
                $orderDataUpdated = true;
            }
        }

        return $orderDataUpdated;
    }

    protected function customUpdateData($order, $order_data)
    {
        if ($order->getCurrency() === 'CZK') {
            $exchangeRate = (float)$order->getData('exchange_rate');
            if (!$exchangeRate) {
                $eshopPropertiesPage = \PageFactory::get(PageIds::getEshopPropertiesSettings());
                $exchangeRate = (float)$eshopPropertiesPage->getValue(PropertyTag::EXCHANGE_RATE_CZ_TAG());
            }
            $order_data['Exchange_Rate'] = $exchangeRate;
        }
        return $order_data;
    }


    protected function getTransportTypeName($order, $webUser = null)
    {
        $transportTypeName = $order->getTransportTypeName();
        if ($order->getTransportTypeTag() == CarrierDeliveryType::TAG) {
            $transportTypeName = $order->getTransportTypeName();
            $subdeliveryTag = $order->getData(CarrierDeliveryType::SUBDELIVERY_TAG);
            if (!empty($subdeliveryTag)) {
                $transportTypeName = $subdeliveryTag;
                $subdeliveryPage = $order->getTransportType()->getSubdeliveryPage($subdeliveryTag);
                if ($subdeliveryPage) {
                    $transportTypeName = $subdeliveryPage->getValue(PropertyTag::TITLE_TAG());
                }
            }
        }
        if ($order->getTransportTypeTag() == MMTransportDeliveryType::TAG || $order->getTransportTypeTag() == 'zavolejsikuryra_carrier') {
            $transportTypeName = 'Kurýrska služba - ' . $webUser->getDeliveryTimeAsText();
        }
        return $transportTypeName;
    }
    protected function getCountryName($order)
    {
        return Country::getCountryName($order->getDeliveryCountry());
    }

    protected function getCustomColumns($order, $onixPartnerId)
    {
        $partner_order_ids = DB::table('tblShopOrderOptions')
            ->where('order_tag', '=', 'onix_partner_id')
            ->where('order_value', '=', $onixPartnerId)
            ->pluck('order_id');
        $dl_za_orders = DB::table('tblShopOrderOptions')
            ->where('order_tag', '=', 'onix_cakajuci_dl_za')
            ->whereIn('order_id', $partner_order_ids)
            ->where('order_value', '=', 1)
            ->count();
        $dl_ba_orders = DB::table('tblShopOrderOptions')
            ->where('order_tag', '=', 'onix_cakajuci_dl_ba')
            ->whereIn('order_id', $partner_order_ids)
            ->where('order_value', '=', 1)
            ->count();

        $customColumns = [
            [
                'Name' => 'Z_RINO_OB001_Cakajuci_DL_ZA',
                'Value' => $dl_za_orders > 0 ? -1 : 0
            ],
            [
                'Name' => 'Z_RINO_OB001_Cakajuci_DL_BA',
                'Value' => $dl_ba_orders > 0 ? -1 : 0
            ]
        ];

        if ($order->getCurrency() === 'CZK') {
            $exchangeRate = (float)$order->getData('exchange_rate');
            if (!$exchangeRate) {
                $eshopPropertiesPage = \PageFactory::get(PageIds::getEshopPropertiesSettings());
                $exchangeRate = (float)$eshopPropertiesPage->getValue(PropertyTag::EXCHANGE_RATE_CZ_TAG());
            }
            $customColumns['Exchange_Rate'] = $exchangeRate;
        }

        return $customColumns;
    }


}
