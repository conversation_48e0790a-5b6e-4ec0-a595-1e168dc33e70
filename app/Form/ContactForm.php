<?php

namespace App\Form;

use Buxus\Captcha\Form\Element\HoneypotFormElement;
use Zend_Filter_StringToLower;
use Zend_Filter_StringTrim;
use Zend_Filter_StripTags;
use Zend_Form_Element_Submit;
use Zend_Form_Element_Text;
use Zend_Form_Element_Textarea;
use Zend_Validate_EmailAddress;

class ContactForm extends \ContactForm\ContactForm
{
    protected function initFormElements()
    {
        // First name
        $firstName = new Zend_Form_Element_Text('first_name');
        $firstName->setLabel('Meno:');
        $firstName->setRequired(true);
        $firstName->addFilter(new Zend_Filter_StripTags());
        $firstName->addFilter(new Zend_Filter_StringTrim());
        $this->addElement($firstName);

        // Surname
        $surname = new Zend_Form_Element_Text('surname');
        $surname->setLabel('<PERSON><PERSON><PERSON><PERSON><PERSON>:');
        $surname->setRequired(true);
        $surname->addFilter(new Zend_Filter_StripTags());
        $surname->addFilter(new Zend_Filter_StringTrim());
        $this->addElement($surname);

        // Email
        $email = new Zend_Form_Element_Text('email');
        $email->setLabel('Email:');
        $email->setRequired(true);
        $email->addValidator(new Zend_Validate_EmailAddress());
        $email->addFilter(new Zend_Filter_StringToLower());
        $email->addFilter(new Zend_Filter_StripTags());
        $email->addFilter(new Zend_Filter_StringTrim());
        $this->addElement($email);

        // Text
        $text = new Zend_Form_Element_Textarea('text');
        $text->setLabel('Text:');
        $text->setRequired(true);
        $text->setAttrib('rows', 10);
        $text->setAttrib('cols', 50);
        $text->addFilter(new Zend_Filter_StripTags());
        $text->addFilter(new Zend_Filter_StringTrim());
        $this->addElement($text);

        $honeypot = new HoneypotFormElement('honeypot');
        $this->addElement($honeypot);

        if ($this->getUseCaptcha()) {
            $captcha = new RecaptchaElement('captcha');
            $captcha->setRecaptchaKey(config('contact-form.recaptcha_key'));
            $captcha->setRecaptchaSecret(config('contact-form.recaptcha_secret'));
            $captcha->setLabel('Overenie');
            $this->addElement($captcha);
        }

        // Submit
        $submit = new Zend_Form_Element_Submit($this->getFormTag());
        $submit->setLabel('Odoslať');
        $this->addElement($submit);
    }
}
