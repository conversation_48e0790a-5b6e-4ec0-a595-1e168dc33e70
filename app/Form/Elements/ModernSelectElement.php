<?php

namespace App\Form\Elements;

use FormBase\Element\SelectElement;

class ModernSelectElement extends SelectElement
{
    public function init()
    {
        $decorators = [];
        $decorators[] = array(
            array('CustomDiv' => 'ViewScript'),
            array(
                'viewScript' => 'cart/decorator/modern-select.phtml',
            )
        );

        $decorators[] = array(
            array('WrapperDiv' => 'HtmlTag'),
            array(
                'tag' => 'div',
                'class' => 'modern-input',
            )
        );

        $this->setDecorators($decorators);
    }
}