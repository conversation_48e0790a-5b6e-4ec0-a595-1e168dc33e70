<?php

namespace App\Form;

use Mpdf\Mpdf;
use Mpdf\Output\Destination;

class PdfGenerator
{
    protected $view;
    protected $data;

    public function __construct($view, $data)
    {
        $this->view = $view;
        $this->data = $data;
    }

    public function getView()
    {
        return $this->view;
    }

    public function getData()
    {
        return $this->data;
    }

    public function setView($view)
    {
        $this->view = $view;
    }

    public function setData($data)
    {
        $this->data = $data;
    }

    public function generate()
    {
        $mpdf = new Mpdf([
            'margin_left' => 7,
            'margin_right' => 7,
            'margin_top' => 7,
            'margin_bottom' => 7,
            'tempDir' => storage_path('/forms/tmp/'),
        ]);

        $html = view($this->getView(), [
            'data' => $this->getData(),
        ])->render();

        $mpdf->WriteHTML($html);

        return $mpdf->Output('', Destination::STRING_RETURN);
    }
}
