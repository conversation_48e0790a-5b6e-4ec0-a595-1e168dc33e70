<?php

namespace App\Page;

use App\Eshop\Price\Event\ActionPriceChangedEvent;
use Buxus\Error\ErrorReporter;
use Buxus\Page\PageInterface;
use Buxus\Site\Page\SiteAwarePage;
use Buxus\TemplateFunctions;
use Buxus\Util\PageIds;
use Buxus\Util\PageTypeID;
use Buxus\Util\PropertyTag;
use BuxusEvent;
use Exception;
use GoogleSitemap\Page\SitemapAwarePageInterface;

class BasicPage extends SiteAwarePage implements SitemapAwarePageInterface
{
    public function isValidForSitemap()
    {
        $noIndexIds = [PageIds::getNezaradene() || PageIds::getIvecoOriginalBigDb()];

        if ($this->getPageTypeId() == PageTypeID::ESHOP_CATEGORY_ID() ||
            $this->getPageTypeId() == PageTypeID::ESHOP_SUBCATEGORY_ID()) {
            if (in_array($this->getPageId(), $noIndexIds) &&
                (TemplateFunctions::isDescendantOf($this->getPageId(), PageIds::getNezaradene()) ||
                    TemplateFunctions::isDescendantOf($this->getPageId(), PageIds::getIvecoOriginalBigDb()))) {
                return false;
            }
        }

        if ($this->getPageTypeId() == PageTypeID::ESHOP_PRODUCT_ID()) {
            return \ProductFactory::get($this->getPageId())->isOrderable();
        }

        return true;
    }

    public function setValue($property_tag, $property_value)
    {
        try {
            $actionPricePropertyTag = PropertyTag::ESHOP_EUR_ACTION_PRICE_WITHOUT_VAT_TAG();
            if ($this->hasProperty($actionPricePropertyTag)
                && $property_tag == $actionPricePropertyTag) {
                $event = new ActionPriceChangedEvent(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS), $this->getPageId(), $this->getValue($actionPricePropertyTag), $property_value);
                BuxusEvent::fire($event);
            }
        } catch (Exception $e) {
            ErrorReporter::reportSilent($e);
        }

        parent::setValue($property_tag, $property_value);
    }

    public function duplicate()
    {
        $page = clone $this;
        $page->setPageId(null);
        $page->setPageTag(null);
        $page->setParentPageId(null);
        $page->setNewPage();
        $page->original_parent_page_id = false;

        return $page;
    }

    public function isTheSameAsPropertyValuesOfPage(PageInterface $page): bool
    {
        return empty(array_diff($this->getPropertyValuesAsArray(), $page->getPropertyValuesAsArray()));
    }

    protected function getPropertyValuesAsArray(): array
    {
        $propertyValues = $this->getPropertyValues();
        $propertyValues = array_map(function ($value) {
            return [$value->getProperty()->getTag() => $value->getValue()];
        }, $propertyValues);

        return $propertyValues;
    }
}
