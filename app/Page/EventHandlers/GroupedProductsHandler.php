<?php

namespace App\Page\EventHandlers;

use App\Onix\OnixNotifier;
use Buxus\Ciselniky\Facades\Ciselniky;
use Buxus\Core\Constants;
use Buxus\Event\PagePreSubmitEvent;
use Buxus\Page\PageInterface;
use Buxus\Util\PageIds;
use Buxus\Util\PageTypeID;
use Buxus\Util\PropertyID;
use Buxus\Util\PropertyTag;
use FullTextSearch\Facades\FullTextSearch;
use Illuminate\Support\Facades\DB;

class GroupedProductsHandler
{

    public const ONIX_SUPPLIER_CODE_INDEX = 0;
    public const ONIX_SUPPLIER_NAME_INDEX = 2;

    protected $savedPageId;

    public function handle(PagePreSubmitEvent $event)
    {
        $page = $event->getPage();

        $this->processPage($page);
    }

    protected function shouldBePaired(int $pageId, PageInterface $page, ?string $onixCode): bool
    {
        $childPage = \PageFactory::get($pageId);

        $product = \ProductFactory::get($page->getPageId());
        $childProduct = \ProductFactory::get($pageId);

        if ($product->isOnixProduct() && $childProduct->isOnixProduct()) {
            return false;
        }

        $supplierId = $childPage->getValue(PropertyTag::SUPPLIER_TAG());

        if (empty($supplierId)) {
            return false;
        }

        $supplierValue = Ciselniky::get('product_catalog.supplier')->getValueById($supplierId);

        $supplierPage = $supplierValue->getPage();

        if (empty($supplierPage)) {
            return false;
        }

        $supplierOnixNames = $supplierPage->getValue(PropertyTag::ONIX_SUPPLIER_NAME_TAG());
        $supplierOnixNames = explode(',', $supplierOnixNames);
        $supplierOnixNames = array_map('trim', $supplierOnixNames);

        $supplierData = $page->getValue(PropertyTag::ONIX_SUPPLIER_CODES_DATA_TAG());
        $supplierData = json_decode($supplierData);

        if (empty($supplierData)) {
            return false;
        }

        $supplierData = json_decode(json_encode($supplierData), true);

        foreach ($supplierData as $supplier) {
            $supplier = array_values($supplier);
            $supplierCode = $supplier[self::ONIX_SUPPLIER_CODE_INDEX];
            $supplierName = $supplier[self::ONIX_SUPPLIER_NAME_INDEX];

            if ($supplierCode == $onixCode && in_array($supplierName, $supplierOnixNames)) {
                return true;
            }

            if ($supplierName == 'C.E.I.CONSTRUZIONE EMILIANA INGRANAGGI s.p.a') {
                $supplierCodeSecondary = str_replace('.', '', $supplierCode);

                if ($supplierCodeSecondary == $onixCode && in_array($supplierName, $supplierOnixNames)) {
                    return true;
                }
            }
        }

        return false;
    }

    private function copyProperties($pageIds, $parentPage)
    {
        if (empty($parentPage->getValueWithoutFallback(PropertyTag::ONIX_ID_RECORD_TAG()))) {
            return;
        }

        foreach ($pageIds as $pageId) {
            $page = \PageFactory::get($pageId);

            if ($page->getValue(PropertyTag::SUPPLIER_TAG()) == PageIds::getFebiBilsteinSupplier()) {
                $this->resolveFebiBilsteinImages($parentPage, $page);
            } else {
                if ($this->savedPageId == $parentPage->getPageId()) {
                    $this->resolveImages($parentPage, $page);
                }
            }

            $page->setValue(PropertyTag::TITLE_TAG(), $parentPage->getValueWithoutFallback(PropertyTag::TITLE_TAG()));
            $page->setValue(PropertyTag::TITLE_CZ_TAG(), $parentPage->getValueWithoutFallback(PropertyTag::TITLE_CZ_TAG()));
            $page->setValue(PropertyTag::TITLE_EN_TAG(), $parentPage->getValueWithoutFallback(PropertyTag::TITLE_EN_TAG()));
            $page->setValue(PropertyTag::ESHOP_ROLLER_PRODUCER_TAG(), $parentPage->getValueWithoutFallback(PropertyTag::ESHOP_ROLLER_PRODUCER_TAG()));
            $page->setValue(PropertyTag::CATEGORIZATION_TAG(), $parentPage->getValueWithoutFallback(PropertyTag::CATEGORIZATION_TAG()));

            $page->setValue(PropertyTag::ONIX_CODES_TAG(), $parentPage->getValueWithoutFallback(PropertyTag::ONIX_CODES_TAG()));

            $page->save(false);
            FullTextSearch::indexPage($page->getPageId());

            if (!empty($page->getValue(PropertyTag::SALE_DENIED_COUNTRIES_TAG()))) {
                $parentPage->setValue(PropertyTag::SALE_DENIED_COUNTRIES_TAG(), $page->getValueWithoutFallback(PropertyTag::SALE_DENIED_COUNTRIES_TAG()));
                $parentPage->save(false);
            }
        }
    }

    protected function processPage(PageInterface $page)
    {
        $this->savedPageId = $page->getPageId();

        if (!$page->isActive()) { // release grouped products in passive state.
            $page->setValue(PropertyTag::GROUPED_PRODUCTS_TAG(), []);
            return;
        }

        if (empty($page->getValue(PropertyTag::ONIX_ID_RECORD_TAG()))) { // runs only for real Onix cards
            $this->processSupplierProduct($page);
            return;
        }

        $supplierCodes = $page->getValue(PropertyTag::ONIX_SUPPLIER_CODES_TAG());
        $onixSupplierCodes = $this->getOnixSupplierCodes($page);


        if (!empty($supplierCodes)) {
            $supplierCodes = array_map('trim', explode(',', $supplierCodes));
            $supplierCodes = array_unique(array_merge($supplierCodes, $onixSupplierCodes));
            $secondarySupplierCodes = array_map(function ($code) {
                return str_replace('.', '', $code);
            }, $supplierCodes);

            $records = \DB::table('tblPagePropertyValues')
                ->whereIn('property_id', config('product.supplier_code_property_ids'))
                ->whereIn('property_value', array_merge($supplierCodes, $secondarySupplierCodes))
                ->get();

            $combinations = [];
            foreach ($records as $record) {
                $combinations[$record->page_id] = $record->property_value;
            }

            if (isset($combinations[$page->getPageId()])) {
                unset($combinations[$page->getPageId()]);
            }

            foreach ($combinations as $pageId => $code) {
                if (!$this->shouldBePaired($pageId, $page, $code)) {
                    unset($combinations[$pageId]);
                }
            }

            $pageIds = array_keys($combinations);

            $page->setValue(PropertyTag::GROUPED_PRODUCTS_TAG(), $pageIds);
            $this->setParentPageForGroupedProducts($pageIds, $page->getParentPageId());
            $this->copyProperties($pageIds, $page);
            $this->processProductsSellingInPair($pageIds, $page);
            $this->releaseAssignedProductsFromOthersGroups($pageIds, [$page->getPageId()]);
        } else {
            $page->setValue(PropertyTag::GROUPED_PRODUCTS_TAG(), []);
        }
    }

    protected function resolveFebiBilsteinImages($parentPage, $page)
    {
        if (empty($page->getValue(PropertyTag::PHOTO_ALBUM_THUMBNAIL_TAG())) || !empty($parentPage->getValue(PropertyTag::PHOTO_ALBUM_THUMBNAIL_TAG())) || !empty($parentPage->getChildren())) {
            return;
        }

        if ($parentPage->getValue(PropertyTag::ESHOP_ROLLER_PRODUCER_TAG()) != PageIds::getFebiBilsteinProducer()) {
            return;
        }

        $parentPage->setValue(PropertyTag::PHOTO_ALBUM_THUMBNAIL_TAG(), $page->getValue(PropertyTag::PHOTO_ALBUM_THUMBNAIL_TAG()));

        $parentPageId = $parentPage->getPageId();

        foreach ($page->getChildren() as $child) {
            $child = $child->duplicate();
            $child->setParentPageId($parentPageId);
            $child->save(false);
        }

        $parentPage->save(false);
    }


    protected function resolveImages($parentPage, $page)
    {
        if (empty($parentPage->getValue(PropertyTag::PHOTO_ALBUM_THUMBNAIL_TAG())) && empty($parentPage->getChildren())) {
            return;
        }

        $page->setValue(PropertyTag::PHOTO_ALBUM_THUMBNAIL_TAG(), $parentPage->getValue(PropertyTag::PHOTO_ALBUM_THUMBNAIL_TAG()));

        $pageId = $page->getPageId();

        foreach ($page->getChildren() as $child) {
            if ($child->getPageTypeId() != PageTypeID::PHOTO_ID()) {
                continue;
            }

            $child->delete();
        }

        foreach ($parentPage->getChildren() as $child) {
            if ($child->getPageTypeId() != PageTypeID::PHOTO_ID()) {
                continue;
            }

            $child = $child->duplicate();
            $child->setParentPageId($pageId);
            $child->save(false);
        }

        $page->save(false);
    }

    protected function processProductsSellingInPair($pageIds, $parentPage)
    {
        $sellingInPair = false;

        if ($parentPage->getValue(PropertyTag::SELL_ONLY_IN_PAIR_TAG()) == Constants::C_True_Char) {
            $sellingInPair = true;
        }

        if (!$sellingInPair) {
            foreach ($pageIds as $pageId) {
                $page = \PageFactory::get($pageId);

                if ($page->getValue(PropertyTag::SELL_ONLY_IN_PAIR_TAG()) == Constants::C_True_Char) {
                    $sellingInPair = true;
                    break;
                }
            }
        }

        if ($sellingInPair) {
            $parentPage->setValue(PropertyTag::SELL_ONLY_IN_PAIR_TAG(), Constants::C_True_Char);
            $parentPage->save(false);

            foreach ($pageIds as $pageId) {
                $page = \PageFactory::get($pageId);
                $page->setValue(PropertyTag::SELL_ONLY_IN_PAIR_TAG(), Constants::C_True_Char);
                $page->save(false);
            }
        }
    }

    private function setParentPageForGroupedProducts($pageIds, $parentPageId)
    {
        foreach ($pageIds as $pageId) {
            $page = \PageFactory::get($pageId);
            if ($page->getParentPageId() !== $parentPageId) {
                $page->setParentPageId($parentPageId);
                $page->save(false);
            }
        }
    }

    private function releaseAssignedProductsFromOthersGroups($productIds, $exceptFromPageIds = [])
    {
        if (!count($productIds)) {
            return;
        }
        $existingLinks = DB::table('tblLinkProperties')
            ->where('property_id', '=', PropertyID::GROUPED_PRODUCTS_ID())
            ->whereIn('to_page_id', $productIds)
            ->when(count($exceptFromPageIds) > 0, function ($query) use ($exceptFromPageIds) {
                return $query->whereNotIn('from_page_id', $exceptFromPageIds);
            })
            ->distinct()
            ->get();
        DB::table('tblLinkProperties')
            ->where('property_id', '=', PropertyID::GROUPED_PRODUCTS_ID())
            ->whereIn('to_page_id', $productIds)
            ->when(count($exceptFromPageIds) > 0, function ($query) use ($exceptFromPageIds) {
                return $query->whereNotIn('from_page_id', $exceptFromPageIds);
            })->delete();
        if (count($existingLinks)) {
            foreach ($existingLinks as $link) {
                OnixNotifier::addGroupsLinkMessage([
                    'from' => $link->from_page_id,
                    'to' => $link->to_page_id,
                    'new_from' => implode(',', $exceptFromPageIds),
                ]);
            }
        }
    }

    protected function processSupplierProduct(PageInterface $page)
    {
        $supplierCodePropertyTags = config('product.supplier_code_property_tags');
        $supplierCodePropertyTags = array_diff($supplierCodePropertyTags, [PropertyTag::ONIX_SUPPLIER_CODES_TAG()]);

        foreach ($supplierCodePropertyTags as $propertyTag) {
            $supplierCode = $page->getValue($propertyTag);
            if (!empty($supplierCode)) {
                break;
            }
        }

        if (empty($supplierCode)) {
            return;
        }

        $pageIds = \DB::table('tblPagePropertyValues')
            ->where('property_id', PropertyID::ONIX_SUPPLIER_CODES_ID())
            ->whereRaw('FIND_IN_SET(?, property_value)', [$supplierCode])
            ->pluck('page_id');

        $pageIds = array_unique($pageIds->toArray());

        foreach ($pageIds as $pageId) {
            $page = \PageFactory::get($pageId);
            $this->processPage($page);
        }
    }

    protected function getOnixSupplierCodes(PageInterface $page): array
    {
        $supplierData = $page->getValue(PropertyTag::ONIX_SUPPLIER_CODES_DATA_TAG());
        $supplierData = json_decode($supplierData);

        if (empty($supplierData)) {
            return [];
        }

        $supplierData = json_decode(json_encode($supplierData), true);
        $supplierCodes = [];

        foreach ($supplierData as $supplier) {
            $supplier = array_values($supplier);
            $supplierCodes[] = $supplier[self::ONIX_SUPPLIER_CODE_INDEX];
        }

        return array_map('trim', $supplierCodes);
    }
}
