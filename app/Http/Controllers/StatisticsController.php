<?php

namespace App\Http\Controllers;

class StatisticsController extends Controller
{
    public function show()
    {
        ini_set('memory_limit', '2048M');
        return view('statistics.show');
    }

    public function showUser($userId)
    {
        ini_set('memory_limit', '2048M');
        return view('statistics.show-user', [
            'user_id' => $userId
        ]);
    }

    public function showExtended()
    {
        ini_set('memory_limit', '2048M');
        return view('statistics.extended.show');
    }

    public function showSales()
    {
        ini_set('memory_limit', '2048M');
        return view('statistics.sales.show');
    }

    public function showSlowMovingStock()
    {
        ini_set('memory_limit', '2048M');
        return view('statistics.slow-moving-stock.show');
    }
}
