<?php

namespace App\Http\Controllers;

use App\Eshop\Cart\CartCommandProcessor;
use App\Eshop\Checkout;
use App\PriceOffers\PriceOffer;
use Buxus\Util\PageIds;
use Buxus\Util\Url;
use ShoppingCart;

class CartController
{
    public function cartRemove()
    {
        /** @var CartCommandProcessor $processor */
        $processor = app('buxus.eshop.command-processor-object');

        $processor->removeCart();
        return redirect(\ShoppingCart::getCheckout()->getCartUrl());
    }

    public function removePriceOffer(PriceOffer $priceOffer)
    {
        $priceOffer->remove();
        return redirect(\ShoppingCart::getCheckout()->getCartUrl());
    }

    public function cartSave()
    {
        /** @var CartCommandProcessor $processor */
        $processor = app('buxus.eshop.command-processor-object');

        header("refresh:5;url=" . ShoppingCart::getCheckout()->getCartUrl());
        $cart = $processor->downloadCart();
        $processor->saveCart();
        return $cart;
    }

    public function cartRestore(PriceOffer $priceOffer)
    {
        /** @var CartCommandProcessor $processor */
        $processor = app('buxus.eshop.command-processor-object');

        $processor->restoreCart($priceOffer);
        return redirect(\ShoppingCart::getCheckout()->getCartUrl());
    }

    public function cartDownload($format)
    {
        /** @var CartCommandProcessor $processor */
        $processor = app('buxus.eshop.command-processor-object');

        return $processor->downloadCart($format);
    }

    public function cartShow($format)
    {
        /** @var CartCommandProcessor $processor */
        $processor = app('buxus.eshop.command-processor-object');

        return $processor->showCart($format);
    }
}
