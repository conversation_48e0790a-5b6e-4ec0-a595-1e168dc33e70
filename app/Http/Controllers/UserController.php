<?php

namespace App\Http\Controllers;

use App\Authentication\DomainSwitch;
use App\DeliveryAddress;
use App\Form\PdfGenerator;
use App\Http\Requests\DeliveryAddressChangeRequest;
use App\Models\EnclosureTransaction;
use App\Models\WebUserNotification;
use Arr;
use Buxus\Error\ErrorReporter;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;
use Buxus\Util\Url;
use BuxusAuthentication;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Response;
use WebUserAuthentication;
use WebUserFactory;

class UserController extends Controller
{
    public function downloadInvoice($enclosureRecordId)
    {
        if (\WebUserAuthentication::isAuthenticated()) {
            try {
                $enclosure = \DB::table('onix_enclosures')
                    ->where('enclosure_record_id', $enclosureRecordId)
                    ->select('path', 'partner_id')
                    ->first();
                if (\WebUserAuthentication::getUser()->getCustomOption('onix_partner_id') == $enclosure->partner_id) {
                    return Storage::download($enclosure->path);
                }
            } catch (\Exception $e) {
                ErrorReporter::reportSilent($e);
            }
        }

        return redirect(Url::page(PageIds::getHomepage()));
    }

    public function downloadComplaintForm($complaintFormId)
    {
        $submittedForm = \DB::table('tblSubmitedForms')
            ->where('form_submit_id', $complaintFormId)
            ->first();

        if ((WebUserAuthentication::isAuthenticated() || BuxusAuthentication::isAuthenticated()) && $submittedForm) {
            $data = (array)$this->parseData($submittedForm);
            if ($data['webuser_id'] == WebUserAuthentication::getUserId() || BuxusAuthentication::isAuthenticated()) {
                $data['form_submit_id'] = $submittedForm->form_submit_id;
                $data['form_submit_time'] = $submittedForm->form_submit_time;
                $data['webuser'] = WebUserFactory::getById($data['webuser_id']);
                $pdfGenerator = new PdfGenerator('forms.complaints-pdf', $data);

                $filename = "complaint.pdf";
                return Response::make($pdfGenerator->generate(), 200, [
                    'Content-Type' => 'application/pdf',
                    'Content-Disposition' => 'inline; filename="' . $filename . '"'
                ]);
            }
        }

        return redirect(Url::page(PageIds::getHomepage()));
    }

    public function downloadReturnsForm($returnsFormId)
    {
        $submittedForm = \DB::table('tblSubmitedForms')
            ->where('form_submit_id', $returnsFormId)
            ->first();

        if ((WebUserAuthentication::isAuthenticated() || BuxusAuthentication::isAuthenticated()) && $submittedForm) {
            $data = (array)$this->parseData($submittedForm);
            if ($data['webuser_id'] == WebUserAuthentication::getUserId() || BuxusAuthentication::isAuthenticated()) {
                $data['form_submit_id'] = $submittedForm->form_submit_id;
                $data['form_submit_time'] = $submittedForm->form_submit_time;
                $data['webuser'] = WebUserFactory::getById($data['webuser_id']);
                $pdfGenerator = new PdfGenerator('forms.returns-pdf', $data);

                $filename = "return.pdf";
                return Response::make($pdfGenerator->generate(), 200, [
                    'Content-Type' => 'application/pdf',
                    'Content-Disposition' => 'inline; filename="' . $filename . '"'
                ]);
            }
        }

        return redirect(Url::page(PageIds::getHomepage()));
    }

    public function cartRecount()
    {
        if (\WebUserAuthentication::isAuthenticated()) {
            return \ShoppingCart::getItemCount();
        }
    }

    public function loginToSite(Request $request)
    {
        return DomainSwitch::retrieveLoginAttempt($request);
    }

    protected function parseData($form)
    {
        $xml = '<?xml version="1.0"?><submitted_form>';
        $xml .= trim(str_replace("\n", "", $form->received_properties));
        $xml .= '</submitted_form>';
        return simplexml_load_string($xml);
    }

    public function changeDeliveryAddress(DeliveryAddressChangeRequest $request, DeliveryAddress $deliveryAddress)
    {
        if (\WebUserAuthentication::isAuthenticated()
            && \WebUserAuthentication::getUser()->getUserId() == $deliveryAddress->webuser_id) {
            $deliveryAddress->update($request->toArray());
            $deliveryAddress->save();

            return redirect(Url::page(PageIds::getDeliveryAddresses()));
        }

        return redirect(Url::page(PageIds::getHomepage()));
    }

    public function createDeliveryAddress(DeliveryAddressChangeRequest $request)
    {
        if (\WebUserAuthentication::isAuthenticated()) {
            $userId = \WebUserAuthentication::getUserId();
            DeliveryAddress::create($request->toArray() + ['webuser_id' => $userId]);

            return redirect(Url::page(PageIds::getDeliveryAddresses()));
        }

        return redirect(Url::page(PageIds::getHomepage()));
    }

    public function deleteDeliveryAddress(DeliveryAddress $deliveryAddress)
    {
        if (\WebUserAuthentication::isAuthenticated()
            && \WebUserAuthentication::getUser()->getUserId() == $deliveryAddress->webuser_id) {
            $deliveryAddress->delete();

            return redirect(Url::page(PageIds::getDeliveryAddresses()));
        }

        return redirect(Url::page(PageIds::getHomepage()));
    }

    public function invoices()
    {
        return view('user.invoices');
    }

    public function creditNotes()
    {
        return view('user.credit-notes');
    }

    public function deliveryNotes()
    {
        return view('user.delivery-notes');
    }

    public function creditNoteOffsettingConfirmation()
    {
        $transaction = EnclosureTransaction::verifyTransactionConfirmation();

        if (!$transaction instanceof EnclosureTransaction) {
            return redirect(Url::page(PageIds::getHomepage()));
        }

        return view('user.credit-note-offsetting-confirmation', [
            'transaction' => $transaction,
        ]);
    }

    public function creditNoteOffsettingDenial()
    {
        $transaction = EnclosureTransaction::verifyTransactionDenial();

        if (!$transaction instanceof EnclosureTransaction) {
            return redirect(Url::page(PageIds::getHomepage()));
        }

        return view('user.credit-note-offsetting-denial', [
            'transaction' => $transaction,
        ]);
    }

    public function notificationMarkAsRead(Request $request)
    {
        $userId = 0;
        $itemId = $request->input('item_id');
        $itemType = $request->input('item_type');

        if (\WebUserAuthentication::isAuthenticated()) {
            $userId = \WebUserAuthentication::getUserId();

            if(in_array($itemType, array_keys(config('notifications.types')))) {
                $fakeAuth = new \App\Authentication\FakeAuthentication();
                if(empty($superuser = $fakeAuth->getSuperuser())) {
                    \DB::table('tblWebUserNotifications')
                        ->where('webuser_id', $userId)
                        ->where('notification_type', config('notifications.types.' . $itemType))
                        ->where('notification_item_id', $itemId)
                        ->delete();
                }
            }
        }

        return response()->json([
            'result' => 'success',
            'notifications' => WebUserNotification::getAllNotificationCounts($userId),
            'has_notification' => WebUserNotification::hasNotification(config('notifications.types.' . $itemType), $itemId),
        ]);
    }

    public function notificationGetCounts(Request $request)
    {
        $userId = 0;
        if (\WebUserAuthentication::isAuthenticated()) {
            $userId = \WebUserAuthentication::getUserId();
        }
        return response()->json([
            'result' => 'success',
            'notifications' => WebUserNotification::getAllNotificationCounts($userId),
        ]);
    }

}
