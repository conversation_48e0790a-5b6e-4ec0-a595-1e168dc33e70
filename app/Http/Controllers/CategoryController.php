<?php

namespace App\Http\Controllers;

use App\Eshop\CategoryManager;
use Illuminate\Http\Request;

class CategoryController extends Controller
{
    public function getMainCategories()
    {
        $manager = new CategoryManager();
        $categories = $manager->getMainCategories();
        return $manager->getResultsAsArray($categories);
    }

    public function getSubcategories($categoryId)
    {
        $manager = new CategoryManager();
        $categories = $manager->getSubcategories($categoryId);
        return $manager->getResultsAsArray($categories);
    }
}
