<?php

namespace App\Http\Controllers;

use App\Form\Manager\FormManager;
use Buxus\Util\PageIds;
use Buxus\Util\Url;
use BuxusAuthentication;
use Illuminate\Http\Request;
use Storage;

class FormController extends Controller
{
    public function returns()
    {
        return view('buxus-tools.form.returns');
    }

    public function complaints()
    {
        return view('buxus-tools.form.complaints');
    }

    public function file($fileId)
    {
        $file = \DB::table('tblFormAttachments')
            ->where('id', $fileId)
            ->first();

        $form = \DB::table('tblSubmitedForms')
            ->where('form_submit_id', $file->form_submit_id)
            ->first();

        $manager = new FormManager();
        $form = $manager->parseData($form);

        if (\WebUserAuthentication::isAuthenticated()
            && \WebUserAuthentication::getUser()->getUserId() == $form->webuser_id
            || BuxusAuthentication::isAuthenticated()) {
            return Storage::disk('local')->download($file->path, $file->client_original_name);
        }

        return redirect(Url::page(PageIds::getHomepage()));
    }
}
