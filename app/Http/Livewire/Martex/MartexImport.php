<?php

namespace App\Http\Livewire\Martex;

use App\Imports;
use App\Imports\Components\AbstractImportWithCrossfileComponent;
use App\Imports\Jobs\Martex\MartexMakeProductsWithoutOeCodesPassiveJob;
use App\Imports\Processors\Martex\MartexCrossFile;
use App\Imports\Processors\Martex\MartexImportProcessor;
use Buxus\Util\PageIds;

class MartexImport extends AbstractImportWithCrossfileComponent
{
    protected int $supplier;
    protected int $importType = Imports::MARTEX;
    protected string $fileLocation = 'imports/martex/martex.xlsx';
    protected string $filePreviousLocation = 'imports/martex/martex-previous.xlsx';
    protected ?string $configKey = 'martex';
    protected string $processorClass = MartexImportProcessor::class;
    protected string $crossfileProcessorClass = MartexCrossFile::class;

    public const CONFIG_KEY = 'martex';

    protected bool $shouldQueueCrossfile = true;

    public function __construct($id = null)
    {
        ini_set('memory_limit', '2048M');
        $this->supplier = PageIds::getMartexSupplier();
        parent::__construct($id);
    }

    public function create($delayed = false)
    {
        parent::create($delayed);

        if ($delayed) {
            MartexMakeProductsWithoutOeCodesPassiveJob::dispatchAtMidnight()->onQueue( 'buxus_iveco_big_db_import_' . env('DB_DATABASE'));
        } else {
            MartexMakeProductsWithoutOeCodesPassiveJob::dispatch()->onQueue( 'buxus_iveco_big_db_import_' . env('DB_DATABASE'));
        }
    }
}
