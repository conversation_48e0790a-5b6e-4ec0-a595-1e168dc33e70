<?php

namespace App\Http\Livewire\SpecialTurboMargin;

use App\Imports\Components\AbstractMarginComponent;
use App\MarginLevel;
use App\Supplier;
use Buxus\Livewire\Component;

class SpecialTurboMargin extends AbstractMarginComponent
{
    protected $type = MarginLevel::TYPE_SPECIAL_TURBO;

    public function render()
    {
        $this->producer = Supplier::where('name', 'SPECIAL-TURBO-SUPPLIER')->first();

        return parent::render();
    }
}
