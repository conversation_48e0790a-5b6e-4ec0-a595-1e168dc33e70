<?php

namespace App\Http\Livewire\IndexLevel;

use Buxus\Livewire\Component;

class IndexLevelDelete extends Component
{
    public $indexLevel;

    public function render()
    {
        return view('livewire.index-level.index-level-delete');
    }

    public function delete()
    {
        $this->indexLevel->delete();

        session()->flash('success', 'Cenová hladina bola ú<PERSON>ešne odstránená.');

        $this->emit('indexLevelEdited');
    }
}
