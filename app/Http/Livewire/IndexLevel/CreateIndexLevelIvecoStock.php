<?php

namespace App\Http\Livewire\IndexLevel;

use App\Models\IndexLevel;
use Buxus\Livewire\Component;

class CreateIndexLevelIvecoStock extends Component
{
    public $index_level_from;
    public $additional_price_increase;
    public $additional_price_increase_eu;

    protected $rules = [
        'index_level_from' => 'required|numeric|min:0',
        'additional_price_increase' => 'required|numeric|min:0',
        'additional_price_increase_eu' => 'required|numeric|min:0',
    ];

    public function render()
    {
        return view('livewire.index-level.create-index-level-iveco-stock', [
            'indexLevelStatus' => $this->getIndexLevelStatus(),
        ]);
    }

    public function create()
    {
        $this->validate();

        IndexLevel::create([
            'index_level_from' => $this->index_level_from,
            'additional_price_increase' => $this->additional_price_increase,
            'additional_price_increase_eu' => $this->additional_price_increase_eu,
            'type_tag' => IndexLevel::IVECO_STOCK_TYPE_TAG,
            'index_level_status' => $this->getIndexLevelStatus(),
        ]);

        session()->flash('success', 'Cenová hladina pridaná úspešne.');

        $this->emit('indexLevelEdited');
        $this->reset();
    }

    protected function getIndexLevelStatus()
    {
        $indexLevel = IndexLevel::where('type_tag', IndexLevel::IVECO_STOCK_TYPE_TAG)->first();

        if ($indexLevel) {
            return $indexLevel->index_level_enabled;
        }

        return false;
    }

    public function changeStatus()
    {
        $indexLevels = IndexLevel::where('type_tag', IndexLevel::IVECO_STOCK_TYPE_TAG)->get();
        $indexLevelStatus = $this->getIndexLevelStatus();

        foreach ($indexLevels as $indexLevel) {
            $indexLevel->index_level_enabled = !$indexLevelStatus;
            $indexLevel->save();
        }

        if (!$indexLevelStatus) {
            session()->flash('success', 'Cenové hladiny boli zapnuté úspešne.');
        } else {
            session()->flash('success', 'Cenové hladiny boli vypnuté úspešne.');
        }
    }
}
