<?php

namespace App\Http\Livewire;

use App\Exports\SlowMovingStockStatisticsExport;
use App\Models\StockStateLog;
use Buxus\Livewire\Component;
use Carbon\Carbon;
use Excel;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class SlowMovingStockStatistics extends Component
{
    public $dateFrom;
    public $dateTo;

    public function mount()
    {
        $this->dateFrom = request('date_from') ?? Carbon::now()->subYears(1)->toDateString();
        $this->dateTo = request('date_to') ?? Carbon::now()->addDays(1)->toDateString();
    }

    public function render()
    {
        return view('livewire.slow-moving-stock-statistics');
    }

    public function export()
    {
        $data = collect($this->getData());

        $export = new SlowMovingStockStatisticsExport($data);
        return Excel::download($export, 'slow_moving_stock_statistics.xlsx');
    }

    protected function getData()
    {
        //dd($this->getSlowMovingProductsWithPageInfo($this->dateFrom, $this->dateTo));
        //dd($this->getSlowMovingProductsWithPageInfoEloquent($this->dateFrom, $this->dateTo));


        return $this->getSlowMovingProductsWithPageInfo($this->dateFrom, $this->dateTo);
    }


    /**
     * Get slow moving products with page info (only active products)
     * Returns: page_id, title, onix_main_code
     *
     * @param string $dateFrom Start date (Y-m-d format)
     * @param string $dateTo End date (Y-m-d format)
     * @return array
     */
    private function getSlowMovingProductsWithPageInfo($dateFrom, $dateTo)
    {
        $sql = "
            SELECT DISTINCT
                p.page_id,
                title_pv.property_value as title,
                onix_pv.property_value as onix_main_code,
                stock_pv.property_value as current_stock,
                price_pv.property_value as price,
                producer_pv.property_value as producer_id,
                onix_ns_pv.property_value as onix_ns_number,
                MIN(`ssl`.stock_value) as min_stock,
                MAX(`ssl`.stock_value) as max_stock,
                AVG(`ssl`.stock_value) as avg_stock
            FROM stock_state_log `ssl`
            INNER JOIN tblPages p ON `ssl`.buxus_product_id = p.page_id
            LEFT JOIN tblPagePropertyValues title_pv ON (
                p.page_id = title_pv.page_id
                AND title_pv.property_id = (SELECT property_id FROM tblProperties WHERE property_tag = 'title')
            )
            LEFT JOIN tblPagePropertyValues onix_pv ON (
                p.page_id = onix_pv.page_id
                AND onix_pv.property_id = (SELECT property_id FROM tblProperties WHERE property_tag = 'onix_main_code')
            )
            LEFT JOIN tblPagePropertyValues producer_pv ON (
                p.page_id = producer_pv.page_id
                AND producer_pv.property_id = (SELECT property_id FROM tblProperties WHERE property_tag = 'eshop_roller_producer')
            )
            LEFT JOIN tblPagePropertyValues stock_pv ON (
                p.page_id = stock_pv.page_id
                AND stock_pv.property_id = (SELECT property_id FROM tblProperties WHERE property_tag = 'onix_stock_balance')
            )
            LEFT JOIN tblPagePropertyValues price_pv ON (
                p.page_id = price_pv.page_id
                AND price_pv.property_id = (SELECT property_id FROM tblProperties WHERE property_tag = 'eshop_eur_price_without_vat')
            )
            LEFT JOIN tblPagePropertyValues onix_ns_pv ON (
                p.page_id = onix_ns_pv.page_id
                AND onix_ns_pv.property_id = (SELECT property_id FROM tblProperties WHERE property_tag = 'onix_ns_number')
            )

            WHERE `ssl`.created_at BETWEEN ? AND ?
              -- Only active products
              AND p.page_state_id = 1
              -- Never ordered during the period
              AND NOT EXISTS (
                SELECT 1
                FROM tblShopOrderItems soi
                INNER JOIN tblShopOrders so ON soi.order_id = so.order_id
                WHERE soi.page_id = `ssl`.buxus_product_id
                  AND so.order_datetime BETWEEN ? AND ?
              )
            GROUP BY p.page_id, title_pv.property_value, onix_pv.property_value
            -- Never had zero stock
            HAVING MIN(`ssl`.stock_value) > 0
            ORDER BY avg_stock DESC
        ";

        return DB::select($sql, [$dateFrom, $dateTo, $dateFrom, $dateTo]);
    }



    /**
     * Get slow moving products with page info (Eloquent version)
     *
     * @param string $dateFrom Start date (Y-m-d format)
     * @param string $dateTo End date (Y-m-d format)
     * @return \Illuminate\Support\Collection
     */
    public function getSlowMovingProductsWithPageInfoEloquent($dateFrom, $dateTo)
    {
        // First get the property IDs
        $titlePropertyId = DB::table('tblProperties')
            ->where('property_tag', 'title')
            ->value('property_id');

        $onixMainCodePropertyId = DB::table('tblProperties')
            ->where('property_tag', 'onix_main_code')
            ->value('property_id');

        return DB::table('stock_state_log as ssl')
            ->select([
                'p.page_id',
                'title_pv.property_value as title',
                'onix_pv.property_value as onix_main_code',
                DB::raw('MIN(ssl.stock_value) as min_stock'),
                DB::raw('MAX(ssl.stock_value) as max_stock'),
                DB::raw('AVG(ssl.stock_value) as avg_stock')
            ])
            ->join('tblPages as p', 'ssl.buxus_product_id', '=', 'p.page_id')
            ->leftJoin('tblPagePropertyValues as title_pv', function($join) use ($titlePropertyId) {
                $join->on('p.page_id', '=', 'title_pv.page_id')
                    ->where('title_pv.property_id', '=', $titlePropertyId);
            })
            ->leftJoin('tblPagePropertyValues as onix_pv', function($join) use ($onixMainCodePropertyId) {
                $join->on('p.page_id', '=', 'onix_pv.page_id')
                    ->where('onix_pv.property_id', '=', $onixMainCodePropertyId);
            })
            ->whereBetween('ssl.created_at', [$dateFrom, $dateTo])
            ->where('p.page_state_id', 1) // Only active products
            ->whereNotExists(function ($query) use ($dateFrom, $dateTo) {
                $query->select(DB::raw(1))
                    ->from('stock_state_log as ssl2')
                    ->whereColumn('ssl2.buxus_product_id', 'ssl.buxus_product_id')
                    ->whereBetween('ssl2.created_at', [$dateFrom, $dateTo])
                    ->where('ssl2.stock_value', 0);
            })
            ->whereNotExists(function ($query) use ($dateFrom, $dateTo) {
                $query->select(DB::raw(1))
                    ->from('tblShopOrderItems as soi')
                    ->join('tblShopOrders as so', 'soi.order_id', '=', 'so.order_id')
                    ->whereColumn('soi.page_id', 'ssl.buxus_product_id')
                    ->whereBetween('so.order_datetime', [$dateFrom, $dateTo]);
            })
            ->groupBy(['p.page_id', 'title_pv.property_value', 'onix_pv.property_value'])
            ->having(DB::raw('MIN(ssl.stock_value)'), '>', 0)
            ->orderBy(DB::raw('AVG(ssl.stock_value)'), 'desc')
            ->get();
    }
}
