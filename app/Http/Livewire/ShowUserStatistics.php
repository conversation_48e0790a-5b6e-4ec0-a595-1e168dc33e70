<?php

namespace App\Http\Livewire;

use App\Exports\UserStatisticsExport;
use Buxus\Livewire\Component;
use Cache;
use Carbon\Carbon;
use Excel;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection;

class ShowUserStatistics extends Component
{
    public const SORT_ASCENDING = 'ASC';
    public const SORT_DESCENDING = 'DESC';

    public const SORT_BY_KEY = 'users_sort_by';

    public $dateFrom;
    public $dateTo;
    public $sortMode;


    public function mount()
    {
        $this->dateFrom = request('user_from') ?? '1900-01-01';;
        $this->dateTo = request('user_to') ?? Carbon::now()->addDays(1)->toDateString();
        $this->sortMode = request(self::SORT_BY_KEY) ?? self::SORT_DESCENDING;
    }

    public function render()
    {
        $sortMode = request(self::SORT_BY_KEY) ?? self::SORT_DESCENDING;
        $from = request('user_from') ?? '1900-01-01';
        $to = request('user_to') ?? Carbon::now()->addDays(1)->toDateString();

        $users = $this->paginate($this->getUsers($from, $to, $sortMode), 24, request('user_page'), [
            'path' => Paginator::resolveCurrentPath(),
            'pageName' => 'user_page',
            'query' => [
                'user_from' => $from,
                'user_to' => $to,
                self::SORT_BY_KEY => $sortMode
            ]
        ]);

        return view('livewire.show-user-statistics', [
            'users' => $users,
        ]);
    }

    public function downloadXlsx()
    {
        $export = new UserStatisticsExport($this->getUsers($this->dateFrom, $this->dateTo, $this->sortMode));
        return Excel::download($export, 'users.xlsx');
    }

    protected function paginate($items, $perPage = 15, $page = null, $options = [])
    {
        $page = $page ?: (Paginator::resolveCurrentPage() ?: 1);
        $items = $items instanceof Collection ? $items : Collection::make($items);
        return new LengthAwarePaginator($items->forPage($page, $perPage), $items->count(), $perPage, $page, $options);
    }

    protected function getUsers($from, $to, $sortMode)
    {
        return Cache::remember('user-statistics-' . $from . '-' . $to . '-' . $sortMode, 15 * 60, function () use ($from, $to, $sortMode) {
            $order = 'ORDER BY search_count ' . $sortMode;

            return $this->processUserData($from, $to, $order);
        });
    }

    protected function processUserData($from, $to, $order)
    {
        $users = $this->getUsersFromDatabase($from, $to, $order);

        foreach ($users as $i => $user) {
            if ($users[$i]['site'] == 'cz') {
                if ($users[$i]['total_price'] > 0) {
                    $users[$i]['total_price'] = $users[$i]['total_price'] / config('buxus_eshop.exchange_rate_eur_czk');
                }
            }

            $users[$i]['search_conversion_rate'] = $users[$i]['search_count'] > 0 ? round($users[$i]['order_count'] / $users[$i]['search_count'] * 100, 2) : 0;
            $users[$i]['average_order_price'] = $users[$i]['order_count'] > 0 ? round($users[$i]['total_price'] / $users[$i]['order_count'], 2) : 0;
            $users[$i]['average_search_price'] = $users[$i]['search_count'] > 0 ? round($users[$i]['total_price'] / $users[$i]['search_count'], 2) : 0;

            $users[$i]['search_conversion_rate'] = $users[$i]['search_conversion_rate'] . ' %';

            $users[$i]['total_price'] = number_format($users[$i]['total_price'], 2, ',', ' ') . ' €';
            $users[$i]['average_order_price'] = number_format($users[$i]['average_order_price'], 2, ',', ' ') . ' €';
            $users[$i]['average_search_price'] = number_format($users[$i]['average_search_price'], 2, ',', ' ') . ' €';
        }

        return $users;
    }

    protected function getUsersFromDatabase($from, $to, $order)
    {
        return \BuxusDB::get()->fetchAll('select distinct tWU.user_id,
                tWU.company_name,
                tWU.site,
                (SELECT count(tWUL.user_id) from tblWebUserLog as `tWUL`
                 WHERE tWUL.user_id = tWU.user_id
                   AND action = :action
                   AND tWUL.action_date >= :action_start_date
                   AND tWUL.action_date <= :action_end_date)   as login_count,
                (SELECT count(tWUSL.webuser_id) from tblWebUserSearchLog as `tWUSL`
                 WHERE tWUSL.webuser_id = tWU.user_id
                   AND tWUSL.search_time >= :action_start_date
                   AND tWUSL.search_time <= :action_end_date)  as search_count,
                (SELECT count(tSO.order_id) from tblShopOrders as `tSO`
                 WHERE tSO.order_web_user_id = tWU.user_id
                   AND tSO.order_datetime >= :action_start_date
                   AND tSO.order_datetime <= :action_end_date) as order_count,

                (SELECT sum(tSO.paid_price) from tblShopOrders as `tSO`
                 WHERE tSO.order_web_user_id = tWU.user_id
                   AND tSO.order_datetime >= :action_start_date
                   AND tSO.order_datetime <= :action_end_date) as total_price
from `tblWebUsers` as `tWU` 
                      ' . $order, [
            'action' => 1,
            'action_start_date' => $from,
            'action_end_date' => $to,
        ]);
    }
}
