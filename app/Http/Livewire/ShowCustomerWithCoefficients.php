<?php

namespace App\Http\Livewire;

use App\Coefficient;
use Buxus\Livewire\Component;

class ShowCustomerWithCoefficients extends Component
{
    public $customers;

    public function mount(){
        $userIds = Coefficient::all()->groupBy('user_id')->toArray();
        foreach(array_keys($userIds) as $userId){
            $this->customers[] = \WebUserFactory::getById($userId);
        }
    }

    public function render()
    {
        return view('livewire.show-customer-with-coefficients');
    }
}
