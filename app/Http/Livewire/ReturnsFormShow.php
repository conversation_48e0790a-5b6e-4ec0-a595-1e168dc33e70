<?php

namespace App\Http\Livewire;

use App\Form\ComplaintsForm;
use App\Form\Events\FormUpdatedEvent;
use App\Form\Manager\FormManager;
use App\Form\ReturnsForm;
use App\Models\WebUserNotification;
use Buxus\Livewire\Component;
use BuxusEvent;
use Carbon\Carbon;
use Livewire\WithFileUploads;

class ReturnsFormShow extends Component
{
    use WithFileUploads;

    public $files;

    public $filesToUpload;

    public $formId;

    /** @var FormManager @manager */
    protected $manager;

    public $formTag;

    public $showMore;

    protected $form;

    public $formResponse;
    public $formState;

    public function __construct($id = null)
    {
        $this->manager = new FormManager();

        $this->formTag = ReturnsForm::FORM_TYPE_TAG;

        parent::__construct($id);
    }

    public function hydrate()
    {
        $data = \DB::table('tblSubmitedForms')
            ->where('form_submit_id', $this->formId)
            ->first();

        $this->form = (object)$this->manager->parseData($data);

        $this->formResponse = (string)$this->form->response;
        $this->formState = $this->form->form_state_flag ?? $this->manager->getDefaultState();
        $this->files = \DB::table('tblFormAttachments')
            ->where('form_submit_id', $this->form->form_submit_id)
            ->get();
    }

    public function render()
    {
        $this->hydrate();

        return view('livewire.returns-form-show', [
            'manager' => $this->manager,
            'form' => $this->form
        ]);
    }

    public function update()
    {
        $query = \DB::table('tblSubmitedForms')
            ->where('form_submit_id', $this->formId)
            ->where('form_type_tag', $this->formTag);

        $form = $query->first();

        $properties = [
            'response' => $this->formResponse,
            'response_time' => $this->form->response_time ?? Carbon::now()->toDateTimeString(),
            'latest_update' => Carbon::now()->toDateTimeString(),
        ];

        foreach ($properties as $key => $value) {
            if (str_contains($form->received_properties, "<{$key}>")) {
                if ($key !== 'response_time') {
                    $form->received_properties =
                        $this->manager->replaceBetweenTags(
                            $form->received_properties, "<{$key}>", "</{$key}>", $value
                        );
                }
            } else {
                $form->received_properties .= "<{$key}>{$value}</{$key}>\n";
            }
        }

        $query->update([
            'received_properties' => $form->received_properties,
            'form_state_flag' => $this->formState,
        ]);


        foreach ((array)$this->filesToUpload as $file) {
            $this->manager->uploadFile($form, $file);
        }

        $this->reset('filesToUpload');

//        $event = new FormUpdatedEvent($form);
//
//        BuxusEvent::fire($event);

        session()->flash('success-' . $this->formId, 'Uložené úspešne.');

        $this->emit('formReturnUpdated');
    }

    public function updateAndSend()
    {
        $query = \DB::table('tblSubmitedForms')
            ->where('form_submit_id', $this->formId)
            ->where('form_type_tag', $this->formTag);

        $form = $query->first();

        $properties = [
            'response' => $this->formResponse,
            'response_time' => $this->form->response_time ?? Carbon::now()->toDateTimeString(),
            'latest_update' => Carbon::now()->toDateTimeString(),
        ];

        foreach ($properties as $key => $value) {
            if (str_contains($form->received_properties, "<{$key}>")) {
                if ($key !== 'response_time') {
                    $form->received_properties =
                        $this->manager->replaceBetweenTags(
                            $form->received_properties, "<{$key}>", "</{$key}>", $value
                        );
                }
            } else {
                $form->received_properties .= "<{$key}>{$value}</{$key}>\n";
            }
        }

        $query->update([
            'received_properties' => $form->received_properties,
            'form_state_flag' => $this->formState,
        ]);


        foreach ((array)$this->filesToUpload as $file) {
            $this->manager->uploadFile($form, $file);
        }

        $this->reset('filesToUpload');

        $event = new FormUpdatedEvent($form);

        BuxusEvent::fire($event);

        $parsedForm = $this->manager->parseData($form);
        if(!empty($parsedForm['webuser_id'])) {
            WebUserNotification::addNotification($parsedForm['webuser_id'], config('notifications.types.return'), $this->formId);
        }

        session()->flash('success-' . $this->formId, 'Uložené a odoslané úspešne.');

        $this->emit('formReturnUpdated');
    }

    public function showMore()
    {
        $this->showMore = !$this->showMore;
    }

    public function removeFile($id)
    {
        $this->manager->removeFile($id);
    }
}
