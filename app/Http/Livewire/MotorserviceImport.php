<?php

namespace App\Http\Livewire;

use App\Imports;
use App\Imports\Processors\Motorservice\MotorserviceProcessor;
use Buxus\Livewire\Component;
use Livewire\WithFileUploads;
use App\Imports\Helper\GeneralImportHelper;
use Storage;
use App\Imports\Processors\Motorservice\MotorserviceCrossFile;

class MotorserviceImport extends Component
{
    use WithFileUploads;

    /** @var GeneralImportHelper $helper */
    protected $helper;
    public $file;

    public $crossfile;

    protected $rules = [
        'file' => 'required_without:crossfile',
        'crossfile' => 'required_without:file'
    ];

    public const CONFIG_KEY = 'motorservice';

    public function __construct($id = null)
    {
        $this->helper = new GeneralImportHelper();
        $this->helper->setConfigKey(self::CONFIG_KEY);
    }

    public function render()
    {
        return view('livewire.motorservice-import');
    }

    public function create()
    {
        ini_set('max_execution_time', 300);
        ini_set('memory_limit', '-1');
        $this->validate();

        if ($this->file) {
            $availability = config('imports.' . $this->helper->getConfigKey() . '.default_availability');
            $supplierPageId = config('imports.' . $this->helper->getConfigKey() . '.supplier_page_id');

            $path = $this->helper->save($this->file);

            $import = Imports::create([
                'path' => $path,
                'producer_ciselnik_id' => $supplierPageId,
                'type' => Imports::MOTORSERVICE,
                'availability' => $availability,
                'status' => Imports::STILL_RUNNING,
            ]);

            $processor = new MotorserviceProcessor($availability, $import->id);
            $processor->import($path);
        }

        if ($this->crossfile) {
            $crossfilePath = Storage::disk('local')->putFileAs('imports/motorservice/', $this->crossfile, 'crossfile.xlsx');

            $processor = new MotorserviceCrossFile();
            $processor->import($crossfilePath);
        }

        session()->flash('success', 'Import pridaný úspešne.');
    }
}
