<?php

namespace App\Http\Livewire;

use App\Eshop\Order\RinopartsOrder;
use App\Exports\SalesStatisticsExport;
use App\WebUser\WebUser;
use Buxus\Core\Constants;
use Buxus\Livewire\Component;
use Buxus\WebUser\Contracts\WebUser as WebUserContract;
use Carbon\Carbon;
use Excel;
use Illuminate\Support\Collection;
use WebUserFactory;

class SalesStatistics extends Component
{
    public $month;
    public $dateFrom;
    public $dateTo;
    public const MONTH_OPTION = 'month';
    public const DATE_FROM = 'dateFrom';
    public const DATE_TO = 'dateTo';


    protected $queryString = [
        self::MONTH_OPTION,
        self::DATE_FROM,
        self::DATE_TO,
    ];

    protected $listeners = [
        'updatedMonth' => 'resolveMonth',
        'updatedDateFrom' => 'resolveDateFrom',
        'updatedDateTo' => 'resolveDateTo',
    ];

    public function render()
    {
        $data = $this->getData();
        $months = $this->getMonths();

        return view('livewire.sales-statistics', [
            'data' => $data,
            'months' => $months,
        ]);
    }

    protected function getMonths()
    {
        $months = [];

        for ($i = 2022; $i <= Carbon::now()->year; $i++) {
            $maxMonths = Carbon::now()->year == $i ? Carbon::now()->month : 12;
            for ($j = 1; $j <= $maxMonths; $j++) {
                $months[] = str_pad($j, 2, "0", STR_PAD_LEFT) . '/' . $i;
            }
        }

        return $months;
    }

    protected function getData()
    {
        $data = collect([]);
        foreach ($this->getSalesWebusers() as $webuser) {
            $data->push($this->getSalesPersonData($webuser));
        }
        return $data;
    }

    protected function getSalesPersonData(WebUserContract $webuser): Collection
    {
        $orders = $this->getOrdersForUser($webuser);

        $sum = 0;

        foreach ($orders as $order) {
            if (!$order instanceof RinopartsOrder) {
                continue;
            }

            $sum += $order->getTotalPriceEurWithoutVat();
        }

        return collect([
            'webuser_id' => $webuser,
            'webuser_email' => $webuser->getEmail(),
            'webuser_name' => $webuser->getFirstName() . ' ' . $webuser->getSurname(),
            'order_count' => $orders->count(),
            'sum' => round($sum, 2),
        ]);
    }

    protected function getOrdersForUser(WebUserContract $webuser): Collection
    {
        $email = $webuser->getEmail();

        $orderIds = \DB::table('tblShopOrderOptions')
            ->join('tblShopOrders', 'tblShopOrders.order_id', '=', 'tblShopOrderOptions.order_id')
            ->select('tblShopOrderOptions.order_id')
            ->distinct()
            ->where('order_tag', WebUser::SUPERUSER_EMAIL)
            ->where('order_value', $email)
            ->when(!empty($this->month), function ($q) {
                $q
                    ->where('order_datetime', '>=', Carbon::createFromFormat('m/Y', $this->month)->startOfMonth())
                    ->where('order_datetime', '<=', Carbon::createFromFormat('m/Y', $this->month)->endOfMonth());
            })
            ->when(!empty($this->dateFrom), function ($q) {
                $q
                    ->where('order_datetime', '>=', Carbon::createFromFormat('Y-m-d', $this->dateFrom)->startOfDay());
            })
            ->when(!empty($this->dateTo), function ($q) {
                $q
                    ->where('order_datetime', '<=', Carbon::createFromFormat('Y-m-d', $this->dateTo)->endOfDay());
            })
            ->get()
            ->pluck('order_id');

        $orders = collect([]);
        foreach ($orderIds as $orderId) {
            $orders->push(\OrderFactory::getById($orderId));
        }

        return $orders;
    }

    protected function getSalesWebusers(): Collection
    {
        $webuserIds = \DB::table('tblWebUserOptions')
            ->select('user_id as webuser_id')
            ->distinct()
            ->where('user_option_tag', WebUser::SALES_REPRESENTANT)
            ->where('user_option_value', Constants::C_True_Char)
            ->get()
            ->pluck('webuser_id');

        $webusers = collect([]);

        foreach ($webuserIds as $webuserId) {
            $webuser = WebUserFactory::getById($webuserId);
            $fullname = $webuser->getFirstName() . ' ' . $webuser->getSurname();

            if (empty(trim($fullname)) || str_contains($fullname, 'test') || str_contains($fullname, 'Test')) continue;

            $webusers->push($webuser);
        }

        return $webusers;
    }

    public function export()
    {
        $data = $this->getData();

        $export = new SalesStatisticsExport($data);
        return Excel::download($export, 'sales_stastics.xlsx');
    }

    public function updatedMonth($month)
    {
        $this->emit('updatedMonth', $month);
    }

    public function updatedDateFrom($dateFrom)
    {
        $this->emit('updatedDateFrom', $dateFrom);
    }

    public function updatedDateTo($dateTo)
    {
        $this->emit('updatedDateTo', $dateTo);
    }

    public function resolveMonth($month)
    {
        $this->dateFrom = null;
        $this->dateTo = null;
        $this->month = $month;
    }

    public function resolveDateFrom($dateFrom)
    {
        $this->month = null;
        $this->dateFrom = $dateFrom;
    }

    public function resolveDateTo($dateTo)
    {
        $this->month = null;
        $this->dateTo = $dateTo;
    }
}
