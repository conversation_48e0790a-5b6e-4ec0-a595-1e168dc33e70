<?php

namespace App\Http\Livewire;

use Buxus\Livewire\Component;

class ProducerDelete extends Component
{
    public $producer;

    public function render()
    {
        return view('livewire.producer-delete', [
            'producer' => $this->producer,
        ]);
    }

    public function delete()
    {
        $this->producer->marginLevels()->delete();

        $this->producer->delete();

        session()->flash('success', '<PERSON>ýrobca bol úspešne odstránený.');

        $this->emit('producerEdited');
    }
}
