<?php

namespace App\Http\Livewire;

use App\Imports;
use App\Imports\Processors\DefaultProcessorJob;
use App\Imports\Processors\MecDiesel\MecDieselProcessor;
use Buxus\Livewire\Component;
use Livewire\WithFileUploads;
use App\Imports\Helper\GeneralImportHelper;
use Storage;
use App\Imports\Processors\MecDiesel\MecDieselCrossFile;

class MecDieselImport extends Component
{
    use WithFileUploads;

    /** @var GeneralImportHelper $helper */
    protected $helper;
    public $file;
    protected $configKey;
    public $crossfile;

    protected $rules = [
        'file' => 'required_without:crossfile',
        'crossfile' => 'required_without:file'
    ];

    public const CONFIG_KEY = 'mec_diesel';

    public function __construct($id = null)
    {
        $this->helper = new GeneralImportHelper();
        $this->helper->setConfigKey(self::CONFIG_KEY);
    }

    public function render()
    {
        return view('livewire.mec-diesel-import');
    }

    public function create()
    {
        ini_set('max_execution_time', 300);
        ini_set('memory_limit', '-1');
        $this->validate();

        if ($this->file) {
            $availability = config('imports.' . $this->helper->getConfigKey() . '.default_availability');
            $supplierPageId = config('imports.' . $this->helper->getConfigKey() . '.supplier_page_id');

            $dirname = dirname($this->helper->getImportPath());
            $path = $this->file->storeAs($dirname, 'mec_diesel.xlsx');

            $fullPath = Storage::disk('local')->path($path);

            if (\Illuminate\Support\Facades\Storage::disk('local')->exists($this->helper->getImportPath())) {
                if (Storage::disk('local')->exists($this->helper->getImportPath())) {
                    Storage::disk('local')->delete($this->helper->getImportPathPrevious());
                }
                Storage::move($this->helper->getImportPath(), $this->helper->getImportPathPrevious());
            }

            exec('/usr/local/bin/in2csv ' . $fullPath . ' > ' . dirname($fullPath) . '/mec_diesel.csv');


            $import = Imports::create([
                'path' => $path,
                'producer_ciselnik_id' => $supplierPageId,
                'type' => Imports::MEC_DIESEL,
                'availability' => $availability,
                'status' => Imports::STILL_RUNNING,
            ]);

//            $job = new DefaultProcessorJob(new MecDieselProcessor($availability, $import->id), $path);
            $processor = new MecDieselProcessor($availability, $import->id);
            $processor->import($path);
//            dispatch($job);
        }

        if ($this->crossfile) {
            $crossfilePath = Storage::disk('local')->putFileAs('imports/mec-diesel/', $this->crossfile, 'crossfile.xlsx');

            $processor = new MecDieselCrossFile();
            $processor->import($crossfilePath);
        }

        session()->flash('success', 'Import pridaný úspešne.');
    }
}
