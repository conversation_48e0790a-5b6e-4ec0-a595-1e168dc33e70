<?php

namespace App\Http\Livewire;

use App\Coefficient;
use Buxus\Livewire\Component;

class CoefficientDelete extends Component
{
    public $coefficient;

    public function render()
    {
        return view('livewire.coefficient-delete');
    }

    public function delete()
    {
        $coefficient = Coefficient::find($this->coefficient);
        $coefficient->delete();

        session()->flash('success', 'Koeficient bol úspešne odstránený.');

        $this->emit('coefficientEdited');
    }
}
