<?php

namespace App\Http\Livewire\AugustinGroupBulkyPartsMargin;

use App\Imports\Components\AbstractMarginIndexComponent;
use App\MarginLevel;

class AugustinGroupBulkyPartsMarginIndex extends AbstractMarginIndexComponent
{
    protected $type = MarginLevel::TYPE_AUGUSTIN_GROUP_BULKY_PARTS;

    public function render()
    {
        $marginLevels = MarginLevel::where('type', $this->type)->get();
        return view('margin.augustin-group-bulky-parts.margin-index', [
            'marginLevels' => $marginLevels,
        ]);
    }
}
