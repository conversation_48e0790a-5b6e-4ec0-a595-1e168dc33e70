<?php

namespace App\Http\Livewire\AugustinGroupBulkyPartsMargin;

use App\Imports\Components\AbstractMarginComponent;
use App\MarginLevel;
use App\Supplier;

class AugustinGroupBulkyPartsMargin extends AbstractMarginComponent
{
    protected $type = MarginLevel::TYPE_AUGUSTIN_GROUP_BULKY_PARTS;

    public function render()
    {
        $this->producer = Supplier::where('name', 'AG-BULKY-SUPPLIER')->first();

        return view('margin.augustin-group-bulky-parts.margin');
    }
}
