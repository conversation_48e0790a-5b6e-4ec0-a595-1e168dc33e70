<?php

namespace App\Http\Livewire;

use App\Onix\Onix;
use Buxus\Livewire\Component;
use Illuminate\Pagination\Paginator;
use Livewire\WithPagination;

class DeliveryNotesComponent extends Component
{
    use WithPagination;

    protected $deliveryNotes;
    protected $webuser;

    public $delivery_note_search;
    public $enclosure_type;

    public $deliveryNotePagination;

    protected $paginationTheme = 'bootstrap';


    public function __construct($id = null)
    {
        $this->webuser = \WebUserAuthentication::getUser();
        parent::__construct($id);
    }

    public function getQueryString()
    {
        return 'deliveryNotePagination';
    }

    public function render()
    {
        $this->deliveryNotes = $this->getDeliveryNotes();

        return view('livewire.delivery-notes-component', [
            'deliveryNotes' => $this->deliveryNotes,
        ]);
    }

    protected function getDeliveryNotes()
    {
        return \DB::table('onix_enclosures')
            ->whereNotNull('partner_id')
            ->where('partner_id', '<>', '')
            ->where('partner_id', $this->webuser->getCustomOption('onix_partner_id'))
            ->where('enclosure_type_id', Onix::DELIVERY_NOTE_TYPE_ID)
            ->when($this->delivery_note_search, function ($q) {
                $q->where('vs', 'like', "%{$this->delivery_note_search}%");
            })
            ->orderByDesc('onix_date_document')
            ->paginate(24);
    }

    public function updated()
    {
        $this->resetPage();
    }
}
