<?php

namespace App\Http\Livewire;

use App\Imports\Jobs\WeightAndDimensionImportJob;
use App\Imports\Pairing\SupplierPairingStrategyManager;
use Buxus\Core\Constants;
use Buxus\Livewire\Component;

class WeightSetComponent extends Component
{

    public string $supplier_id = '';
    public string $supplier_code = '';
    public string $weight = '';
    public string $length = '';
    public string $width = '';
    public string $height = '';

    public function render()
    {
        return view('livewire.weight-set-component', [
            'importableSuppliers' => $this->getImportableSuppliers(),
        ]);
    }

    protected function getImportableSuppliers()
    {
        $supplierRoller = \Ciselniky::get('product_catalog.supplier');
        $allSuppliers = $supplierRoller->getAllValues();

        $importableSuppliers = [];

        $supplierWithPairingManagers = array_keys(SupplierPairingStrategyManager::getPairingManagersMapping());

        foreach ($allSuppliers as $supplier) {
            if (!in_array($supplier->getId(), $supplierWithPairingManagers)) {
                continue;
            }

            $importableSuppliers[$supplier->getId()] = $supplier->getName();
        }

        asort($importableSuppliers);

        return $importableSuppliers;
    }

    public function set()
    {
        $pairingManager = SupplierPairingStrategyManager::getPairingManagerForSupplierId($this->supplier_id);

        $job = new WeightAndDimensionImportJob(
            $this->supplier_code,
            $pairingManager,
            $this->weight,
            $this->height,
            $this->length,
            $this->width,
            Constants::C_True_Char
        );

        dispatch($job);

        session()->flash('success', 'Váha bola úspešne nastavená.');
    }
}
