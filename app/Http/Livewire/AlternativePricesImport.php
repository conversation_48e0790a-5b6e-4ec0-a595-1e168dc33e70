<?php

namespace App\Http\Livewire;

use App\Imports;
use App\Imports\Helper\AlternativePricesHelper;
use Buxus\Ciselniky\Facades\Ciselniky;
use Buxus\Livewire\Component;
use Livewire\WithFileUploads;
use App\Imports\Processors\AlternativePrices\AlternativePricesProcessor;

class AlternativePricesImport extends Component
{
    use WithFileUploads;

    public $file;

    public $supplier;
    public $newSupplier;

    protected $suppliersList;
    protected $helper;

    protected $rules = [
        'file' => 'required',
        'supplier' => 'required',
        'newSupplier' => 'required_if:supplier,0',
    ];

    public function __construct($id = null)
    {
        $this->helper = new AlternativePricesHelper();

        $this->supplier = 0;
        $this->suppliersList =
            [
                0 => 'Nový dodávateľ'
            ] + $this->helper->getSupplierList();

        parent::__construct($id);
    }

    public function render()
    {
        return view('livewire.alternative-prices-import', [
            'suppliers' => $this->suppliersList,
        ]);
    }

    public function create()
    {
        $this->validate();

        $availability = config('imports.alternative_prices.default_availability');

        $supplier = !empty($this->supplier)
            ? Ciselniky::get('product_catalog.supplier')->getValueById($this->supplier)
            : Ciselniky::get('product_catalog.supplier')->getValueByName(trim($this->newSupplier));

        $import = Imports::create([
            'path' => $this->file->store(Imports::getPath(Imports::ALTERNATIVE_PRICES_IMPORT)),
            'producer_ciselnik_id' => $supplier->getId(),
            'type' => Imports::ALTERNATIVE_PRICES_IMPORT,
            'availability' => $availability,
            'status' => Imports::STILL_RUNNING,
        ]);

        $this->helper->setSupplier($supplier);
        $path = $this->helper->save($this->file);

        $processor = new AlternativePricesProcessor($availability, $import->id, $supplier);
        $processor->import($path);

        session()->flash('success', 'Import pridaný úspešne.');
    }
}
