<?php

namespace App\Http\Livewire;

use App\MarginLevel;
use Buxus\Livewire\Component;

class MarginLevelIvecoBigDbIndex extends Component
{
    public $producer;

    protected $listeners = ['marginLevelEdited' => 'render'];

    public function render()
    {
        $marginLevels = MarginLevel::where('type', MarginLevel::TYPE_IVECO_BIG_DB)->get();
        return view('livewire.margin-level-iveco-big-db-index', [
            'marginLevels' => $marginLevels,
        ]);
    }

    public function update($marginLevel, $form)
    {
        $marginLevel = MarginLevel::find($marginLevel['id']);

        $marginLevel->price_from = $form['price_from'];
        $marginLevel->margin = $form['margin'];
        $marginLevel->margin_eu = $form['margin_eu'];
        $marginLevel->save();

        session()->flash('success', 'Cenová hladina bola upravená úspešne.');

        $this->emit('marginLevelEdited');
    }
}
