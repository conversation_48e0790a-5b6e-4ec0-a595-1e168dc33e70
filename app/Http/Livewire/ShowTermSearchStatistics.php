<?php

namespace App\Http\Livewire;

use App\Statistics\ExportTermStatistics;
use Buxus\Livewire\Component;
use Excel;

class ShowTermSearchStatistics extends Component
{
    protected $logs;

    public $dateFrom;
    public $dateTo;

    public function mount()
    {
        $this->dateFrom = request('search_term_from');
        $this->dateTo = request('search_term_to');
        $page = request('searchTermLogPage');
        $term = request('term');

        $this->logs = \Cache::remember("search_term_statistics_paginated_$page-{$this->dateFrom}-{$this->dateTo}-$term", 15 * 60, function () use ($page, $term) {
            $query = \DB::table('tblWebUserSearchLog as tWUSL')
                ->select('tWUSL.*')
                ->when($this->dateFrom, function ($q) {
                    $q->where('search_time', '>=', $this->dateFrom);
                })->when($this->dateTo, function ($q) {
                    $q->where('search_time', '<=', $this->dateTo);
                })->when(request('term'), function ($q) use ($term) {
                    $q->where('search_term', $term);
                })->groupBy('search_term')->select('*', \DB::raw('count(*) as count'));

            return $query->paginate(24, ['*'], 'searchTermLogPage', $page);
        });
    }

    protected function getLogsForExport()
    {
        $term = request('term');
        return \Cache::remember("search_term_statistics_{$this->dateFrom}-{$this->dateTo}-$term", 15 * 60, function () use ($term) {
            $query = \DB::table('tblWebUserSearchLog as tWUSL')
                ->select('tWUSL.*')
                ->when($this->dateFrom, function ($q) {
                    $q->where('search_time', '>=', $this->dateFrom);
                })->when($this->dateTo, function ($q) {
                    $q->where('search_time', '<=', $this->dateTo);
                })->when(request('term'), function ($q) use ($term) {
                    $q->where('search_term', $term);
                })->groupBy('search_term')->select('*', \DB::raw('count(*) as count'));

            return $query->get();
        });
    }

    public function render()
    {
        return view('livewire.show-term-search-statistics', [
            'logs' => $this->logs,
        ]);
    }

    public function downloadXlsx()
    {
        $export = new ExportTermStatistics($this->getLogsForExport(), $this->dateFrom, $this->dateTo);
        return Excel::download($export, 'logs.xlsx');
    }
}
