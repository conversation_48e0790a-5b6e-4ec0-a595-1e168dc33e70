<?php

namespace App\Http\Livewire;

use App\Imports;
use App\Imports\Processors\NRF\NRFProcessor;
use Buxus\Livewire\Component;
use Livewire\WithFileUploads;
use App\Imports\Helper\GeneralImportHelper;
use Storage;
use App\Imports\Processors\NRF\NRFCrossFile;

class NRFImport extends Component
{
    use WithFileUploads;

    /** @var GeneralImportHelper $helper */
    protected $helper;
    public $file;
    protected $configKey;
    public $crossfile;

    protected $rules = [
        'file' => 'required_without:crossfile',
        'crossfile' => 'required_without:file'
    ];

    public const CONFIG_KEY = 'nrf';

    public function __construct($id = null)
    {
        $this->helper = new GeneralImportHelper();
        $this->helper->setConfigKey(self::CONFIG_KEY);
    }

    public function render()
    {
        return view('livewire.n-r-f-import');
    }

    public function create()
    {
        ini_set('max_execution_time', 300);
        ini_set('memory_limit', '-1');
        $this->validate();

        $config = config('imports.' . $this->helper->getConfigKey());

        if ($this->file) {
            $path = $this->helper->save($this->file);

            $import = Imports::create([
                'path' => $path,
                'producer_ciselnik_id' => $config['supplier_page_id'],
                'type' => Imports::NRF,
                'availability' => $config['default_availability'],
                'status' => Imports::STILL_RUNNING,
            ]);

            $processor = new NRFProcessor($config['default_availability'], $import->id);
            $processor->import($path);
        }

        if ($this->crossfile) {
            $crossfilePath = Storage::disk('local')->putFileAs('imports/nrf/', $this->crossfile, 'crossfile.xlsx');

            $processor = new NRFCrossFile();
            $processor->import($crossfilePath);
        }


        session()->flash('success', 'Import pridaný úspešne.');
    }

    protected function movePreviousFile()
    {
        if (\Illuminate\Support\Facades\Storage::disk('local')->exists($this->helper->getImportPath())) {
            if (Storage::disk('local')->exists($this->helper->getImportPath())) {
                Storage::disk('local')->delete($this->helper->getImportPathPrevious());
            }
            Storage::move($this->helper->getImportPath(), $this->helper->getImportPathPrevious());
        }
    }
}
