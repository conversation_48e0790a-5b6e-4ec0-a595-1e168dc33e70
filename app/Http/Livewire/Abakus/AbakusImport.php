<?php

namespace App\Http\Livewire\Abakus;

use App\Imports;
use App\Imports\Components\AbstractImportComponent;
use App\Imports\Components\AbstractImportWithCrossfileComponent;
use App\Imports\Processors\Abakus\AbakusCrossFile;
use App\Imports\Processors\Abakus\AbakusImportProcessor;
use Buxus\Util\PageIds;

class AbakusImport extends AbstractImportComponent
{
    protected int $supplier;
    protected int $importType = Imports::ABAKUS;
    protected string $fileLocation = 'imports/abakus/abakus.xlsx';
    protected string $filePreviousLocation = 'imports/abakus/abakus-previous.xlsx';
    protected ?string $configKey = 'abakus';
    protected string $processorClass = AbakusImportProcessor::class;

    public const CONFIG_KEY = 'abakus';

    public function __construct($id = null)
    {
        ini_set('memory_limit', '2048M');
        $this->supplier = PageIds::getAbakusSupplier();
        parent::__construct($id);
    }
}
