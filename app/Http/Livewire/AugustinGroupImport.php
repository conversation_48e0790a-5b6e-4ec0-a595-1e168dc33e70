<?php

namespace App\Http\Livewire;

use App\Imports;
use App\Imports\Processors\AugustinGroup\AugustinGroupProcessor;
use Buxus\Livewire\Component;
use Livewire\WithFileUploads;
use App\Imports\Helper\GeneralImportHelper;

class AugustinGroupImport extends Component
{
    use WithFileUploads;

    /** @var GeneralImportHelper $helper */
    protected $helper;
    public $file;
    protected $configKey;

    protected $rules = [
        'file' => 'required',
    ];

    public const CONFIG_KEY = 'augustin_group';

    public function __construct($id = null)
    {
        $this->helper = new GeneralImportHelper();
        $this->configKey = self::CONFIG_KEY;
    }

    public function render()
    {
        return view('livewire.augustin-group-import');
    }

    public function create(bool $dispatchAtMidnight = false)
    {
        $this->validate();

        $availability = config('imports.' . $this->configKey . '.default_availability');

        $supplierPageId = config('imports.' . $this->configKey . '.supplier_page_id');

        $import = Imports::create([
            'path' => $this->file->store(Imports::getPath(Imports::AUGUSTIN_GROUP)),
            'producer_ciselnik_id' => $supplierPageId,
            'type' => Imports::AUGUSTIN_GROUP,
            'availability' => $availability,
            'status' => Imports::STILL_RUNNING,
        ]);

        $this->helper->setConfigKey($this->configKey);
        $path = $this->helper->save($this->file);

        $processor = new AugustinGroupProcessor($availability, $import->id, $dispatchAtMidnight);
        $processor->import($path);

        session()->flash('success', 'Import pridaný úspešne.');
    }

    public function createDelayed()
    {
        $this->create(true);
    }
}
