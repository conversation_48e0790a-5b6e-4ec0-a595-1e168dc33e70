<?php

namespace App\Http\Livewire;

use App\Supplier;
use Buxus\Ciselniky\Facades\Ciselniky;
use Buxus\Livewire\Component;

class CreateNewProducer extends Component
{
    public $producerId;

    protected $producers;

    protected $rules = [
        'producerId' => 'required|unique:producers,producer_ciselnik_id'
    ];

    public function __construct()
    {
        $producers = [];
        foreach (Ciselniky::get('product_catalog.producer')->getAllValues() as $producer) {
            $producers[$producer->getId()] = $producer->getName();
        }

        if ($producers) {
            asort($producers);
        }

        $this->producers = $producers;
        if ($producers) {
            $this->producerId = array_key_first($producers);
        }
    }

    public function render()
    {
        return view('livewire.create-new-producer', [
            'producers' => $this->producers,
        ]);
    }

    public function create()
    {
        $this->name = Ciselniky::get('product_catalog.producer')->getValueById($this->producerId)->getName();
        $this->validate();

        Supplier::create([
            'name' => $this->name,
            'producer_ciselnik_id' => (int) $this->producerId,
        ]);

        session()->flash('success', 'Výrobca bol úspešne pridaný.');

        $this->emit('producerEdited');
    }
}
