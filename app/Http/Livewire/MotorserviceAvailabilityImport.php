<?php

namespace App\Http\Livewire;

use App\Imports;
use App\Imports\Processors\Motorservice\MotorserviceAvailabilityProcessor;
use Buxus\Livewire\Component;
use Livewire\WithFileUploads;
use App\Imports\Helper\GeneralImportHelper;

class MotorserviceAvailabilityImport extends Component
{
    use WithFileUploads;

    /** @var GeneralImportHelper $helper */
    protected $helper;
    public $file;
    public $submit;

    protected $rules = [
        'file' => 'required',
    ];

    public const CONFIG_KEY = 'motorservice_availability';

    public function __construct($id = null)
    {
        $this->helper = new GeneralImportHelper();
        $this->helper->setConfigKey(self::CONFIG_KEY);
    }

    public function render()
    {
        return view('livewire.motorservice-availability-import');
    }

    public function create($delayed = false)
    {
        ini_set('max_execution_time', 300);
        ini_set('memory_limit', '-1');
        $this->validate();

        if ($this->file) {
            $availability = config('imports.' . $this->helper->getConfigKey() . '.default_availability');
            $supplierPageId = config('imports.' . $this->helper->getConfigKey() . '.supplier_page_id');

            $path = $this->helper->save($this->file);

            $import = Imports::create([
                'path' => $path,
                'producer_ciselnik_id' => $supplierPageId,
                'type' => Imports::MOTORSERVICE_AVAILABILITY,
                'availability' => $availability,
                'status' => $delayed ? Imports::DELAYED : Imports::STILL_RUNNING,
            ]);

            $processor = new MotorserviceAvailabilityProcessor($import->id, $delayed);
            $processor->import($path);
        }

        session()->flash('success', 'Import pridaný úspešne.');
    }

    public function createDelayed()
    {
        $this->create(true);
    }
}
