<?php

namespace App\Http\Livewire\OeGermanyMargin;

use App\Imports\Components\AbstractMarginComponent;
use App\MarginLevel;
use App\Supplier;
use Buxus\Livewire\Component;

class OeGermanyMargin extends AbstractMarginComponent
{
    protected $type = MarginLevel::TYPE_OE_GERMANY;

    public function render()
    {
        $this->producer = Supplier::where('name', 'OE_GERMANY-SUPPLIER')->first();

        return parent::render();
    }
}
