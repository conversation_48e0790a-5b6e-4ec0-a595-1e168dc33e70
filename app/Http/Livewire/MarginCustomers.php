<?php

namespace App\Http\Livewire;

use App\Http\Controllers\ImportToolControllers\MarginToolController;
use Buxus\Livewire\Component;

class MarginCustomers extends Component
{
    public $users;
    public $customer;

    public function mount()
    {
        $users = \DB::table('tblWebUsers')->get()->toArray();
        foreach ($users as $user) {
            $this->users[$user->user_id] = "{$user->first_name} {$user->surname} ({$user->username})";
        }

        if (is_array($this->users)) {
            $this->customer = array_key_first($this->users);
        }
    }

    public function render()
    {
        return view('livewire.margin-customers');
    }

    public function customer()
    {
        return redirect()->action(
            [MarginToolController::class, 'editCustomer'], ['customer' => $this->customer]
        );
    }
}
