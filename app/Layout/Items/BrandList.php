<?php

namespace App\Layout\Items;

use Buxus\Page\PageInterface;
use Buxus\Util\PropertyTag;
use Buxus\Util\Url;
use Layout\AbstractLayoutItem;
use Layout\LayoutItemInterface;
use Buxus\Property\LinkPropertyItem;

class BrandList extends AbstractLayoutItem implements LayoutItemInterface
{
    protected $template = 'layouts.brand-list';

    /**
     * @param PageInterface $ownerPage
     *
     * @return array
     */
    public function getViewData(PageInterface $ownerPage): array
    {
        $brands = [];
        $links = $this->getSource()->getValue(PropertyTag::BRAND_PAGE_TAG());
        foreach ($links as $link) {
            /** @var LinkPropertyItem $link * */
            $to_page_id = $link->getToPageId();
            $page = \PageFactory::get($to_page_id);

            $link = $page->getValue(PropertyTag::BRAND_CATEGORY_LINK_TAG());

            if (!empty($link) && is_array($link)) {
                $link = reset($link);
            } else {
                $link = '#';
            }

            if ($page) {
                if (!empty($link) && $link != '#') {
                    $brands[] = [
                        'title' => $page->getValue(PropertyTag::TITLE_TAG()),
                        'image' => $page->getValue(PropertyTag::IMAGE_TAG()),
                        'url' => Url::staticUrl(Url::page($link)),
                    ];
                } else {
                    $brands[] = [
                        'title' => $page->getValue(PropertyTag::TITLE_TAG()),
                        'image' => $page->getValue(PropertyTag::IMAGE_TAG()),
                        'url' => '#',
                    ];
                }
            }
        }

        return [
            'brands' => $brands
        ];
    }

}
