<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MarginLevel extends Model
{
    use HasFactory;

    const TYPE_DEFAULT = 1;
    const TYPE_IVECO_SMALL_DB = 2;
    const TYPE_IVECO_BIG_DB = 3;
    const TYPE_AUGUSTIN_GROUP = 4;
    const TYPE_MEC_DIESEL = 5;
    const TYPE_NRF = 6;
    const TYPE_FEBI_BILSTEIN = 7;
    const TYPE_MOTORSERVICE = 8;
    const TYPE_EMINIA = 9;
    const TYPE_CASCO = 10;
    const TYPE_SPECIAL_TURBO = 11;
    const TYPE_MARTEX = 12;
    const TYPE_SABO = 13;
    const TYPE_REMANTE = 14;
    const TYPE_OE_GERMANY = 15;
    const TYPE_MEAT_DORIA = 16;
    const TYPE_ABAKUS = 17;
    const TYPE_AUGUSTIN_GROUP_BULKY_PARTS = 18;

    protected $fillable = [
        'producer_id',
        'price_from',
        'margin',
        'margin_eu',
        'type',
    ];

    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }
}
