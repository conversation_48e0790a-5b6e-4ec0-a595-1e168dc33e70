<?php

namespace App\Events\Order;

use App\Authentication\FakeAuthentication;
use Buxus\Error\ErrorReporter;
use Buxus\Eshop\Event\OrderCreatedEvent;
use Buxus\WebUser\Contracts\WebUser;

class AddSuperuserOnOrderCreated
{
    public function handle(OrderCreatedEvent $event)
    {
        try {
            $order = $event->getOrder();

            $fakeAuth = new FakeAuthentication();
            $superuser = $fakeAuth->getSuperuser();

            $user = \WebUserFactory::getById($order->getUserId());

            if (empty($user) || empty($superuser)) {
                return;
            }

            if ($superuser->getUserId() == $user->getUserId()) {
                return;
            }

            if ($superuser instanceof WebUser) {
                $order->setData('superuser_id', $superuser->getUserId());
                $order->setData('superuser_email', $superuser->getEmail());
                $order->setData('superuser_name', $superuser->getFirstName() . ' ' . $superuser->getSurname());
            }

            $order->save();
        } catch (\Throwable $e) {
            ErrorReporter::reportSilent($e);
        }
    }
}
