<?php

namespace App\Widget;

use Buxus\Substrate\Menu\HeaderMenu;
use Buxus\Substrate\Menu\TopMenu;
use Buxus\Util\PageIds;
use Buxus\Widgets\AbstractWidget;

class HeaderTopMenuWidget extends AbstractWidget
{
    public function run()
    {

//        $this->view->design = \PageFactory::get(PageIds::getDizajn());
//        if (PageIds::exists('nastavenie_udajov')) {
//            $this->view->udaje = \PageFactory::get(PageIds::getPageId('nastavenie_udajov'));
//        }
//



        $menu = new HeaderMenu();
        return view('widgets.header.top-menu', [
            'menu' => $menu
        ]);
    }
}
