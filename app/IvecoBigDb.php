<?php

namespace App;

use BuxusSite;

class IvecoBigDb
{
    protected $importPath;
    protected $importPathPrevious;

    protected $appPath;
    protected $appPathPrevious;

    protected $storagePath;
    protected $storagePathPrevious;

    protected $site;

    public function __construct($site = 'sk')
    {
        BuxusSite::pushSite($site);

        $this->importPath = $this->getImportPath();
        $this->importPathPrevious = $this->getImportPathPrevious();

        $this->appPath = $this->getAppPath();
        $this->appPathPrevious = $this->getAppPathPrevious();

        $this->storagePath = $this->getStoragePath();
        $this->storagePathPrevious = $this->getStoragePathPrevious();

        $this->site = $site;

        BuxusSite::popSite();
    }

    public function getImportPath()
    {
        if ($this->importPath === null) {
            $this->importPath = config('imports.iveco_big_db.import_path');
        }

        return $this->importPath;
    }

    public function getImportPathPrevious()
    {
        if ($this->importPathPrevious === null) {
            $this->importPathPrevious = config('imports.iveco_big_db.import_previous_path');
        }

        return $this->importPathPrevious;
    }

    public function getAppPath()
    {
        if ($this->appPath === null) {
            $this->appPath = 'app/' . $this->getImportPath();
        }

        return $this->appPath;
    }

    public function getAppPathPrevious()
    {
        if ($this->appPathPrevious === null) {
            $this->appPathPrevious = 'app/' . $this->getImportPathPrevious();
        }

        return $this->appPathPrevious;
    }

    public function getStoragePath()
    {
        if ($this->storagePath === null) {
            $this->storagePath = storage_path($this->getAppPath());
        }

        return $this->storagePath;
    }

    public function getStoragePathPrevious()
    {
        if ($this->storagePathPrevious === null) {
            $this->storagePathPrevious = storage_path($this->getAppPathPrevious());
        }

        return $this->storagePathPrevious;
    }

    public function getType()
    {
        return BuxusSite::executeInSiteContext($this->site, function () {
            return config('imports.iveco_big_db.type');
        });
    }
}
