<?php

namespace App\Eshop;

use <PERSON>uxus\Page\PageInterface;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;

trait ProducerAwareTrait
{
    public function isIvecoOriginal()
    {
        $page = $this->getPage();

        if ($page instanceof PageInterface) {
            if ($page->hasProperty(PropertyTag::ESHOP_ROLLER_PRODUCER_TAG())) {
                return $page->getValue(PropertyTag::ESHOP_ROLLER_PRODUCER_TAG()) == PageIds::getProducerIvecoOriginal();
            }
        }

        return false;
    }

    public function isFiatOriginal()
    {
        $page = $this->getPage();

        if ($page instanceof PageInterface) {
            if ($page->hasProperty(PropertyTag::ESHOP_ROLLER_PRODUCER_TAG())) {
                return $page->getValue(PropertyTag::ESHOP_ROLLER_PRODUCER_TAG()) == PageIds::getProducerFiatOriginal();
            }
        }

        return false;
    }
}
