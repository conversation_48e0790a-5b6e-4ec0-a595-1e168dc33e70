<?php

namespace App\Eshop;

use Buxus\Eshop\Contracts\ShoppingCart as ShoppingCartContract;
use Buxus\Eshop\Event\ShoppingCartInitEvent;
use Buxus\Eshop\Event\ShoppingCartResolvedEvent;
use BuxusEvent;
use Cache;

class ShoppingCartFactory extends \Buxus\Eshop\ShoppingCartFactory
{
    /**
     * @return ShoppingCartContract
     */
    public static function resolveActiveCart()
    {
        $cart = null;

        if (\Session::has(self::$cartSessionKey)) {
            $cart = \Session::get(self::$cartSessionKey);
            if (!$cart instanceof ShoppingCartContract || !$cart->isCartVersionValid()) {
                $cart = null;
            }
        }

        if (\WebUserAuthentication::isAuthenticated()) {
            $sessionId = session_id();

            $cacheKey = ShoppingCart::SHOPPING_CART_DIRTY_SEEN_KEY . \WebUserAuthentication::getUserId();

            $seen = Cache::get($cacheKey);

            if (is_array($seen) && !in_array($sessionId, $seen)) {
                array_push($seen, $sessionId);
                Cache::set($cacheKey, $seen);
                $cart = null;
            }
        }

        // Here we will check whether the cart contains a paid order, this might happen if the user does not visit the
        // thank you page after finishing a purchase
        if ($cart !== null && ($order = $cart->getOrder()) && $order->isPaid()) {
            static::reset();
            $cart = null;
        }

        if ($cart === null) {
            $cart = app('buxus.eshop.cart-object');
            \Session::put(self::$cartSessionKey, $cart);

            if ($cart instanceof ShoppingCartContract) {
                static $cartInitialized = false;
                if (!$cartInitialized) {
                    $cartInitialized = true;
                    $cart->init();
                    BuxusEvent::fire(new ShoppingCartInitEvent($cart));
                }
            }
        }

        if ($cart instanceof ShoppingCartContract) {
            static $cartResolved = false;
            if (!$cartResolved) {
                $cartResolved = true;
                $cart->recount();
                BuxusEvent::fire(new ShoppingCartResolvedEvent($cart));
            }
        }

        return $cart;
    }
}
