<?php

namespace App\Eshop;

use App\Eshop\Price\RefurbishedParts\Deposit;
use App\View\Components\CartActionsComponent;
use App\View\Components\CartSaveModalComponent;
use App\View\Components\PriceOffersComponent;
use Buxus\Core\Constants;
use Buxus\Eshop\Item\BuxusPageBackedItemInterface;
use Buxus\Eshop\Item\MultipleOrderItemsEmitterInterface;
use Buxus\Eshop\Item\MultiShopItemInterface;
use Buxus\Eshop\Item\ShopItemInterface;
use Buxus\Eshop\Order\OrderInterface;
use Buxus\Eshop\Order\OrderItemInterface;
use Buxus\Eshop\Price\PriceType;
use Buxus\Eshop\ShoppingCartFactory;
use Buxus\Testing\Acceptance\AcceptanceTestingOrderInterface;
use Buxus\Util\PropertyTag;
use BuxusSite;

class ShoppingCart extends \Buxus\Eshop\ShoppingCart
{
    public const SHOPPING_CART_DIRTY_SEEN_KEY = 'cart-dirty-seen-';

    protected function fillOrderData(OrderInterface $order)
    {
        $checkout = $this->getCheckout();

        $order->setOrderState(0);
        $order->setSite(\BuxusSite::site());
        $order->setOrderDatetime(date('Y-m-d H:i:s'));

        $order->setCurrency($checkout->getCurrency());

        $order->setFirstName($checkout->getFirstName());
        $order->setSurname($checkout->getSurname());

        $order->setDeliveryName($checkout->getDeliveryName());

        $order->setDeliveryCompanyName($checkout->getDeliveryCompanyName());
        $order->setInvoiceCompanyName($checkout->getCompanyName());

        $order->setDeliveryStreet($checkout->getDeliveryStreet());
        $order->setDeliveryCity($checkout->getDeliveryCity());
        $order->setDeliveryZip($checkout->getDeliveryZip());
        $order->setDeliveryCountry($checkout->getDeliveryCountry());
        $order->setDeliveryPhone($checkout->getDeliveryPhone());

        $order->setDeliveryAddressIsIdentical($checkout->getDeliveryAddressIsIdentical());

        $order->setInvoiceStreet($checkout->getInvoiceStreet());
        $order->setInvoiceCity($checkout->getInvoiceCity());
        $order->setInvoiceZip($checkout->getInvoiceZip());
        $order->setInvoiceCountry($checkout->getInvoiceCountry());

        $order->setEmail($checkout->getEmail());

        $order->setTransportType($checkout->getTransportType());
        $order->setPaymentType($checkout->getPaymentType());

        $order->setPostPrice($checkout->getTransportType()->getFinalPriceValue());

        $order->setCustomerType($checkout->getCustomerType());
        $order->setData('delivery_price', $checkout->getTransportType()->getFinalPriceValue());
        $order->setData('payment_price', $checkout->getPaymentType()->getFinalPriceValue());

        foreach ($checkout->getOptions() as $key => $option) {
            $order->setData($key, $option);
        }

        $order->setPostPriceVat(20);
        $order->setPaidPrice(round($this->getFinalPriceValue(), 2));
        $order->setOrderRawPrice(round($this->getPriceObject(PriceType::TOTAL_RAW_PRICE)->getValue(), 2));

        $order->setDic($checkout->getDic());
        $order->setIco($checkout->getIco());
        $order->setIcdph($checkout->getIcdph());
        $order->setNote($checkout->getNote());

        if (empty($order->getUserId())) {
            $order->setUserId($checkout->getUserId());
        }

        $order->setPhone($checkout->getPhone());

        // add items
        $order_items = [];
        foreach ($this->getItems() as $masterItem) {
            $subitems = [];
            if ($masterItem instanceof MultipleOrderItemsEmitterInterface) {
                $subitems = $masterItem->getItemsForOrder();
            } else {
                $subitems[] = $masterItem;
            }

            foreach ($subitems as $item) {
                $order_item = $this->fillOrderItemData($item);

                if ($order_item !== null) {
                    $order_items[] = $order_item;
                }
            }
        }
        $order->setItems($order_items);

        if ($order instanceof AcceptanceTestingOrderInterface && \AcceptanceTesting::isTestActive()) {
            $order->markAsAcceptanceTestingOrder();
        }
    }

    public function resetInstance()
    {
        ShoppingCartFactory::reset();
    }

    public function recount()
    {
        parent::recount();
        $this->processDeposits();
        $this->processProductsInPair();
    }

    public function processDeposits()
    {
        foreach ($this->getItems() as $item) {
            if ($this->isEligibleForDeposit($item)) {
                $this->addDeposit($item);
            } else {
                $this->removeDeposit($item);
            }
        }
    }

    public function processProductsInPair()
    {
        foreach ($this->getItems() as $item) {
            if (!$item instanceof Product) {
                continue;
            }

            if ($item->shouldOnlySellInPair()) {
                $this->processItemInPair($item);
            }
        }
    }

    protected function processItemInPair(Product $item)
    {
        if (!$item->shouldOnlySellInPair()) {
            return;
        }

        if ($item->getAmount() % 2 == 0) {
            return;
        }

        $item->setAmount($item->getAmount() + 1);
    }


    protected function isEligibleForDeposit($item)
    {
        return $this->isItemRefurbished($item);
    }

    protected function addDeposit($product)
    {
        try {
            $deposit = new Deposit($product);
            $tag = $deposit->getTag();

            if (!empty($this->getItem($tag))) {
                $this->getItem($tag)->setAmount($product->getAmount());
            } else {
                $deposit->setAmount($product->getAmount());
                $this->addItem($deposit);
            }

        } catch (\Exception $e) {
        }
    }

    protected function removeDeposit($item)
    {
        if (!$item instanceof Deposit) {
            $this->removeItem(Deposit::TAG . '_' . $item->getTag());
        }
    }

    protected function isItemRefurbished($item)
    {
        if ($item instanceof BuxusPageBackedItemInterface) {
            $page = $item->getPage();
            if ($page->hasProperty(PropertyTag::IS_REFURBISHED_PART_TAG())) {
                return $page->getValue(PropertyTag::IS_REFURBISHED_PART_TAG()) == Constants::C_True_Char;
            }
        }

        return false;
    }

    public function renderCartActions()
    {
        $component = new CartActionsComponent();
        return $component->render();
    }

    public function renderPriceOffers()
    {
        $component = new PriceOffersComponent();
        return $component->render();
    }

    public function renderCartSaveModal()
    {
        $component = new CartSaveModalComponent();
        return $component->render();
    }

    /**
     * @return ShopItemInterface[]
     */
    public function getItems()
    {
        $items = [];
        foreach ($this->items as $item) {
            if ($item instanceof AdderAwareInterface) {
                if (!$item->shouldBeVisible()) {
                    continue;
                }
            }

            if ($item instanceof MultiShopItemInterface) {
                $subitems = $item->getSubitems();
                foreach ($subitems as $subitem) {
                    $items[$subitem->getTag()] = $subitem;
                }
            } else {
                $items[$item->getTag()] = $item;
            }
        }

        return $items;
    }

    public function getItemCount()
    {
        $count = 0;
        foreach ($this->items as $item) {
            if ($item instanceof AdderAwareInterface && !$item->shouldBeVisible()) {
                continue;
            }

            if ($item instanceof MultiShopItemInterface) {
                foreach ($item->getSubitems() as $subitem) {
                    if ($subitem instanceof ShopItemInterface) {
                        if ($subitem->getAmount() > 0) {
                            $count++;
                        }
                    }
                }
            } else {
                if ($item instanceof ShopItemInterface) {
                    if ($item->getAmount() > 0) {
                        $count++;
                    }
                }
            }

        }
        return $count;
    }

    public function isEmpty()
    {
        return (count($this->getItems()) == 0);
    }

    public function shouldShowDeliveryPrice()
    {
        return BuxusSite::site() != 'en';
    }

    protected function fillOrderItemData(ShopItemInterface $item): ?OrderItemInterface
    {
        $order_item = parent::fillOrderItemData($item);

        if ($item instanceof Product) {
            if(empty($order_item->getOption('warehouse_reservation'))) {
                $order_item->setOption('warehouse_reservation', serialize($item->getInitialWarehouseReservationInfo()));
            }
            $order_item->setOption('item_stock_state', (string)$item->renderAvailabilityOrder($item->getAmount()));
        }

        return $order_item;
    }
}
