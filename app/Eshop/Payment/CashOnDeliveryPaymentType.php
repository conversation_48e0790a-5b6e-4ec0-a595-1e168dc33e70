<?php

namespace App\Eshop\Payment;

use App\Eshop\Delivery\CarrierDeliveryType;
use Buxus\Eshop\Contracts\ShoppingCart;
use Buxus\Eshop\Item\Delivery\GenericDeliveryType;
use Buxus\Util\PageIds;
use BuxusSite;

class CashOnDeliveryPaymentType extends RinopartsGenericPaymentType
{
    public const TAG = 'post';

    public function isSelectableWithTransportType(\Buxus\Eshop\ShoppingCart $cart, GenericDeliveryType $processedTransportType): bool
    {
        if ($processedTransportType->getTag() == CarrierDeliveryType::TAG
            && BuxusSite::site() == 'en') {
            return false;
        }
        return true;
    }
}
