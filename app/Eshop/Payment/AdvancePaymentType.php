<?php

namespace App\Eshop\Payment;

use App\Eshop\Delivery\CarrierDeliveryType;
use Buxus\Eshop\Contracts\ShoppingCart;
use Buxus\Eshop\Item\Delivery\GenericDeliveryType;
use BuxusSite;
use WebUserAuthentication;

class AdvancePaymentType extends RinopartsGenericPaymentType
{
    public const TAG = 'advance_payment';

    public function isSelectableWithTransportType(\Buxus\Eshop\ShoppingCart $cart, GenericDeliveryType $processedTransportType): bool
    {
        if (WebUserAuthentication::isAuthenticated()) {
            $user = WebUserAuthentication::getUser();
            if ($processedTransportType->getTag() == CarrierDeliveryType::TAG
                && !$user->canUseInvoice()
                && BuxusSite::site() == 'en') {
                return true;
            }
        }

        return false;
    }

    public function isEnabled(ShoppingCart $cart)
    {
        return BuxusSite::site() == 'en';
    }
}
