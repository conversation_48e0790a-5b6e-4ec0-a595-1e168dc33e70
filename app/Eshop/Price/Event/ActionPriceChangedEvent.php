<?php

namespace App\Eshop\Price\Event;

use Buxus\Event\Event;

class ActionPriceChangedEvent extends Event
{
    protected $backtrace;
    protected $pageId;
    protected $actionPriceBefore;
    protected $actionPriceAfter;

    public function __construct($backtrace, $pageId, $actionPriceBefore, $actionPriceAfter)
    {
        $this->backtrace = $backtrace;
        $this->pageId = $pageId;
        $this->actionPriceBefore = $actionPriceBefore;
        $this->actionPriceAfter = $actionPriceAfter;
    }

    /**
     * @return mixed
     */
    public function getBacktrace()
    {
        return $this->backtrace;
    }

    /**
     * @return mixed
     */
    public function getPageId()
    {
        return $this->pageId;
    }

    /**
     * @return mixed
     */
    public function getActionPriceBefore()
    {
        return $this->actionPriceBefore;
    }

    /**
     * @return mixed
     */
    public function getActionPriceAfter()
    {
        return $this->actionPriceAfter;
    }
}

