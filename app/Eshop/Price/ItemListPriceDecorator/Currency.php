<?php

namespace App\Eshop\Price\ItemListPriceDecorator;

use Buxus\Eshop\Contracts\ShoppingCart;
use Buxus\Eshop\Oraculum\AbstractPriceListDecorator;
use Buxus\Eshop\Oraculum\RatableItemListInterface;

class Currency extends AbstractPriceListDecorator
{
    public function decorate(RatableItemListInterface $item)
    {
        if (\BuxusSite::site() == 'cz') {
            if ($item instanceof ShoppingCart) {
                $item->getFinalPriceObject()->setCurrency('CZK');
            }
        }
    }
}
