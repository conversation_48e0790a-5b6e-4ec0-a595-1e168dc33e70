<?php

namespace App\Eshop\Price\ItemPriceDecorator;

use Buxus\Error\ErrorReporter;
use Buxus\Eshop\FreeDelivery\FreeDeliveryManager;
use Buxus\Eshop\FreeDelivery\Item\FreeDeliveryModifierItemInterface;
use Buxus\Eshop\Item\BuxusPageBackedItemInterface;
use Buxus\Eshop\Item\Delivery\GenericDeliveryType;
use Buxus\Eshop\Item\Payment\GenericPaymentType;
use Buxus\Eshop\Oraculum\AbstractPriceDecorator;
use Buxus\Eshop\Oraculum\RatableItemInterface;
use Buxus\Eshop\Oraculum\RatableItemListInterface;
use Buxus\Eshop\Price\Price;
use Buxus\Eshop\Price\PriceType;
use Buxus\Util\PropertyTag;

class RinopartsDiscountDelivery extends AbstractPriceDecorator
{
    const PRICE_BEFORE_FREE_DELIVERY_TAG = 'price_before_free_delivery';
    const PRICE_WITHOUT_VAT_BEFORE_FREE_DELIVERY_TAG = 'price_without_vat_before_free_delivery';

    protected $freeLimit = null;

    /**
     * @param RatableItemInterface $item
     */
    public function decorate(RatableItemInterface $item)
    {
        $item->setPriceObject(new Price(self::PRICE_BEFORE_FREE_DELIVERY_TAG, $item->getFinalPriceValue()));
        $item->setPriceObject(new Price(self::PRICE_WITHOUT_VAT_BEFORE_FREE_DELIVERY_TAG,
            $item->getFinalPriceWithoutVatValue()));

        $item->setPriceObject(new Price(PriceType::ITEM_PRICE, 120, 'CZK'));
        $item->setPriceObject(new Price(PriceType::TOTAL_PRICE, 120, 'CZK'));
        $item->setPriceObject(new Price(PriceType::ITEM_PRICE_WITHOUT_VAT, 100, 'CZK'));
        $item->setPriceObject(new Price(PriceType::TOTAL_PRICE_WITHOUT_VAT, 100, 'CZK'));

        $item->setFinalPriceTag(PriceType::TOTAL_PRICE);
        $item->setFinalPriceTagWithoutVat(PriceType::TOTAL_PRICE_WITHOUT_VAT);
    }

    public function isApplicable(RatableItemInterface $item)
    {
        if (\BuxusSite::site() != 'cz') {
            return false;
        }

        if ($item instanceof FreeDeliveryModifierItemInterface
            && $item->disablesFreeDeliveryForEntireOrder()) {
            return false;
        }

        if ($item instanceof GenericDeliveryType) {
            /** @var FreeDeliveryManager $freeDeliveryManager */
            $freeDeliveryManager = app('buxus:free-delivery:manager');

            try {
                if ($item instanceof BuxusPageBackedItemInterface && $item->getPage()->getValue(PropertyTag::ESHOP_EUR_PRICE_INCLUDING_VAT_CZ_TAG()) > 100) {
                    $limit = 1500;
                    if (is_numeric($limit) && $limit > 0) {
                        $cartPriceObject = null;
                        if ($this->cart instanceof RatableItemListInterface) {
                            $cartTotalPrice = $this->cart->getFinalPriceWithoutVatValue();
                            $transportType = $this->cart->getCheckout()->getTransportType();
                            $paymentType = $this->cart->getCheckout()->getPaymentType();

                            if ($paymentType instanceof GenericPaymentType) {
                                $cartTotalPrice = $cartTotalPrice - $paymentType->getFinalPriceWithoutVatValue();
                            }

                            if ($transportType instanceof GenericDeliveryType) {
                                $cartTotalPrice = $cartTotalPrice - $transportType->getFinalPriceWithoutVatValue();
                            }

                            if ($cartTotalPrice >= $limit) {
                                return true;
                            }
                        }
                    }
                }
            } catch (\Exception $e) {
                ErrorReporter::reportSilent($e);
            }
        }

        return false;
    }
}
