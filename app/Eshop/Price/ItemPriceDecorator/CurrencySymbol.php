<?php

namespace App\Eshop\Price\ItemPriceDecorator;

use Buxus\Eshop\Oraculum\AbstractPriceDecorator;
use Buxus\Eshop\Oraculum\RatableItemInterface;
use Buxus\Site\Facade\BuxusSite;

class CurrencySymbol extends AbstractPriceDecorator
{
    /**
     * @param RatableItemInterface $item
     * @throws \Exception
     */
    public function decorate(RatableItemInterface $item)
    {
        $site = BuxusSite::site();

        if ($site == 'cz') {
            foreach($item->getPriceObjects() as $priceObj){
                $priceObj->setCurrency('CZK');
            }
        }
    }
}
