<?php

namespace App\Eshop\Price\ItemPriceDecorator;

use App\Eshop\Product;
use Buxus\Eshop\Item\AbstractShopItem;
use Buxus\Eshop\Oraculum\AbstractPriceDecorator;
use Buxus\Eshop\Oraculum\RatableItemInterface;
use Buxus\Eshop\Price\Price;
use Buxus\Eshop\Price\PriceType;
use Buxus\Util\PropertyTag;
use WebUserAuthentication;

class ExternalPrices extends AbstractPriceDecorator
{
    /**
     * @param RatableItemInterface $item
     * @throws \Exception
     */
    public function decorate(RatableItemInterface $item)
    {
        $priceObj = $item->getPriceObject(PriceType::ITEM_PRICE_WITHOUT_VAT);

        $externalPrice = $item->getExternalData()->getPrice();

        $trace[] = $item->getExternalData()->getTrace() ?? [];


        if (!empty($externalPrice)) {
            $externalPrice = (float)str_replace(',', '.', $externalPrice);

            $priceObj->setValue($externalPrice);

            $item->setPriceObject($priceObj);
            $item->setFinalPriceTagWithoutVat(PriceType::ITEM_PRICE_WITHOUT_VAT);
        }

        $trace = array_unique((array)$trace, SORT_REGULAR);
        $item->setOption('trace', $trace);

        if ($item instanceof AbstractShopItem) {
            $item->setOption('external_initial_price_without_vat', $externalPrice);
        }
    }

    public function isApplicable(RatableItemInterface $item)
    {
        if (!\WebUserAuthentication::isAuthenticated()) return false;

//        $user = \WebUserAuthentication::getUser();
//        if (!$user->canUseIvecoSmallDb() && !$user->canUseIvecoBigDb()) return false;

        if ($item instanceof Product) {
            return !$item->isInStock();
        }

        return false;
    }
}

