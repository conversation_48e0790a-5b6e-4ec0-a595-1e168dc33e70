<?php

namespace App\Eshop\Price\ItemPriceDecorator;

use App\Eshop\Price\RefurbishedParts\Deposit;
use Buxus\Eshop\Item\AbstractShopItem;
use Buxus\Eshop\Item\Delivery\GenericDeliveryType;
use Buxus\Eshop\Oraculum\AbstractPriceDecorator;
use Buxus\Eshop\Oraculum\RatableItemInterface;
use Buxus\Eshop\Price\Price;
use Buxus\Eshop\Price\PriceType;
use Buxus\Site\Facade\BuxusSite;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;

class Currency extends AbstractPriceDecorator
{
    /**
     * @param RatableItemInterface $item
     * @throws \Exception
     */
    public function decorate(RatableItemInterface $item)
    {
        $price = $item->getFinalPriceWithoutVatValue();
        $page = \PageFactory::get(PageIds::getEshopPropertiesSettings());

        $price = $price * (float)$page->getValue(PropertyTag::EXCHANGE_RATE_CZ_TAG());
        $priceObj = new Price(PriceType::ITEM_PRICE_WITHOUT_VAT, $price);
        $priceObj->setCurrency('CZK');

        if ($item instanceof AbstractShopItem) {
            $page = \PageFactory::get(PageIds::getEshopPropertiesSettings());
            $exchangeRate = (float)$page->getValue(PropertyTag::EXCHANGE_RATE_CZ_TAG());

            if (!empty($externalInitialPrice = $item->getOptions()['external_initial_price_without_vat'])) {
                $item->setOption('external_initial_price_without_vat', $externalInitialPrice * $exchangeRate);
            }

            if (!empty($initialPrice = $item->getOptions()['initial_price_without_vat'])) {
                $item->setOption('initial_price_without_vat', $initialPrice * $exchangeRate);
            }
        }

        if ($item instanceof GenericDeliveryType) {
            $price = $item->getFinalPriceValue();
            $price = $price * (float)$page->getValue(PropertyTag::EXCHANGE_RATE_CZ_TAG());
            $priceObj = new Price(PriceType::ITEM_PRICE, $price);
            $priceObj->setCurrency('CZK');
        }

        $item->setPriceObject($priceObj);
        $item->setFinalPriceTagWithoutVat(PriceType::ITEM_PRICE_WITHOUT_VAT);
    }

    public function isApplicable(RatableItemInterface $item)
    {
        if (BuxusSite::site() != 'cz') return false;

        if ($item->getPage()->hasProperty(PropertyTag::ESHOP_EUR_PRICE_INCLUDING_VAT_CZ_TAG())
            && !empty($item->getPage()->getValue(PropertyTag::ESHOP_EUR_PRICE_INCLUDING_VAT_CZ_TAG()))) {
            return false;
        }

        if($item instanceof Deposit && $item->isUsingPriceInCZK()){
            return false;
        }

        return true;
    }
}
