<?php

namespace App\Eshop\Price\ItemPriceDecorator;

use App\Eshop\Product;
use App\Imports;
use App\Supplier;
use Buxus\Cache\Facades\PageBoundCache;
use Buxus\Core\Constants;
use Buxus\Eshop\Item\AbstractShopItem;
use Buxus\Eshop\Oraculum\AbstractPriceDecorator;
use Buxus\Eshop\Oraculum\RatableItemInterface;
use Buxus\Eshop\Price\Price;
use Buxus\Eshop\Price\PriceType;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;
use BuxusSite;

class AugustinGroupBulkyParts extends AbstractPriceDecorator
{

    public function decorate(RatableItemInterface $item)
    {
        $finalPriceObjWithoutVat = $item->getFinalPriceObjectWithoutVat();

        $price = new Price(PriceType::ITEM_PRICE_WITHOUT_VAT, $finalPriceObjWithoutVat->getValue() + $this->getMarginLevelValue($finalPriceObjWithoutVat));
        $item->setPriceObject($price);
    }

    protected function getMarginLevelValue($finalPriceObject)
    {
        $margin = $this->getMargin($finalPriceObject);

        if (empty($margin)) {
            return 0;
        }

        if (BuxusSite::site() == 'sk' || BuxusSite::site() == 'cz') {
            $marginLevelValue = $margin->margin;
        } else {
            $marginLevelValue = $margin->margin_eu;
        }

        if ($finalPriceObject->getCurrency() == 'CZK') {
            return $marginLevelValue * $this->getExchangeRate();
        }

        return $marginLevelValue;
    }

    protected function getExchangeRate()
    {
        return PageBoundCache::get(PageIds::getEshopPropertiesSettings(), 'exchange_rate_cz', function () {
            $page = \PageFactory::get(PageIds::getEshopPropertiesSettings());
            return (float)$page->getValue(PropertyTag::EXCHANGE_RATE_CZ_TAG());
        }, 24 * 60);
    }

    protected function getMargin($finalPriceObject)
    {
        $producer = Supplier::where('producer_ciselnik_id', Imports::AUGUSTIN_GROUP_BULKY_PARTS_ID)->get()->first();

        if ($producer && $producer->price_levels_on) {
            return $producer->marginLevels->where('price_from', '<=', $finalPriceObject->getValue())
                ->sortByDesc('price_from')
                ->first();
        }

        return null;
    }

    public function isApplicable(RatableItemInterface $item)
    {
        if (!$item instanceof Product) {
            return false;
        }

        if ($item->getPage()->getValue(PropertyTag::AUGUSTIN_GROUP_BULKY_PART_TAG()) !== Constants::C_True_Char) {
            return false;
        }

        return parent::isApplicable($item);
    }
}
