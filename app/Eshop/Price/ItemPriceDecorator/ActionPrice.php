<?php

namespace App\Eshop\Price\ItemPriceDecorator;

use App\Eshop\Product;
use Buxus\Eshop\Item\AbstractShopItem;
use Buxus\Eshop\Item\ShopItemInterface;
use Buxus\Eshop\Oraculum\AbstractPriceDecorator;
use Buxus\Eshop\Oraculum\RatableItemInterface;
use Buxus\Eshop\Price\Price;
use Buxus\Eshop\Price\PriceType;
use Buxus\Util\PropertyTag;
use BuxusSite;

class ActionPrice extends AbstractPriceDecorator
{
    /**
     * @param RatableItemInterface $item
     * @throws \Exception
     */
    public function decorate(RatableItemInterface $item)
    {
        $actionPrice = BuxusSite::site() == 'en'
            ? $item->getPage()->getValueCurrentSiteOnly(PropertyTag::ESHOP_EUR_ACTION_PRICE_WITHOUT_VAT_TAG())
            : $item->getPage()->getValue(PropertyTag::ESHOP_EUR_ACTION_PRICE_WITHOUT_VAT_TAG());

        if (!empty($actionPrice)) {
            $actionPrice = (float)str_replace(',', '.', $actionPrice);

            $price = new Price(PriceType::ITEM_PRICE_WITHOUT_VAT, $actionPrice);
            $item->setPriceObject($price);
            $item->setFinalPriceTagWithoutVat(PriceType::ITEM_PRICE_WITHOUT_VAT);

            if ($item instanceof AbstractShopItem) {
                $trace = $item->getOptions()['trace'];
                $trace[] = [
                    'decorator' => ActionPrice::class,
                    'using' => "Action price: {$actionPrice}"
                ];
                $trace = array_unique((array)$trace, SORT_REGULAR);
                $item->setOption('trace', $trace);
            }
        }
    }

    public function isApplicable(RatableItemInterface $item)
    {
        if ($item instanceof Product && $item->isInStock()) {
            return parent::isApplicable($item);
        }

        return false;
    }
}
