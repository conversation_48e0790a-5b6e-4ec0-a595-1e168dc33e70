<?php

namespace App\Eshop\Price\ItemPriceDecorator;

use App\WebUser\WebUser;
use Buxus\Eshop\Item\AbstractShopItem;
use Buxus\Eshop\Oraculum\AbstractPriceDecorator;
use Buxus\Eshop\Oraculum\RatableItemInterface;
use Buxus\Eshop\Price\Price;
use Buxus\Eshop\Price\PriceType;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;

class CustomerGroup extends AbstractPriceDecorator
{

    public function decorate(RatableItemInterface $item)
    {
        $customerGroup = \WebUserAuthentication::getUser()->getCustomerGroup();

        $priceObj = $item->getFinalPriceObjectWithoutVat();
        $groupsPage = \PageFactory::get(PageIds::getCustomerGroupsCharges());

        $trace = $item->getOptions()['trace'];

        if ($customerGroup != 0 && !is_null($customerGroup)) {
            switch ($customerGroup) {
                case WebUser::CUSTOMER_GROUPS['competition']['id']:
                    $priceObj->setValue($priceObj->getValue() + (($priceObj->getValue() / 100) * (float) $groupsPage->getValue(PropertyTag::COMPETITION_CHARGE_TAG())));
                    $using = 'Customer group: ' . WebUser::CUSTOMER_GROUPS['competition']['id'];
                    break;
                case WebUser::CUSTOMER_GROUPS['end_customer']['id']:
                    $priceObj->setValue($priceObj->getValue() + (($priceObj->getValue() / 100) * (float) $groupsPage->getValue(PropertyTag::END_CUSTOMER_CHARGE_TAG())));
                    $using = 'Customer group: ' . WebUser::CUSTOMER_GROUPS['end_customer']['id'];
                    break;
                case WebUser::CUSTOMER_GROUPS['small_vendor']['id']:
                    $priceObj->setValue($priceObj->getValue() + (($priceObj->getValue() / 100) * (float) $groupsPage->getValue(PropertyTag::SMALL_VENDOR_CHARGE_TAG())));
                    $using = 'Customer group: ' . WebUser::CUSTOMER_GROUPS['small_vendor']['id'];
                    break;
                case WebUser::CUSTOMER_GROUPS['car_service']['id']:
                    $priceObj->setValue($priceObj->getValue() + (($priceObj->getValue() / 100) * (float) $groupsPage->getValue(PropertyTag::CAR_SERVICE_CHARGE_TAG())));
                    $using = 'Customer group: ' . WebUser::CUSTOMER_GROUPS['car_service']['id'];
                    break;
                default:
                    $using = 'No customer group used';
                    break;
            }
        }
        else{
            $using = 'No customer group used';
        }

        $trace[] = [
            'decorator' => CustomerGroup::class,
            'using' => $using
        ];

        $trace = array_unique((array)$trace, SORT_REGULAR);
        $item->setOption('trace', $trace);
    }

    public function isApplicable(RatableItemInterface $item)
    {
        if ($item instanceof AbstractShopItem && \WebUserAuthentication::isAuthenticated() && !empty(\WebUserAuthentication::getUser()->getCustomerGroup())) {
            return parent::isApplicable($item);
        }

        return false;
    }
}
