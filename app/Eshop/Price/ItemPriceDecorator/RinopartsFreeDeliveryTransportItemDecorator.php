<?php

namespace App\Eshop\Price\ItemPriceDecorator;

use Buxus\Eshop\FreeDelivery\FreeDeliveryManager;
use Buxus\Eshop\FreeDelivery\Item\FreeDeliveryModifierItemInterface;
use Buxus\Eshop\Item\BuxusPageBackedItemInterface;
use Buxus\Eshop\Item\Delivery\GenericDeliveryType;
use Buxus\Eshop\Oraculum\RatableItemInterface;
use Buxus\Eshop\Oraculum\RatableItemListInterface;

class RinopartsFreeDeliveryTransportItemDecorator extends \Buxus\Eshop\FreeDelivery\ItemPriceDecorator\FreeDeliveryTransportItemDecorator
{
    public function isApplicable(RatableItemInterface $item)
    {
        if ($item instanceof FreeDeliveryModifierItemInterface
            && $item->disablesFreeDeliveryForEntireOrder()) {
            return false;
        }

        if ($item instanceof GenericDeliveryType) {
            /** @var FreeDeliveryManager $freeDeliveryManager */
            $freeDeliveryManager = app('buxus:free-delivery:manager');

            if ($freeDeliveryManager->checkCartForExplicitFreeDeliveryItems($this->cart)) {
                return true;
            }

            try {
                $limit = $this->getFreeDeliveryLimit();
                if (is_numeric($limit)) {
                    $cartPriceObject = null;
                    if ($this->cart instanceof RatableItemListInterface) {
                        $cartTotalPrice = $freeDeliveryManager->getCartTotalPrice($this->cart);
                        if ($cartTotalPrice >= $limit) {
                            return true;
                        }
                    }
                }
            } catch (\Exception $e) {
                // do nothing
            }

            try {
                if ($item instanceof BuxusPageBackedItemInterface) {
                    $limit = $item->getPage()->getValueWithoutFallback('minimal_price_for_free_delivery');
                    if (is_numeric($limit) && $limit > 0) {
                        $cartPriceObject = null;
                        if ($this->cart instanceof RatableItemListInterface) {
                            $cartTotalPrice = $freeDeliveryManager->getCartTotalPrice($this->cart);
                            if ($cartTotalPrice >= $limit) {
                                return true;
                            }
                        }
                    }
                }
            } catch (\Exception $e) {
                // do nothing
            }
        }

        return false;
    }
}
