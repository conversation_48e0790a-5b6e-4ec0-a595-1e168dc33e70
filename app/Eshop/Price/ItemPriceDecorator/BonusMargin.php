<?php

namespace App\Eshop\Price\ItemPriceDecorator;

use Buxus\Eshop\Item\AbstractShopItem;
use Buxus\Eshop\Oraculum\AbstractPriceDecorator;
use Buxus\Eshop\Oraculum\RatableItemInterface;
use Buxus\Eshop\Price\PriceType;

class BonusMargin extends AbstractPriceDecorator
{
    public function decorate(RatableItemInterface $item)
    {
        $priceObj = $item->getPriceObject(PriceType::ITEM_PRICE_WITHOUT_VAT);

        $bonusMargin = (float)str_replace(',', '.', \WebUserAuthentication::getUser()->getCustomOption('bonus_margin'));

        if (!empty($bonusMargin) && is_numeric($bonusMargin)) {
            $priceObj->setValue($priceObj->getValue() + ($priceObj->getValue() * $bonusMargin / 100));

            $trace = $item->getOptions()['trace'];
            $trace[] = [
                'decorator' => BonusMargin::class,
                'using' => "Price with bonus margin: {$priceObj->getValue()}"
            ];
            $trace = array_unique((array)$trace, SORT_REGULAR);
            $item->setOption('trace', $trace);
        }
    }

    public function isApplicable(RatableItemInterface $item)
    {
        if ($item instanceof AbstractShopItem
            && \WebUserAuthentication::isAuthenticated()
            && !empty(\WebUserAuthentication::getUser()->getCustomOption('bonus_margin'))) {
            return parent::isApplicable($item);
        }

        return false;
    }
}
