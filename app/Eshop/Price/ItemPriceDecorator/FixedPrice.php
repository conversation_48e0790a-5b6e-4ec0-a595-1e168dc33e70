<?php

namespace App\Eshop\Price\ItemPriceDecorator;

use Buxus\Eshop\Item\AbstractShopItem;
use Buxus\Eshop\Item\ShopItemInterface;
use Buxus\Eshop\Oraculum\AbstractPriceDecorator;
use Buxus\Eshop\Oraculum\RatableItemInterface;
use Buxus\Eshop\Price\Price;
use Buxus\Eshop\Price\PriceType;
use Buxus\Util\PropertyTag;
use BuxusSite;

class FixedPrice extends AbstractPriceDecorator
{
    /**
     * @param RatableItemInterface $item
     * @throws \Exception
     */
    public function decorate(RatableItemInterface $item)
    {
        $fixedPriceObj = $item->getPriceObject(PriceType::ITEM_PRICE_WITHOUT_VAT);
        $fixedPrice = $item->getPage()->getValue(PropertyTag::ESHOP_EUR_FIXED_PRICE_WITHOUT_VAT_TAG());
        if (BuxusSite::site() == 'cz') {
            $fixedPrice = $item->getPage()->getValue(PropertyTag::ESHOP_CZK_FIXED_PRICE_WITHOUT_VAT_TAG());
        }

        if (!empty($fixedPrice)) {
            $fixedPrice = (float)str_replace(',', '.', $fixedPrice);
            $fixedPriceObj->setValue($fixedPrice);

            $item->setPriceObject($fixedPriceObj);
            $item->setFinalPriceTagWithoutVat(PriceType::ITEM_PRICE_WITHOUT_VAT);

            if ($item instanceof AbstractShopItem) {
                $trace = $item->getOptions()['trace'];

                $trace[] = [
                    'decorator' => FixedPrice::class,
                    'using' => "Fixed price: {$fixedPrice}"
                ];

                $trace = array_unique((array)$trace, SORT_REGULAR);
                $item->setOption('trace', $trace);
            }
        }
    }
}
