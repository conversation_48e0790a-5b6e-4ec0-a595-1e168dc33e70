<?php

namespace App\Eshop\Price\ItemPriceDecorator;

use App\Coefficient;
use App\Eshop\Price\ExternalPriceStrategy;
use App\Eshop\Price\MarginStrategy;
use App\Supplier;
use Buxus\Eshop\Item\AbstractShopItem;
use Buxus\Eshop\Oraculum\AbstractPriceDecorator;
use Buxus\Eshop\Oraculum\RatableItemInterface;
use Buxus\Eshop\Price\Price;
use Buxus\Eshop\Price\PriceType;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;
use BuxusSite;

class Margin extends AbstractPriceDecorator
{
    /**
     * @param RatableItemInterface $item
     * @throws \Exception
     */
    public function decorate(RatableItemInterface $item)
    {
        $strategy = new MarginStrategy($item);
        $finalPrice = $strategy->getData()->getPrice();

        $trace = $item->getOptions()['trace'];
        $trace[] = $strategy->getData()->getTrace();
        $trace = array_unique((array)$trace, SORT_REGULAR);
        $item->setOption('trace', $trace);

        $priceObject = new Price(PriceType::ITEM_PRICE_WITHOUT_VAT, $finalPrice);

        $item->setPriceObject($priceObject);
        $item->setFinalPriceTagWithoutVat(PriceType::ITEM_PRICE_WITHOUT_VAT);
    }

    public function isApplicable(RatableItemInterface $item)
    {
        return \WebUserAuthentication::isAuthenticated() && $item instanceof AbstractShopItem;
    }
}
