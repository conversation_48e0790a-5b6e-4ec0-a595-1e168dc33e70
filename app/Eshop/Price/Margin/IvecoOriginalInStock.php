<?php

namespace App\Eshop\Price\Margin;

use App\Eshop\Price\ItemPriceDecorator\ExternalPrices;
use App\Eshop\Product;
use App\Supplier;
use Buxus\Eshop\Item\AbstractShopItem;
use Buxus\Util\PropertyTag;

class IvecoOriginalInStock extends BaseMargin
{
    public function shouldUse()
    {
        $item = $this->item;

        if (!$item instanceof Product) {
            return false;
        }

        if(!($item->isInStock() && $item->isIvecoOriginal())){
            return false;
        }

        return true;
    }

    public function getCoefficient()
    {
        $user = \WebUserAuthentication::getUser();
        return $this->getValidatedCoefficient($user->getCustomOption('iveco_original_in_stock_coefficient'));
    }

    public function getProducerCiselnikId()
    {
        return Supplier::where('name', 'IVECO original')->first()->producer_ciselnik_id;
    }
}
