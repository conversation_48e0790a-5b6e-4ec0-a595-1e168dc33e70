<?php

namespace App\Eshop\Price\Margin;

use App\Coefficient;
use App\Eshop\Price\ItemPriceDecorator\ExternalPrices;
use App\Eshop\Price\ItemPriceDecorator\Margin;
use App\Supplier;
use Buxus\Eshop\Item\AbstractShopItem;
use Buxus\Eshop\Price\PriceType;
use Buxus\Util\PropertyTag;
use BuxusSite;

abstract class BaseMargin
{
    protected $item;
    protected $propertyTag;

    public function __construct(AbstractShopItem $item)
    {
        $this->item = $item;
    }

    public function shouldUse()
    {
        return true;
    }

    public function canUse()
    {
        return true;
    }

    public function getTrace()
    {
        $using = (!empty($this->getMarginLevel())) ? $this->getMarginLevel()->toArray() : null;

        return [
            'decorator' => Margin::class,
            'using' => $using,
        ];
    }

    protected function getPriceType()
    {
        $trace = $this->item->getOptions()['trace'];

        foreach ((array)$trace as $arr) {
            if ($arr['decorator'] == ExternalPrices::class) {
                return $arr['using'];
            }
        }

        return null;
    }

    protected function getPriceObject()
    {
        return $this->item->getPriceObject(PriceType::ITEM_PRICE_WITHOUT_VAT);
    }

    protected function getMarginLevel()
    {
        $priceObject = $this->getPriceObject();
        $producerCiselnikId = $this->getProducerCiselnikId();
        $producer = Supplier::where('producer_ciselnik_id', $producerCiselnikId)->first();

        if ($producer && $producer->price_levels_on) {
            return $producer->marginLevels->where('price_from', '<=', $priceObject->getValue())
                ->sortByDesc('price_from')
                ->first();
        }

        return null;
    }

    protected function getMarginLevelValue()
    {
        $marginLevel = $this->getMarginLevel();

        if (!empty($marginLevel)) {
            if (BuxusSite::site() == 'sk' || BuxusSite::site() == 'cz') {
                return $marginLevel->margin;
            } else {
                return $marginLevel->margin_eu;
            }
        }

        return 1;
    }

    public function getPrice()
    {
        $priceObject = $this->getPriceObject();

        return (($priceObject->getValue() + (($priceObject->getValue() / 100)) * $this->getMargin()) * $this->getCoefficient()) * $this->getGeneralCoefficient();
    }

    protected function getMargin()
    {
        if (empty($this->getMarginLevel())) {
            return 1;
        }

        return $this->getMarginLevelValue();
    }

    protected function getCoefficient()
    {
        $user = \WebUserAuthentication::getUser();

        $coefficient = Coefficient::getCoefficients($user->getUserId(), $this->getProducerCiselnikId());
        return $this->getValidatedCoefficient($coefficient->coefficient);
    }

    protected function getGeneralCoefficient()
    {
        $user = \WebUserAuthentication::getUser();

        return $this->getValidatedCoefficient($user->getCustomOption('margin_coefficient'));
    }

    protected function getValidatedCoefficient($marginCoefficient)
    {
        if ($marginCoefficient <= 0 || is_null($marginCoefficient)) {
            $marginCoefficient = 1;
        } else {
            $marginCoefficient = (float)$marginCoefficient / 100;
            $marginCoefficient += 1;
        }

        return $marginCoefficient;
    }

    protected function getProducerCiselnikId()
    {
        return $this->item->getPage()->getValue(PropertyTag::ESHOP_ROLLER_PRODUCER_TAG());
    }
}
