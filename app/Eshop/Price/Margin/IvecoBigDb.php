<?php

namespace App\Eshop\Price\Margin;

use App\Eshop\Price\ItemPriceDecorator\ExternalPrices;
use App\Supplier;
use Buxus\Eshop\Item\AbstractShopItem;
use Buxus\Util\PropertyTag;

class IvecoBigDb extends BaseMargin
{
    public function shouldUse()
    {
        $priceType = $this->getPriceType();
        return str_starts_with($priceType, PropertyTag::ESHOP_EUR_IVECO_BIG_DB_PRICE_WITHOUT_VAT_TAG());
    }

    public function getCoefficient()
    {
        $user = \WebUserAuthentication::getUser();
        return $this->getValidatedCoefficient($user->getCustomOption('margin_coefficient_iveco_big_db'));
    }

    public function getProducerCiselnikId()
    {
        return Supplier::where('name', 'IVECO big db')->first()->producer_ciselnik_id;
    }
}
