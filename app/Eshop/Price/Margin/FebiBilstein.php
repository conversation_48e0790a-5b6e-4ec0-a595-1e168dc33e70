<?php

namespace App\Eshop\Price\Margin;

use App\Eshop\Price\ItemPriceDecorator\ExternalPrices;
use App\Supplier;
use Buxus\Eshop\Item\AbstractShopItem;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;

class FebiBilstein extends BaseMargin
{
    public function canUse()
    {
        return $this->getPriceType() == PropertyTag::FEBI_BILSTEIN_PRICE_WITHOUT_VAT_TAG();
    }

    public function getProducerCiselnikId()
    {
        return Supplier::where('name', 'FEBI-BILSTEIN-SUPPLIER')->first()->producer_ciselnik_id;
    }
}
