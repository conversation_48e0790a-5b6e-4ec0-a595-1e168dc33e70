<?php

namespace App\Eshop\Price\Margin;

use App\Supplier;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;

class IvecoOriginal extends BaseMargin
{
    public function canUse()
    {
        $priceType = $this->getPriceType();
        return str_starts_with($priceType, PropertyTag::ESHOP_EUR_IVECO_PRICE_WITHOUT_VAT_TAG());
    }

    public function getProducerCiselnikId()
    {
        return Supplier::where('name', 'IVECO nákupné ceny')->first()->producer_ciselnik_id;
    }

    public function getCoefficient()
    {
        $user = \WebUserAuthentication::getUser();
        return $this->getValidatedCoefficient($user->getCustomOption('margin_coefficient_iveco_small_db'));
    }
}
