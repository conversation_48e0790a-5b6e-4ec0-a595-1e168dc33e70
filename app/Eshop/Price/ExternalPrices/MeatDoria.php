<?php

namespace App\Eshop\Price\ExternalPrices;

use Buxus\Eshop\Item\AbstractShopItem;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;

class MeatDoria extends BaseExternalPrice
{
    protected $item;
    protected $propertyTag;

    public function __construct(AbstractShopItem $item)
    {
        $this->propertyTag = PropertyTag::MEAT_DORIA_PRICE_WITHOUT_VAT_TAG();
        parent::__construct($item);
    }

    public function getPrice()
    {
        $transportSurcharge = $this->getTransportSurcharge();
        $coefficient = ($transportSurcharge + 100) / 100;

        $price = parent::getPrice();
        return $price * $coefficient;
    }

    protected function getTransportSurcharge()
    {
        $settingsPage = \PageFactory::get(PageIds::getMeatDoriaSettings());
        $transportSurcharge = $settingsPage->getValue(PropertyTag::TRANSPORT_SURCHARGE_TAG());

        if (empty($transportSurcharge)) {
            $transportSurcharge = 0;
        }

        return $transportSurcharge;
    }
}
