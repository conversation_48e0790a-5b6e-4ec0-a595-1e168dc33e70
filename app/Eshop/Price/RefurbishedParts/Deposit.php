<?php

namespace App\Eshop\Price\RefurbishedParts;

use App\Eshop\AdderAwareInterface;
use App\Eshop\Product;
use App\Eshop\Traits\AdderAwareTrait;
use Buxus\Eshop\Contracts\PriceViewer;
use Buxus\Eshop\CustomCartItem\CustomCartItemInterface;
use Buxus\Eshop\Item\BuxusPageBackedItemInterface;
use Buxus\Eshop\Item\ConfigurableProductItemInterface;
use Buxus\Eshop\Item\RenderableShopItemInterface;
use Buxus\Eshop\Item\ShopItemInterface;
use Buxus\Eshop\Item\SpecialShopItemInterface;
use Buxus\Eshop\Oraculum\OraculumContext;
use Buxus\Eshop\Oraculum\RatableItemInterface;
use Buxus\Eshop\Oraculum\RatableItemTrait;
use Buxus\Eshop\Price\PriceInitItemInterface;
use Buxus\Eshop\View;
use Buxus\Page\PageInterface;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;
use BuxusSite;

class Deposit implements PriceInitItemInterface,
    ShopItemInterface,
    RatableItemInterface,
    SpecialShopItemInterface,
    RenderableShopItemInterface,
    BuxusPageBackedItemInterface,
    AdderAwareInterface,
    CustomCartItemInterface
{
    use RatableItemTrait;
    use AdderAwareTrait;

    public const TAG = 'deposit';

    //refurbished product that has its deposit set
    protected $product;
    protected $page;
    protected $page_id;

    public function __construct(Product $product)
    {
        $this->product = $product;
        $this->page = \PageFactory::get(PageIds::getDeposit());
        $this->page_id = $this->page->getPageId();
        $this->setOption('deposited_product_id', $this->product->getPageId());
    }

    public function getInitialPriceIncludingVatValue()
    {
        return $this->getInitialPriceIncludingVatValue() * config('buxus_eshop.vat_rate');
    }

    public function getInitialPriceWithoutVatValue()
    {
        return $this->product->getPage()->getValue(PropertyTag::ESHOP_EUR_DEPOSIT_AMOUNT_WITHOUT_VAT_TAG());
    }

    public function getTag()
    {
        return self::TAG . '_' . $this->product->getPageId();
    }

    public function isUsingPriceInCZK()
    {
        if (BuxusSite::site() == 'cz') {
            return !empty($this->product->getPage()->getValueCurrentSiteOnly(PropertyTag::ESHOP_EUR_DEPOSIT_AMOUNT_WITHOUT_VAT_TAG()));
        }

        return false;
    }

    public function __clone()
    {
        $newPriceObjects = [];

        if (!empty($this->price_objects)) {
            foreach ($this->price_objects as $key => $priceObject) {
                $newPriceObjects[$key] = clone $priceObject;
            }
        }

        $this->price_objects = $newPriceObjects;
    }


    public function addAmount($amount)
    {
        $amount = (int)$amount;

        if ($amount > 0) { // The amount is more then zero
            $this->amount += $amount;
        }
    }

    public function getAmount()
    {
        return $this->amount;
    }

    public function setAmount($amount)
    {
        $amount = (int)$amount;
        $this->amount = (($amount > 0) ? $amount : 1);
    }


    public function getName()
    {
        return \Trans::strParamed('eshop', 'Záloha za vratný diel %s', [$this->product->getPage()->getValue(PropertyTag::TITLE_TAG())]);
    }

    public function recount()
    {
        app('buxus.eshop.oraculum')->runItemDecorators($this, OraculumContext::CATALOG);
    }

    public function setOption($name, $value)
    {
        $this->options[$name] = $value;
    }

    public function getOptions()
    {
        $options = $this->options;

        if ($this instanceof ConfigurableProductItemInterface) {
            if (count($this->configuration)) {
                foreach ($this->configuration as $key => $value) {
                    $options['product_config_' . $key] = $value;
                }
            }
        }

        return $options;
    }

    public function isOrderable()
    {
        return true;
    }

    public function isEditable()
    {
        return
            $this->canBeManuallyAdded() &&
            $this->amountCanBeChanged() &&
            $this->canBeManuallyRemoved();
    }

    public function canBeManuallyAdded()
    {
        return false;
    }

    public function amountCanBeChanged()
    {
        return false;
    }

    public function canBeManuallyRemoved()
    {
        return false;
    }

    public function shouldBeIncludedInTotalPriceSum()
    {
        return true;
    }

    public function shouldBeIncludedInOrder()
    {
        return true;
    }

    public function renderMiniCartContents()
    {
        $view = new View();
        $view->product = $this;
        return $view->render('deposit/minicart_contents.phtml');
    }

    public function renderCheckoutContents()
    {
        $view = new View();
        $view->product = $this;
        return $view->render('deposit/checkout_contents.phtml');
    }

    public function renderCheckoutSummaryContents()
    {
        $view = new View();
        $view->product = $this;
        return $view->render('deposit/checkout_summary_contents.phtml');
    }

    public function getItemDataInfo()
    {
        $price_viewer = app(PriceViewer::class);
        return array(
            'item-amount' => $this->getAmount(),
            'item-total-price-no-vat' => $this->getFinalPriceWithoutVatValue(),
            'item-total-price' => $this->getFinalPriceValue(),
            'item-total-price-no-vat-formated' => $price_viewer->formatPrice($this->getFinalPriceObjectWithoutVat()),
            'item-total-price-formated' => $price_viewer->formatPrice($this->getFinalPriceObject()),
        );
    }

    public function getImage()
    {
        return;
    }

    public function getUrl()
    {
        return;
    }

    public function getPageId()
    {
        return $this->page_id;
    }

    public function getPage()
    {
        return $this->page;
    }

    public function setPage(PageInterface $page)
    {
        $this->page = $page;
    }

    public function setPageId($page_id)
    {
        $this->page_id = $page_id;
    }

    public function getCustomCartItemTag(): string
    {
        return $this->getTag();
    }

    public function getProducer()
    {
        return '';
    }

    public function renderAvailability()
    {
        return '';
    }
}
