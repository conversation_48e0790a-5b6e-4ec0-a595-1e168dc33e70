<?php

namespace App\Eshop\Traits;

use App\Authentication\FakeAuthentication;
use WebUserAuthentication;

trait AdderAwareTrait
{
    /**
     * @return int|null
     */
    public function getAddedBy(): ?int
    {
        return $this->getOptions()['added_by'];
    }

    /**
     * @param int|null $addedBy
     */
    public function setAddedBy(?int $addedBy): void
    {
        $this->setOption('added_by', $addedBy);
    }

    /**
     * @return bool
     */
    public function shouldBeVisible(): bool
    {
        if (empty($this->getAddedBy())) {
            return true;
        }

        if (!WebUserAuthentication::isAuthenticated()) {
            return false;
        }

        if ($this->getAddedBy() == WebUserAuthentication::getUserId()) {
            return true;
        }

        $fakeAuth = new FakeAuthentication();

        if (!empty($fakeAuth->getSuperuser())) {
            return true;
        }

        return false;
    }

    public function logAddedBy(): void
    {
        $fakeAuth = new FakeAuthentication();

        if (!empty($fakeAuth->getSuperuser())) {
            $this->setAddedBy($fakeAuth->getSuperuser()->getUserId());
            return;
        }


        if (WebUserAuthentication::isAuthenticated()) {
            $this->setAddedBy(WebUserAuthentication::getUserId());
        }
    }
}