<?php

namespace App\Eshop\ShoppingCart\Form\Element;

use Eshop\ShoppingCart\Form\Element\ModernTextElement;

class ModernTextElementReadonly extends ModernTextElement
{
    public function init()
    {
        $decorators[] = array(
            array('CustomDiv' => 'ViewScript'),
            array(
                'viewScript' => 'cart/decorator/modern-input-readonly.phtml',
            )
        );

        $decorators[] = array(
            array('WrapperDiv' => 'HtmlTag'),
            array(
                'tag' => 'div',
                'class' => 'modern-input',
            )
        );

        $this->setDecorators($decorators);
    }
}
