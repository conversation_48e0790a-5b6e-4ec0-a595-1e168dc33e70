<?php

namespace App\Eshop;

use App\Page\BasicPage;
use Buxus\Util\PageIds;
use Buxus\Util\PageTypeID;
use Buxus\Util\PropertyTag;
use Illuminate\Support\Collection;

class CategoryManager
{
    public function getMainCategories()
    {
        return \PageFactory::builder()
            ->whereParent(PageIds::getEshopCatalog())
            ->wherePageType(PageTypeID::ESHOP_CATEGORY_ID)
            ->get();
    }

    public function getSubcategories($categoryId)
    {
        return \PageFactory::builder()
            ->whereParent($categoryId)
            ->wherePageType(PageTypeID::ESHOP_SUBCATEGORY_ID())
            ->get();
    }

    public function getResultsAsArray(Collection $collection)
    {
        $categories = [];

        foreach ($collection as $page) {
            $categories[$page->getPageId()] = $page->getValue(PropertyTag::TITLE_TAG());
        }

        return $categories;
    }
}
