<?php

namespace App\Eshop;

use Buxus\Core\Constants;
use Buxus\Substrate\Menu\LeftEshopMenu;
use Buxus\Substrate\Menu\MenuItem;
use Buxus\TemplateFunctions;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;

class CategoriesMenu extends LeftEshopMenu
{
    protected function setFirstLevel()
    {
        $page = \PageFactory::get(PageIds::getMenu());
        $this->first_level_page_list = $page->getValue(PropertyTag::PRODUCT_MENU_CATEGORY_LIST_TAG());
        $this->first_level_offset = 0;
    }

    /**
     * Create structure of menu
     *
     * @param array $first_level_page_list
     * @param int $level_offset
     */
    protected function createStructure($first_level_page_list, $level_offset = 0)
    {
        foreach ($first_level_page_list as $item) {
            $item_id = $item->getToPageId();
            $page = \PageFactory::get($item_id);
            if ($page && $page->getValue(PropertyTag::SHOW_CATEGORY_TAG()) == Constants::C_False_Char) {
                continue;
            }
            $first_level_item = new MenuItem($item_id);


            $second_level_page_list = TemplateFunctions::SelectOneTypeChildren($item_id, $this->show_page_type_ids);
            foreach ($second_level_page_list as $item) {
                $item_id = $item['page_id'];

                $second_level_item = new MenuItem($item_id);


                $third_level_page_list = TemplateFunctions::SelectOneTypeChildren($item_id, $this->show_page_type_ids);
                foreach ($third_level_page_list as $item) {
                    $item_id = $item['page_id'];
                    $third_level_item = new MenuItem($item_id);

                    if ((isset($this->stack[$level_offset + 3]) && ($this->stack[$level_offset + 3]['page_id'] == $item_id))) { // The item is selected
                        $third_level_item->setSelected(true);
                    }

                    $second_level_item->addItem($third_level_item);
                }

                if ((isset($this->stack[$level_offset + 2]) && ($this->stack[$level_offset + 2]['page_id'] == $item_id))) { // Render third level
                    $second_level_item->setSelected(true);
                }

                $first_level_item->addItem($second_level_item);
            }

            if ((isset($this->stack[$level_offset + 1]) && ($this->stack[$level_offset + 1]['page_id'] == $item_id))) { // Render second level
                $first_level_item->setSelected(true);
            }

            $this->addItem($first_level_item);
        }
    }
}
