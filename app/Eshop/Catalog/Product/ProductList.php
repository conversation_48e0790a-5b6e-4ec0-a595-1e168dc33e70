<?php

namespace App\Eshop\Catalog\Product;

use App\Eshop\Product;
use App\FulltextSearch\FulltextSearch;
use Buxus\Core\Constants;
use Buxus\TemplateFunctions;
use Buxus\Util\ConfigUtils;
use Buxus\Util\PageIds;
use Buxus\Util\PageTypeID;
use Buxus\Util\PropertyID;
use Buxus\Util\PropertyTag;
use Buxus\WebUser\Facades\WebUserAuthentication;
use BuxusMVC;
use Cache;
use ProductFactory;
use Zend_Controller_Request_Abstract;

class ProductList extends \Eshop\Catalog\Product\ProductList
{
    public $hidden;

    /**
     * @param Zend_Controller_Request_Abstract $request
     * @param int $page_id
     */
    public function __construct(Zend_Controller_Request_Abstract $request, $page_id = null)
    {
        $page = BuxusMVC::page();

        $valid_page_list_ids = [];

        if (($page->getPageId() == PageIds::getOilsAndFluids(false) || TemplateFunctions::isDescendantOf($page->getPageId(), PageIds::getOilsAndFluids(false))) && WebUserAuthentication::isAuthenticated()) {
            [$page_list_ids, $valid_page_list_ids, $products] = Cache::remember('product_catalog_1' . $page->getPageId(), 30 * 60, function () use ($page) {
                $page_list_ids = $this->selectProductsIds($page->getPageId())->get();
                $products = $this->processProductSearchLogic($page_list_ids);

                $valid_page_list_ids = array_keys($products['result']->getProducts());

                return [$page_list_ids, $valid_page_list_ids, $products];
            });
        } elseif ($page->getValue(PropertyTag::SHOW_ALL_PRODUCTS_TAG()) == Constants::C_True_Char
            && WebUserAuthentication::isAuthenticated()
        ) {
            $page_list_ids = $this->selectProductsIds($page->getPageId())->get();

            foreach ($page_list_ids as $page_list_id) {
                if (ProductFactory::get($page_list_id->page_id)->isOrderable()) {
                    $valid_page_list_ids[] = $page_list_id->page_id;
                }
            }
        } else {
            if (is_null($page_id)) {
                $page_id = $page->getPageId();
            }

            $page_list_ids = $this->selectProductsIds($page_id);

            if (\WebUserAuthentication::isAuthenticated()) {
                $page_list_ids = $page_list_ids->limit(50)->get();
                foreach ($page_list_ids as $page_list_id) {
                    if (count($valid_page_list_ids) == 20) {
                        break;
                    }
                    if (ProductFactory::get($page_list_id->page_id)->isOrderable()) {
                        $valid_page_list_ids[] = $page_list_id->page_id;
                    }
                }
            } else {
                $page_list_ids = $page_list_ids->paginate(20);
                $valid_page_list_ids = collect($page_list_ids->items())->pluck('page_id')->toArray();
            }
        }

        foreach ($valid_page_list_ids as $page_item) {
            $page_list[] = [
                'product' => \ProductFactory::get($page_item)
            ];
        }

        $this->pagination = $page_list_ids;
        $this->items = $page_list;
        if (isset($products['hidden'])) {
            $this->hidden = $products['hidden']->getProducts();
        }
    }


    private function selectProductsIds($page_id)
    {
        $page_types = ConfigUtils::pageTypeIdList('krabica.product_list.allowed_page_types');

        $page_list_ids = \DB::table('tblPages as page')
            ->select('page.page_id')
            ->whereIn('page.page_type_id', $page_types)
            ->where('page.page_state_id', Constants::C_active_page_state_id)
            ->leftJoin('tblPages as photo', function ($q) {
                $q->on('page.page_id', '=', 'photo.parent_page_id')
                    ->where('photo.page_type_id', '=', PageTypeID::PHOTO_ID())
                    ->where('photo.page_state_id', '=', Constants::C_active_page_state_id);
            })
            ->join('tblLinkProperties as linked', 'page.page_id', '=', 'linked.from_page_id')
            ->where('linked.property_id', '=', PropertyID::CATEGORIZATION_ID())
            ->where('linked.to_page_id', '=', $page_id)
            ->orderByDesc('photo.page_type_id')
            ->groupBy('page.page_id');

        return $page_list_ids;
    }

    protected function processProductSearchLogic($pageListIds)
    {
        $fulltextSearch = new FulltextSearch($this->getProductsFromResult($pageListIds));
        $items = $fulltextSearch->processItems();
        $result = $items['result']->getProducts();

        $availabilityOrder = [
            Product::AVAILABILITY_AVAILABLE => 0,
            Product::AVAILABILITY_AT_SUPPLIER => 1,
            Product::AVAILABILITY_AT_REQUEST => 2,
            Product::AVAILABILITY_NOT_AVAILABLE => 3,
        ];

        usort($result, function ($a, $b) use ($availabilityOrder) {
            return $availabilityOrder[$a->getAvailability()] <=> $availabilityOrder[$b->getAvailability()];
        });

        $items['result']->setProducts($result);

        return $items;
    }

    protected function getProductsFromResult($pageListIds): array
    {
        $products = [];

        foreach ($pageListIds as $item) {
            $product = \ProductFactory::get($item->page_id);

            if (!$product->isOrderable()) {
                continue;
            }

            $products[$item->page_id] = $product;

            foreach ($products as $product) {
                $groupedProducts = $product->getPairedProducts();

                foreach ((array)$groupedProducts as $groupedProduct) {
                    if (!$groupedProduct->isOrderable()) {
                        continue;
                    }

                    $products[$groupedProduct->getPageId()] = $groupedProduct;
                }
            }
        }

        return $products;
    }

    public function countItems()
    {
        return count((array)$this->getItems());
    }
}
