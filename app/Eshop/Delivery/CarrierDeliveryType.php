<?php

namespace App\Eshop\Delivery;

use Buxus\Core\Constants;
use Buxus\Eshop\Item\Delivery\GenericDeliveryType;
use Buxus\TemplateFunctions;
use Buxus\Util\PageIds;
use Buxus\Util\PageTypeID;
use Buxus\Util\PropertyTag;
use BuxusSite;
use Zend_Form;
use Zend_Form_Element_Hidden;
use Buxus\Eshop\Contracts\Checkout;
use Zend_Form_Element;

class CarrierDeliveryType extends RinopartsGenericDeliveryType
{
    public const TAG = 'courier';
    public const SUBDELIVERY_TAG = 'subdelivery_tag';

    public function getFormContent()
    {
        if (!self::shouldInjectForm()) {
            return parent::getFormContent();
        }

        $childrenIds = TemplateFunctions::SelectAllOneTypeChildren($this->getPageId(), PageTypeID::DELIVERY_SUBTYPE_ID());

        $deliveries = [];

        foreach ($childrenIds as $childrenId) {
            $page = \PageFactory::get($childrenId);
            if ($page->getValue(PropertyTag::HIDE_ON_DOMAIN_TAG()) !== Constants::C_True_Char) {
                $deliveries[] = [
                    'title' => $page->getValue(PropertyTag::TITLE_TAG()),
                    'tag' => $page->getValue(PropertyTag::ESHOP_TAG_TAG()),
                ];
            }
        }

        return view('delivery.form-content', [
            'deliveries' => $deliveries
        ]);
    }

    public function injectItemCustomForm(Zend_Form $form)
    {
        if (!self::shouldInjectForm()) {
            return parent::injectItemCustomForm($form);
        }

        $subdelivery_tag = new Zend_Form_Element_Hidden(self::SUBDELIVERY_TAG);
        $subdelivery_tag->setDecorators(['ViewHelper']);
        $form->addElement($subdelivery_tag);
    }

    public function checkoutCleanupHandler(Checkout $checkout, $values)
    {
        $checkout->setOption(self::SUBDELIVERY_TAG, null);
    }

    public function checkoutSaveHandler(Checkout $checkout, $values)
    {
        if (!self::shouldInjectForm()) {
            return;
        }

        $checkout->setOption(self::SUBDELIVERY_TAG, $values[self::SUBDELIVERY_TAG]);
    }

    public function isValid($data, Zend_Form $form, Zend_Form_Element $element)
    {
        if (empty($data[self::SUBDELIVERY_TAG]) && self::shouldInjectForm()) {
            return false;
        }

        return parent::isValid($data, $form, $element);
    }

    public function getSubdeliveryPage($subdeliveryTag)
    {
        $subdeliveryPage = \PageFactory::builder()
            ->whereParent($this->getPageId())
            ->wherePropertyValue(PropertyTag::ESHOP_TAG_TAG(), '=', $subdeliveryTag)
            ->first();

        return $subdeliveryPage;
    }

    public static function shouldInjectForm()
    {
        return BuxusSite::site() != 'en';
    }

    public function getDescription()
    {
        if (!self::shouldInjectForm()) {
            return \Trans::str('cart', 'Cena za dopravu bude vypočítaná osobitne');
        }

        return parent::getDescription();
    }
}
