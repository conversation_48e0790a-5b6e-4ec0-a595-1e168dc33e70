<?php

namespace App\Eshop\Cart;

use App\Eshop\Product;

class AvailabilityRendererOrder extends AvailabilityRenderer
{
    public function render()
    {
        return view('cart.availability-order', [
            'total' => $this->product->getFullStockBalance(),
            'shouldBeShownAsUnavailable' => (!(
                    $this->product->isIvecoBigDb()
                    || $this->product->isIvecoSmallDbStock()
                    || $this->product->isOnixProduct()
                    || $this->product->isMotorserviceSupplier()
                ))
                || ($this->product->isCeiSupplier()),
            'stock' => $this->getStockBalance(),
            'stock_actual' => $this->getStockBalanceActual(),
            'supplier' => $this->getSupplierBalance(),
            'amount' => $this->amount,
        ]);
    }

    protected function getStockBalanceActual()
    {
        return $this->product->getStockBalance();
    }
}
