<?php

namespace App\Eshop\Cart;

use App\Eshop\AdderAwareInterface;
use App\Events\CartRemoveEvent;
use App\PriceOffers\PriceOffer;
use App\PriceOffers\PriceOfferManager;
use Buxus\Error\ErrorReporter;
use Buxus\Eshop\Contracts\CartCommandProcessor as CartCommandProcessorContract;
use Buxus\Eshop\Event\CartAddProductEvent;
use Buxus\Eshop\Event\CartChangeProductAmountEvent;
use Buxus\Eshop\Item\ConfigurableProductItemInterface;
use Buxus\Eshop\Item\ShopItemInterface;
use Buxus\Eshop\ShoppingCartFactory;
use Buxus\Event\BuxusEvent;
use Buxus\Stdlib\ArrayUtils;
use Illuminate\Http\Request;

class CartCommandProcessor extends \Buxus\Eshop\Command\CartCommandProcessor
{
    public function process(Request $request): ?object
    {
        $this->request = $request;

        $command = $request->input('command');
        try {
            $commandPayload = $this->processCommand($command);
            $data = [
                'result' => static::RESULT_STATUS_SUCCESS,
                'command' => $command,
                'payload' => $commandPayload,
            ];
        } catch (\Exception $e) {
            $data = [
                'result' => static::RESULT_STATUS_ERROR,
                'command' => $command,
                'message' => $e->getMessage(),
            ];
        }

        $this->getCart()->recount();

        return $this->processOutput($data);
    }

    protected function addProduct(string $command, CartCommandProcessorContract $processor): array
    {
        $productId = $processor->requiredParam('product_id');
        $productAmount = $processor->optionalParam('amount', 1);

        $productId = (int)$productId;

        $item = $this->app->make('buxus.eshop.product-factory')->get($productId);

        $canBeManuallyAdded = true;
        if ($item instanceof ShopItemInterface) {
            $canBeManuallyAdded = $item->canBeManuallyAdded();
        }

        if ($item instanceof AdderAwareInterface) {
            $item->logAddedBy();
        }

        if ($canBeManuallyAdded) {
            $item->setAmount($productAmount);

            if ($item instanceof ConfigurableProductItemInterface) {
                $product_options = $processor->optionalParam('options', '');
                if (!empty($product_options)) {
                    $options = @json_decode(base64_decode($product_options), true);
                    if (is_array($options)) {
                        foreach ($options as $key => $value) {
                            if (!is_array($value) && !is_object($value)) {
                                $item->addProductConfigOption($key, $value);
                            }
                        }
                    }
                }
            }

            if ($item->isPriceValid()) {
                $cart = $processor->getCart();
                $cart->addItem($item);

                $addEvent = new CartAddProductEvent($item, $productAmount);
                BuxusEvent::fire($addEvent);

                $cart->recount();
            }


            try {
                if ($addEvent->reloadCart()) {
                    $this->setReloadPage();
                }

                $result = $addEvent->getData() + [
                        'item_count_in_cart' => $cart->getItem($item->getTag())->getAmount()
                    ];
            } catch (\Throwable $e) {
                ErrorReporter::reportSilent($e);
                $result = [];
            }
        } else {
            $result = [];
        }

        $this->setRedirectToShoppingCart();

        return $result;
    }

    public function removeCart()
    {
        $cart = $this->getCart();
        foreach ((array)$cart->getItems() as $item) {
            $cart->removeItem($item->getTag());
        }

        $cartRemoveEvent = new CartRemoveEvent();
        BuxusEvent::fire($cartRemoveEvent);

        ShoppingCartFactory::reset();
    }

    protected function changeAmount(string $command, CartCommandProcessorContract $processor): array
    {
        $productId = $processor->requiredParam('product_id');
        $productAmount = $processor->requiredParam('amount');

        $cart = $processor->getCart();

        $originalItemCount = $cart->getItemCount();

        $result = [];

        $item = $cart->getItem($productId);

        $amountCanBeChanged = true;
        if ($item instanceof ShopItemInterface) {
            $amountCanBeChanged = $item->amountCanBeChanged();
        }

        if ($item instanceof AdderAwareInterface) {
            $item->logAddedBy();
        }

        if ($amountCanBeChanged && !is_null($item) && $item->getAmount() != $productAmount) {
            $oldProductAmount = $item->getAmount();
            $item->setAmount($productAmount);

            \Persistence::persist($cart);
            $changeEvent = new CartChangeProductAmountEvent($item, $productAmount, $oldProductAmount);
            BuxusEvent::fire($changeEvent);

            $result = $this->getCartDataInfo();
            $result['change_operation'] = ($oldProductAmount > $productAmount ? 'remove' : 'add');
            $result = ArrayUtils::merge($result, $changeEvent->getData());

            if ($changeEvent->reloadCart()) {
                $this->setReloadPage();
            }
        }

        if ($originalItemCount != $cart->getItemCount()) {
            $this->setReloadPage();
        }

        return $result;
    }

    public function addItemToCart($item, $productAmount)
    {
        $productId = $item->getPageId();

        $item = $this->app->make('buxus.eshop.product-factory')->get($productId);

        $canBeManuallyAdded = true;
        if ($item instanceof ShopItemInterface) {
            $canBeManuallyAdded = $item->canBeManuallyAdded();
        }

        if ($canBeManuallyAdded) {
            $item->setAmount($productAmount);

            if ($item->isPriceValid()) {
                $cart = $this->getCart();
                $cart->addItem($item);

                $addEvent = new CartAddProductEvent($item, $productAmount);
                BuxusEvent::fire($addEvent);

                $cart->recount();
            }

            $result = $addEvent->getData() + [
                    'item_count_in_cart' => $cart->getItem($item->getTag())->getAmount()
                ];
        } else {
            $result = [];
        }

        return $result;
    }

    public function addItemToCartProgramatically($item, $productAmount)
    {
        $productId = $item->getPageId();

        $item = $this->app->make('buxus.eshop.product-factory')->get($productId);

        $canBeManuallyAdded = true;
        if ($item instanceof ShopItemInterface) {
            $canBeManuallyAdded = $item->canBeManuallyAdded();
        }

        if ($canBeManuallyAdded) {
            $item->setAmount($productAmount);

            if ($item->isPriceValid()) {
                $cart = $this->getCart();
                $cart->addItem($item);


                $cart->recount();
            }

            $result = [];
        } else {
            $result = [];
        }

        return $result;
    }

    public function saveCart()
    {
        $name = request('cart_name');

        $manager = new PriceOfferManager();
        $manager->savePriceOffer($this->getCart(), $name);
    }

    public function restoreCart(PriceOffer $priceOffer)
    {
        $manager = new PriceOfferManager();
        $manager->restorePriceOffer($priceOffer, $this);
    }

    public function downloadCart($format = DownloadManager::PDF)
    {
        $manager = new DownloadManager($this->getCart()->getItems());
        return $manager->download($format);
    }

    public function showCart($format = DownloadManager::PDF)
    {
        $manager = new DownloadManager($this->getCart()->getItems());
        return $manager->showInBrowser($format);
    }
}
