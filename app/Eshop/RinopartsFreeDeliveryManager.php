<?php

namespace App\Eshop;

use Buxus\Eshop\FreeDelivery\FreeDeliveryManager;
use Buxus\Eshop\FreeDelivery\Item\FreeDeliveryModifierItemInterface;
use Buxus\Eshop\Item\ShopItemInterface;
use Buxus\Eshop\Oraculum\RatableItemInterface;
use Buxus\Eshop\Oraculum\RatableItemListInterface;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;

class RinopartsFreeDeliveryManager extends FreeDeliveryManager
{
    public function getCartTotalPrice(RatableItemListInterface $item_list)
    {
        $sum = 0;

        /* @var $item RatableItemInterface */
        foreach ($item_list as $item) {
            if ($item instanceof ShopItemInterface) {
                if (!$item->shouldBeIncludedInTotalPriceSum()) {
                    continue;
                }
            }
            if ($item instanceof FreeDeliveryModifierItemInterface) {
                if ($item->disablesFreeDeliveryForEntireOrder()) {
                    return 0.0;
                }

                if ($item->isExcludedFromFreeDelivery()) {
                    continue;
                }
            }

            // sum prices
            $price = $item->getFinalPriceWithoutVatValue();
            if ($price > 0) {
                $sum += $item->getFinalPriceWithoutVatValue();
            }
        }

        return $sum;
    }
}
