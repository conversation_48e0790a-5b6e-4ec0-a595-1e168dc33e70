<?php

namespace App\Console\Commands;

use App\Imports\Jobs\FebiBilstein\FebiBilsteinStockJob;
use Illuminate\Console\Command;

class FebiBilsteinUpdateStockCommand extends Command
{
    protected const SUPPLIER_CODE = 3;
    protected const STOCK_BALANCE = 8;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'febi-bilstein:update-stock';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $content = $this->getContent();

        foreach ($content as $row) {
            FebiBilsteinStockJob::dispatch(
                $row[self::SUPPLIER_CODE],
                $row[self::STOCK_BALANCE]
            );
        }
    }

    protected function getContent()
    {
        $filepath = $this->getFilepath();

        $content = file_get_contents($filepath);
        $content = str_getcsv($content, "\n");

        $rows = [];

        foreach ($content as $row) {
            $rows[] = str_getcsv($row, ';');
        }


        return $rows;
    }

    protected function getFilepath()
    {
        $dir = storage_path('app/imports/febi_bilstein_sl/');

        $filenames = array_filter(scandir($dir), function ($str) {
            return str_contains($str, 'Full');
        });

        $latestFileDateTime = '';
        $latestFilename = '';

        foreach ($filenames as $filename) {
            $path = $dir . $filename;
            if (empty($latestFileDateTime)) {
                $latestFileDateTime = filemtime($path);
            }

            if (filemtime($path) > $latestFileDateTime) {
                $latestFilename = $path;
                $latestFileDateTime = filemtime($path);
            }
        }

        return $latestFilename;
    }
}
