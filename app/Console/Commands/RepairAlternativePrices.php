<?php

namespace App\Console\Commands;

use App\Jobs\PageDeleteJob;
use App\Jobs\RepairAlternativePrice;
use App\Product\PairManager;
use Buxus\Page\PageInterface;
use Buxus\Util\PropertyID;
use Buxus\Util\PropertyTag;
use BuxusDB;
use Illuminate\Console\Command;
use PageFactory;

class RepairAlternativePrices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'repair:alternative-prices';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $sql = "select tP.page_id
                from tblPages as tP
                         join tblPagePropertyValues as tPPV on tP.page_id = tPPV.page_id and tPPV.property_id = 188
                         left join tblPagePropertyValues as tPPV2 on tP.page_id = tPPV2.page_id and tPPV2.property_id = 130
                where tPPV.property_value IS NOT NULL
                  and tPPV.property_value <> ''
                  and (tPPV2.property_value IS NULL or tPPV.property_value = '');
                ";

        $pages = BuxusDB::get()->fetchAll($sql);

        foreach ($pages as $page) {
            if (!empty($page['page_id'])) {
                $job = new PageDeleteJob($page['page_id']);
                dispatch($job)->onQueue('buxus_rinoparts_test_repair');
            }
        }
    }
}
