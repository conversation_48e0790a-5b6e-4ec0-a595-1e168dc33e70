<?php

namespace App\Console\Commands;

use Buxus\Error\ErrorReporter;
use Buxus\Util\DB;
use Buxus\Util\PropertyID;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;

class PrepareArchiveUrlsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'seo:prepare-archive-urls';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import URLs from CSV data and match them with buxus pages.';

    protected const SEO = 0;
    protected const CODE = 3;
    protected const ONIX_ID = 4;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $filepath = storage_path('/produkty_seo.csv');
            $csvFile = file($filepath);
            $data = [];
            $flag = true;
            foreach ($csvFile as $line) {
                if ($flag) {
                    $flag = false;
                    continue;
                }
                $data[] = str_getcsv($line, ';');
            }

            $hosts = [
                'sk' => env('HOST'),
                'cz' => env('HOST_CZ'),
                'en' => env('HOST_EN'),
            ];

            $time = Carbon::now()->format('Y-m-d H:i:s');

            foreach ($data as $row) {
                if ($row[self::ONIX_ID]) {
                    $page_id = \DB::table('tblPagePropertyValues')
                            ->where('property_id', PropertyID::ONIX_NS_NUMBER_ID())
                            ->where('property_value', $row[self::ONIX_ID])
                            ->pluck('page_id')
                            ->first();

                    if (!empty($page_id)) {
                        foreach ($hosts as $lang => $host) {
                            \DB::table('tblSeoUrlArchive')->upsert([
                                'page_id' => $page_id,
                                'protocol' => 'https://',
                                'domain' => $host,
                                'lang' => $lang,
                                'parameters' => '',
                                'path' => '/' . $row[self::SEO],
                                'archive_type' => 'archived',
                                'archive_time' => $time
                            ], 'path');
                        }
                    }
                }
            }
        } catch (Exception $e) {
            ErrorReporter::reportSilent($e);
        }
    }
}
