<?php

namespace App\Console\Commands;

use App\Imports\Processors\AugustinGroup\AugustinGroupStockAndPricing;
use App\Imports\Processors\Remante\RemanteStockAndPricing;
use Illuminate\Console\Command;
use Storage;

class RemanteUpdateStockAndPricingCommand extends Command
{
    protected $signature = 'remante:update-stock-and-pricing';
    protected $description = 'Updates stock and pricing for Remante supplier';

    public function handle()
    {
        $path = config('imports.remante_availability.update_path');

        if (Storage::disk('local')->exists($path)) {
            $file = Storage::disk('local')->get($path);
        }

        if (!empty($file)) {
            $processor = new RemanteStockAndPricing();
            $processor->import(storage_path('app/'. $path));
        }
    }
}
