<?php

namespace App\Console\Commands;

use App\Imports\Processors\Abakus\AbakusStock;
use Illuminate\Console\Command;
use Maatwebsite\Excel\Excel;
use Storage;

class AbakusUpdateStockCommand extends Command
{
    protected $signature = 'abakus:update-stock';
    protected $description = 'Updates stock and pricing for MeatDoria supplier';

    public function handle()
    {
        $path = config('imports.abakus_availability.update_path');

        if (Storage::disk('local')->exists($path)) {
            $file = Storage::disk('local')->get($path);
        }

        if (!empty($file)) {
            $processor = new AbakusStock();
            $processor->import(storage_path('app/' . $path));
        }
    }
}
