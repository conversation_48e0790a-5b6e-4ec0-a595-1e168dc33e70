<?php

namespace App\Console\Commands;

use App\Imports\Jobs\MecDiesel\MecDieselStockAndPricingJob;
use Buxus\Util\PropertyID;
use Illuminate\Console\Command;
use Storage;

class MecDieselUpdateStockAndPricingCommand extends Command
{
    protected $signature = 'mec-diesel:update-stock-and-pricing';
    protected $description = 'Updates stock and pricing for Mec Diesel supplier';

    protected const SUPPLIER_CODE = 0;
    protected const STOCK_ITALY = 5;
    protected const STOCK_CZECH = 6;
    protected const PRICE = 7;
    protected const DEPOSIT_PRICE = 8;

    public function handle()
    {
        $path = config('imports.mec_diesel.update_path');

        if (Storage::disk('local')->exists($path)) {
            $file = Storage::disk('local')->get($path);
        }

        $rows = explode("\n", $file);

        $rowsParsed = [];

        foreach ($rows as $row) {
            $row = explode('|', $row);
            $rowsParsed[] = array_map('trim', $row);
        };

        $outdatedProducts = $this->getOutdatedProducts(array_column($rowsParsed, self::SUPPLIER_CODE));

        foreach ($rows as $row) {
            $row = explode('|', $row);

            MecDieselStockAndPricingJob::dispatch(
                trim($row[self::SUPPLIER_CODE]),
                trim($row[self::STOCK_ITALY]),
                trim($row[self::STOCK_CZECH]),
                trim($row[self::PRICE]),
                trim($row[self::DEPOSIT_PRICE]),
            );
        }

        foreach ($outdatedProducts as $outdatedProduct) {
            MecDieselStockAndPricingJob::dispatch(
                $outdatedProduct,
                0,
                0,
            );
        }
    }

    protected function getOutdatedProducts($supplierCodesFromImport)
    {
        return \DB::table('tblPagePropertyValues')
            ->where('property_id', PropertyID::MEC_DIESEL_SUPPLIER_CODE_ID())
            ->whereNotNull('property_value')
            ->where('property_value', '<>', '')
            ->get()
            ->pluck('property_value')
            ->diff(collect($supplierCodesFromImport));
    }
}
