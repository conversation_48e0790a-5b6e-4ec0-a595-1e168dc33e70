<?php

namespace App\Console\Commands;

use App\Delivery\DeliveryManager;
use App\Delivery\DPDDeliveryTrackingManager;
use App\Imports\DPDTrackingInfoImport;
use App\Models\DPDIntermediateStepTracking;
use Illuminate\Console\Command;
use Maatwebsite\Excel\Facades\Excel;
use Monolog\Handler\RotatingFileHandler;
use Monolog\Logger;
use phpseclib3\Net\SFTP;
use Storage;

class DPDFileWatcherCommand extends Command
{
    protected $signature = 'rinoparts:dpd-file-watcher';
    protected $description = 'Command description';

    protected ?DPDDeliveryTrackingManager $manager = null;
    protected ?SFTP $sftp = null;
    protected ?Logger $logger = null;

    public function handle()
    {
        $filesToBeProcessed = $this->saveFiles();

        foreach ($filesToBeProcessed as $file) {
            $this->processFile($file);
        }

        $this->removeOldFiles();
        $this->removeOldDatabaseEntries();
    }

    protected function processFile(string $file)
    {
        $collection = Excel::toCollection(new DPDTrackingInfoImport(), storage_path('app/dpd-tracking/' . $file), null, \Maatwebsite\Excel\Excel::CSV);
        $collection = $collection->flatten(1);
        $collection = $collection->map(function ($item) {
            return [
                'customer_reference' => $item['customer_reference'] ?? null,
                'parcelno' => $item['parcelno'] ?? null,
            ];
        });

        $collection = $collection->filter(function ($item) {
            return !empty($item['customer_reference']) && !empty($item['parcelno']);
        });

        $final = [];

        foreach ($collection as $item) {
            $customerReferences = explode(',', $item['customer_reference']);

            foreach ($customerReferences as $customerReference) {
                $final[trim($customerReference)][] = $item['parcelno'];
            }
        }


        foreach ($final as $customerReference => $parcelNumbers) {
            foreach ($parcelNumbers as $parcelNumber) {
                DPDIntermediateStepTracking::upsert([
                    'customer_reference' => $customerReference,
                    'parcel_no' => trim($parcelNumber),
                    'filename_reference' => $file,
                ], ['customer_reference', 'parcel_no'], ['filename_reference']);
            }
            $this->pairWithDeliveryNote($customerReference);
        }
    }

    protected function pairWithDeliveryNote(string $vs): void
    {
        $deliveryNoteShipmentNumbers = $this->getManager()->getDeliveryTrackingInfo($vs);

        if (empty($deliveryNoteShipmentNumbers)) {
            return;
        }

        foreach ($deliveryNoteShipmentNumbers as $key => $values) {
            DeliveryManager::updateTrackingNumbers($vs, $key, $values);
        }

        $this->getLogger()->info("Paired delivery note with tracking numbers", [
            'vs' => $vs,
            'carrier' => DPDDeliveryTrackingManager::CARRIER_TAG,
        ]);
    }

    protected function getFileList()
    {
        $sftp = $this->getSftpConnection();

        $sourceDirectory = 'OUT/';

        $files = $sftp->nlist($sourceDirectory);

        $files = array_filter($files, function ($file) use ($sourceDirectory) {
            return (!str_ends_with($file, '.sem')) && ($file != '.' && $file != '..');
        });

        sort($files);

        return $files;
    }

    protected function saveFiles()
    {
        $files = $this->getFileList();
        $sftp = $this->getSftpConnection();

        $localFiles = scandir(storage_path('app/dpd-tracking/'));

        $files = array_diff($files, $localFiles);

        foreach ($files as $file) {
            $localFilePath = storage_path('app/dpd-tracking/' . $file);
            $remoteFilePath = 'OUT/' . $file;

            if (!$sftp->get($remoteFilePath, $localFilePath)) {
                $this->error("Failed to download file: {$file}");
                continue;
            }

            $this->getLogger()->info("Downloaded file", [
                'file' => $file,
                'local_path' => $localFilePath,
                'remote_path' => $remoteFilePath,
            ]);
        }

        return $files;
    }

    protected function removeOldFiles()
    {
        $files = scandir(storage_path('app/dpd-tracking/'));

        foreach ($files as $file) {
            $filePath = storage_path('app/dpd-tracking/' . $file);
            if (is_file($filePath) && time() - filemtime($filePath) > $this->getFileStorageLimit()) {
                unlink($filePath);
            }
        }
    }

    protected function getFileStorageLimit(): int
    {
        return 14 * 24 * 60 * 60;
    }

    protected function removeOldDatabaseEntries(): void
    {
        DPDIntermediateStepTracking::where('created_at', '<', now()->subSeconds($this->getDatabaseStorageLimit()))
            ->delete();
    }

    protected function getDatabaseStorageLimit(): int
    {
        return 90 * 24 * 60 * 60;
    }

    protected function getLogger(): Logger
    {
        if ($this->logger === null) {
            $this->logger = new Logger('dpd-tracking');
            $this->logger->pushHandler(new RotatingFileHandler(storage_path('logs/dpd/dpd-tracking.log'), $this->getFileStorageLimit()));
        }

        return $this->logger;
    }

    protected function getManager(): ?DPDDeliveryTrackingManager
    {
        if ($this->manager === null) {
            $this->manager = new DPDDeliveryTrackingManager();
        }

        return $this->manager;
    }

    protected function getSftpConnection(): ?SFTP
    {
        if ($this->sftp === null) {
            $this->sftp = new SFTP(config('dpd.sftp_host'));
            $this->sftp->login(config('dpd.sftp_username'), config('dpd.sftp_password'));
        }

        return $this->sftp;
    }
}
