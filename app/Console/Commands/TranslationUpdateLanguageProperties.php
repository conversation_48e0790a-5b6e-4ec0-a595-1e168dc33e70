<?php

namespace App\Console\Commands;

use App\Translate\PropertyIsNotMultilingualException;
use Buxus\Translate\Property\PropertyDuplicator;
use BuxusSite;
use Illuminate\Console\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class TranslationUpdateLanguageProperties extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'translation:update-lang-properties {property_tags?*}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create language versions of all language-dependent properties, or only the ones supplied in property_tags argument';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $arguments = $this->arguments();

        $languages = BuxusSite::getAvailableSites();
        $propertyTags = $arguments['property_tags'];
        $configuredPropertyTags = config('translate.language_dependent_property_tags', []);
        $properties = [];

        if (empty($propertyTags)) {
            $properties = $configuredPropertyTags;
        } else {
            foreach ($propertyTags as $index => $propertyTag) {
                try {
                    if (!in_array($propertyTag, $configuredPropertyTags) && !isset($configuredPropertyTags[$propertyTag])) {
                        throw new \App\Exceptions\PropertyIsNotMultilingualException('Property ' . $propertyTag . ' is not configured to be multilingual');
                    }
                } catch (PropertyIsNotMultilingual $e) {
                    $this->error($e->getMessage());
                    continue;
                }

                $propertyTagConfig = $properties[$propertyTag];
                $properties[$propertyTag] = $propertyTagConfig;
            }
        }

        if (is_array($languages)) {
            $key = array_search(BuxusSite::getDefaultSite(), $languages);
            unset($languages[$key]);


            \Illuminate\Support\Facades\Artisan::call('translation:make-lang-property', [
                'lang' => implode(',', $languages),
                'property_tag' => $properties,
                '--force' => true,
            ]);

        }

    }
}
