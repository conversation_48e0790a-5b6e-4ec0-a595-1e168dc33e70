<?php

namespace App\Console\Commands;

use App\Imports\Jobs\NRF\NRFUpdateStockJob;
use Buxus\Util\PropertyID;
use Illuminate\Console\Command;
use Storage;

class NRFUpdateStockCommand extends Command
{
    protected const SUPPLIER_CODE = 0;
    protected const STOCK_BALANCE = 1;

    protected $signature = 'nrf:update-stock';
    protected $description = 'Command description';

    public function handle()
    {
        $ftpConfig = config('imports.nrf.ftp');

        $url = "ftp://{$ftpConfig['username']}:{$ftpConfig['password']}@{$ftpConfig['host']}/{$ftpConfig['remote_filename']}";

        $content = file_get_contents($url);
        $path = config('imports.nrf.update_path');

        Storage::disk('local')->put($path, $content);

        if (Storage::disk('local')->exists($path)) {
            $file = Storage::disk('local')->get($path);
        }

        $rows = explode("\n", $file);
        $rowsParsed = [];
        $supplierCodes = [];

        foreach ($rows as $row) {
            $rowParsed = explode(';', $row);
            $supplierCodes[] = $rowParsed[self::SUPPLIER_CODE];
            $rowsParsed[] = explode(';', $row);
        }

        $this->processPrevious($supplierCodes);

        foreach ($rowsParsed as $rowParsed) {
            NRFUpdateStockJob::dispatch(
                trim($rowParsed[self::SUPPLIER_CODE]),
                (int)trim($rowParsed[self::STOCK_BALANCE])
            );
        }
    }

    protected function processPrevious($supplierCodes)
    {
        $currentSupplierCodes = \DB::table('tblPagePropertyValues')
            ->where('property_id', PropertyID::NRF_SUPPLIER_CODE_ID())
            ->where('property_value', '<>', '')
            ->get()
            ->pluck('property_value');

        $currentSupplierCodes
            ->diff(collect($supplierCodes))
            ->each(function ($supplierCode) {
                NRFUpdateStockJob::dispatch($supplierCode, 0);
            });
    }
}
