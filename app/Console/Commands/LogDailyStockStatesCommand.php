<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class LogDailyStockStatesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stock:log-daily-states';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Log stock states, once a day.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // INSERT INTO stock_state_log (buxus_product_id, stock_value, created_at)
        // SELECT P.page_id, cast(PV.property_value as INTEGER), NOW() FROM tblPages P
        // INNER JOIN tblPagePropertyValues PV ON P.page_id = PV.page_id
        // WHERE
        // P.page_state_id = 1
        // AND PV.property_id = 135
        // AND PV.property_value IS NOT NULL
        // AND PV.property_value <> ''

        DB::insert("INSERT INTO stock_state_log (buxus_product_id, stock_value, created_at)
            SELECT P.page_id, cast(PV_STOCK.property_value as SIGNED), NOW() FROM tblPages P
                INNER JOIN tblPagePropertyValues PV_ONIX ON P.page_id = PV_ONIX.page_id
                INNER JOIN tblPagePropertyValues PV_STOCK ON P.page_id = PV_STOCK.page_id
            WHERE
            P.page_state_id = 1
              AND PV_ONIX.property_id = 128
              AND PV_ONIX.property_value IS NOT NULL
              AND PV_ONIX.property_value <> ''
            AND PV_STOCK.property_id = 135
            AND PV_STOCK.property_value IS NOT NULL
            AND PV_STOCK.property_value <> ''");


        return 0;
    }
}
