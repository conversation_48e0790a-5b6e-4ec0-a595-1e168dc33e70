<?php

namespace App\Console\Commands;

use App\Imports\Jobs\MecDiesel\MecDieselStockAndPricingJob;
use App\Imports\Processors\Eminia\EminiaImportProcessor;
use Buxus\Util\PropertyID;
use Excel;
use Illuminate\Console\Command;
use Storage;

class EminiaUpdateStockAndPricingCommand extends Command
{
    protected $signature = 'eminia:update-stock-and-pricing';
    protected $description = 'Updates stock and pricing for Eminia supplier';

    public function handle()
    {
        $path = config('imports.eminia.update_path');

        if (Storage::disk('local')->exists($path)) {
            $processor = new EminiaImportProcessor();
            Excel::import($processor, $path);
        }
    }
}
