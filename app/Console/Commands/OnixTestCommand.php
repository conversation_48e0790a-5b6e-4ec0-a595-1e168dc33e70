<?php

namespace App\Console\Commands;

use App\Onix\Onix;
use Buxus\Logger\Logger;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class OnixTestCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'onix:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        //self::getPartner();
        //self::sendOrder();
        //self::getOrders();
        self::importOrdersFromTo('2022-03-08', '2022-03-10');
        return 0;
    }

    static function getOrders()
    {
        $onix = new Onix();
        $orders = $onix->getOrdersForDay('2022-03-02');

//        $orders = $onix->getOrders('1318');
        foreach($orders as $order) {
            if($order->Guid_Ext == 2196) {
                dd($order);
            }
        }
        dd($orders);
    }

    static function importOrdersFromTo($from, $to)
    {
        $from = strtotime($from);
        $to = strtotime($to);

        for(;$from<$to; $from+=3600*24) {


            echo 'DATE:' . date('Y-m-d', $from) . "\n";
            $logger = new Logger('test');
            $onix = new Onix();
            $onix_orders = $onix->getOrdersForDay(date('Y-m-d', $from));

            echo 'pocet: '.count($onix_orders) . "\n";
            foreach ($onix_orders as $onix_order) {
                $buxus_order_id = (int)$onix_order->Guid_Ext;
                if ($buxus_order_id) {
                    if (DB::table('tblShopOrders')->where('order_id', '=', $buxus_order_id)->first()) {
                        $buxusOrder = \OrderFactory::getById($buxus_order_id);
                        if ($buxusOrder && $buxusOrder->getVariableSymbol() === $onix_order->External_Number) {
                            $onix->updateBuxusOrder($buxusOrder, $onix_order, $logger);
                        }
                    }
                }
            }
        }
    }

    static function sendOrder()
    {
        exit;
        $json = '{"json":{"Ns_Number_Partner":"2125","Guid_Ext":848,"External_Number":"22000798","Curr":"EUR","Transport_Type":"Osobn\u00fd odber - \u017dilina","Payment_Type":"Hotovos\u0165","Items":[{"Stock_Code":"S1","Item_Type":1,"Amount_Unit":1,"User_Amount":1,"Unit_Price_Base":11.605,"Measure_Unit":"ks","Reservated_Amount":1,"Stock_Items_Ns_Number":"023323"},{"Item_Name":"DOPRAVA","Stock_Items_Name":"DOPRAVA","Item_Type":2,"Unit_Price_Base":0}],"Variable_Symbol":"22000798","Partner_Da_Name":"Test testovac\u00ed","Partner_Da_Street":"Testovacia 88","Partner_Da_Street_No":"","Partner_Da_Postcode":"12345","Partner_Da_City":"Testovice nad Testom","Partner_Email":"<EMAIL>","Partner_Telephone_Number":"+************","Delivery_Text":"test","CustomColumns":[{"Name":"Z_RINO_OB001_Cakajuci_DL_ZA","Value":-1},{"Name":"Z_RINO_OB001_Cakajuci_DL_BA","Value":-1}]}}';
        $data = json_decode($json, JSON_OBJECT_AS_ARRAY)['json'];

        $data['Partner_Da_Name'] = 'testovaciaadresa3';


        $data['Partner_Da_Street'] = "Testovacia 89";
        $data['Partner_Da_Street_No'] = "";
        $data['Partner_Da_Postcode'] = "974 01";
        $data['Partner_Da_City'] = "Senica";
        //$data['Partner_Da_State'] = "ITA";

        $data['Delivery_Text'] = 'Test ' . date('Y-m-d H:i:s');

        $onix = new Onix();
        $onix->sendOrder('2125', $data);
    }

    static function getPartner($onixPartnerId = '2125')
    {
        $onix = new Onix();

        $partners = $onix->getPartners($onixPartnerId);

        dd($partners);
    }
}
