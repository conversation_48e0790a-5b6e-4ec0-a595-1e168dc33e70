<?php

namespace App\Console\Commands;

use App\Imports\Processors\AugustinGroup\AugustinGroupProcessor;
use App\Imports\Processors\AugustinGroup\AugustinGroupStockAndPricing;
use Illuminate\Console\Command;
use Storage;

class AugustinGroupUpdateStockAndPricingCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'augustin-group:update-stock-and-pricing';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Updates stock and pricing for Augustin Group supplier';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $ftpConfig = config('imports.augustin_group.ftp');
        $url = "ftp://{$ftpConfig['username']}:{$ftpConfig['password']}@{$ftpConfig['host']}/{$ftpConfig['remote_filename']}";

        $content = file_get_contents($url);
        $path = config('imports.augustin_group.update_path');

        Storage::disk('local')->put($path, $content);

        if (Storage::disk('local')->exists($path)) {
            $file = Storage::disk('local')->get($path);
        }

        if (!empty($file)) {
            $processor = new AugustinGroupStockAndPricing();
            $processor->import(storage_path('app/'. $path));
        }
    }
}
