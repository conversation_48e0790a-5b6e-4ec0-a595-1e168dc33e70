<?php

namespace App\PriceOffers;

use App\Authentication\FakeAuthentication;
use Buxus\WebUser\Facades\WebUserAuthentication;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PriceOffer extends Model
{
    public const SUPERUSER = 'superuser';
    public const USER = 'user';

    public const STATE_SAVED = 0;
    public const STATE_USED = 1;

    public const WITH_OE_NUMBERS = 'with_oe_numbers';

    protected $table = 'tblPriceOffers';

    protected $fillable = [
        'saved_by',
        'saved_for',
        'tag',
        'state',
    ];

    public function priceOfferItems()
    {
        return $this->hasMany(PriceOfferItems::class, 'price_offer_id', 'id');
    }

    public function getSavedFor()
    {
        return \WebUserFactory::getById($this->saved_for);
    }

    public function getFinalPriceWithoutVatValue()
    {
        $price = 0;

        foreach ($this->priceOfferItems as $item) {
            $price += $item->getProduct()->getFinalPriceWithoutVatValue();
        }

        return $price;
    }

    public function getFinalPriceValue()
    {
        $price = 0;

        foreach ($this->priceOfferItems as $item) {
            $price += $item->getProduct()->getFinalPriceValue();
        }

        return $price;
    }

    public function remove()
    {
        if (\WebUserAuthentication::isAuthenticated()) {
            if ($this->saved_for == WebUserAuthentication::getUserId() || !empty(FakeAuthentication::getUser())) {
                $this->priceOfferItems()->delete();
                $this->delete();
            }
        }
    }
}
