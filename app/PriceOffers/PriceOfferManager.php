<?php

namespace App\PriceOffers;

use App\Authentication\FakeAuthentication;
use App\Events\CartChangeEventHandler;
use App\Form\PdfGenerator;
use App\WebUser\RinopartsWebUserAuthentication;
use Buxus\Eshop\Contracts\CartCommandProcessor;
use Buxus\Eshop\Contracts\ShoppingCart;
use Buxus\Util\PageIds;
use Buxus\Util\Url;
use Response;
use WebUserAuthentication;

class PriceOfferManager
{
    public function savePriceOffer(ShoppingCart $cart, $name = null)
    {
        if (!WebUserAuthentication::isAuthenticated()) {
            Url::redirectPermanent(Url::page(PageIds::getAuthLogin()));
        }

        $fakeAuth = new FakeAuthentication();
        $superuser = $fakeAuth->getSuperuser();

        $webuser = \WebUserAuthentication::getUser();

        $priceOffer = new PriceOffer();
        $priceOffer->name = strip_tags($name);
        $priceOffer->saved_by = $superuser ? $superuser->getUserId() : $webuser->getUserId();
        $priceOffer->saved_for = $webuser->getUserId();
        $priceOffer->tag = $superuser ? PriceOffer::SUPERUSER : PriceOffer::USER;
        $priceOffer->state = PriceOffer::STATE_SAVED;
        $priceOffer->save();

        $this->saveItemsForPriceOffer($priceOffer, $cart->getItems());
    }

    public function saveItemsForPriceOffer(PriceOffer $priceOffer, array $items)
    {
        foreach ($items as $item) {
            $this->saveItemForPriceOffer($priceOffer, $item);
        }
    }

    public function saveItemForPriceOffer(PriceOffer $priceOffer, $item)
    {
        return $priceOffer->priceOfferItems()->create([
            'price_offer_id' => $priceOffer->id,
            'page_id' => $item->getPageId(),
            'amount' => $item->getAmount(),
        ]);
    }

    public function restorePriceOffer(PriceOffer $offer, CartCommandProcessor $processor)
    {
        $priceOfferItems = $offer->priceOfferItems;

        foreach ($priceOfferItems as $priceOfferItem) {
            $item = \ProductFactory::get($priceOfferItem->page_id);
            $processor->addItemToCartProgramatically($item, $priceOfferItem->amount);
        }

        (new CartChangeEventHandler())->handle();
    }


    public function printPriceOffer(PriceOffer $offer)
    {
        $pdfGenerator = new PdfGenerator('price-offers.template', [
            'priceOffer' => $offer
        ]);

        $pdfGenerator->setStylesheet(Url::staticUrl(Url::asset('css/price-offers.css')));

        $filename = "price-offer-{$offer->id}.pdf";
        return Response::make($pdfGenerator->generate(), 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="' . $filename . '"'
        ]);
    }

    public function getPriceOffersForUser($userId = null)
    {
        if ($userId === null && WebUserAuthentication::isAuthenticated()) {
            $userId = WebUserAuthentication::getUser()->getUserId();
        }

        if ($userId === null) {
            return null;
        }

        $fakeAuth = new FakeAuthentication();

        return PriceOffer::where('saved_for', $userId)
            ->orderBy('created_at', 'desc')
            ->when(!$fakeAuth->getSuperuser(), function ($q) {
                return $q->where('tag', PriceOffer::USER);
            })->get();
    }
}
