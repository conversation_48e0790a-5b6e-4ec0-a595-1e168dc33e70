<?php

namespace App\Checkout\Process;

use App\Checkout\Process\Form\DeliveryDataForm;
use App\DeliveryAddress;
use Buxus\Error\ErrorReporter;
use Eshop\ShoppingCart\Checkout\Process\Form\Plugin\DeliveryDataFormElementPluginInterface;
use Illuminate\Http\Request;

class DeliveryDataProcess extends \Eshop\ShoppingCart\Checkout\Process\DeliveryDataProcess
{
    public function saveData()
    {
        $values = $this->form->getValues();
        $new_values = $this->data;

        foreach ($values as $key => $value) {
            $new_values[$key] = $value;
        }
        $this->data = $new_values;

        // the form has been submitted, set that the user data was set
        if (isset($this->data['user_populated_init'])) {
            $this->data['user_populated'] = $this->data['user_populated_init'];
            unset($this->data['user_populated_init']);
        }

        $this->fireDeliveryDataFilledEvent($this->data, $this->getCheckout());

        $this->checkout->setCustomerType($this->data['customer_type']);

        $this->checkout->setFirstName($this->data['first_name']);
        $this->checkout->setInvoiceName($this->data['first_name'] . ' ' . $this->data['surname']);
        $this->checkout->setSurname($this->data['surname']);

        $this->checkout->setCompanyName($this->data['company_name']);
        $this->checkout->setEmail($this->data['email']);

        if (is_numeric($values['delivery_address_choice'])) {
            $deliveryAddress = DeliveryAddress::find($values['delivery_address_choice']);
            $this->checkout->setDeliveryAddressIsIdentical(false);
            $this->checkout->setDeliveryName($deliveryAddress->fullname);
            $this->checkout->setDeliveryCompanyName($deliveryAddress->company_name);
            $this->checkout->setDeliveryPhone($deliveryAddress->delivery_phone);
            $this->checkout->setDeliveryStreet($deliveryAddress->street);
            $this->checkout->setDeliveryCity($deliveryAddress->city);
            $this->checkout->setDeliveryZip($deliveryAddress->zip);
            $this->checkout->setDeliveryCountry($deliveryAddress->country ?: \BuxusSite::site());
        } else {
            $this->checkout->setDeliveryAddressIsIdentical($this->data['delivery_address_choice'] == 'identical');
            $this->checkout->setDeliveryName($this->data['delivery_name']);
            $this->checkout->setDeliveryCompanyName($this->data['delivery_company_name']);
            $this->checkout->setDeliveryPhone($this->data['delivery_phone']);
            $this->checkout->setDeliveryStreet($this->data['delivery_street']);
            $this->checkout->setDeliveryCity($this->data['delivery_city']);
            $this->checkout->setDeliveryZip($this->data['delivery_zip']);
            $this->checkout->setDeliveryCountry($this->data['delivery_country'] ?: \BuxusSite::site());
        }

        $this->checkout->setInvoiceStreet($this->data['street']);
        $this->checkout->setInvoiceCity($this->data['city']);
        $this->checkout->setInvoiceCountry($this->data['country'] ?: \BuxusSite::site());
        $this->checkout->setInvoiceZip($this->data['zip']);

        $this->checkout->setIco($this->data['ico']);
        $this->checkout->setDic($this->data['dic']);
        $this->checkout->setIcdph($this->data['drc']);
        $this->checkout->setNote($this->data['note']);
        $this->checkout->setPhone($this->data['phone']);

        foreach ($this->getFormPlugins() as $plugin) {
            /**
             * @var DeliveryDataFormElementPluginInterface $plugin
             */
            $plugin->decorateCheckoutAfterValidatedSubmit($this->checkout, $this->data);
        }

        if (\WebUserAuthentication::isAuthenticated() && (!app(Request::class)->get('_save_only'))) {
            try {
                $user = \WebUserAuthentication::getUser();

                $user_data = $this->data;

                $delivery_data_filled = false;
                foreach (config('buxus_shopping_cart.delivery_data_fields') as $key) {
                    if (!empty(trim($user_data[$key]))) {
                        $delivery_data_filled = true;
                        break;
                    }
                }

                $user_delivery_data_already_filled = false;
                $user_existing_data = $user->getAllData();
                foreach (config('buxus_shopping_cart.delivery_data_fields') as $key) {
                    if (!empty($user_existing_data[$key])) {
                        $user_delivery_data_already_filled = true;
                        break;
                    }
                }

                if (false === $delivery_data_filled || (true === $user_delivery_data_already_filled && false == config('buxus_shopping_cart.always_update_delivery_address'))) {
                    foreach (config('buxus_shopping_cart.delivery_data_fields') as $key) {
                        unset($user_data[$key]);
                    }
                }

                $user->fillData($user_data);

                foreach ($this->getFormPlugins() as $plugin) {
                    /**
                     * @var DeliveryDataFormElementPluginInterface $plugin
                     */
                    $plugin->decorateWebUser($user, $user_data);
                }

                $user->save();
            } catch (\Exception $e) {
                ErrorReporter::reportSilent($e);
            }
        }

        return true;
    }
}
