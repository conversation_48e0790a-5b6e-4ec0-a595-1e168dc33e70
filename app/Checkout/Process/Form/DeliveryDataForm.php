<?php

namespace App\Checkout\Process\Form;

use App\DeliveryAddress;
use App\Eshop\ShoppingCart\Form\Element\ModernTextElementReadonly;
use BuxusSite;
use App\Form\Elements\ModernSelectElement;
use Eshop\ShoppingCart\Form\Element;
use FormBase\Element\LinkElement;
use FormBase\Element\SubmitElement;
use Zend_Form_Element;
use Zend_Form_Exception;
use Zend_Form_SubForm;

class DeliveryDataForm extends \Eshop\ShoppingCart\Checkout\Process\Form\DeliveryDataForm
{
    public function isValid($data)
    {
        if (isset($data['delivery_address_choice']) && is_numeric($data['delivery_address_choice'])) {
            $deliveryAddress = DeliveryAddress::find($data['delivery_address_choice']);

            if (empty($deliveryAddress)) {
                $this->getElement('delivery_address_choice')->addError(\Trans::raw(lang('cart', 'Vybraná adresa neexistuje')));
                return false;
            }

            if (!$deliveryAddress->isValid()) {
                $this->getElement('delivery_address_choice')->addError(\Trans::raw(lang('cart', 'Vybraná adresa je nevalídna')));
                return false;
            }

            return true;
        }
        if (config('buxus_shopping_cart.login_in_delivery_data')) {
            // hack for the login form
            $login_result = $this->checkLogin($data);
            if (!is_null($login_result)) {
                return $login_result;
            }
        }

        $person_elements = $this->getPersonElementNames();
        $body_corporate_elements = $this->getCorporateElementNames();

        if ((isset($data['customer_type'])) && ($data['customer_type'] == 'person')) { // The person is selected
            foreach ($body_corporate_elements as $element_name) {
                $this->getElement($element_name)->setRequired(false)->setValidators([]);
            }
        } else { // The body corporate is selected
            foreach ($person_elements as $element_name) {
                //$this->getElement($element_name)->setRequired(false)->setValidators([]);
                $this->getElement($element_name)->setRequired(false)->setValidators([]);
            }
        }

        $delivery_elements = $this->getRequiredDeliveryAddressElements();
        $delivery_required = true;
        if ((isset($data['delivery_address_choice'])) && ($data['delivery_address_choice'] == 'identical')) {
            $delivery_required = false;
        }
        foreach ($delivery_elements as $delivery_element_name) {
            $delivery_element = $this->getElement($delivery_element_name);
            if ($delivery_element instanceof \Zend_Form_Element) {
                $delivery_element->setRequired($delivery_required);
                if (!$delivery_required) {
                    $delivery_element->setValidators([]);
                }
            }
        }

        $this->getElement('delivery_name')->setRequired(false);
        $this->getElement('delivery_company_name')->setRequired($data['delivery_address_choice'] == 'different');
        $this->getElement('delivery_phone')->setRequired($data['delivery_address_choice'] == 'different');
        $this->getElement('delivery_street')->setRequired($data['delivery_address_choice'] == 'different');
        $this->getElement('delivery_city')->setRequired($data['delivery_address_choice'] == 'different');
        $this->getElement('delivery_zip')->setRequired($data['delivery_address_choice'] == 'different');

        $valid = $this->parentIsValid($data);

        return $valid;
    }

    protected function parentIsValid($data)
    {
        if (!is_array($data)) {
            require_once 'Zend/Form/Exception.php';
            throw new Zend_Form_Exception(__METHOD__ . ' expects an array');
        }
        $translator = $this->getTranslator();
        $valid = true;
        $eBelongTo = null;

        if ($this->isArray()) {
            $eBelongTo = $this->getElementsBelongTo();
            $data = $this->_dissolveArrayValue($data, $eBelongTo);
        }
        $context = $data;
        /** @var Zend_Form_Element $element */
        foreach ($this->getElements() as $key => $element) {
            if (null !== $translator && $this->hasTranslator()
                && !$element->hasTranslator()) {
                $element->setTranslator($translator);
            }
            $check = $data;
            if (($belongsTo = $element->getBelongsTo()) !== $eBelongTo) {
                $check = $this->_dissolveArrayValue($data, $belongsTo);
            }
            if (!isset($check[$key])) {
                $valid = $element->isValid(null, $context) && $valid;
            } else {
                $valid = $element->isValid($check[$key], $context) && $valid;
                $data = $this->_dissolveArrayUnsetKey($data, $belongsTo, $key);
            }
        }
        /** @var Zend_Form_SubForm $form */
        foreach ($this->getSubForms() as $key => $form) {
            if (null !== $translator && $this->hasTranslator()
                && !$form->hasTranslator()) {
                $form->setTranslator($translator);
            }
            if (isset($data[$key]) && !$form->isArray()) {
                $valid = $form->isValid($data[$key]) && $valid;
            } else {
                $valid = $form->isValid($data) && $valid;
            }
        }

        $this->_errorsExist = !$valid;

        // If manually flagged as an error, return invalid status
        if ($this->_errorsForced) {
            return false;
        }

        return $valid;
    }


    protected function validate($data)
    {
        $result = $this->parentIsValid($data);
        $this->highlightErrorElements();
        return $result;
    }

    protected function getCorporateElementNames()
    {
        $corporateElements = array(
            'ico',
            'dic',
            'drc',
        );

        $pluginCorporateNames = [];
        foreach ($this->getPlugins() as $plugin) {
            $pluginCorporateNames = $plugin->getCorporateElementNames();
        }

        $corporateElements = array_merge($corporateElements, $pluginCorporateNames);

        return $corporateElements;
    }

    public function init()
    {
        if (config('buxus_shopping_cart.login_in_delivery_data')) {
            if (!\WebUserAuthentication::isAuthenticated()) {
                $username = new Element\ModernTextElement('username');
                $username->setLabel(\Trans::raw(lang('cart', 'Prihlasovacie meno')));
                $username->setValueClass('col-sm-6');
                $this->addElement($username);

                $password = new Element\ModernPasswordElement('password');
                $password->setLabel(\Trans::raw(lang('cart', 'Heslo')));
                $this->addElement($password);

                $login = new Element\ModernSubmitElement('login');
                $login->setLabel(\Trans::raw(lang('cart', 'Prihlásiť')));
                $login->addClass("btn-login");
                $login->clearValueClasses();
                $login->clearWrapperClasses();
                $this->addElement($login);
            }
        }

        if (\BuxusSite::site() == 'cz' || \BuxusSite::site() == 'en') {
            $required = false;
        } else {
            $required = true;
        }

        if (\ShoppingCart::getCheckout()->getTransportType()->getTag() == 'personal') {
            $delivery_addresses = [];
            $delivery_address_choice_options = [];
            $delivery_address_choice_options['identical'] = \ShoppingCart::getCheckout()->getTransportType()->getName();
        } else {
            $delivery_addresses = $this->getDeliveryAddresses();
            // Generate delivery address choices
            $delivery_address_choice_options = array();
            $delivery_address_choice_options['identical'] = \Trans::raw(lang('cart', 'Adresa rovnaká ako fakturačná'));
            $delivery_address_choice_options = $delivery_address_choice_options + $delivery_addresses;
            $delivery_address_choice_options['different'] = \Trans::raw(lang('cart', 'Nová adresa'));
        }
        // Company name
        $company_name = new ModernTextElementReadonly('company_name');
        $company_name->setLabel(\Trans::raw(lang('cart', 'Názov firmy')));
        $company_name->setRequired(true);
        $company_name->addClass('form_autosave without-label');
        $company_name->setAttrib('readonly', 'readonly');
        $this->addElement($company_name);

        $companyDataStringLength = ['max' => 255];
        // IČO
        $ico = new ModernTextElementReadonly('ico');
        $ico->setLabel(\Trans::raw(lang('cart', 'IČO')));
        $ico->setRequired($required);
        $ico->addValidator(new \Zend_Validate_StringLength($companyDataStringLength));
        $ico->addClass('form_autosave');
        $ico->setValueClass('col-sm-4');
        $ico->setAttrib('readonly', 'readonly');
        $this->addElement($ico);

        // DIČ
        $dic = new ModernTextElementReadonly('dic');
        $dic->setLabel(\Trans::raw(lang('cart', 'DIČ')));
        $dic->setRequired($required);
        $dic->addValidator(new \Zend_Validate_StringLength($companyDataStringLength));
        $dic->addClass('form_autosave');
        $dic->setValueClass('col-sm-4');
        $dic->setAttrib('readonly', 'readonly');
        $this->addElement($dic);

        // IČ DPH
        $drc = new ModernTextElementReadonly('drc');
        $drc->setLabel(\Trans::raw(lang('cart', 'IČ DPH')));
        $dic->addValidator(new \Zend_Validate_StringLength($companyDataStringLength));
        $drc->addClass('form_autosave');
        $drc->setValueClass('col-sm-4');
        $drc->setAttrib('readonly', 'readonly');
        $this->addElement($drc);

        // Email
        $email = new ModernTextElementReadonly('email');
        $email->setLabel(\Trans::raw(lang('cart', 'Email')));
        $email->setRequired(true);
        $email->addValidator(new \Zend_Validate_EmailAddress());
        $email->addFilter(new \Zend_Filter_StringTrim());
        $email->addClass('form_autosave');
        $email->addWrapperClass('email_wrapper');
        $email->setAttrib('readonly', 'readonly');
        $this->addElement($email);

        // Phone
        $phone = new ModernTextElementReadonly('phone');
        $phone->setLabel(\Trans::raw(lang('cart', 'Telefón')));
        $phone->setRequired(true);
        $phone->addValidator(new \Zend_Validate_Regex('/^[\+]?[0-9 ]+$/'));
        $phone->addClass('form_autosave');
        $phone->setValueClass('col-sm-4');
        $phone->setAttrib('readonly', 'readonly');
        $this->addElement($phone);

        // Street
        $street = new ModernTextElementReadonly('street');
        $street->setLabel(\Trans::raw(lang('cart', 'Ulica a číslo')));
        $street->setRequired(true);
        $street->addClass('form_autosave without-label');
        $street->setAttrib('readonly', 'readonly');
        $this->addElement($street);

        // City
        $city = new ModernTextElementReadonly('city');
        $city->setLabel(\Trans::raw(lang('cart', 'Mesto')));
        $city->setRequired(true);
        $city->addClass('form_autosave without-label');
        $city->setAttrib('readonly', 'readonly');
        $this->addElement($city);

        // ZIP
        $zip = new ModernTextElementReadonly('zip');
        $zip->setLabel(\Trans::raw(lang('cart', 'PSČ')));
        $zip->setRequired(true);
        $zip->addClass('form_autosave without-label');
        $zip->setValueClass('col-sm-4');
        $zip->setAttrib('readonly', 'readonly');
        $this->addElement($zip);

        $country = new ModernTextElementReadonly('country');
        $country->setLabel(\Trans::raw(lang('cart', 'Krajina')));
        $country->setRequired(false);
        $country->addClass('form_autosave without-label');
        $country->setValueClass('col-sm-4');
        $country->setAttrib('readonly', 'readonly');
        $this->addElement($country);

        // Delivery address choice
        $delivery_address_choice = new Element\ModernRadioElement('delivery_address_choice');
        $delivery_address_choice->setLabel(\Trans::raw(lang('cart', 'Doručenie')));
        $delivery_address_choice->setMultiOptions($delivery_address_choice_options);
        //$delivery_address_choice->setValue('different');
        $delivery_address_choice->setRequired(true);
        $delivery_address_choice->setSeparator('');
        $delivery_address_choice->addClass('form_autosave legacy-checkbox');
        $delivery_address_choice->addLabelClass('col-sm-3-half');
        $this->addElement($delivery_address_choice);

        // Delivery name
        $delivery_name = new Element\ModernTextElement('delivery_name');
        $delivery_name->setLabel(\Trans::raw(lang('cart', 'Meno a priezvisko')));
        $delivery_name->addClass('form_autosave');
        $delivery_name->addLabelClass('col-sm-3-half');
        $delivery_name->setRequired(false);
        $this->addElement($delivery_name);

        // Delivery company name
        $delivery_company_name = new Element\ModernTextElement('delivery_company_name');
        $delivery_company_name->setLabel(\Trans::raw(lang('cart', 'Firma')));
        $delivery_company_name->addClass('form_autosave');
        $delivery_company_name->addLabelClass('col-sm-3-half');
        $delivery_company_name->setRequired(true);
        $delivery_company_name->setAllowEmpty(false);
        $this->addElement($delivery_company_name);

        // Delivery phone
        $deliveryPhone = new Element\ModernTextElement('delivery_phone');
        $deliveryPhone->setLabel(\Trans::raw(lang('cart', 'Telefón')));
        $deliveryPhone->addValidator(new \Zend_Validate_Regex('/^[\+]?[0-9 ]+$/'));
        $deliveryPhone->addClass('form_autosave');
        $deliveryPhone->addLabelClass('col-sm-3-half');
        $this->addElement($deliveryPhone);

        // Delivery street
        $delivery_street = new Element\ModernTextElement('delivery_street');
        $delivery_street->setLabel(\Trans::raw(lang('cart', 'Ulica a číslo')));
        $delivery_street->setRequired(true);
        $delivery_street->addClass('form_autosave');
        $delivery_street->addLabelClass('col-sm-3-half');
        $this->addElement($delivery_street);

        // Delivery city
        $delivery_city = new Element\ModernTextElement('delivery_city');
        $delivery_city->setLabel(\Trans::raw(lang('cart', 'Mesto')));
        $delivery_city->setRequired(true);
        $delivery_city->addClass('form_autosave');
//        $delivery_city->setAttrib('tooltip', TooltipList::FORM_BASKET_DELIVERY_DATA_DELIVERY_CITY);
        $delivery_city->addLabelClass('col-sm-3-half');
        $this->addElement($delivery_city);

        // Delivery ZIP
        $delivery_zip = new Element\ModernTextElement('delivery_zip');
        $delivery_zip->setLabel(\Trans::raw(lang('cart', 'PSČ')));
        $delivery_zip->setRequired(true);
        $delivery_zip->addClass('form_autosave');
//        $delivery_zip->setAttrib('tooltip', TooltipList::FORM_BASKET_DELIVERY_DATA_DELIVERY_ZIP);
        $delivery_zip->addLabelClass('col-sm-3-half');
        $delivery_zip->setValueClass('col-sm-2');
        $this->addElement($delivery_zip);


        $deliveryCountryOptions = ['' => '---'];
        $deliveryCountryOptions = array_merge($deliveryCountryOptions, config('buxus_eshop.delivery_countries'));

        $deliveryCountryValue = '';

        if (\WebUserAuthentication::isAuthenticated()) {
            $user = \WebUserAuthentication::getUser();

            if (!empty($user->getDeliveryCountry()) && isset($deliveryCountryOptions[$user->getDeliveryCountry()])) {
                $deliveryCountryValue = $user->getDeliveryCountry();
            }
        }

        $deliveryCountry = new ModernSelectElement('delivery_country');
        $deliveryCountry->setLabel(\Trans::raw(lang('cart', 'Krajina')));
        $deliveryCountry->setRequired();
        $deliveryCountry->addClass('form_autosave');
        $deliveryCountry->addLabelClass('col-sm-3-half');
        $deliveryCountry->setValueClass('col-sm-2');
        $deliveryCountry->setMultiOptions($deliveryCountryOptions);
        $deliveryCountry->setValue($deliveryCountryValue);
        $this->addElement($deliveryCountry);


        // Note
        $note = new Element\ModernTextareaElement('note');
        $note->setLabel(\Trans::raw(lang('cart', 'Poznámky:')));
        $note->addClass('form_autosave');
        $note->setAttrib('rows', '5');
        $note->setAttrib('cols', '5');
//        $note->setAttrib('tooltip', TooltipList::FORM_BASKET_DELIVERY_DATA_NOTE);
        $note->addLabelClass('col-sm-3-half');
        $this->addElement($note);

        $previous_process = $this->process->getCheckout()->getPreviousProcess($this->process->getTag());
        if (!is_null($previous_process)) {
            $button = new LinkElement('back');
            $button->addClass('btn btn-cart-back');
            $button->clearValueClasses();
            $button->clearWrapperClasses();
            $button->setLabel(\Trans::raw('<i class="glyphicon glyphicon-chevron-left"></i> ' . lang('cart', 'Späť na dodávku a platbu')));
            $button->setAttrib('href', $previous_process->getUrl());
            $this->addElement($button);
        }

        $button = new SubmitElement($this->getFormTag());
        $button->addClass(' btn-primary');
        $button->clearValueClasses();
        $button->clearWrapperClasses();
        $button->setLabel(\Trans::raw(lang('cart', 'Pokračovať v objednávke')));
        $this->addElement($button);

        foreach ($this->getPlugins() as $plugin) {
            $plugin->addElementsToForm($this);
        }

        // Set translation
        $this->setTranslation();

        $this->applyWrapperClasses($this->getCorporateElementNames(), 'eshop-body-corporate-fields');
        $this->applyWrapperClasses($this->getPersonElementNames(), 'eshop-person-fields');
        $this->applyWrapperClasses($this->getDeliveryAddressElementNames(), 'eshop-delivery-address');
    }

    public function getDeliveryAddresses()
    {
        if (\WebUserAuthentication::isAuthenticated()) {
            $user = \WebUserAuthentication::getUser();
            $deliveryAddresses = DeliveryAddress::where('webuser_id', $user->getUserId())->get();

            if ($deliveryAddresses->isNotEmpty()) {
                foreach ($deliveryAddresses as $deliveryAddress) {
                    $deliveryAddressesArray[$deliveryAddress->id] = ($deliveryAddress->company_name ?: $deliveryAddress->fullname) . ", {$deliveryAddress->street}, {$deliveryAddress->city}, {$deliveryAddress->country}";
                }

                return $deliveryAddressesArray;
            }
        }

        return [];
    }

    protected function getPersonElementNames()
    {
        return [];
    }

    protected function getDeliveryAddressElementNames()
    {
        $deliveryAddressElementNames = parent::getDeliveryAddressElementNames();

        if (BuxusSite::site() == 'en') {
            $deliveryAddressElementNames[] = 'delivery_country';
        }

        return $deliveryAddressElementNames;
    }

    protected function getRequiredDeliveryAddressElements()
    {
        $requiredDeliveryAddressElements = array(
            'delivery_street',
            'delivery_city',
            'delivery_zip',
            'delivery_country',
        );

        $pluginRequiredDeliveryAddressElements = [];
        foreach ($this->getPlugins() as $plugin) {
            $pluginRequiredDeliveryAddressElements = $plugin->getRequiredDeliveryAddressElementNames();
        }

        $requiredDeliveryAddressElements = array_merge($requiredDeliveryAddressElements, $pluginRequiredDeliveryAddressElements);

        return $requiredDeliveryAddressElements;
    }
}
