<?php

namespace App\Email;

use App;
use Buxus\Email\BaseEmail;
use Buxus\Error\ErrorReporter;
use Exception;

class ExtendedBaseEmail extends BaseEmail
{
    public function send($dest)
    {
        if (App::environment('live')) {
            return parent::send($dest);
        }

        try {
            $this->sendInternal($dest, 'smtp');
        } catch (Exception $e) {
            ErrorReporter::reportSilent($e);
        }

        return true;
    }
}
