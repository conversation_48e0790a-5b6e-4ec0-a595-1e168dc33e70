<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use PriceViewer;

class SlowMovingStockStatisticsExport implements FromCollection, WithHeadings
{
    protected $collection;
    protected $producers_cache;

    public function __construct(Collection $collection)
    {
        $this->collection = $collection;
        $this->producers_cache = \Ciselniky::get('producer')->getAllValues();
    }

    public function headings(): array
    {
        return [
            'ONIX číslo dielu',
            'Hlavn<PERSON> kód',
            'Názov produktu',
            'Výrob<PERSON>',
            'Počet kusov na sklade (aktuálny)',
            'Nakupná cena za kus',
        ];
    }

    public function collection()
    {
        return $this->collection->map(function ($item) {
            $producer = '';
            if (!empty($item->producer_id)) {
                $producer = $this->producers_cache[$item->producer_id]->getName();
            }

            return collect([
                'onix_ns_number' => $item->onix_ns_number ?? '',
                'main_code' => $item->onix_main_code ?? '',
                'title' => $item->title ?? '',
                'producer' => $producer,
                'current_stock' => number_format($item->current_stock ?? 0, 0),
                'price' => PriceViewer::formatRawPrice($item->price ?? 0, '€'),
            ]);
        });
    }
}
