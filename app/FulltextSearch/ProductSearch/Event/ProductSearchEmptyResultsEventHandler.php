<?php

namespace App\FulltextSearch\ProductSearch\Event;

use App\ProductSearchStatistics;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;

class ProductSearchEmptyResultsEventHandler implements ShouldQueue
{
    use Queueable;

    /**
     * Get the name of the listener's queue.
     *
     * @return string
     */
    public function viaQueue()
    {
        return 'statistics_data_' . env('DB_DATABASE');
    }

    public function handle(ProductSearchEmptyResultsEvent $event)
    {
        $statistics = ProductSearchStatistics::create([
            'search_term' => $event->getTerm(),
        ]);

        $statistics->save();
    }
}
