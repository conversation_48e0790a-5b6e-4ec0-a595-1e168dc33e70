<?php

namespace App\FulltextSearch\ProductSearch\Event;

use App\Eshop\Product;
use App\ProductSearchStatistics;
use Buxus\Ciselniky\ValueInterface;
use Buxus\Error\ErrorReporter;
use Buxus\Util\PropertyTag;
use Ciselniky;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;

class ProductImpressionEventHandler implements ShouldQueue
{
    use Queueable;

    /**
     * Get the name of the listener's queue.
     *
     * @return string
     */
    public function viaQueue()
    {
        return 'statistics_data_' . env('DB_DATABASE');
    }

    public function handle(ProductImpressionEvent $event)
    {
        $product = $event->getProduct();

        $producer = $this->getProducerForProduct($product);

        $statistics = ProductSearchStatistics::create([
            'page_id' => $product->getPage()->getPageId(),
            'onix_number' => $product->getPage()->getValue(PropertyTag::ONIX_NS_NUMBER_TAG()),
            'producer' => $producer,
            'main_code' => $product->getProductCode(),
            'title' => $product->getPage()->getValueWithoutFallback(PropertyTag::TITLE_TAG()),
            'search_term' => $event->getTerm(),
            'image' => $product->hasImages(),
            'database' => $product->getSourceDatabaseName(),
        ]);

        $statistics->save();
    }

    protected function getProducerForProduct(Product $product)
    {
        $producer = '';

        $ciselnik = Ciselniky::get('producer');

        try {
            $value = $ciselnik->getValueById($product->getPage()->getValue(PropertyTag::ESHOP_ROLLER_PRODUCER_TAG()));
            if ($value instanceof ValueInterface) {
                $producer = $value->getName();
            }
        } catch (\Exception $e) {
        }

        return $producer;
    }
}
