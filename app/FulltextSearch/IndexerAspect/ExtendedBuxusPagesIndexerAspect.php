<?php

namespace App\FulltextSearch\IndexerAspect;

use App\Eshop\Product;
use App\FulltextSearch\ExtendedSearchIndex;
use App\FulltextSearch\IndexerAspect\Properties\AlternativePricesCodePropertyPreprocessor;
use App\FulltextSearch\IndexerAspect\Properties\OnixSupplierCodePropertyPreprocessor;
use App\FulltextSearch\IndexerAspect\Properties\OnixSupplierDataPropertyPreprocessor;
use App\FulltextSearch\IndexerAspect\Properties\PropertyPreprocessorInterface;
use Buxus\Page\PageInterface;
use Buxus\Util\PropertyTag;
use FullTextSearch\IndexerAspect\BuxusPagesIndexerAspect;
use FullTextSearch\SearchDocument;
use FullTextSearch\SearchIndex;

class ExtendedBuxusPagesIndexerAspect extends BuxusPagesIndexerAspect
{

    protected function getCustomPropertyBindings(): array
    {
        return [
            PropertyTag::ALTERNATIVE_PRICES_CODE_TAG() => AlternativePricesCodePropertyPreprocessor::class,
            PropertyTag::CASCO_SUPPLIER_CODE_TAG() => AlternativePricesCodePropertyPreprocessor::class,
            PropertyTag::MEC_DIESEL_SUPPLIER_CODE_TAG() => AlternativePricesCodePropertyPreprocessor::class,
            PropertyTag::ONIX_SUPPLIER_CODES_TAG() => OnixSupplierCodePropertyPreprocessor::class,
            PropertyTag::ONIX_SUPPLIER_CODES_DATA_TAG() => OnixSupplierDataPropertyPreprocessor::class,
        ];
    }

    protected function normalizeSpecialCharacters($str)
    {
        $unwanted_array = [
            chr(0) => ' ',
            chr(1) => ' ',
            chr(2) => ' ',
            chr(3) => ' ',
            chr(4) => ' ',
            chr(5) => ' ',
            chr(6) => ' ',
            chr(7) => ' ',
            chr(8) => ' ',
            chr(11) => ' ',
            chr(12) => ' ',
            chr(14) => ' ',
            chr(15) => ' ',
            chr(16) => ' ',
            chr(17) => ' ',
            chr(18) => ' ',
            chr(19) => ' ',
            chr(20) => ' ',
            chr(21) => ' ',
            chr(22) => ' ',
            chr(23) => ' ',
            chr(24) => ' ',
            chr(25) => ' ',
            chr(26) => ' ',
            chr(27) => ' ',
            chr(28) => ' ',
            chr(29) => ' ',
            chr(30) => ' ',
            chr(31) => ' ',
        ];

        if (is_array($str)) {
            $escaped = [];
            foreach ($str as $value) {
                $value = trim($value);
                $valueWithoutSpaces = str_replace(' ', '', $value);
                if ($valueWithoutSpaces != $value) {
                    $escaped[] = strtr($valueWithoutSpaces, $unwanted_array);
                }
                $escaped[] = strtr($value, $unwanted_array);
            }

            return $escaped;
        }

        return strtr($str, $unwanted_array);
    }

    public function process(PageInterface $page, SearchDocument $doc)
    {
        if ($this->site !== null) {
            \BuxusSite::pushSite($this->site);
        }

        if ($this->language !== null) {
            \Trans::pushLanguage($this->language);
        }

        if ($this->callback !== null && is_callable($this->callback)) {
            $properties = call_user_func($this->callback, $page);
            foreach ($properties as $key => $value) {
                $doc->$key = $value;
            }
        } else {
            foreach ($this->properties as $property) {
                if (in_array($property, array_keys($this->getCustomPropertyBindings()))) {
                    $value = $this->processCustomProperties($page, $property);
                } else {
                    $value = $this->getPropertyValue($page, $property);
                }

                if ($this->getFieldType($property) == ExtendedSearchIndex::FIELD_TYPE_MULTIVALUED) {
                    if (!is_array($value)) {
                        $value = explode(',', $value);
                    }
                    $value = array_filter($value);
                }

                if ($this->getFieldType($property) == SearchIndex::FIELD_TYPE_DATETIME) {
                    $value = date('Y-m-d\TH:i:s\Z', strtotime($value));
                }

                if ($value === null && isset($this->defaultValues[$property])) {
                    $value = $this->defaultValues[$property];
                }

                if ($this->removeHtml) {
                    $value = strip_tags($value);
                    $value = preg_replace('@&nbsp;@', ' ', $value);
                    $value = html_entity_decode($value);
                }

                if (is_array($value)) {
                    $value = array_map(function ($value) {
                        return str_replace('.', '', $value);
                    }, $value);
                }

                if (is_array($value)) {
                    $value = array_map(function ($value) {
                        return Product::prepareStringForSearch($value);
                    }, $value);
                }
                // generally remove silly characters
                $value = $this->normalizeSpecialCharacters($value);

                $doc->$property = $value;
            }
        }

        if ($this->language !== null) {
            \Trans::popLanguage();
        }

        if ($this->site !== null) {
            \BuxusSite::popSite();
        }
    }

    protected function processCustomProperties(PageInterface $page, $property)
    {
        $bindings = $this->getCustomPropertyBindings();

        /** @var PropertyPreprocessorInterface $preprocessor */
        $preprocessor = new $bindings[$property]();
        return $preprocessor->process($page, $property);
    }
}
