<?php

namespace App\FulltextSearch\IndexerAspect\Properties;

use Buxus\Page\PageInterface;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;

class AlternativePricesCodePropertyPreprocessor implements PropertyPreprocessorInterface
{
    public function process(PageInterface $page, $property)
    {
        $supplier = $page->getValue(PropertyTag::SUPPLIER_TAG());

        if (in_array($supplier, $this->getAllowedSuppliers())) {
            return explode(',', $page->getValue($property));
        }

        if (!in_array($supplier, $this->getConditionallyAllowedSuppliers())) {
            return null;
        }

        return $this->processConditionalSupplierCodes($page, $property);
    }

    protected function getAllowedSuppliers(): array
    {
        return [
            PageIds::getFebiBilsteinSupplier(),
            PageIds::getErreviSupplier(),
            PageIds::getLemaSupplier(),
            PageIds::getCovindSupplier(),
            PageIds::getSaboSupplier(),
            PageIds::getVignalSupplier(),
            PageIds::getNrfSupplier(),
            PageIds::getCleanSupplier(),
        ];
    }

    protected function getConditionallyAllowedSuppliers(): array
    {
        return [
            PageIds::getCeiSupplier(),
            PageIds::getCascoSupplier(),
            PageIds::getMecDieselSupplier(),
        ];
    }

    protected function processConditionalSupplierCodes(PageInterface $page, $property)
    {
        $supplier = $page->getValue(PropertyTag::SUPPLIER_TAG());

        if ($supplier == PageIds::getCascoSupplier() && $page->getValue(PropertyTag::ESHOP_ROLLER_PRODUCER_TAG()) == PageIds::getCascoProducer()
        ) {
            return explode(',', $page->getValue($property));
        }

        if ($supplier == PageIds::getMecDieselSupplier() &&
            (
                $page->getValue(PropertyTag::ESHOP_ROLLER_PRODUCER_TAG()) == PageIds::getMecDieselProducer() ||
                $page->getValue(PropertyTag::ESHOP_ROLLER_PRODUCER_TAG()) == PageIds::getBlinkenProducer() ||
                $page->getValue(PropertyTag::ESHOP_ROLLER_PRODUCER_TAG()) == PageIds::getErarProducer() ||
                empty($page->getValue(PropertyTag::MEC_DIESEL_OE_NUMBERS_TAG()))
            )
        ) {
            return explode(',', $page->getValue($property));
        }

        if ($supplier == PageIds::getCeiSupplier()
        ) {
            $propertyValue = $page->getValue($property);

            $propertyValue = explode(',', $propertyValue);
            $propertyValue = array_map('trim', $propertyValue);
            $propertyValue = array_map(function ($value) {
                return str_replace('.', '', $value);
            }, $propertyValue);

            return $propertyValue;
        }


        return null;
    }
}