<?php

namespace App\FulltextSearch\Filters;

use App\Eshop\Product;

class Onix implements FulltextSearchFilterInterface
{
    protected $products;
    protected $resultProducts;
    protected $hiddenProducts;

    public function __construct()
    {
        $this->resultProducts = [];
        $this->hiddenProducts = [];
    }

    public function filter($products): array
    {
        $this->initProducts($products);

        $this->initResultProducts();

        return $this->resultProducts;
    }

    protected function initProducts($products)
    {
        foreach ($products as $product) {
            $this->products[$product->getPageId()] = $product;
        }
    }

    protected function initResultProducts()
    {
        $this->initProductsWithoutSupplierCode();
        $this->initBestOffers();
    }

    protected function initBestOffers()
    {
        foreach ($this->products as $product) {
            if ($product->isOnixProduct() && $product->isInStock()) {
                $this->resultProducts[$product->getPageId()] = $product;
            }
        }

        $this->subtractResultProducts();

        $lowestPrice = $this->resultProducts[array_key_first($this->resultProducts)]->getFinalPriceWithoutVatValue() ?? 1000000000;

        foreach ($this->resultProducts as $resultProduct) {
            if ($resultProduct->getFinalPriceWithoutVatValue() < $lowestPrice) {
                $lowestPrice = $resultProduct->getFinalPriceWithoutVatValue();
            }
        }

        foreach ($this->products as $product) {
            if (($product->isInStock() || $product->isInExternalStock()) && $product->getFinalPriceWithoutVatValue() < $lowestPrice) {
                $this->resultProducts[$product->getPageId()] = $product;
            } else {
                $this->hiddenProducts[$product->getPageId()] = $product;
            }
        }

        $this->subtractResultProducts();
    }

    protected function initProductsWithoutSupplierCode()
    {
        foreach ($this->products as $k => $product) {
            if (empty($product->getSupplierCodes())) {
                $this->resultProducts[$k] = $product;
            }
        }

        $this->subtractResultProducts();
    }

    protected function subtractResultProducts()
    {
        foreach ($this->resultProducts as $k => $v) {
            unset($this->products[$k]);
        }
    }

    public function getHiddenProducts(): array
    {
        return $this->hiddenProducts;
    }
}
