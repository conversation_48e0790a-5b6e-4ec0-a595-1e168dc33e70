<?php

namespace App\FulltextSearch\Event;

use App\Notifier\TooManySearchesNotifier;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;

class WebUserSearchEventHandler
{
    public function handle(WebUserSearchEvent $event)
    {
        $table = \DB::table('tblWebUserSearchLog');

        $user = $event->getUser();

        $table->insert([
            'webuser_id' => $user->getUserId(),
            'search_term' => $event->getTerm(),
            'search_time' => Carbon::now(),
        ]);

        $this->notifyAboutTooManySearches($event);
    }

    protected function notifyAboutTooManySearches(WebUserSearchEvent $event)
    {
        $table = \DB::table('tblWebUserSearchLog');

        $user = $event->getUser();

        $searches = $table
            ->join('tblWebUsers', 'tblWebUserSearchLog.webuser_id', '=', 'tblWebUsers.user_id')
            ->whereDate('search_time', Carbon::now()->toDateString())
            ->where('tblWebUserSearchLog.webuser_id', $user->getUserId())
            ->selectRaw('tblWebUsers.company_name, tblWebUserSearchLog.webuser_id, COUNT(DISTINCT search_term) as searches')
            ->first();

        if ($searches->searches > 40) {
            $notifier = new TooManySearchesNotifier();
            $notifier->notify($user, $searches);
        }
    }
}
