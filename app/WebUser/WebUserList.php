<?php

namespace App\WebUser;

use BuxusDB;
use Zend_Filter_Input;
use Zend_Validate_InArray;
use Zend_Validate_Regex;

class WebUserList
{
    /**
     * Filter data
     *
     * @var array
     */
    protected $filter = array();

    /**
     * ID of user
     *
     * @var int
     */
    protected $user_id;

    /**
     * Admin rights flag
     *
     * @var string
     */
    protected $admin_right;

    /**
     * Mailinglist values
     *
     * @var array
     */
    protected $mailing_list_values;

    /**
     * List of user options
     *
     * @var array
     */
    protected $user_option_tag_values;

    /**
     * List of all selected users IDs
     *
     * @var array
     */
    protected $users_id;

    /**
     * The interval of users data which will be shown
     *
     * @var array
     */
    protected $partial_user_data;

    /**
     * Constructor
     *
     * @param array $filter
     */
    public function __construct($filter, $user_id, $admin_right)
    {
        $this->user_id = $user_id;
        $this->admin_right = $admin_right;

        // Select keys
        $atf_keys = $this->extractKeys($this->getAtfSelectValues());
        $mailing_list_keys = $this->extractKeys($this->getMailinglistValues());
        $user_option_tag_keys = $this->extractKeys($this->getUserOptionTagValues());
        $user_option_operator_keys = $this->extractKeys($this->getUserOptionOperatorValues());
        $order_result_by_keys = $this->extractKeys($this->getOrderResultByValues());
        $labels = $this->extractKeys($this->getLabelSelectValues());

        // Define validators
        $validators = array();
        $validators['search_username'] = array();
        $validators['search_e_mail'] = array();
        $validators['search_surname'] = array();
        $validators['search_company_name'] = array();
        $validators['search_company_name_text'] = array();
        $validators['search_active'] = array(new Zend_Validate_InArray($atf_keys, true));
        $validators['search_sales_representant'] = array(new Zend_Validate_InArray($atf_keys, true));
        $validators['search_expire_from'] = array(new Zend_Validate_Regex('/^[0-9]{4}-[0-9]{2}-[0-9]{2}$/'), 'allowEmpty' => true);
        $validators['search_expire_to'] = array(new Zend_Validate_Regex('/^[0-9]{4}-[0-9]{2}-[0-9]{2}$/'), 'allowEmpty' => true);
        $validators['search_register_from'] = array(new Zend_Validate_Regex('/^[0-9]{4}-[0-9]{2}-[0-9]{2}$/'), 'allowEmpty' => true);
        $validators['search_register_to'] = array(new Zend_Validate_Regex('/^[0-9]{4}-[0-9]{2}-[0-9]{2}$/'), 'allowEmpty' => true);
        $validators['search_enable_login'] = array(new Zend_Validate_InArray($atf_keys, true));
        $validators['search_enable_mailing_lists'] = array(new Zend_Validate_InArray($atf_keys, true));
        $validators['search_enable_blog'] = array(new Zend_Validate_InArray($atf_keys, true));
        $validators['mailing_lists_tag'] = array(new Zend_Validate_InArray($mailing_list_keys, true), 'allowEmpty' => true);
        $validators['search_label'] = array(new Zend_Validate_InArray($labels, true));
        $validators['search_site'] = array(new Zend_Validate_InArray(\BuxusSite::getAvailableSites(), true));
        // User options
        $this->filter['user_options'] = array();
        for ($i = 0; $i < C_NR_SEARCH_LINES; $i++) {
            $validators['user_option_tag_' . $i] = array(new Zend_Validate_InArray($user_option_tag_keys, true), 'allowEmpty' => true);
            $validators['user_option_operator_' . $i] = array(new Zend_Validate_InArray($user_option_operator_keys, true));
            $validators['user_option_value_' . $i] = array('allowEmpty' => true);
        }

        $validators['results_order_by'] = array(new Zend_Validate_InArray($order_result_by_keys, true));
        $validators['C_HTML_ItemListCount'] = array('Int', 'allowEmpty' => true);
        $validators['first_item_index'] = array('Int', 'allowEmpty' => true);
        $validators['filter_applied'] = array(new Zend_Validate_InArray(array('0', '1'), true));

        // Filter data
        $input_filter = new Zend_Filter_Input(array(), $validators, $filter, array('presence' => 'required'));
        $this->filter = $input_filter->getUnescaped();
        //dd($this->filter);

        // Default values
        $default_values = array(
            'search_active' => 'A',
            'search_sales_representant' => 'A',
            'search_enable_login' => 'A',
            'search_enable_mailing_lists' => 'A',
            'search_enable_blog' => 'A',
            'search_site' => 'A',
            'results_order_by' => 'username',
            'C_HTML_ItemListCount' => (int)GetSystemOption('C_list_page_limit'),
            'first_item_index' => 0,
            'filter_applied' => 0,
        );

        // Set default values of user options
        for ($i = 0; $i < C_NR_SEARCH_LINES; $i++) {
            $default_values['user_option_operator_' . $i] = '=';
        }

        // Set default values
        $tags = array_merge(array_keys($input_filter->getInvalid()), array_keys($input_filter->getMissing()));
        foreach ($tags as $tag) {
            $this->filter[$tag] = ((isset($default_values[$tag])) ? $default_values[$tag] : null);
        }
    }

    /**
     * Return filter value
     *
     * @param string $tag
     * @return mixed
     */
    public function getFilterValue($tag)
    {
        if (isset($this->filter[$tag])) { // The filter is defined
            return $this->filter[$tag];
        }

        return null;
    }

    /**
     * Set filter value
     *
     * @param string $tag
     * @param mixed $value
     */
    public function setFilterValue($tag, $value)
    {
        $this->filter[$tag] = $value;
    }

    /**
     * Return filter values
     *
     * @return array
     */
    public function getFilterValues()
    {
        return $this->filter;
    }

    /**
     * Load user data
     */
    protected function loadUsersId()
    {
        $users_id = array();

        if ($this->getFilterValue('filter_applied') == 1) { // The form was sent
            $sql_params = array();

            $query = "
			SELECT
				DISTINCT WU.user_id, " . BuxusDB::get()->addslashes(str_contains($this->getFilterValue('results_order_by'), ' desc') ? str_replace(' desc', '', $this->getFilterValue('results_order_by')) : $this->getFilterValue('results_order_by')) . "
			FROM
				tblWebUsers WU

				LEFT JOIN
					tblWebUserOptions WUO
					ON
						WU.user_id = WUO.user_id ";

            if ($this->getFilterValue('mailing_lists_tag')) { // Mailinglist is defined
                $query .= "
			INNER JOIN
				tblWebUserOptions WUO1
			ON
				WU.user_id = WUO1.user_id
				AND
				WUO1.user_option_tag = :mailing_lists_tag";
                $sql_params[':mailing_lists_tag'] = 'mailing_list_' . $this->getFilterValue('mailing_lists_tag');
            }

            $label_filter = $this->getFilterValue('search_label');
            if ($label_filter != '_') {
                $query .= "
                    INNER JOIN
                        tblWebUserLabels WUL
                    ON
                        WU.user_id = WUL.user_id
                        AND
                        WUL.label = :label";
                $sql_params[':label'] = $label_filter;
            }

            for ($i = 0; $i < C_NR_SEARCH_LINES; $i++) {
                $option_name = 'user_option_tag_' . $i;
                $option_operator = 'user_option_operator_' . $i;
                $option_value = 'user_option_value_' . $i;

                if (($this->getFilterValue($option_name)) && ($this->getFilterValue($option_operator)) && ($this->getFilterValue($option_value) != '')) {
                    $table_name = 'WUO' . ($i + 2);
                    switch ($this->getFilterValue($option_operator)) {
                        case '=':
                            $query .= "
						INNER JOIN tblWebUserOptions " . $table_name . " ON WU.user_id = " . $table_name . ".user_id AND TRIM(" . $table_name . ".user_option_tag) = :option_name" . ($i + 2) . " AND TRIM(" . $table_name . ".user_option_value) = :option_value" . ($i + 2);
                            $sql_params[':option_name' . ($i + 2)] = $this->getFilterValue($option_name);
                            $sql_params[':option_value' . ($i + 2)] = $this->getFilterValue($option_value);
                            break;
                        case 'begin':
                            $query .= "
						INNER JOIN tblWebUserOptions " . $table_name . " ON WU.user_id = " . $table_name . ".user_id AND TRIM(" . $table_name . ".user_option_tag) = :option_name" . ($i + 2) . " AND TRIM(" . $table_name . ".user_option_value) LIKE :option_value" . ($i + 2);
                            $sql_params[':option_name' . ($i + 2)] = $this->getFilterValue($option_name);
                            $sql_params[':option_value' . ($i + 2)] = $this->getFilterValue($option_value) . '%';
                            break;
                        case 'contain':
                            $query .= "
						INNER JOIN tblWebUserOptions " . $table_name . " ON WU.user_id = " . $table_name . ".user_id AND TRIM(" . $table_name . ".user_option_tag) = :option_name" . ($i + 2) . " AND TRIM(" . $table_name . ".user_option_value) LIKE :option_value" . ($i + 2);
                            $sql_params[':option_name' . ($i + 2)] = $this->getFilterValue($option_name);
                            $sql_params[':option_value' . ($i + 2)] = '%' . $this->getFilterValue($option_value) . '%';
                            break;
                        case 'behind':
                            $query .= "
						INNER JOIN tblWebUserOptions " . $table_name . " ON WU.user_id = " . $table_name . ".user_id AND TRIM(" . $table_name . ".user_option_tag) = :option_name" . ($i + 2) . " AND TRIM(" . $table_name . ".user_option_value) > :option_value" . ($i + 2);
                            $sql_params[':option_name' . ($i + 2)] = $this->getFilterValue($option_name);
                            $sql_params[':option_value' . ($i + 2)] = $this->getFilterValue($option_value);
                            break;
                        case 'before':
                            $query .= "
						INNER JOIN tblWebUserOptions " . $table_name . " ON WU.user_id = " . $table_name . ".user_id AND TRIM(" . $table_name . ".user_option_tag) = :option_name" . ($i + 2) . " AND TRIM(" . $table_name . ".user_option_value) < :option_value" . ($i + 2);
                            $sql_params[':option_name' . ($i + 2)] = $this->getFilterValue($option_name);
                            $sql_params[':option_value' . ($i + 2)] = $this->getFilterValue($option_value);
                            break;
                        case 'geq':
                            $query .= "
						INNER JOIN tblWebUserOptions " . $table_name . " ON WU.user_id = " . $table_name . ".user_id AND TRIM(" . $table_name . ".user_option_tag) = :option_name" . ($i + 2) . " AND " . $table_name . ".user_option_value >= :option_value" . ($i + 2);
                            $sql_params[':option_name' . ($i + 2)] = $this->getFilterValue($option_name);
                            $sql_params[':option_value' . ($i + 2)] = $this->getFilterValue($option_value);
                            break;
                        case 'leq':
                            $query .= "
						INNER JOIN tblWebUserOptions " . $table_name . " ON WU.user_id = " . $table_name . ".user_id AND TRIM(" . $table_name . ".user_option_tag) = :option_name" . ($i + 2) . " AND " . $table_name . ".user_option_value <= :option_value" . ($i + 2);
                            $sql_params[':option_name' . ($i + 2)] = $this->getFilterValue($option_name);
                            $sql_params[':option_value' . ($i + 2)] = $this->getFilterValue($option_value);
                            break;
                        case 'gt':
                            $query .= "
						INNER JOIN tblWebUserOptions " . $table_name . " ON WU.user_id = " . $table_name . ".user_id AND TRIM(" . $table_name . ".user_option_tag) = :option_name" . ($i + 2) . " AND " . $table_name . ".user_option_value > :option_value" . ($i + 2);
                            $sql_params[':option_name' . ($i + 2)] = $this->getFilterValue($option_name);
                            $sql_params[':option_value' . ($i + 2)] = $this->getFilterValue($option_value);
                            break;
                        case 'lt':
                            $query .= "
						INNER JOIN tblWebUserOptions " . $table_name . " ON WU.user_id = " . $table_name . ".user_id AND TRIM(" . $table_name . ".user_option_tag) = :option_name" . ($i + 2) . " AND " . $table_name . ".user_option_value < :option_value" . ($i + 2);
                            $sql_params[':option_name' . ($i + 2)] = $this->getFilterValue($option_name);
                            $sql_params[':option_value' . ($i + 2)] = $this->getFilterValue($option_value);
                            break;
                    }
                }

                if ($this->getFilterValue($option_name) && $this->getFilterValue($option_operator) === 'not_empty') {
                    $table_name = 'WUO' . ($i + 2);
                    $query .= "
						INNER JOIN tblWebUserOptions " . $table_name . " ON WU.user_id = " . $table_name . ".user_id AND TRIM(" . $table_name . ".user_option_tag) = :option_name" . ($i + 2) . " AND TRIM(" . $table_name . ".user_option_value) <> '' ";
                    $sql_params[':option_name' . ($i + 2)] = $this->getFilterValue($option_name);
                }
            }

            $query_where = array();
            if (!is_null($this->getFilterValue('search_username'))) { // The username is defined
                $query_where[] = "
					(username LIKE :username || CONVERT(surname USING utf8) LIKE :username || CONVERT(first_name USING utf8) LIKE :username || CONVERT(CONCAT(first_name,' ', surname)  USING utf8)LIKE :username) ";
                $sql_params[':username'] = $this->getFilterValue('search_username') . '%';
            }
            if (!is_null($this->getFilterValue('search_e_mail'))) { // The email is defined
                $query_where[] = "
					e_mail LIKE :search_e_mail";
                $sql_params[':search_e_mail'] = '%' . $this->getFilterValue('search_e_mail') . '%';
            }
            if (!is_null($this->getFilterValue('search_surname'))) { // The surneme is defined
                $query_where[] = "
					surname LIKE :surname";
                $sql_params[':surname'] = $this->getFilterValue('search_surname') . '%';
            }
            if (!is_null($this->getFilterValue('search_company_name'))) { // The company name is defined
                $query_where[] = "
					company_name LIKE :company_name";
                $sql_params[':company_name'] = '%' . $this->getFilterValue('search_company_name') . '%';
            }
            if (!is_null($this->getFilterValue('search_company_name_text'))) { // The company name is defined
                $query_where[] = "
					company_name LIKE :company_name";
                $sql_params[':company_name'] = '%' . $this->getFilterValue('search_company_name_text') . '%';
            }
            if ($this->getFilterValue('search_active') != 'A') { // The state is defined
                $query_where[] = "
					WU.active = :active";
                $sql_params[':active'] = $this->getFilterValue('search_active');
            }
            if ($this->getFilterValue('search_site') != 'A') { // The state is defined
                $query_where[] = "
					WU.site = :site";
                $sql_params[':site'] = $this->getFilterValue('search_site');
            }
            if ($this->getFilterValue('search_enable_login') != 'A') { // The login flag is selected
                $query_where[] = "
					WU.enable_login = :enable_login";
                $sql_params[':enable_login'] = $this->getFilterValue('search_enable_login');
            }
            if ($this->getFilterValue('search_enable_mailing_lists') != 'A') { // The sending mailinglist flag is selected
                $query_where[] = "
					WU.enable_mailing_lists = :enable_mailing_lists";
                $sql_params[':enable_mailing_lists'] = $this->getFilterValue('search_enable_mailing_lists');
            }
            if ($this->getFilterValue('mailing_lists_tag')) { // Mailinglist is defined
                $query_where[] = "
					WUO1.user_option_value = :mailing_list_enabled_value";
                $sql_params[':mailing_list_enabled_value'] = 'T';
            }
            if ($this->getFilterValue('search_sales_representant') != 'A') { // Mailinglist is defined
                $query_where[] = "WUO.user_option_tag = :user_option_tag_sales AND
					WUO.user_option_value = :search_sales_representant";
                $sql_params[':search_sales_representant'] = $this->getFilterValue('search_sales_representant');
                $sql_params[':user_option_tag_sales'] = 'is_sales_representant';
            }

            //dd($this->getFilterValues());
            //dd($this->getFilterValue('search_sales_representant'), $query_where, $sql_params);

            if ($this->getFilterValue('search_enable_blog') != 'A') { // The blog flag is selected
                $query_where[] = "
					WU.enable_blog = :enable_blog";
                $sql_params[':enable_blog'] = $this->getFilterValue('search_enable_blog');
            }

            /**
             * Expiration filter: valid are users within the selected range or
             * those that do not expire (their valid_till_time column is NULL):
             */
            if (($this->getFilterValue('search_expire_from') != '') || ($this->getFilterValue('search_expire_to') != '')) {
                $valid_condition = array();
                if ($this->getFilterValue('search_expire_from') != '') {
                    $valid_condition[] = "WU.valid_till_time >= :valid_till_time";
                    $sql_params[':valid_till_time'] = $this->getFilterValue('search_expire_from');
                }
                if ($this->getFilterValue('search_expire_to') != '') {
                    $valid_condition[] = "WU.valid_till_time <= :valid_till_time";
                    $sql_params[':valid_till_time'] = $this->getFilterValue('search_expire_to');
                }

                $query_where[] = "
					(
						(" . implode(' AND ', $valid_condition) . ")
						OR
						WU.valid_till_time IS NULL
					)";
            }

            if (($this->getFilterValue('search_register_from') != '') || ($this->getFilterValue('search_register_to') != '')) {
                $valid_condition = array();
                if ($this->getFilterValue('search_register_from') != '') {
                    $valid_condition[] = "WU.creation_date >= :creation_date_min";
                    $sql_params[':creation_date_min'] = $this->getFilterValue('search_register_from');
                }
                if ($this->getFilterValue('search_register_to') != '') {
                    $valid_condition[] = "WU.creation_date <= :creation_date_max";
                    $sql_params[':creation_date_max'] = $this->getFilterValue('search_register_to');
                }

                $query_where[] = "
					(
						(" . implode(' AND ', $valid_condition) . ")
						OR
						WU.creation_date IS NULL
					)";
            }

            if (!empty($query_where)) { // There is some conditions
                $query .= "
					WHERE " . implode(' AND ', $query_where);
            }

            if (!str_contains($this->getFilterValue('results_order_by'), ' desc')) {
                $query .= "
				ORDER BY " . BuxusDB::get()->addslashes($this->getFilterValue('results_order_by'));
            } else {
                $query .= "
				ORDER BY " . BuxusDB::get()->addslashes(str_replace(' desc', '', $this->getFilterValue('results_order_by')) . ' desc');
            }


            //echo '<pre>' . $query . '</pre>';
            $users_id = BuxusDB::get()->fetchCol($query, $sql_params);
        }

        $this->users_id = $users_id;
    }

    /**
     * Load user data
     */
    protected function loadPartialUserData()
    {
        $partial_user_data = array();

        if (($this->getFilterValue('filter_applied') == 1) && (!empty($this->users_id))/* && is_array($this->users_id) && count($this->users_id)*/) { // The form was sent and the some user were selected
            $first_item_index = $this->getFilterValue('first_item_index');
            if ($first_item_index > count($this->users_id)) { // Incorrect value
                $first_item_index = floor(count($this->users_id) / $this->getFilterValue('C_HTML_ItemListCount')) * $this->getFilterValue('C_HTML_ItemListCount');
            }
            $last_item_index = min(count($this->users_id), $first_item_index + $this->getFilterValue('C_HTML_ItemListCount'));

            //echo $first_item_index . ' / ' . $last_item_index . '<br />';
            $partial_users_id = array_slice($this->users_id, $first_item_index, $last_item_index - $first_item_index);

            // Load data
            $query = "
				SELECT
                    tblWebUsers.user_id,
                    tblWebUsers.username,
                    tblWebUsers.e_mail,
                    tblWebUsers.first_name,
                    tblWebUsers.company_name,
                    tblWebUsers.surname,
                    tblWebUsers.valid_till_time,
                    tblWebUsers.active,
                    tblWebUsers.enable_login,
                    tblWebUsers.enable_mailing_lists,
                    tblWebUsers.site,
				    count(tSO.order_web_user_id) AS order_count,
				    tWUO.user_option_tag AS phone_tag,
				       tWUO.user_option_value AS phone_value
				FROM
				 	tblWebUsers
				 	   LEFT JOIN tblShopOrders as tSO
				    ON tblWebUsers.user_id = tSO.order_web_user_id
				LEFT JOIN tblWebUserOptions AS tWUO
				ON tblWebUsers.user_id = tWUO.user_id

				AND
				tWUO.user_option_tag = 'phone'
				 WHERE
					tblWebUsers.user_id IN (" . BuxusDB::get()->quoteInto('?', $partial_users_id) . ")
					GROUP BY tblWebUsers.user_id
				ORDER BY FIELD(tblWebUsers.user_id," . BuxusDB::get()->quoteInto('?', $partial_users_id) . ")";
            $partial_user_data = BuxusDB::get()->fetchAll($query);


            $this->setFilterValue('first_item_index', $first_item_index);
        }

        $this->partial_user_data = $partial_user_data;
    }

    /**
     * Get all users ID
     *
     * @return array
     */
    public function getUsersId()
    {
        if (is_null($this->users_id)) { // The data has not loaded yet
            $this->loadUsersId();
        }

        return $this->users_id;
    }

    /**
     * Get interval of user data which should be shown
     *
     * @return array
     */
    public function getPartialUserData()
    {
        if (is_null($this->users_id)) { // The data has not loaded yet
            $this->loadUsersId();
        }

        if (is_null($this->partial_user_data)) { // The data has not loaded yet
            $this->loadPartialUserData();
        }

        return $this->partial_user_data;
    }

    /**
     * Extract keys
     *
     * @param array $array
     * @return array
     */
    protected function extractKeys($array)
    {
        $result = array();
        foreach ($array as $item) {
            $result[] = $item['key'];
        }

        return $result;
    }

    /**
     * Get general A-T-F selectbox data
     *
     * @return array
     */
    public function getAtfSelectValues()
    {
        $atf_select_values = array();
        $atf_select_values[] = array('key' => 'A', 'text' => __bx('webuser::administrate_web_users.All'));
        $atf_select_values[] = array('key' => 'T', 'text' => __bx('webuser::administrate_web_users.Yes'));
        $atf_select_values[] = array('key' => 'F', 'text' => __bx('webuser::administrate_web_users.No'));

        return $atf_select_values;
    }

    public function getLabelSelectValues()
    {
        /**
         * @var \Buxus\WebUser\Labels\WebUserLabelsManager $user_labels_manager
         */
        $user_labels_manager = app('buxus:webuser:labels-manager');
        $labels = $user_labels_manager->getLabels();

        $result = array();
        $result[] = array('key' => '_', 'text' => __bx('webuser::administrate_web_users.All'));
        foreach ($labels as $label_tag => $label) {
            $result[] = array(
                'key' => $label_tag,
                'text' => $label->getName(),
            );
        }
        return $result;
    }

    /**
     * Get mailinglist data
     *
     * @return array
     */
    public function getMailinglistValues()
    {
        if (is_null($this->mailing_list_values)) { // The data has not loaded yet
            $mailing_list_tags = GetMailingLists($this->user_id, $this->admin_right);
            $mailing_lists = array();
            $mailing_lists[] = array('key' => '', 'text' => __bx('webuser::administrate_web_users.All'));
            if ($this->admin_right) { // The user has admin rights
                $mailing_lists[] = array('key' => 'NULL', 'text' => __bx('webuser::administrate_web_users.No_Preferences'));
            }
            foreach ($mailing_list_tags as $mailing_list_item) {
                $mailing_lists[] = array('key' => $mailing_list_item['entity_tag'], 'text' => $mailing_list_item['entity_name']);
            }
            $this->mailing_list_values = $mailing_lists;
        }

        return $this->mailing_list_values;
    }

    /**
     * Get user option tags
     *
     * @return array
     */
    public function getUserOptionTagValues()
    {
        if (is_null($this->user_option_tag_values)) { // The data has not loaded yet
            $query = "
				SELECT
					entity_tag AS `key`,
					entity_name AS `text`
				FROM
					tblSimpleEntities
				WHERE
					entity_type_tag = 'web_user_option'
				ORDER BY entity_name";
            $this->user_option_tag_values = BuxusDB::get()->fetchAll($query);

            if (empty($this->user_option_tag_values)) { // The options tags are not defined
                $query = "
					SELECT
						user_option_tag AS `key`,
						user_option_tag AS `text`
					FROM
						tblWebUserOptions
					WHERE
						user_option_tag NOT LIKE 'mailing_list_%'
					GROUP BY user_option_tag
					ORDER BY user_option_tag";
                $this->user_option_tag_values = BuxusDB::get()->fetchAll($query);
            }
        }

        return $this->user_option_tag_values;
    }

    /**
     * Get user option operators
     *
     * @return array
     */
    public function getUserOptionOperatorValues()
    {
        $user_option_operators = array();
        $user_option_operators[] = array('key' => '=', 'text' => __bx('webuser::administrate_web_users.OperatorIsEqual'));
        $user_option_operators[] = array('key' => 'begin', 'text' => __bx('webuser::administrate_web_users.OperatorBeginWithText'));
        $user_option_operators[] = array('key' => 'contain', 'text' => __bx('webuser::administrate_web_users.OperatorContainText'));
        $user_option_operators[] = array('key' => 'behind', 'text' => __bx('webuser::administrate_web_users.OperatorBehindText'));
        $user_option_operators[] = array('key' => 'before', 'text' => __bx('webuser::administrate_web_users.OperatorBeforeText'));
        $user_option_operators[] = array('key' => 'not_empty', 'text' => __bx('webuser::administrate_web_users.OperatorNotEmptyText'));
        $user_option_operators[] = array('key' => 'geq', 'text' => __bx('webuser::administrate_web_users.OperatorGreaterEQ'));
        $user_option_operators[] = array('key' => 'leq', 'text' => __bx('webuser::administrate_web_users.OperatorLessEQ'));
        $user_option_operators[] = array('key' => 'gt', 'text' => __bx('webuser::administrate_web_users.OperatorGreater'));
        $user_option_operators[] = array('key' => 'lt', 'text' => __bx('webuser::administrate_web_users.OperatorLess'));

        return $user_option_operators;
    }

    /**
     * Get order result values
     *
     * @return array
     */
    public function getOrderResultByValues()
    {
        $order_results_by = array();
        $order_results_by[] = array('key' => 'username', 'text' => __bx('webuser::administrate_web_users.Username'));
        $order_results_by[] = array('key' => 'valid_till_time', 'text' => __bx('webuser::administrate_web_users.ExpireDate'));
        $order_results_by[] = array('key' => 'e_mail', 'text' => __bx('webuser::administrate_web_users.Email'));
        $order_results_by[] = array('key' => 'surname,first_name', 'text' => __bx('webuser::administrate_web_users.Surname'));
        $order_results_by[] = array('key' => 'creation_date', 'text' => 'Dátum registrácie (od najstaršieho)');
        $order_results_by[] = array('key' => 'creation_date desc', 'text' => 'Dátum registrácie (od najnovšieho)');

        return $order_results_by;
    }
}
