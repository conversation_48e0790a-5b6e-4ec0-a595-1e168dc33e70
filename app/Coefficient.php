<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Coefficient extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'producer_ciselnik_id',
        'coefficient'
    ];

    public static function getCoefficients($userId, $producerId){
        return self::where('user_id', $userId)->where('producer_ciselnik_id', $producerId)->first();
    }
}
