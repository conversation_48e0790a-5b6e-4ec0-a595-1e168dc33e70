<?php

namespace App\Jobs;

use App\Invoice\Invoice;
use Artisan;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class InvoiceNoticesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $updateId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($updateId)
    {
        $this->onQueue('rinoparts_onix_' . env('DB_DATABASE'));
        $this->delay(now()->addMinutes(3));

        $this->updateId = $updateId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Artisan::call('invoice:send-notices');
        \DB::table('tblInvoicesUpdateLog')->upsert(['id' => $this->updateId, 'state' => Invoice::UPDATE_DONE,], ['id', 'update_tag'], ['state']);
    }
}
