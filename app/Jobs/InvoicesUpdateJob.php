<?php

namespace App\Jobs;

use App\Invoice\Invoice;
use Artisan;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class InvoicesUpdateJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->onQueue('rinoparts_onix_' . env('DB_DATABASE'));
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $table = \DB::table('tblInvoicesUpdateLog');

        $updateId = $table->insertGetId([
            'update_tag' => Invoice::UPDATE_TAG,
            'state' => Invoice::UPDATE_IN_PROGRESS,
            'created_at' => now(),
        ]);

        Artisan::call('onix:import-invoices');

        $table->upsert([
            'id' => $updateId, 'state' => Invoice::UPDATE_DONE,
        ], ['id', 'update_tag'], ['state']);

        $updateId = $table->insertGetId([
            'update_tag' => Invoice::NOTICES_TAG,
            'state' => Invoice::UPDATE_IN_PROGRESS,
            'created_at' => now(),
        ]);

        dispatch(new InvoiceNoticesJob($updateId));
    }
}
