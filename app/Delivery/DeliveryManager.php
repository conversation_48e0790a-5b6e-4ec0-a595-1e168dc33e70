<?php

namespace App\Delivery;

use App\Onix\Import\OnixInvoicesImport;
use BuxusSite;

class DeliveryManager
{
    public static function shouldShowTrackingNumber($carrier, $carrierTrackingNumber)
    {
        if (empty($carrier) || empty($carrierTrackingNumber)) {
            return false;
        }

        $carrier = self::getCarrierMapped($carrier);

        if (!view()->exists('delivery.tracking.carrier-' . strtolower($carrier))) {
            return false;
        }

        if (count(explode(',', $carrierTrackingNumber)) > 1) {
            if (!view()->exists('delivery.tracking.multiple.carrier-' . strtolower($carrier))) {
                return false;
            }
        }

        return true;
    }

    public static function getTrackingLinkButton($carrier, $carrierTrackingNumber)
    {
        $carrier = self::getCarrierMapped($carrier);

        if (count(explode(',', $carrierTrackingNumber)) > 1) {
            return view('delivery.tracking.multiple.carrier-' . strtolower($carrier), [
                'carrier' => $carrier,
                'carrierTrackingNumbers' => array_map('trim', explode(',', $carrierTrackingNumber)),
            ])->render();
        }

        return view('delivery.tracking.carrier-' . strtolower($carrier), [
            'carrierTrackingNumber' => $carrierTrackingNumber,
        ])->render();
    }

    public static function updateTrackingNumbers(string $vs, string $carrier, array $deliveryNoteShipmentNumbers)
    {
        if (empty($vs) || empty($carrier) || empty($deliveryNoteShipmentNumbers)) {
            return;
        }

        \DB::table('onix_enclosures')
            ->where('vs', $vs)
            ->where('enclosure_type_id', OnixInvoicesImport::ENCLOSURE_TYPE_DELIVERY_NOTE)
            ->update([
                'carrier' => $carrier,
                'carrier_tracking_number' => implode(',', $deliveryNoteShipmentNumbers),
            ]);
    }

    public static function getDeliveryNoteVsWithoutTrackingForLastWeek(): \Illuminate\Support\Collection
    {
        return \DB::table('onix_enclosures')
            ->where('inserted', '>=', now()->subWeek())
            ->where('enclosure_type_id', OnixInvoicesImport::ENCLOSURE_TYPE_DELIVERY_NOTE)
            ->where(function ($q) {
                $q->whereNull('carrier_tracking_number')
                    ->orWhere('carrier_tracking_number', '');
            })
            ->get()
            ->pluck('vs');
    }

    protected static function getCarrierMapped($carrier)
    {
        $carrierMapping = [
            'ExpressOne' => 'SPS',
            DPDDeliveryTrackingManager::CARRIER_TAG => 'DPD',
        ];

        if (array_key_exists($carrier, $carrierMapping)) {
            return $carrierMapping[$carrier];
        }

        return $carrier;
    }

    public static function runTrackings($vs)
    {
        $managers = [
            new PPLDeliveryTrackingManager(),
            new SPSDeliveryTrackingManager(),
            new DPDDeliveryTrackingManager(),
        ];

        foreach ($managers as $manager) {
            $deliveryNoteShipmentNumbers = $manager->getDeliveryTrackingInfo($vs);

            if (!empty($deliveryNoteShipmentNumbers)) {
                break;
            }
        }

        return $deliveryNoteShipmentNumbers;
    }

    public static function getLanguage()
    {
        return BuxusSite::site();
    }
}