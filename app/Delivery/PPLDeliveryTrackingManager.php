<?php

namespace App\Delivery;

use Buxus\Error\ErrorReporter;
use League\OAuth2\Client\Token\AccessTokenInterface;

class PPLDeliveryTrackingManager
{
    protected ?\League\OAuth2\Client\Provider\GenericProvider $provider = null;
    protected ?AccessTokenInterface $accessToken = null;

    public function getDeliveryTrackingInfo($vs): ?array
    {
        try {
            $infoFromPPL = $this->makePPLRequest($vs);
        } catch (\InvalidArgumentException $e) {
            ErrorReporter::reportSilent($e);
            $this->accessToken = null;
            $infoFromPPL = $this->makePPLRequest($vs);
        }

        if (empty($infoFromPPL)) {
            return null;
        }

        $deliveryNoteShipmentNumbers = [];

        foreach ($infoFromPPL as $shipment) {
            if (!isset($shipment->shipmentNumber)) {
                continue;
            }

            $deliveryNoteShipmentNumbers['PPL'][] = $shipment->shipmentNumber;
        }

        if (empty($deliveryNoteShipmentNumbers)) {
            return null;
        }

        return $deliveryNoteShipmentNumbers;
    }

    protected function makePPLRequest(string $vs): array
    {
        $request = $this->getProvider()->getAuthenticatedRequest(
            'GET',
            config('ppl.url') . $vs,
            $this->getAccessToken()
        );

        $client = new \GuzzleHttp\Client();
        $response = $client->send($request);

        $contents = json_decode($response->getBody()->getContents());

        if (empty($contents)) {
            $request = $this->getProvider()->getAuthenticatedRequest(
                'GET',
                config('ppl.alternative_url') . $vs,
                $this->getAccessToken()
            );

            $response = $client->send($request);
            $contents = json_decode($response->getBody()->getContents());
        }

        return $contents;
    }

    protected function getProvider(): \League\OAuth2\Client\Provider\GenericProvider
    {
        if ($this->provider === null) {
            $this->provider = new \League\OAuth2\Client\Provider\GenericProvider([
                'clientId' => config('ppl.client_id'),
                'clientSecret' => config('ppl.client_secret'),
                'urlAccessToken' => config('ppl.url_access_token'),
                'urlAuthorize' => config('ppl.url_authorize'),
                'urlResourceOwnerDetails' => config('ppl.url_resource_owner_details'),
            ]);
        }

        return $this->provider;
    }

    protected function getAccessToken(): AccessTokenInterface
    {
        if ($this->accessToken === null) {
            $this->accessToken = $this->getProvider()->getAccessToken('client_credentials');
        }

        if ($this->accessToken->hasExpired()) {
            $this->accessToken = $this->getProvider()->getAccessToken('client_credentials', [
                'refresh_token' => $this->accessToken->getRefreshToken()
            ]);
        }

        return $this->accessToken;
    }
}