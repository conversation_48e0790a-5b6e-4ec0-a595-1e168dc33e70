<?php

namespace App\Delivery;

use SoapClient;

class SPSDeliveryTrackingManager
{
    protected const SPS_CUSTOMER_NUMBER = 19143;
    protected const EXPRESS_ONE_CUSTOMER_NUMBER = 5020;

    public function getDeliveryTrackingInfo($vs)
    {
        $types = [
            1 => self::SPS_CUSTOMER_NUMBER,
            2 => self::EXPRESS_ONE_CUSTOMER_NUMBER,
        ];

        $infoFromSps = [];

        foreach ($types as $type => $customerNumber) {
            $response = $this->makeSpsRequest($vs, $customerNumber, $type);

            if (empty($response)) {
                continue;
            }

            $infoFromSps[$type] = $this->makeSpsRequest($vs, $customerNumber, $type);
        }

        if (empty($infoFromSps)) {
            return;
        }

        $deliveryNoteShipmentNumbers = [];

        foreach ($infoFromSps as $type => $shipments) {
            if (empty($shipments)) {
                continue;
            }

            foreach ($shipments as $shipment) {
                if (!isset($shipment->ShipmentNumber)) {
                    continue;
                }

                $deliveryType = $type === 1 ? 'SPS' : 'ExpressOne';
                $deliveryNoteShipmentNumbers[$deliveryType][] = $shipment->ShipmentNumber;
            }
        }

        $deliveryNoteShipmentNumbers = array_filter($deliveryNoteShipmentNumbers);

        if (empty($deliveryNoteShipmentNumbers)) {
            return;
        }

        return $deliveryNoteShipmentNumbers;
    }

    protected function makeSpsRequest($vs, $customerNumber, $type)
    {
        $client = new SoapClient('http://t-t.sps-sro.sk/service_soap.php?wsdl');

        return $client->__soapCall('getListOfShipments', [
            'kundenr' => $customerNumber,
            'verknr' => $vs,
            'km_mandr' => $type,
            'versdat' => '',
            'langi' => 'EN',
        ]);
    }
}