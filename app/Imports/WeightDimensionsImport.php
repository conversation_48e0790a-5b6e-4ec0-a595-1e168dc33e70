<?php

namespace App\Imports;

use App\Http\Livewire\WeightImportComponent;
use App\Imports;
use App\Imports\Jobs\WeightAndDimensionImportJob;
use App\Imports\Pairing\SupplierPairingStrategyManager;
use Buxus\Core\Constants;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;

class WeightDimensionsImport implements ToCollection
{
    protected $supplierId;
    protected $weightUnit;
    protected $dimensionUnit;

    public function __construct($supplierId, $weightUnit, $dimensionUnit)
    {
        $this->supplierId = $supplierId;
        $this->weightUnit = $weightUnit;
        $this->dimensionUnit = $dimensionUnit;
    }

    /**
     * @param Collection $collection
     */
    public function collection(Collection $collection)
    {
        $collection = $collection->filter(function ($row) {
            return !empty($row[0]);
        });

        $pairingManager = SupplierPairingStrategyManager::getPairingManagerForSupplierId($this->supplierId);
        $import = $this->createImport($collection->count());

        $last = $collection->pop();

        foreach ($collection as $row) {
            $row = $this->transformRow($row);
            $job = new WeightAndDimensionImportJob($row[0], $pairingManager, $row[4], $row[1], $row[2], $row[3], $this->supplierId, Constants::C_False_Char, $import->id);
            dispatch($job)->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));;
        }

        $last = $this->transformRow($last);
        $job = new WeightAndDimensionImportJob($last[0], $pairingManager, $last[4] * 1000, $last[1], $last[2], $last[3], $this->supplierId, Constants::C_False_Char, $import->id, true);
        dispatch($job)->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));;
    }

    protected function createImport($itemCount): Imports
    {
        return Imports::create([
            'path' => "imports/weight/$this->supplierId.xlsx",
            'producer_ciselnik_id' => $this->supplierId,
            'type' => Imports::WEIGHT,
            'availability' => 1,
            'status' => Imports::STILL_RUNNING,
            'items_processed' => $itemCount,
        ]);
    }

    protected function transformRow($row)
    {
        if ($this->weightUnit === WeightImportComponent::WEIGHT_UNIT_KG) {
            $row[4] = floatval($row[4]) * 1000;
        }

        if ($this->dimensionUnit === WeightImportComponent::DIMENSION_UNIT_CM) {
            $row[1] = floatval($row[1]) * 10;
            $row[2] = floatval($row[2]) * 10;
            $row[3] = floatval($row[3]) * 10;
        }

        return $row;
    }
}
