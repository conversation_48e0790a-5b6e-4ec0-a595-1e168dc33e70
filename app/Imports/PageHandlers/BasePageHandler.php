<?php

namespace App\Imports\PageHandlers;

use App\Product\Codes\ProductCodesChangedEvent;
use Buxus\Page\PageInterface;
use BuxusEvent;
use PageTypesConstantsIDs;

abstract class BasePageHandler
{
    protected ?PageInterface $page;
    protected array $propertyValues;
    protected ?string $pageName;

    public function __construct(?PageInterface $page = null)
    {
        $this->page = $page;
        if ($page instanceof PageInterface) {
            $this->setPageName($page->getPageName());
        }
    }

    public function createPage($parentPageId, $triggers = false): bool
    {
        if (!$this->canCreate()) {
            return false;
        }

        $page = \PageFactory::create($parentPageId, PageTypesConstantsIDs::ESHOP_PRODUCT_ID());
        $page->setPageName($this->getPageName());

        $propertyValues = $this->getPropertyValues();

        foreach ($propertyValues as $property => $value) {
            if ($page->hasProperty($property)) {
                $page->setValue($property, $value);
            }
        }

        $mutex = new \malkusch\lock\mutex\FlockMutex(fopen(__FILE__, 'rb'));
        $mutex->synchronized(function () use ($page, $triggers) {
            $page->save($triggers);
        });

        $this->setPage($page);

        $event = new ProductCodesChangedEvent($page);
        BuxusEvent::fire($event);

        return true;
    }

    public function updatePage($triggers = false): bool
    {
        if (!$this->canUpdate()) {
            return false;
        }

        $page = $this->getPage();

        if ($this->getPageName()) {
            $page->setPageName($this->getPageName());
        }

        $propertyValues = $this->getPropertyValues();

        foreach ($propertyValues as $property => $value) {
            if ($page->hasProperty($property)) {
                $page->setValue($property, $value);
            }
        }

        $mutex = new \malkusch\lock\mutex\FlockMutex(fopen(__FILE__, 'rb'));
        $mutex->synchronized(function () use ($page, $triggers) {
            $page->save($triggers);
        });

        $event = new ProductCodesChangedEvent($page);
        BuxusEvent::fire($event);

        return true;
    }

    /**
     * @return array
     */
    public function getPropertyValues(): array
    {
        return $this->propertyValues;
    }

    /**
     * @param array $propertyValues
     */
    public function setPropertyValues(array $propertyValues): void
    {
        $this->propertyValues = $propertyValues;
    }

    public function canCreate(): bool
    {
        return true;
    }

    public function canUpdate(): bool
    {
        return true;
    }

    /**
     * @return PageInterface|null
     */
    public function getPage(): ?PageInterface
    {
        return $this->page;
    }

    /**
     * @param PageInterface|null $page
     */
    public function setPage(?PageInterface $page): void
    {
        $this->page = $page;
    }

    /**
     * @return string|null
     */
    public function getPageName(): ?string
    {
        return $this->pageName;
    }

    /**
     * @param string|null $pageName
     */
    public function setPageName(?string $pageName): void
    {
        $this->pageName = $pageName;
    }
}
