<?php

namespace App\Imports\Helper;

use Buxus\Ciselniky\Facades\Ciselniky;
use Buxus\Ciselniky\ValueInterface;
use Illuminate\Support\Facades\Storage;

class GeneralImportHelper
{
    protected $configKey;

    protected $importPath;
    protected $importPathPrevious;
    protected $appPath;
    protected $appPathPrevious;
    protected $storagePath;
    protected $storagePathPrevious;

    public function __construct()
    {
        $this->importPath = $this->getImportPath();
        $this->importPathPrevious = $this->getImportPathPrevious();
    }

    public function setConfigKey($configKey)
    {
        $this->configKey = $configKey;

        $this->resetPaths();
    }

    protected function resetPaths()
    {
        $this->importPath = null;
        $this->importPathPrevious = null;
        $this->appPath = null;
        $this->appPathPrevious = null;
        $this->storagePath = null;
        $this->storagePathPrevious = null;
    }

    public function getConfigKey()
    {
        return $this->configKey;
    }

    public function save($file)
    {
        if (Storage::disk('local')->exists($this->getImportPath())) {
            if (Storage::disk('local')->exists($this->getImportPath())) {
                Storage::disk('local')->delete($this->getImportPathPrevious());
            }
            Storage::move($this->getImportPath(), $this->getImportPathPrevious());
        }

        $importPathInfo = pathinfo($this->getImportPath());

        return $file->storeAs($importPathInfo['dirname'], $importPathInfo['basename']);
    }

    public function getAppPath()
    {
        if ($this->appPath === null) {
            $this->appPath = 'app/' . $this->getImportPath();
        }

        return $this->appPath;
    }

    public function getAppPathPrevious()
    {
        if ($this->appPathPrevious === null) {
            $this->appPathPrevious = 'app/' . $this->getImportPathPrevious();
        }

        return $this->appPathPrevious;
    }

    public function getStoragePath()
    {
        if ($this->storagePath === null) {
            $this->storagePath = storage_path($this->getAppPath());
        }

        return $this->storagePath;
    }

    public function getStoragePathPrevious()
    {
        if ($this->storagePathPrevious === null) {
            $this->storagePathPrevious = storage_path($this->getAppPathPrevious());
        }

        return $this->storagePathPrevious;
    }

    public function getImportPath()
    {
        if ($this->importPath === null) {
            $this->importPath = str_replace('*', $this->getConfigKey(), config('imports.' . $this->getConfigKey() . '.import_path'));
        }

        return $this->importPath;
    }

    public function getImportPathPrevious()
    {
        if ($this->importPathPrevious === null) {
            $this->importPathPrevious = str_replace('*', $this->getConfigKey(), config('imports.' . $this->getConfigKey() . '.import_previous_path'));
        }

        return $this->importPathPrevious;
    }
}
