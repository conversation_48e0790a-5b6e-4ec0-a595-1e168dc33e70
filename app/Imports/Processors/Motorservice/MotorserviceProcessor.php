<?php

namespace App\Imports\Processors\Motorservice;

use App\Http\Livewire\MotorserviceImport;
use App\Imports;
use App\Imports\Helper\GeneralImportHelper;
use App\Imports\Jobs\Motorservice\MotorserviceImportJob;
use App\Imports\Jobs\Motorservice\RemovePriceJob;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Excel;

class MotorserviceProcessor implements ToCollection, WithHeadingRow
{
    use Importable;

    protected $availability;
    protected $import_id;
    protected $categories;

    protected $path;
    protected $pathPrevious;

    protected $supplier;

    /**
     * @var GeneralImportHelper
     */
    protected $helper;

    public function __construct(int $availability, int $import_id)
    {
        $this->availability = $availability;
        $this->import_id = $import_id;

        $this->helper = new GeneralImportHelper();
        $this->helper->setConfigKey(MotorserviceImport::CONFIG_KEY);

        $this->path = $this->helper->getStoragePath();
        $this->pathPrevious = $this->helper->getStoragePathPrevious();

        ini_set('memory_limit', '-1');
    }

    public function collection($collection)
    {
        $collection = $collection->filter(function ($item) {
            return !empty($item['ms_motor_cislo']);
        });

        $this->processPrevious($collection);

        $import = Imports::find($this->import_id);
        $import->items_processed = $collection->count();
        $import->save();

        $last = $collection->pop();

        foreach ($collection as $item) {
            MotorserviceImportJob::dispatch($item, $this->import_id);
        }

        MotorserviceImportJob::dispatch($last, $this->import_id, true);
    }

    protected function processPrevious($collection)
    {
        Excel::toCollection(new MotorservicePrevious(), $this->pathPrevious)
            ->first()
            ->pluck('ms_motor_cislo')
            ->filter()
            ->diff($collection->pluck('ms_motor_cislo'))
            ->each(fn($diffRow) => RemovePriceJob::dispatch($diffRow, $this->import_id));
    }

    public function headingRow(): int
    {
        return 1;
    }
}
