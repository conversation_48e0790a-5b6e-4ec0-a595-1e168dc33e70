<?php

namespace App\Imports\Processors\Martex;

use App\Imports\Jobs\Martex\MartexMakeProductsWithoutOeCodesPassiveJob;
use App\Imports\Jobs\Martex\MartexReferenceJob;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Collection;
use Maat<PERSON>bsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class MartexCrossFile implements ToCollection, WithHeadingRow, WithChunkReading, ShouldQueue
{
    use Importable;

    protected $timeout = 3600;
    protected bool $delayed;

    public function __construct($delayed = false)
    {
        $this->delayed = $delayed;
    }

    public function collection(Collection $collection)
    {
        $oeCodes = [];

        foreach ($collection as $item) {
            if (empty($item['kod'])) {
                continue;
            }

            if (empty($item['oe_cisla'])) {
                continue;
            }

            $oeCodes[$item['kod']][] = $item['oe_cisla'];
        }

        $oeCodes = array_filter($oeCodes);

        foreach ($oeCodes as $key => $values) {
            if ($this->delayed) {
                MartexReferenceJob::dispatchAtMidnight($key, $values);
            } else {
                MartexReferenceJob::dispatch($key, $values);
            }
        }
    }

    public function headingRow()
    {
        return 1;
    }

    public function chunkSize(): int
    {
        return 100000;
    }
}
