<?php

namespace App\Imports\Processors\FebiBilstein;

use App\Imports\Jobs\FebiBilstein\FebiBilsteinImageJob;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FebiBilsteinImageFile implements ToCollection, WithHeadingRow
{
    use Importable;

    protected $images;

    public function collection(Collection $collection)
    {
        $collection = $collection->filter(function ($item) {
            return !empty($item['bilstein_group_no']) && !empty($item['url']);
        });

        foreach ($collection as $item) {
            $this->images[$item['bilstein_group_no']][] = [
                'url' => $item['url'],
                'type' => $item['type'],
            ];
        }

        foreach ($this->images as $key => $item) {
            FebiBilsteinImageJob::dispatch($key, $item);
        }
    }

    public function headingRow()
    {
        return 1;
    }
}
