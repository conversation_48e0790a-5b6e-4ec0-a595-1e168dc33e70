<?php

namespace App\Imports\Processors\FebiBilstein;

use App\Imports\Jobs\FebiBilstein\FebiBilsteinReferenceJob;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FebiBilsteinCrossFile implements ToCollection, WithHeadingRow
{
    use Importable;

    protected $reference;

    public function collection(Collection $collection)
    {
        $collection = $collection->filter(function ($item) {
            return !empty($item['febi_cislo']) && !empty($item['oe_cislo']);
        });

        foreach ($collection as $row) {
            $this->reference[trim($row['febi_cislo'])][] = trim($row['oe_cislo']);
        }

        foreach ($this->reference as $key => $values) {
            FebiBilsteinReferenceJob::dispatch($key, $values);
        }
    }

    public function headingRow()
    {
        return 1;
    }
}
