<?php

namespace App\Imports\Processors\AlternativePrices;

use App\Imports;
use App\Imports\Jobs\AlternativePrices\AlternativePricesImportJob;
use Buxus\Ciselniky\ValueInterface;
use Buxus\Util\PageIds;
use Buxus\Util\PageTypeID;
use Buxus\Util\PropertyTag;
use Excel;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use App\Imports\Helper\AlternativePricesHelper;
use App\Imports\Jobs\AlternativePrices\RemovePriceJob;

class AlternativePricesProcessor implements ToArray, WithHeadingRow
{
    use Importable;

    protected $availability;
    protected $import_id;
    protected $categories;

    protected $path;
    protected $pathPrevious;

    protected $supplier;

    /**
     * @var AlternativePricesHelper
     */
    protected $helper;

    public function __construct(int $availability, int $import_id, ValueInterface $supplier)
    {
        $this->availability = $availability;
        $this->import_id = $import_id;
        $this->supplier = $supplier;

        $this->helper = new AlternativePricesHelper();

        $this->helper->setSupplier($supplier);

        $this->path = $this->helper->getStoragePath();
        $this->pathPrevious = $this->helper->getStoragePathPrevious();

        ini_set('memory_limit', '-1');
    }

    public function array($arr)
    {
        $collection = collect($arr);
        $collection = $collection->groupBy('code');

        $collection = $collection->filter(function ($value, $key) {
            return !empty($key);
        });

        if (file_exists($this->pathPrevious)) {
            $importCodes = array_column($arr, 'code');
            $importCodesPrevious = Excel::toArray(new AlternativePricesPrevious(), $this->pathPrevious);
            $importCodesPrevious = array_column($importCodesPrevious[0], 'code');
            $diff = array_diff($importCodesPrevious, $importCodes);

            foreach ($diff as $diffRow) {
                $job = new RemovePriceJob($this->supplier, $diffRow, $this->import_id);
                dispatch($job)->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));
            }
        }

        $import = Imports::find($this->import_id);
        $import->items_processed = count($arr);
        $import->save();

        $last = $collection->pop();

        foreach ($collection as $items) {
            $job = new AlternativePricesImportJob($this->supplier, $items, $this->import_id);
            dispatch($job)->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));
        }

        $job = new AlternativePricesImportJob($this->supplier, $last, $this->import_id, true);
        dispatch($job)->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));
    }

    public function headingRow(): int
    {
        return 1;
    }
}
