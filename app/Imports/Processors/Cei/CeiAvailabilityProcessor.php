<?php

namespace App\Imports\Processors\Cei;

use App\Http\Livewire\CeiAvailabilityImport;
use App\Imports;
use App\Imports\Helper\GeneralImportHelper;
use App\Imports\Jobs\Cei\CeiAvailabilityImportJob;
use App\Jobs\Traits\DispatchableAtMidnight;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;

class CeiAvailabilityProcessor implements ToCollection
{
    use Importable, DispatchableAtMidnight;

    protected $import_id;
    protected $categories;

    protected $path;
    protected $pathPrevious;

    protected $supplier;

    /**
     * @var GeneralImportHelper
     */
    protected $helper;
    protected $delayed_import;

    public function __construct(int $import_id, bool $delayed = false)
    {
        $this->import_id = $import_id;
        $this->delayed_import = $delayed;

        $this->helper = new GeneralImportHelper();
        $this->helper->setConfigKey(CeiAvailabilityImport::CONFIG_KEY);

        $this->path = $this->helper->getStoragePath();
        $this->pathPrevious = $this->helper->getStoragePathPrevious();

        ini_set('memory_limit', '-1');
    }

    public function collection($collection)
    {
        $import = Imports::find($this->import_id);
        $import->items_processed = $collection->count();
        $import->save();

        $last = $collection->pop();

        foreach ($collection as $item) {
            $job = new CeiAvailabilityImportJob($item, $this->import_id);

            if ($this->delayed_import) {
                self::dispatchAtMidnight($job);
            } else {
                dispatch($job);
            }
        }

        $job = new CeiAvailabilityImportJob($last, $this->import_id, true);

        if ($this->delayed_import) {
            self::dispatchAtMidnight($job);
        } else {
            dispatch($job);
        }
    }
}
