<?php

namespace App\Imports\Processors\OeGermany;

use App\Http\Livewire\OeGermany\OeGermanyImport;
use App\Imports\AbstractImportProcessor;
use App\Imports\Jobs\OeGermany\OeGermanyImportJob;
use App\Imports\Jobs\OeGermany\RemoveStockJob;
use Buxus\Util\PropertyID;
use DB;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class OeGermanyImportProcessor extends AbstractImportProcessor implements WithHeadingRow
{
    protected string $importJobClass = OeGermanyImportJob::class;
    protected bool $shouldProcessPrevious = false;

    protected string $onQueue = 'buxus_iveco_big_db_import';
    protected $codeColumnKey = 'code';
    protected $titleColumnKey = 'nazov';

    protected $additionalProperties = [
        'vyrobca',
    ];

    public function __construct(int $availability = null, int $import_id = null, bool $dispatchAtMidnight = false, $additionalProperties = [])
    {
        parent::__construct($availability, $import_id, $dispatchAtMidnight, $additionalProperties);

        $this->helper->setConfigKey(OeGermanyImport::CONFIG_KEY);
    }

    public function headingRow(): int
    {
        return 1;
    }

    /**
     * @param Collection $collection
     */
    protected function processPrevious($collection)
    {
        $currentSupplierCodes = DB::table('tblPagePropertyValues')
            ->where('property_id', PropertyID::OE_GERMANY_SUPPLIER_CODE_ID())
            ->where('property_value', '<>', '')
            ->get()
            ->pluck('property_value');

        $currentSupplierCodes
            ->diff($collection)
            ->each(function ($supplierCode) {
                dispatch(new RemoveStockJob($supplierCode, $this->import_id));
            });
    }
}
