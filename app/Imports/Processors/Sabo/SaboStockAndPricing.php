<?php

namespace App\Imports\Processors\Sabo;

use App\Imports\Jobs\Sabo\SaboStockAndPricingJob;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class SaboStockAndPricing implements ToCollection, WithHeadingRow
{
    use Importable;

    protected const CODE_COLUMN_KEY = 'kod_towaru';
    protected const STOCK_COLUMN_KEY = 'stan_towaru';
    protected const PRICE_COLUMN_KEY = 'cena_netto_po_rabacie';

    public function collection(Collection $collection)
    {
        $collection = $collection->map(function ($item) {
            return $item->map(function ($value) {
                return trim($value);
            });
        });

        $collection = $collection->filter(function ($item) {
            return !empty($item[self::CODE_COLUMN_KEY]);
        });

        foreach ($collection as $item) {
            SaboStockAndPricingJob::dispatch(
                $item[self::CODE_COLUMN_KEY],
                $item[self::STOCK_COLUMN_KEY],
                $item[self::PRICE_COLUMN_KEY],
            );
        }

        return Command::SUCCESS;
    }
}
