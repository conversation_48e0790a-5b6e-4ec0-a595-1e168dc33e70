<?php

namespace App\Imports\Processors\Sabo;

use App\Http\Livewire\Sabo\SaboAvailabilityImport;
use App\Imports;
use App\Imports\Helper\GeneralImportHelper;
use App\Imports\Jobs\Sabo\SaboAvailabilityImportJob;
use App\Jobs\Traits\DispatchableAtMidnight;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class SaboAvailabilityProcessor implements ToCollection, WithHeadingRow, WithMultipleSheets
{
    use Importable, DispatchableAtMidnight;

    protected $import_id;
    protected $categories;

    protected $path;
    protected $pathPrevious;

    protected $supplier;

    /**
     * @var GeneralImportHelper
     */
    protected $helper;
    protected $delayed_import;

    public function __construct(int $import_id, bool $delayed = false)
    {
        $this->import_id = $import_id;
        $this->delayed_import = $delayed;

        $this->helper = new GeneralImportHelper();
        $this->helper->setConfigKey(SaboAvailabilityImport::CONFIG_KEY);

        $this->path = $this->helper->getStoragePath();
        $this->pathPrevious = $this->helper->getStoragePathPrevious();

        ini_set('memory_limit', '-1');
    }

    public function collection($collection)
    {
        $collection = $collection->filter(function ($item) {
            return !empty($item['codice_articolo']);
        });


        $import = Imports::find($this->import_id);
        $import->items_processed = $collection->count();
        $import->save();

        $last = $collection->pop();

        foreach ($collection as $item) {
            $job = new SaboAvailabilityImportJob($item, $this->import_id);
            if ($this->delayed_import) {
                self::dispatchAtMidnight($job);
            } else {
                dispatch($job);
            }
        }

        $job = new SaboAvailabilityImportJob($last, $this->import_id, true);

        if ($this->delayed_import) {
            self::dispatchAtMidnight($job);
        } else {
            dispatch($job);
        }
    }

    public function headingRow(): int
    {
        return 1;
    }

    public function sheets(): array
    {
        return [
            0 => $this,
        ];
    }
}
