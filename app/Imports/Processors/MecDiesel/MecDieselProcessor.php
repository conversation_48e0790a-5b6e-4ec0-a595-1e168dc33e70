<?php

namespace App\Imports\Processors\MecDiesel;

use App\Http\Livewire\MecDieselImport;
use App\Imports;
use App\Imports\Helper\GeneralImportHelper;
use App\Imports\Jobs\MecDiesel\MecDieselImportJob;
use App\Imports\Jobs\MecDiesel\RemovePriceJob;
use Illuminate\Contracts\Queue\ShouldQueue;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Excel;

class MecDieselProcessor implements ToCollection, WithHeadingRow
{
    use Importable;

    protected $availability;
    protected $import_id;
    protected $categories;

    protected $path;
    protected $pathPrevious;

    protected $supplier;

    /**
     * @var GeneralImportHelper
     */
    protected $helper;

    public function __construct(int $availability, int $import_id)
    {
        $this->availability = $availability;
        $this->import_id = $import_id;

        $this->helper = new GeneralImportHelper();
        $this->helper->setConfigKey(MecDieselImport::CONFIG_KEY);

        $this->path = $this->helper->getStoragePath();
        $this->pathPrevious = $this->helper->getStoragePathPrevious();

        ini_set('memory_limit', '-1');
    }

    public function collection($collection)
    {
        $collection = $collection->filter(function ($item) {
            return !empty($item['mec_cislo']);
        });

        $this->processPrevious($collection);

        $import = Imports::find($this->import_id);
        $import->items_processed = $collection->count();
        $import->save();

        $last = $collection->pop();

        foreach ($collection as $item) {
            MecDieselImportJob::dispatch($item, $this->import_id);
        }

        MecDieselImportJob::dispatch($last, $this->import_id, true);
    }

    protected function processPrevious($collection)
    {
        return Excel::toCollection(new MecDieselPrevious(), $this->pathPrevious)
            ->first()
            ->pluck('mec_cislo')
            ->filter()
            ->diff($collection->pluck('mec_cislo'))
            ->each(fn($diffRow) => RemovePriceJob::dispatch($diffRow, $this->import_id));
    }

    public function headingRow(): int
    {
        return 1;
    }
}
