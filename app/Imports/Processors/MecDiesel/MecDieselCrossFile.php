<?php

namespace App\Imports\Processors\MecDiesel;

use App\Imports\Jobs\MecDiesel\MecDieselReferenceJob;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class MecDieselCrossFile implements ToCollection, WithHeadingRow
{
    use Importable;

    protected $reference;

    public function collection(Collection $collection)
    {
        $collection = $collection->filter(function ($item) {
            return !empty($item['mec_cislo']) && !empty($item['cross']);
        });

        foreach ($collection as $row) {
            $this->reference[trim($row['mec_cislo'])][] = trim($row['cross']);
        }

        foreach ($this->reference as $key => $values) {
            MecDieselReferenceJob::dispatch($key, $values);
        }
    }

    public function headingRow()
    {
        return 1;
    }
}
