<?php

namespace App\Imports\Processors\MeatDoria;

use App\Imports\Jobs\Martex\MartexMakeProductsWithoutOeCodesPassiveJob;
use App\Imports\Jobs\Martex\MartexReferenceJob;
use App\Imports\Jobs\MeatDoria\MeatDoriaReferenceJob;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class MeatDoriaCrossFile implements ToCollection, WithHeadingRow, WithChunkReading, ShouldQueue
{
    use Importable;

    protected $timeout = 3600;
    protected bool $delayed;

    public function __construct($delayed = false)
    {
        $this->delayed = $delayed;
    }

    public function collection(Collection $collection)
    {
        $oeCodes = [];

        foreach ($collection as $item) {
            if (empty($item['meatdoria'])) {
                continue;
            }

            if (empty($item['oem'])) {
                continue;
            }

            $oeCodes[$item['meatdoria']][] = $item['oem'];
        }

        $oeCodes = array_filter($oeCodes);

        foreach ($oeCodes as $key => $values) {
            if ($this->delayed) {
                MeatDoriaReferenceJob::dispatchAtMidnight($key, $values);
            } else {
                MeatDoriaReferenceJob::dispatch($key, $values);
            }
        }
    }

    public function headingRow()
    {
        return 1;
    }

    public function chunkSize(): int
    {
        return 100000;
    }
}
