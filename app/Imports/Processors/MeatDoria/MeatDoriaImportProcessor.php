<?php

namespace App\Imports\Processors\MeatDoria;

use App\Http\Livewire\MeatDoria\MeatDoriaImport;
use App\Imports\AbstractImportProcessor;
use App\Imports\Jobs\MeatDoria\MeatDoriaImportJob;
use App\Imports\Jobs\MeatDoria\RemoveStockJob;
use Buxus\Util\PropertyID;
use DB;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class MeatDoriaImportProcessor extends AbstractImportProcessor implements WithHeadingRow
{
    protected string $importJobClass = MeatDoriaImportJob::class;
    protected bool $shouldProcessPrevious = false;

    protected string $onQueue = 'buxus_iveco_big_db_import';
    protected $codeColumnKey = 'md';
    protected $titleColumnKey = 'nazov';
    protected $priceColumnKey = 'cena';

    protected $additionalProperties = [
        'brand',
        'nazov2',
        'rozmery',
        'vaha',
        'ean',
        'intrastat',
    ];

    public function __construct(int $availability = null, int $import_id = null, bool $dispatchAtMidnight = false, $additionalProperties = [])
    {
        parent::__construct($availability, $import_id, $dispatchAtMidnight, $additionalProperties);

        $this->helper->setConfigKey(MeatDoriaImport::CONFIG_KEY);
    }

    public function headingRow(): int
    {
        return 1;
    }

    /**
     * @param Collection $collection
     */
    protected function processPrevious($collection)
    {
        $currentSupplierCodes = DB::table('tblPagePropertyValues')
            ->where('property_id', PropertyID::MEAT_DORIA_SUPPLIER_CODE_ID())
            ->where('property_value', '<>', '')
            ->get()
            ->pluck('property_value');

        $currentSupplierCodes
            ->diff($collection)
            ->each(function ($supplierCode) {
                dispatch(new RemoveStockJob($supplierCode, $this->import_id));
            });
    }
}
