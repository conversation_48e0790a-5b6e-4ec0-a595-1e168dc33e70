<?php

namespace App\Imports\Processors\Remante;

use App\Imports\Jobs\Remante\RemanteStockAndPricingJob;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class RemanteStockAndPricing implements ToCollection, WithHeadingRow
{
    use Importable;

    public function headingRow()
    {
        return 1;
    }

    public function collection(Collection $collection)
    {
        $collection = $collection->filter(function ($item) {
            return !empty($item['code']);
        });

        foreach ($collection as $item) {
            RemanteStockAndPricingJob::dispatch(
                $item['code'],
                $item['stock'],
                $item['price'],
            );
        }

        return Command::SUCCESS;
    }
}
