<?php

namespace App\Imports\Processors\Remante;

use App\Http\Livewire\Remante\RemanteImport;
use App\Imports\AbstractImportProcessor;
use App\Imports\Jobs\Remante\RemanteImportJob;
use App\Imports\Jobs\Remante\RemoveStockJob;
use Buxus\Util\PropertyID;
use DB;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class RemanteImportProcessor extends AbstractImportProcessor implements WithHeadingRow
{
    protected string $importJobClass = RemanteImportJob::class;
    protected bool $shouldProcessPrevious = false;

    protected string $onQueue = 'buxus_iveco_big_db_import';
    protected $codeColumnKey = 'remante_kod';
    protected $priceColumnKey = 'cena';
    protected $titleColumnKey = 'nazov';

    protected $additionalProperties = [
        'vyrobca',
        'nazov_2',
        'cislo_dilu',
        'zaloha',
        'oem'
    ];

    public function __construct(int $availability = null, int $import_id = null, bool $dispatchAtMidnight = false, $additionalProperties = [])
    {
        parent::__construct($availability, $import_id, $dispatchAtMidnight, $additionalProperties);

        $this->helper->setConfigKey(RemanteImport::CONFIG_KEY);
    }

    public function headingRow(): int
    {
        return 1;
    }

    /**
     * @param Collection $collection
     */
    protected function processPrevious($collection)
    {
        $currentSupplierCodes = DB::table('tblPagePropertyValues')
            ->where('property_id', PropertyID::REMANTE_SUPPLIER_CODE_ID())
            ->where('property_value', '<>', '')
            ->get()
            ->pluck('property_value');

        $currentSupplierCodes
            ->diff($collection)
            ->each(function ($supplierCode) {
                dispatch(new RemoveStockJob($supplierCode, $this->import_id));
            });
    }
}
