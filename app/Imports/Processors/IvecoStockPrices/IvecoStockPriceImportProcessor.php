<?php

namespace App\Imports\Processors\IvecoStockPrices;

use App\Imports\Jobs\IvecoStockPrices\IvecoStockPricesImportJob;
use App\Imports\Processors\AbstractPriceImportProcessor;
use Buxus\Util\PropertyTag;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class IvecoStockPriceImportProcessor extends AbstractPriceImportProcessor implements WithHeadingRow
{
    protected string $path = 'app/imports/iveco-original-prices-import/iveco-original-prices-previous.xlsx';
    protected string $pathPrevious = 'app/imports/iveco-original-prices-import/iveco-original-prices-previous.xlsx';

    protected string $importJobClass = IvecoStockPricesImportJob::class;
    protected bool $shouldProcessPrevious = false;

    protected string $onQueue = 'buxus_iveco_big_db_import';
    protected $codeColumnKey = 'part_code';
    protected $priceColumnKey = 'stock_price';

    protected $additionalProperties = [
        'sub_to_part_number',
        'sub_from_part_number',
        'vaha',
        'sirka',
        'vyska',
        'dlzka'
    ];

    public function headingRow(): int
    {
        return 1;
    }
}
