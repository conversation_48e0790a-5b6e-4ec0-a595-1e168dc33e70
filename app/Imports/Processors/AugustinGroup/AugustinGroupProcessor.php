<?php

namespace App\Imports\Processors\AugustinGroup;

use App\Http\Livewire\AugustinGroupImport;
use App\Imports;
use App\Imports\Helper\GeneralImportHelper;
use App\Imports\Jobs\AugustinGroup\AugustinGroupImportJob;
use App\Imports\Jobs\AugustinGroup\RemovePriceJob;
use App\Jobs\Traits\DispatchableAtMidnight;
use Buxus\Util\PropertyID;
use DB;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Cell\DefaultValueBinder;

class AugustinGroupProcessor implements ToCollection, WithHeadingRow
{
    use Importable, DispatchableAtMidnight;

    protected $availability;
    protected $import_id;
    protected $categories;

    protected $path;
    protected $pathPrevious;

    protected $supplier;

    /**
     * @var GeneralImportHelper
     */
    protected $helper;
    protected bool $dispatchAtMidnight;

    public function __construct(int $availability, int $import_id, bool $dispatchAtMidnight = false)
    {
        $this->availability = $availability;
        $this->import_id = $import_id;

        $this->helper = new GeneralImportHelper();

        $this->helper->setConfigKey(AugustinGroupImport::CONFIG_KEY);

        $this->path = $this->helper->getStoragePath();
        $this->pathPrevious = $this->helper->getStoragePathPrevious();

        $this->dispatchAtMidnight = $dispatchAtMidnight;

        ini_set('memory_limit', '-1');
    }

    public function collection($collection)
    {
        $collection = $collection->filter(function ($item) {
            return !empty($item['number']);
        });

        $this->processPrevious($collection->pluck('number'));

        $import = Imports::find($this->import_id);
        $import->items_processed = $collection->count();
        if ($this->dispatchAtMidnight) {
            $import->status = Imports::DELAYED;
        }
        $import->save();

        $last = $collection->pop();

        foreach ($collection as $item) {
            $job = new AugustinGroupImportJob($item, $this->import_id);
            if ($this->dispatchAtMidnight) {
                self::dispatchAtMidnight($job)->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));

            } else {
                dispatch($job)->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));
            }
        }

        $job = new AugustinGroupImportJob($last, $this->import_id, true);

        if ($this->dispatchAtMidnight) {
            self::dispatchAtMidnight($job)->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));
        } else {
            dispatch($job)->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));
        }
    }

    public function headingRow(): int
    {
        return 1;
    }

    protected function processPrevious($collection)
    {
        $currentSupplierCodes = DB::table('tblPagePropertyValues')
            ->where('property_id', PropertyID::AUGUSTIN_GROUP_NUMBER_ID())
            ->where('property_value', '<>', '')
            ->get()
            ->pluck('property_value');

        $currentSupplierCodes
            ->diff($collection)
            ->each(function ($supplierCode) {
                $job = new RemovePriceJob($supplierCode, $this->import_id);

                if ($this->dispatchAtMidnight) {
                    self::dispatchAtMidnight($job)
                        ->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));
                } else {
                    dispatch($job)
                        ->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));
                }
            });
    }
}
