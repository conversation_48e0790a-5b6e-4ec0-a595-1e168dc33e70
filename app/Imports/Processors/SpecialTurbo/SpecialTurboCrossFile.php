<?php

namespace App\Imports\Processors\SpecialTurbo;

use App\Imports\Jobs\SpecialTurbo\SpecialTurboReferenceJob;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class SpecialTurboCrossFile implements ToCollection, WithHeadingRow, WithChunkReading
{
    use Importable;

    protected $timeout = 3600;
    protected bool $delayed;

    public function __construct($delayed = false)
    {
        $this->delayed = $delayed;
    }

    public function collection(Collection $collection)
    {
        $oeCodes = [];

        foreach ($collection as $item) {
            if (empty($item['katalog'])) {
                continue;
            }

            if (!empty($item['oe_cislo'])) {
                $oeCodes[$item['katalog']][] = $item['oe_cislo'];
            }

            foreach ($item as $key => $value) {
                if ($key == 'katalog' || $key == 'oe_cislo' || empty($value)) {
                    continue;
                }

                $oeCodes[$item['katalog']][] = $value;
            }
        }

        $oeCodes = array_filter($oeCodes);

        foreach ($oeCodes as $key => $values) {
            if ($this->delayed) {
                SpecialTurboReferenceJob::dispatchAtMidnight($key, $values);
            } else {
                SpecialTurboReferenceJob::dispatch($key, $values);
            }
        }
    }

    public function headingRow()
    {
        return 1;
    }

    public function chunkSize(): int
    {
        return 100000;
    }
}
