<?php

namespace App\Imports\Processors\Abakus;

use App\Http\Livewire\Abakus\AbakusImport;
use App\Imports\AbstractImportProcessor;
use App\Imports\Jobs\Abakus\AbakusImportJob;
use App\Imports\Jobs\Abakus\RemoveStockJob;
use Buxus\Util\PropertyID;
use DB;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class AbakusImportProcessor extends AbstractImportProcessor implements WithHeadingRow
{
    protected string $importJobClass = AbakusImportJob::class;
    protected bool $shouldProcessPrevious = false;

    protected string $onQueue = 'buxus_iveco_big_db_import';
    protected $codeColumnKey = 'kod';
    protected $titleColumnKey = 'nazov';
    protected $priceColumnKey = 'cena';

    protected $additionalProperties = [
        'vyrobca',
        'cross_no',
        'nr_oe_1',
        'nr_oe_2',
        'nr_oe_3',
        'nr_oe_4',
        'length_cm',
        'width_cm',
        'height_cm',
        'weight',
    ];

    public function __construct(int $availability = null, int $import_id = null, bool $dispatchAtMidnight = false, $additionalProperties = [])
    {
        parent::__construct($availability, $import_id, $dispatchAtMidnight, $additionalProperties);

        $this->helper->setConfigKey(AbakusImport::CONFIG_KEY);
    }

    public function headingRow(): int
    {
        return 1;
    }

    /**
     * @param Collection $collection
     */
    protected function processPrevious($collection)
    {
        $currentSupplierCodes = DB::table('tblPagePropertyValues')
            ->where('property_id', PropertyID::ABAKUS_SUPPLIER_CODE_ID())
            ->where('property_value', '<>', '')
            ->get()
            ->pluck('property_value');

        $currentSupplierCodes
            ->diff($collection)
            ->each(function ($supplierCode) {
                dispatch(new RemoveStockJob($supplierCode, $this->import_id));
            });
    }
}
