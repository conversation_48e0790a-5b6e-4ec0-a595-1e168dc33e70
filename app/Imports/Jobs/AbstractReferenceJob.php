<?php

namespace App\Imports\Jobs;

use App\Imports\Pairing\BasePairingManager;
use App\Jobs\Traits\DispatchableAtMidnight;
use App\Logger\ImportsLogger;
use Buxus\Error\ErrorReporter;
use Cache;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

abstract class AbstractReferenceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, DispatchableAtMidnight;

    /**
     * @var \Buxus\Logger\Logger
     */
    protected $logger;

    protected $supplierCode;
    protected $oeCodes;

    protected $pairingManagerClass = null;
    protected $oeNumbersPropertyTag = null;

    public function __construct($supplierCode, $oeCodes)
    {
        $this->supplierCode = $supplierCode;
        $this->oeCodes = $oeCodes;

        $this->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));
    }

    public function handle()
    {
        $this->logger = (new ImportsLogger)->getLogger();
        $this->logger->info("[" . self::class . "]" . " Referencing: " . $this->supplierCode . " - " . implode(',', $this->oeCodes));

        $pairing = new $this->pairingManagerClass(BasePairingManager::METHOD_GET_ALL);

        try {
            $pages = $pairing->getPages($this->supplierCode);

            foreach ($pages as $page) {
                $this->logger->info("[" . self::class . "]" . " Paired to page: " . $this->supplierCode . " - " . $page->getPageId());

                Cache::lock('page_reference_job_' . $page->getPageId(), 30)->get(function () use ($page) {
                    $oeNumbers = explode(',', $page->getValue($this->oeNumbersPropertyTag));
                    $oeNumbers = array_merge($oeNumbers, $this->oeCodes);
                    $oeNumbers = array_unique($oeNumbers);
                    $oeNumbers = array_filter($oeNumbers, function ($oeNumber) {
                        return !empty($oeNumber);
                    });


                    $page->setValue($this->oeNumbersPropertyTag, implode(',', $oeNumbers));
                    $page->save();
                });
            }
        } catch (\Throwable $e) {
            ErrorReporter::reportSilent($e);
        }
    }
}
