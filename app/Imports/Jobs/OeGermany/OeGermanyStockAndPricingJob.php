<?php

namespace App\Imports\Jobs\OeGermany;

use App\Imports;
use App\Logger\ImportsLogger;
use Buxus\Error\ErrorReporter;
use Buxus\Page\PageInterface;
use Buxus\Util\PropertyTag;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class OeGermanyStockAndPricingJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $code;
    protected $price;
    protected $on_stock;

    /**
     * @var \Buxus\Logger\Logger
     */
    protected $logger;


    public function __construct($code, $on_stock, $price = null)
    {
        $this->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));

        $this->code = $code;
        $this->price = $price;
        $this->on_stock = $on_stock;
    }

    public function handle()
    {
        $this->logger = (new ImportsLogger)->getLogger();

        try {
            $page = \PageFactory::builder()
                ->wherePropertyValue(PropertyTag::OE_GERMANY_SUPPLIER_CODE_TAG(), trim($this->code))
                ->first();

            if ($page instanceof PageInterface) {
                $this->updatePage($page);
            }
        } catch (\Exception $e) {
            ErrorReporter::reportSilent($e);
        }
    }

    protected function updatePage(PageInterface $page)
    {
        if ($this->price > 0 && $this->price != null) {
            $page->setValue(PropertyTag::OE_GERMANY_PRICE_WITHOUT_VAT_TAG(), $this->price);
        }

        $page->setValue(PropertyTag::OE_GERMANY_STOCK_BALANCE_TAG(), $this->on_stock);
        $page->setValue(PropertyTag::OE_GERMANY_LATEST_IMPORT_TAG(), Carbon::now()->toDateTimeString());
        $page->save();

        $this->logger->info("[OE_GERMANY] Changing price and stock of page with ID: {$page->getPageId()}, price {$this->price}, stock {$this->on_stock}");
    }

}
