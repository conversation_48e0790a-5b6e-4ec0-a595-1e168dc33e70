<?php

namespace App\Imports\Jobs\Martex;

use App\Imports\Pairing\MartexPairingManager;
use App\Logger\ImportsLogger;
use Buxus\Error\ErrorReporter;
use Buxus\Page\PageInterface;
use Buxus\Util\PropertyTag;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class MartexStockJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $supplierCode;
    protected $stock;

    /**
     * @var \Buxus\Logger\Logger
     */
    protected $logger;

    public function __construct($supplierCode, $stock)
    {
        $this->supplierCode = $supplierCode;
        $this->stock = $stock;

        $this->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));
    }

    public function handle()
    {
        $this->logger = (new ImportsLogger)->getLogger();

        try {
            $manager = new MartexPairingManager();
            $page = $manager->getPages($this->supplierCode);

            if (!empty($page)) {
                $this->updatePage($page);
            }
        } catch (\Exception $e) {
            ErrorReporter::reportSilent($e);
        }
    }

    protected function updatePage(PageInterface $page)
    {
        $page->setValue(PropertyTag::MARTEX_STOCK_BALANCE_TAG(), (int)$this->stock);
        $page->setValue(PropertyTag::MARTEX_LATEST_IMPORT_TAG(), Carbon::now()->toDateTimeString());

        $page->save(false);

        $this->logger->info("[MARTEX_STOCK] Changing price and stock of page with ID: {$page->getPageId()}, code {$this->supplierCode}, stock {$this->stock}");
    }
}
