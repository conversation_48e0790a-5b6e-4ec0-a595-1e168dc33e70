<?php

namespace App\Imports\Jobs\NRF;

use App\Imports\Pairing\BasePairingManager;
use App\Imports\Pairing\MecDieselPairingManager;
use App\Imports\Pairing\NRFPairingManager;
use App\Logger\ImportsLogger;
use Buxus\Error\ErrorReporter;
use Buxus\Util\PropertyTag;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class NRFReferenceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var \Buxus\Logger\Logger
     */
    protected $logger;

    protected $NRFCode;
    protected $oeCodes;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($NRFCode, $oeCodes)
    {
        $this->NRFCode = $NRFCode;
        $this->oeCodes = $oeCodes;

        $this->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->logger = (new ImportsLogger)->getLogger();
        $this->logger->info("[" . self::class . "]" . " NRFReferenceJob: " . $this->NRFCode . " - " . implode(',', $this->oeCodes));

        $pairing = new NRFPairingManager(BasePairingManager::METHOD_GET_ALL);

        try {
            $pages = $pairing->getPages($this->NRFCode);

            foreach ($pages as $page) {
                $this->logger->info("[" . self::class . "]" . " Paired to page: " . $this->NRFCode . " - " . $page->getPageId());

                $oeNumbers = explode(',', $page->getValue(PropertyTag::NRF_OE_NUMBERS_TAG()));
                $oeNumbers = array_merge($oeNumbers, $this->oeCodes);
                $oeNumbers = array_unique($oeNumbers);
                $oeNumbers = array_filter($oeNumbers, function ($oeNumber) {
                    return !empty($oeNumber);
                });


                $page->setValue(PropertyTag::NRF_OE_NUMBERS_TAG(), implode(',', $oeNumbers));
                $page->save();
            }
        } catch (\Throwable $e) {
            ErrorReporter::reportSilent($e);
        }
    }
}
