<?php

namespace App\Imports\Jobs\NRF;

use App\Eshop\Product\GroupedProducts;
use App\Imports;
use App\Imports\Helper\CategoryHelper;
use App\Imports\Pairing\BasePairingManager;
use App\Imports\Pairing\NRFPairingManager;
use App\Logger\ImportsLogger;
use App\Product\Codes\ProductCodesChangedEvent;
use App\Product\ProductConstants;
use Buxus\Error\ErrorReporter;
use Buxus\Logger\Logger;
use Buxus\Page\PageInterface;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;
use BuxusEvent;
use Carbon\Carbon;
use Ciselniky;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use PageTypesConstantsIDs;
use ProductFactory;

class NRFImportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var Logger
     */
    protected $logger;
    protected $item;
    protected $importId;

    protected $last;

    public function __construct($item, $importId, $last = false)
    {
        $this->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));

        $this->item = $item->toArray();
        $this->importId = $importId;
        $this->last = $last;
    }

    public function handle()
    {
        $this->logger = (new ImportsLogger())->getLogger();

        try {
            $item = $this->item;

            $pairing = new NRFPairingManager();
            $pairing->setMethod(BasePairingManager::METHOD_GET_FIRST);

            $page = $pairing->getPages((string)$item['nrf_cislo_produktu']);

            if ($page instanceof PageInterface) {
                $this->updatePage($page, $item);
            } else {
                $this->createPage($item);
            }

            if ($this->last) {
                $this->setImportToDone();
            }
        } catch (\Exception $e) {
            ErrorReporter::reportSilent($e);
            Imports::find($this->importId)->increment('errors');
        }
    }

    protected function getProductData($item)
    {
        $categories = [
            'kat1' => trim($item['kat_1']),
        ];

        $categoryHelper = new CategoryHelper();

        return [
            'name' => trim($item['nazov']),
            PropertyTag::NRF_SUPPLIER_CODE_TAG() => trim($item['nrf_cislo_produktu']),
            PropertyTag::NRF_OE_NUMBER_TAG() => trim($item['oe_number']),
            PropertyTag::NRF_PRICE_WITHOUT_VAT_TAG() => trim($item['cena']),
            PropertyTag::TITLE_TAG() => trim($item['oe_number']) . ' ' . trim($item['nazov']),
            PropertyTag::ESHOP_ROLLER_PRODUCER_TAG() => Ciselniky::get('product_catalog.producer')->getValueByName(trim($item['znacka']))->getId(),
            PropertyTag::PRODUCT_SOURCE_TAG() => ProductConstants::SOURCE_NRF,
            PropertyTag::SUPPLIER_TAG() => PageIds::getNRFSupplier(),
            PropertyTag::CATEGORIZATION_TAG() => $categoryHelper->getAllParentPageIds($categories),
            PropertyTag::NRF_LATEST_IMPORT_TAG() => Carbon::now()->toDateTimeString(),
            'parent_page_id' => $categoryHelper->getSupposedParentPageId($categories),
        ];
    }

    protected function getProductUpdateData($item)
    {
        return [
            PropertyTag::NRF_OE_NUMBER_TAG() => trim($item['oe_number']),
            PropertyTag::NRF_PRICE_WITHOUT_VAT_TAG() => trim($item['cena']),
            PropertyTag::ESHOP_ROLLER_PRODUCER_TAG() => Ciselniky::get('product_catalog.producer')->getValueByName(trim($item['znacka']))->getId(),
            PropertyTag::SUPPLIER_TAG() => PageIds::getNRFSupplier(),
            PropertyTag::NRF_LATEST_IMPORT_TAG() => Carbon::now()->toDateTimeString(),
        ];
    }

    protected function createPage($item)
    {
        $productData = $this->getProductData($item);

        $page = \PageFactory::create($productData['parent_page_id'], PageTypesConstantsIDs::ESHOP_PRODUCT_ID());
        $page->setPageName($productData['name']);

        $mutex = new \malkusch\lock\mutex\FlockMutex(fopen(__FILE__, 'rb'));
        $mutex->synchronized(function () use ($page) {
            $page->save(false);
        });

        $product = ProductFactory::get($page->getPageId());
        $product->fillPropertiesAndSave($productData);

        GroupedProducts::processForCode($productData[PropertyTag::NRF_SUPPLIER_CODE_TAG()]);
        $event = new ProductCodesChangedEvent($page);
        BuxusEvent::fire($event);

        $this->logger->info("[NRF] Created page with ID: {$page->getPageId()}}, price {$productData[PropertyTag::NRF_PRICE_WITHOUT_VAT_TAG()]}");
        Imports::find($this->importId)->increment('creates_processed');
    }

    protected function updatePage(PageInterface $page, $item)
    {
        $productData = $this->getProductUpdateData($item);

        $product = ProductFactory::get($page->getPageId());
        $product->fillPropertiesAndSave($product, false);

        $event = new ProductCodesChangedEvent($page);
        BuxusEvent::fire($event);

        $this->logger->info("[NRF] Updated page with ID: {$page->getPageId()}, price {$productData[PropertyTag::NRF_PRICE_WITHOUT_VAT_TAG()]}");
        Imports::find($this->importId)->increment('updates_processed');
    }

    protected function setImportToDone()
    {
        $import = Imports::find($this->importId);
        $import->status = Imports::DONE;
        $import->save();
    }
}
