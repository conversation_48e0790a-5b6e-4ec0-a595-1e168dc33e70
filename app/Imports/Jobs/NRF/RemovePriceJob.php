<?php

namespace App\Imports\Jobs\NRF;

use App\Http\Livewire\NRFImport;
use App\Imports;
use App\Imports\Pairing\NRFPairingManager;
use App\Logger\ImportsLogger;
use Buxus\Error\ErrorReporter;
use Buxus\Logger\Logger;
use Buxus\Page\PageInterface;
use Buxus\Util\PropertyTag;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RemovePriceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var Logger
     */
    protected $logger;
    protected string $code;
    protected string $pricePropertyTag;
    protected string $stockPropertyTag;
    protected int $import_id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($code, $import_id)
    {
        $this->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));

        $this->code = $code;

        $this->pricePropertyTag = config('imports.' . NRFImport::CONFIG_KEY . '.price_property_tag');
        $this->stockPropertyTag = config('imports.' . NRFImport::CONFIG_KEY . '.stock_property_tag');
        $this->import_id = $import_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->logger = (new ImportsLogger)->getLogger();

        try {
            $pairing = new NRFPairingManager();
            $pairing->setMethod(Imports\Pairing\BasePairingManager::METHOD_GET_FIRST);
            $page = $pairing->getPages($this->code);

            if ($page instanceof PageInterface) {
                $price = $page->getValue($this->pricePropertyTag);

                $page->setValue($this->pricePropertyTag, null);
                $page->setValue($this->stockPropertyTag, null);

                $mutex = new \malkusch\lock\mutex\FlockMutex(fopen(__FILE__, 'rb'));
                $mutex->synchronized(function () use ($page) {
                    $page->save(false);
                });

                $this->logger->info("\n[NRF] Removing product price: {$this->code}, page ID: {$page->getPageId()}, from price: {$price}");

                Imports::find($this->import_id)->increment('deletes_processed');
            }
        } catch (\Throwable $e) {
            ErrorReporter::reportSilent($e);
            Imports::find($this->import_id)->increment('errors');
        }
    }
}
