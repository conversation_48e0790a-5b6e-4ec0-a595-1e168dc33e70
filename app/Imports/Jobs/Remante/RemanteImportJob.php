<?php

namespace App\Imports\Jobs\Remante;

use App\Imports;
use App\Imports\Helper\CategoryHelper;
use App\Imports\Jobs\AbstractImportJob;
use App\Imports\Pairing\RemantePairingManager;
use App\Logger\ImportsLogger;
use Buxus\Core\Constants;
use Buxus\Error\ErrorReporter;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;
use Carbon\Carbon;

class RemanteImportJob extends AbstractImportJob
{
    protected string $pairingManagerClass = RemantePairingManager::class;

    protected function getPropertyValueChangesForUpdate(): array
    {
        $properties = [];

        return array_merge($properties, [
            PropertyTag::ESHOP_ROLLER_PRODUCER_TAG() => $this->getProducerId(),
            PropertyTag::SUPPLIER_TAG() => PageIds::getRemanteSupplier(),
            PropertyTag::REMANTE_SUPPLIER_CODE_TAG() => $this->supplierCode,
            PropertyTag::REMANTE_OE_NUMBER_TAG() => $this->getMainOeNumber(),
            PropertyTag::REMANTE_OE_NUMBERS_TAG() => implode(',', $this->getOeNumbers()),
            PropertyTag::REMANTE_PRICE_WITHOUT_VAT_TAG() => $this->price,
            PropertyTag::REMANTE_LATEST_IMPORT_TAG() => Carbon::now()->toDateTimeString(),
        ]);
    }

    protected function getPropertyValueChangesForCreate(): array
    {
        $properties = [];

        $title = $this->getMainOeNumber() . ' ' . $this->title;

        if ($this->hasDeposit()) {
            $title .= ' ' . $this->additionalProperties;
            $properties[PropertyTag::ESHOP_EUR_DEPOSIT_AMOUNT_WITHOUT_VAT_TAG()] = $this->getDepositValue();
            $properties[PropertyTag::IS_REFURBISHED_PART_TAG()] = Constants::C_True_Char;
            $properties[PropertyTag::ONIX_DEPOSIT_CODE_TAG()] = '013024';
        }

        return array_merge($properties, [
            PropertyTag::TITLE_TAG() => $title,
            PropertyTag::ESHOP_ROLLER_PRODUCER_TAG() => $this->getProducerId(),
            PropertyTag::SUPPLIER_TAG() => PageIds::getRemanteSupplier(),
            PropertyTag::REMANTE_SUPPLIER_CODE_TAG() => $this->supplierCode,
            PropertyTag::REMANTE_OE_NUMBER_TAG() => $this->getMainOeNumber(),
            PropertyTag::REMANTE_OE_NUMBERS_TAG() => implode(',', $this->getOeNumbers()),
            PropertyTag::REMANTE_PRICE_WITHOUT_VAT_TAG() => $this->price,
            PropertyTag::REMANTE_LATEST_IMPORT_TAG() => Carbon::now()->toDateTimeString(),
        ]);
    }

    protected function preprocessData()
    {
        $this->supplierCode = trim($this->supplierCode);
        $this->title = trim($this->title);
        $this->price = trim($this->price);

        foreach ($this->additionalProperties as $key => $value) {
            $this->additionalProperties[$key] = trim($value);
        }
    }

    public function handle()
    {
        $this->logger = (new ImportsLogger)->getLogger();
        $this->preprocessData();

        try {
            /** @var RemantePairingManager $manager */
            $manager = new $this->pairingManagerClass();
            $page = $manager->getPages($this->supplierCode);

            if (!empty($page)) {
                $this->updatePage($page);

                if ($this->last) {
                    $this->setImportToDone();
                }

                return;
            }
        } catch (\Exception $e) {
            if ($this->importId) {
                Imports::find($this->importId)->increment('errors');
            }
            ErrorReporter::reportSilent($e);
            $this->logger->error($e->getMessage());
        }

        parent::handle();
    }

    protected function getParentPageIdForCreate(): ?int
    {
        return PageIds::getNezaradene();
    }

    protected function getProducerId(): int
    {
        $producer = $this->additionalProperties['vyrobca'];

        $producer = trim($producer);
        return \Ciselniky::get('product_catalog.producer')->getValueByName($producer)->getId();
    }

    protected function getMainOeNumber(): ?string
    {
        return $this->additionalProperties['cislo_dilu'];
    }

    protected function getOeNumbers(): ?array
    {
        return array_map('trim', explode(',', $this->additionalProperties['oem']));
    }

    protected function hasDeposit(): bool
    {
        return $this->additionalProperties['nazov_2'] == 'REMANUFACTURED' || $this->additionalProperties['zaloha'] > 0;
    }

    protected function getDepositValue()
    {
        return $this->additionalProperties['zaloha'];
    }
}
