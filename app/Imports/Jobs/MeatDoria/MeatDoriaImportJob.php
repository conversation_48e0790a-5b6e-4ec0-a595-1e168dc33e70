<?php

namespace App\Imports\Jobs\MeatDoria;

use App\Imports;
use App\Imports\Helper\CategoryHelper;
use App\Imports\Jobs\AbstractImportJob;
use App\Imports\Pairing\MeatDoriaPairingManager;
use App\Logger\ImportsLogger;
use Buxus\Core\Constants;
use Buxus\Error\ErrorReporter;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;
use Carbon\Carbon;

class MeatDoriaImportJob extends AbstractImportJob
{
    protected string $pairingManagerClass = MeatDoriaPairingManager::class;
    protected ?string $price = null;

    protected function getPropertyValueChangesForUpdate(): array
    {
        $properties = [];
        $properties = array_merge($properties, $this->getSizeProperties());

        return array_merge($properties, [
            PropertyTag::ESHOP_ROLLER_PRODUCER_TAG() => $this->getProducerId(),
            PropertyTag::SUPPLIER_TAG() => PageIds::getMeatDoriaSupplier(),
            PropertyTag::WEIGHT_TAG() => $this->getWeight(),
            PropertyTag::SUPPLIER_EAN_TAG() => $this->getEan(),
            PropertyTag::MEAT_DORIA_INTRASTAT_TAG() => $this->getIntrastatCode(),
            PropertyTag::MEAT_DORIA_PRICE_WITHOUT_VAT_TAG() => $this->getPrice(),
            PropertyTag::MEAT_DORIA_SUPPLIER_CODE_TAG() => $this->supplierCode,
            PropertyTag::MEAT_DORIA_LATEST_IMPORT_TAG() => Carbon::now()->toDateTimeString(),
        ]);
    }

    protected function getPropertyValueChangesForCreate(): array
    {
        $properties = [];
        $properties = array_merge($properties, $this->getSizeProperties());

        return array_merge($properties, [
            PropertyTag::TITLE_TAG() => $this->getTitle(),
            PropertyTag::ESHOP_ROLLER_PRODUCER_TAG() => $this->getProducerId(),
            PropertyTag::SUPPLIER_TAG() => PageIds::getMeatDoriaSupplier(),
            PropertyTag::WEIGHT_TAG() => $this->getWeight(),
            PropertyTag::SUPPLIER_EAN_TAG() => $this->getEan(),
            PropertyTag::MEAT_DORIA_INTRASTAT_TAG() => $this->getIntrastatCode(),
            PropertyTag::MEAT_DORIA_PRICE_WITHOUT_VAT_TAG() => $this->getPrice(),
            PropertyTag::MEAT_DORIA_SUPPLIER_CODE_TAG() => $this->supplierCode,
            PropertyTag::MEAT_DORIA_LATEST_IMPORT_TAG() => Carbon::now()->toDateTimeString(),
        ]);
    }

    protected function preprocessData()
    {
        $this->supplierCode = trim($this->supplierCode);
        $this->title = trim($this->title);

        foreach ($this->additionalProperties as $key => $value) {
            $this->additionalProperties[$key] = trim($value);
        }
    }

    protected function getParentPageIdForCreate(): ?int
    {
        return PageIds::getNezaradene();
    }

    protected function getProducerId(): int
    {
        $producer = $this->additionalProperties['brand'];

        $producer = trim($producer);
        return \Ciselniky::get('product_catalog.producer')->getValueByName($producer)->getId();
    }

    public function getTitle(): string
    {
        return implode(' ', [
            $this->supplierCode,
            $this->title,
            $this->additionalProperties['nazov2'],
        ]);
    }

    protected function getWeight(): ?float
    {
        $weight = $this->additionalProperties['vaha'];

        if (empty($weight)) {
            return null;
        }

        $weight = floatval($weight);

        return $weight * 1000;
    }

    protected function getSizeProperties(): array
    {
        $size = $this->additionalProperties['rozmery'];

        if (empty($size)) {
            return [];
        }

        $size = explode('x', $size);

        if (count($size) !== 3) {
            return [];
        }

        $size = array_map('trim', $size);
        $size = array_map('floatval', $size);
        $size = array_map(function ($value) {
            return $value * 10;
        }, $size);

        return [
            PropertyTag::WIDTH_TAG() => $size[0],
            PropertyTag::HEIGHT_TAG() => $size[1],
            PropertyTag::LENGTH_TAG() => $size[2],
        ];
    }

    protected function getEan(): string
    {
        return $this->additionalProperties['ean'];
    }

    protected function getIntrastatCode(): string
    {
        return $this->additionalProperties['intrastat'];
    }

    public function getPrice(): string
    {
        $price = $this->price;

        if (empty($price)) {
            return '';
        }

        return trim(str_replace('€', '', $price));
    }
}
