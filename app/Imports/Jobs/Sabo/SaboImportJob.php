<?php

namespace App\Imports\Jobs\Sabo;

use App\Imports;
use App\Imports\Helper\CategoryHelper;
use App\Imports\Jobs\AbstractImportJob;
use App\Imports\Pairing\SaboPairingManager;
use App\Logger\ImportsLogger;
use Buxus\Core\Constants;
use Buxus\Error\ErrorReporter;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;
use Carbon\Carbon;

class SaboImportJob extends AbstractImportJob
{
    protected string $pairingManagerClass = SaboPairingManager::class;

    protected function getPropertyValueChangesForUpdate(): array
    {
        $properties = [];

        return array_merge($properties, [
            PropertyTag::TITLE_TAG() => $this->supplierCode . ' ' . $this->title,
            PropertyTag::ESHOP_ROLLER_PRODUCER_TAG() => $this->getProducerId(),
            PropertyTag::SUPPLIER_TAG() => PageIds::getSaboSupplier(),
            PropertyTag::SABO_SUPPLIER_CODE_TAG() => $this->supplierCode,
            PropertyTag::SABO_PRICE_WITHOUT_VAT_TAG() => $this->price,
            PropertyTag::SABO_LATEST_IMPORT_TAG() => Carbon::now()->toDateTimeString(),
        ]);
    }

    protected function getPropertyValueChangesForCreate(): array
    {
        $properties = [];

        return array_merge($properties, [
            PropertyTag::TITLE_TAG() => $this->supplierCode . ' ' . $this->title,
            PropertyTag::ESHOP_ROLLER_PRODUCER_TAG() => $this->getProducerId(),
            PropertyTag::SUPPLIER_TAG() => PageIds::getSaboSupplier(),
            PropertyTag::SABO_SUPPLIER_CODE_TAG() => $this->supplierCode,
            PropertyTag::SABO_PRICE_WITHOUT_VAT_TAG() => $this->price,
            PropertyTag::SABO_LATEST_IMPORT_TAG() => Carbon::now()->toDateTimeString(),
        ]);
    }

    protected function preprocessData()
    {
        $this->supplierCode = trim($this->supplierCode);
        $this->title = trim($this->title);
        $this->price = trim($this->price);

        foreach ($this->additionalProperties as $key => $value) {
            $this->additionalProperties[$key] = trim($value);
        }
    }

    public function handle()
    {
        $this->logger = (new ImportsLogger)->getLogger();
        $this->preprocessData();

        try {
            /** @var SaboPairingManager $manager */
            $manager = new $this->pairingManagerClass();
            $page = $manager->getPages($this->supplierCode);

            if (!empty($page)) {
                $this->updatePage($page);

                if ($this->last) {
                    $this->setImportToDone();
                }

                return;
            }
        } catch (\Exception $e) {
            if ($this->importId) {
                Imports::find($this->importId)->increment('errors');
            }
            ErrorReporter::reportSilent($e);
            $this->logger->error($e->getMessage());
        }

        parent::handle();
    }

    protected function getParentPageIdForCreate(): ?int
    {
        return PageIds::getNezaradene();
    }

    protected function getProducerId(): int
    {
        $producer = $this->additionalProperties['vyrobca'];

        $producer = trim($producer);
        return \Ciselniky::get('product_catalog.producer')->getValueByName($producer)->getId();
    }
}
