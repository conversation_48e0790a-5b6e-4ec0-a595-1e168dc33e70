<?php

namespace App\Imports\Jobs\IvecoBigDb;

use App\Imports;
use App\Logger\ImportsLogger;
use App\Product\Codes\ProductCodesChangedEvent;
use Buxus\Error\ErrorReporter;
use Buxus\Page\PageInterface;
use Buxus\Util\PageIds;
use Buxus\Util\PageTypeID;
use Buxus\Util\PropertyID;
use Buxus\Util\PropertyTag;
use BuxusEvent;
use BuxusSite;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class IvecoProductBigDbImportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    protected $page;
    protected $code;
    protected $name;
    protected $price;
    protected $logger;
    protected $availability;
    protected $importId;
    protected $categoryId;
    protected $last;
    protected $site;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($code, $name, $price, $availability, $importId, $categoryId, $last = false, $site = 'sk')
    {
        $this->code = $code;
        $this->name = $name;
        $this->price = $price;
        $this->availability = $availability;
        $this->importId = $importId;
        $this->categoryId = $categoryId;
        $this->last = $last;
        $this->site = $site;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->logger = (new ImportsLogger)->getLogger();

        BuxusSite::executeInSiteContext($this->site, function () {
            try {
                $sql = "select DISTINCT ppv.page_id from `tblPagePropertyValues` as `ppv` WHERE ppv.property_id = ? AND ppv.property_value = ?";
                $existingPages = \DB::select($sql, [PropertyID::IVECO_BIG_DB_IMPORT_CODE_ID(), $this->code]);

                if (!(is_array($existingPages) && count($existingPages) > 0)) {
                    $sql = "select DISTINCT ppv.page_id from `tblPagePropertyValues` as `ppv` WHERE ( (ppv.property_id = ? AND ppv.property_value = ?) OR (ppv.property_id = ? AND ppv.property_value = ?))";
                    $existingPages = \DB::select($sql, [PropertyID::ONIX_MAIN_CODE_ID(), $this->code, PropertyID::IVECO_BIG_DB_IMPORT_CODE_EN_ID(), $this->importId]);
                }

                $found = false;
                if (is_array($existingPages) && count($existingPages) > 0) {
                    foreach ($existingPages as $existingPage) {
                        $existingPage = \PageFactory::get($existingPage->page_id);
                        if ($existingPage->getPageTypeId() != PageTypeID::ESHOP_PRODUCT_ID()) {
                            continue;
                        }
                        if ($existingPage->getValue(PropertyTag::ESHOP_ROLLER_PRODUCER_TAG()) == PageIds::getProducerIvecoOriginal()) {
                            $this->updateProduct($existingPage);
                            $found = true;
                        }
                    }
                }

                if (!$found) {
                    $this->createProduct();
                }

                if ($this->last) {
                    $this->setImportToDone();
                }
            } catch (\Exception $e) {
                ErrorReporter::reportSilent($e);

                $this->logger = (new ImportsLogger)->getLogger();
                $this->logger->error("\n[IVECO BIG DB] Something went wrong while updating product: {$this->code}\n{$e->getMessage()}");
                Imports::find($this->importId)->increment('errors');
            }
        });
    }

    protected function updateProduct(PageInterface $page)
    {
        if (empty($page->getValue(PropertyTag::TITLE_TAG()))) {
            $page->setValue(PropertyTag::TITLE_TAG(), $this->code . ' ' . $this->name);
        };

        $page->setValue(PropertyTag::TITLE_EN_TAG(), $this->code . ' ' . $this->name);
        $page->setValue(config('imports.iveco_big_db.availability_property_tag'), $this->availability);
        $page->setValue(config('imports.iveco_big_db.import_code_property_tag'), $this->code);
        $page->setValue(config('imports.iveco_big_db.price_property_tag'), $this->price);
        $page->setValue(config('imports.iveco_big_db.latest_import_property_tag'), Carbon::now());
        $page->setValue(PropertyTag::CATEGORIZATION_TAG(), [PageIds::getCategoryIveco()]);
        $page->save(false);

        $event = new ProductCodesChangedEvent($page);
        BuxusEvent::fire($event);

        $this->logger->info("\n[IVECO BIG DB] Updating product: {$this->code}, page ID: {$page->getPageId()}, from price: {$page->getValue(PropertyTag::ESHOP_EUR_IVECO_BIG_DB_PRICE_WITHOUT_VAT_TAG())}, to price: {$this->price}");
        Imports::find($this->importId)->increment('updates_processed');
    }

    protected function createProduct()
    {
        $page = \PageFactory::create($this->categoryId, PageTypeID::ESHOP_PRODUCT_ID());
        $page->setPageName($this->name);
        $page->setValue(PropertyTag::TITLE_TAG(), $this->code . ' ' . $this->name);
        $page->setValue(PropertyTag::TITLE_EN_TAG(), $this->code . ' ' . $this->name);
        $page->setValue(config('imports.iveco_big_db.price_property_tag'), $this->price);
        $page->setValue(PropertyTag::ESHOP_ROLLER_PRODUCER_TAG(), PageIds::getProducerIvecoOriginal());
        $page->setValue(config('imports.iveco_big_db.import_code_property_tag'), $this->code);
        $page->setValue(config('availability_property_tag'), $this->availability);
        $page->setValue(config('imports.iveco_big_db.latest_import_property_tag'), Carbon::now());
        $page->setValue(PropertyTag::CATEGORIZATION_TAG(), [PageIds::getCategoryIveco()]);
        $page->save();

        $event = new ProductCodesChangedEvent($page);
        BuxusEvent::fire($event);

        Imports::find($this->importId)->increment('creates_processed');

        $this->logger->info("\n[IVECO BIG DB] Creating product: {$this->code}, page ID: {$page->getPageId()}, with price: {$this->price}");

    }

    protected function setImportToDone()
    {
        $import = Imports::find($this->importId);
        $import->status = Imports::DONE;
        $import->save();
    }
}
