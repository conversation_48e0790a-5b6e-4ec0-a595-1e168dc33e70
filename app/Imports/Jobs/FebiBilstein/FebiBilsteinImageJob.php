<?php

namespace App\Imports\Jobs\FebiBilstein;

use App\Http\Livewire\FebiBilsteinImport;
use App\Imports;
use App\Imports\Pairing\BasePairingManager;
use App\Imports\Pairing\FebiBilsteinPairingManager;
use App\Imports\Pairing\NRFPairingManager;
use App\Logger\ImportsLogger;
use Arr;
use Buxus\Ciselniky\ValueInterface;
use Buxus\Core\Constants;
use Buxus\Error\ErrorReporter;
use Buxus\Page\PageInterface;
use Buxus\Util\PageTypeID;
use Buxus\Util\PropertyTag;
use File;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class FebiBilsteinImageJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var \Buxus\Logger\Logger
     */
    protected $logger;

    protected $febiBilsteinCode;
    protected $imageData;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($febiBilsteinCode, $imageData)
    {
        $this->febiBilsteinCode = $febiBilsteinCode;
        $this->imageData = $imageData;

        $this->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->logger = (new ImportsLogger)->getLogger();
        $this->logger->info("[" . self::class . "]" . " ImageJob: " . $this->febiBilsteinCode . " - " . implode(',', $this->imageData));

        $pairing = new FebiBilsteinPairingManager(BasePairingManager::METHOD_GET_ALL);

        try {
            $pages = $pairing->getPages($this->febiBilsteinCode);

            foreach ($pages as $page) {
                $this->logger->info("[" . self::class . "]" . " Paired to page: " . $this->febiBilsteinCode . " - " . $page->getPageId());

                foreach ($this->imageData as $imageData) {
                    $imagePath = 'product-images/febi_bilstein/' . basename($imageData['url']);

                    $skipCreation = false;
                    foreach ($page->getChildren() as $child) {
                        if ($child->getValue(PropertyTag::PHOTO_FILE_TAG()) == $imagePath) {
                            $skipCreation = true;
                        }
                    }

                    $content = file_get_contents($imageData['url']);
                    $path = public_path('buxus/images/' . $imagePath);
                    File::ensureDirectoryExists(dirname($path));
                    file_put_contents($path, $content);

                    if (!$skipCreation) {
                        $photoPage = \PageFactory::create($page->getPageId(), PageTypeID::PHOTO_ID());
                        $photoPage->setPageName($imagePath);
                        $photoPage->setPageStateId(Constants::C_active_page_state_id);
                        $photoPage->setValue(PropertyTag::TITLE_TAG(), $page->getValue(PropertyTag::TITLE_TAG()));
                        $photoPage->setValue(PropertyTag::PHOTO_FILE_TAG(), $imagePath);
                        $photoPage->setValue(PropertyTag::PHOTO_ALT_TAG(), $page->getValue(PropertyTag::TITLE_TAG()));
                        $photoPage->save(false);
                    }

                    if (empty($page->getValue(PropertyTag::PHOTO_ALBUM_THUMBNAIL_TAG())) && $imageData['type'] == 'article image') {
                        $page->setValue(PropertyTag::PHOTO_ALBUM_THUMBNAIL_TAG(), $imagePath);
                        $page->save();
                    }
                }
            }
        } catch (\Throwable $e) {
            ErrorReporter::reportSilent($e);
        }
    }
}
