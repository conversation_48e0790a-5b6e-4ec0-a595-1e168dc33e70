<?php

namespace App\Imports\Jobs\MecDiesel;

use App\Imports\Pairing\MecDieselPairingManager;
use App\Logger\ImportsLogger;
use Buxus\Core\Constants;
use Buxus\Error\ErrorReporter;
use Buxus\Page\PageInterface;
use Buxus\Util\PropertyTag;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class MecDieselStockAndPricingJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $supplierCode;
    protected $stockItaly;
    protected $stockCzech;
    protected $price;
    protected $depositPrice;

    /**
     * @var \Buxus\Logger\Logger
     */
    protected $logger;

    public function __construct($supplierCode, $stockItaly, $stockCzech, $price = null, $depositPrice = null)
    {
        $this->supplierCode = $supplierCode;
        $this->stockItaly = $stockItaly;
        $this->stockCzech = $stockCzech;
        $this->price = $price;
        $this->depositPrice = $depositPrice;

        $this->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));
    }

    public function handle()
    {
        $this->logger = (new ImportsLogger)->getLogger();

        try {
            $manager = new MecDieselPairingManager();
            $page = $manager->getPages($this->supplierCode);

            if (!empty($page)) {
                $this->updatePage($page);
            }
        } catch (\Exception $e) {
            ErrorReporter::reportSilent($e);
        }
    }

    protected function updatePage(PageInterface $page)
    {
        if ($this->price != null) {
            $page->setValue(PropertyTag::MEC_DIESEL_PRICE_WITHOUT_VAT_TAG(), (float)$this->price);
        }

        if ($this->shouldHaveDeposit()) {
            $page->setValue(PropertyTag::IS_REFURBISHED_PART_TAG(), Constants::C_True_Char);
            $page->setValue(PropertyTag::ESHOP_EUR_DEPOSIT_AMOUNT_WITHOUT_VAT_TAG(), (float)$this->depositPrice);
            $page->setValue(PropertyTag::ONIX_DEPOSIT_CODE_TAG(), '013024');
        } else {
            $page->setValue(PropertyTag::IS_REFURBISHED_PART_TAG(), Constants::C_False_Char);
            $page->setValue(PropertyTag::ESHOP_EUR_DEPOSIT_AMOUNT_WITHOUT_VAT_TAG(), null);
            $page->setValue(PropertyTag::ONIX_DEPOSIT_CODE_TAG(), null);
        }


        $page->setValue(PropertyTag::MEC_DIESEL_STOCK_BALANCE_ITALY_TAG(), (int)$this->stockItaly);
        $page->setValue(PropertyTag::MEC_DIESEL_STOCK_BALANCE_CZECH_TAG(), (int)$this->stockCzech);

        $page->setValue(PropertyTag::MEC_DIESEL_LATEST_IMPORT_TAG(), Carbon::now()->toDateTimeString());

        $page->save(false);

        $logInfo = "[MEC_DIESEL] Changing price and stock of page with ID: {$page->getPageId()}, code {$this->supplierCode}, price {$this->price}, stock Italy {$this->stockItaly}, stock Czech {$this->stockCzech}";

        if ($this->shouldHaveDeposit()) {
            $logInfo .= ", deposit price {$this->depositPrice}";
        }

        $this->logger->info($logInfo);
    }

    protected function shouldHaveDeposit(): bool
    {
        return $this->depositPrice != null && $this->depositPrice > 0;
    }
}
