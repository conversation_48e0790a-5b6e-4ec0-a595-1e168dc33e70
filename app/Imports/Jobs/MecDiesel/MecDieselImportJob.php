<?php

namespace App\Imports\Jobs\MecDiesel;

use App\Eshop\Product\GroupedProducts;
use App\Imports;
use App\Imports\Helper\CategoryHelper;
use App\Logger\ImportsLogger;
use App\Product\Codes\ProductCodesChangedEvent;
use Buxus\Core\Constants;
use Buxus\Error\ErrorReporter;
use Buxus\Page\PageInterface;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;
use BuxusEvent;
use Carbon\Carbon;
use Ciselniky;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use PageTypesConstantsIDs;
use ProductFactory;

class MecDieselImportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $item;
    protected $importId;

    protected $last;

    /**
     * @var \Buxus\Logger\Logger
     */
    protected $logger;

    public function __construct($item, $importId, $last = false)
    {
        $this->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));

        $this->item = $item;
        $this->importId = $importId;
        $this->last = $last;
    }

    public function handle()
    {
        $this->logger = (new ImportsLogger)->getLogger();

        try {
            $item = $this->item;

            $page = \PageFactory::builder()
                ->wherePropertyValue(PropertyTag::MEC_DIESEL_SUPPLIER_CODE_TAG(), trim($item['mec_cislo']))
                ->first();

            if ($page instanceof PageInterface) {
                $this->updatePage($page, $item);
            } else {
                $this->createPage($item);
            }

            if ($this->last) {
                $this->setImportToDone();
            }
        } catch (\Exception $e) {
            ErrorReporter::reportSilent($e);
            Imports::find($this->importId)->increment('errors');
        }
    }

    protected function getProductData($item)
    {
        $categories = [
            'kat1' => trim($item['kat_1']),
        ];

        $categoryHelper = new CategoryHelper();

        $title = trim($item['oe_number']) . ' ' . trim($item['nazov']);

        if (!empty($item['repas'])) {
            $title .= ' ' . trim($item['repas']);
        }

        return [
            'name' => trim($item['nazov']),
            PropertyTag::MEC_DIESEL_SUPPLIER_CODE_TAG() => trim($item['mec_cislo']),
            PropertyTag::MEC_DIESEL_OE_NUMBER_TAG() => trim($item['oe_number']),
            PropertyTag::MEC_DIESEL_PRICE_WITHOUT_VAT_TAG() => trim($item['price']),
            PropertyTag::TITLE_TAG() => trim($title),
            PropertyTag::MEC_DIESEL_STOCK_BALANCE_ITALY_TAG() => trim($item['stock_italy']),
            PropertyTag::ESHOP_ROLLER_PRODUCER_TAG() => Ciselniky::get('product_catalog.producer')->getValueByName(trim($item['brand']))->getId(),
            PropertyTag::ESHOP_EUR_DEPOSIT_AMOUNT_WITHOUT_VAT_TAG() => $item['old_core_deposit'],
            PropertyTag::IS_REFURBISHED_PART_TAG() => $item['old_core_deposit'] > 0 ? Constants::C_True_Char : Constants::C_False_Char,
            PropertyTag::SUPPLIER_TAG() => PageIds::getMecDieselSupplier(),
            PropertyTag::CATEGORIZATION_TAG() => $categoryHelper->getAllParentPageIds($categories),
            PropertyTag::MEC_DIESEL_LATEST_IMPORT_TAG() => Carbon::now()->toDateTimeString(),
            'parent_page_id' => $categoryHelper->getSupposedParentPageId($categories),
        ];
    }

    protected function getProductUpdateData($item)
    {
        $title = trim($item['oe_number']) . ' ' . trim($item['nazov']);

        if (!empty($item['repas'])) {
            $title .= ' ' . trim($item['repas']);
        }

        $properties = [];

        if (!empty($item['repas'])) {
            $properties = [
                PropertyTag::TITLE_TAG() => trim($title)
            ];
        }

        return array_merge($properties, [
            PropertyTag::MEC_DIESEL_OE_NUMBER_TAG() => trim($item['oe_number']),
            PropertyTag::MEC_DIESEL_PRICE_WITHOUT_VAT_TAG() => trim($item['price']),
            PropertyTag::MEC_DIESEL_STOCK_BALANCE_ITALY_TAG() => trim($item['stock_italy']),
            PropertyTag::MEC_DIESEL_STOCK_BALANCE_CZECH_TAG() => 0,
            PropertyTag::ESHOP_ROLLER_PRODUCER_TAG() => Ciselniky::get('product_catalog.producer')->getValueByName(trim($item['brand']))->getId(),
            PropertyTag::ESHOP_EUR_DEPOSIT_AMOUNT_WITHOUT_VAT_TAG() => $item['old_core_deposit'],
            PropertyTag::IS_REFURBISHED_PART_TAG() => $item['old_core_deposit'] > 0 ? Constants::C_True_Char : Constants::C_False_Char,
            PropertyTag::SUPPLIER_TAG() => PageIds::getMecDieselSupplier(),
            PropertyTag::MEC_DIESEL_LATEST_IMPORT_TAG() => Carbon::now()->toDateTimeString(),
        ]);
    }

    protected function createPage($item)
    {
        $productData = $this->getProductData($item);

        $page = \PageFactory::create($productData['parent_page_id'], PageTypesConstantsIDs::ESHOP_PRODUCT_ID());
        $page->setPageName($productData['name']);

        $mutex = new \malkusch\lock\mutex\FlockMutex(fopen(__FILE__, 'rb'));
        $mutex->synchronized(function () use ($page) {
            $page->save();
        });

        $product = ProductFactory::get($page->getPageId());
        $product->fillPropertiesAndSave($productData);

        GroupedProducts::processForCode($productData[PropertyTag::MEC_DIESEL_SUPPLIER_CODE_TAG()]);
        $event = new ProductCodesChangedEvent($page);
        BuxusEvent::fire($event);

        $this->logger->info("[MEC DIESEL] Created page with ID: {$page->getPageId()}, price {$productData[PropertyTag::MEC_DIESEL_PRICE_WITHOUT_VAT_TAG()]}, stock italy {$productData[PropertyTag::MEC_DIESEL_STOCK_BALANCE_ITALY_TAG()]}, stock cz {$productData[PropertyTag::MEC_DIESEL_STOCK_BALANCE_CZECH_TAG()]}");
        Imports::find($this->importId)->increment('creates_processed');
    }

    protected function updatePage(PageInterface $page, $item)
    {
        $productData = $this->getProductUpdateData($item);

        $product = ProductFactory::get($page->getPageId());
        $product->fillPropertiesAndSave($productData);

        $event = new ProductCodesChangedEvent($page);
        BuxusEvent::fire($event);

        $this->logger->info("[MEC DIESEL] Updated page with ID: {$page->getPageId()}, price {$productData[PropertyTag::MEC_DIESEL_PRICE_WITHOUT_VAT_TAG()]}, stock italy {$productData[PropertyTag::MEC_DIESEL_STOCK_BALANCE_ITALY_TAG()]}, stock cz {$productData[PropertyTag::MEC_DIESEL_STOCK_BALANCE_CZECH_TAG()]}");
        Imports::find($this->importId)->increment('updates_processed');
    }

    protected function setImportToDone()
    {
        $import = Imports::find($this->importId);
        $import->status = Imports::DONE;
        $import->save();
    }
}
