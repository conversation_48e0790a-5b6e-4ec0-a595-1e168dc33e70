<?php

namespace App\Imports\Jobs;

use App\Imports;
use App\Imports\Pairing\BasePairingManager;
use App\Imports\Pairing\IvecoStockPricesPairingManager;
use App\Logger\ImportsLogger;
use Buxus\Error\ErrorReporter;
use Buxus\Util\PropertyTag;
use BuxusSite;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use PageFactory;

class RemovePriceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    protected $code;
    protected $propertyTag;
    protected $import_id;
    protected $page;
    protected $site;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($code, $propertyTag, $import_id, $site = 'sk')
    {
        $this->code = $code;
        $this->site = $site;
        $this->propertyTag = $propertyTag;
        $this->import_id = $import_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            BuxusSite::pushSite($this->site);

            $this->logger = (new ImportsLogger)->getLogger();
            $pairingManager = new IvecoStockPricesPairingManager(BasePairingManager::METHOD_GET_ALL);
            $pages = $pairingManager->getPages($this->code);

            foreach ($pages as $page) {
                $page->setValue($this->propertyTag, null);
                $page->save(false);

                Imports::find($this->import_id)->increment('deletes_processed');
                $this->logForPage($page);
            }


        } catch (\Throwable $e) {
            ErrorReporter::reportSilent($e);
            Imports::find($this->import_id)->increment('errors');
        } finally {
            BuxusSite::popSite();
        }
    }

    protected function logForPage($page)
    {
        if (strpos($this->propertyTag, PropertyTag::ESHOP_EUR_IVECO_BIG_DB_PRICE_WITHOUT_VAT_TAG()) !== false) {
            $this->logger->info("\n[IVECO BIG DB] Removing product price: {$this->code}, page ID: {$page->getPageId()}, from price: {$page->getValue(PropertyTag::ESHOP_EUR_IVECO_BIG_DB_PRICE_WITHOUT_VAT_TAG())}");
        } else {
            $this->logger->info("\n[IVECO Nákupné ceny] Removing product price: {$this->code}, page ID: {$page->getPageId()}, from price: {$page->getValue(PropertyTag::ESHOP_EUR_IVECO_BIG_DB_PRICE_WITHOUT_VAT_TAG())}");
        }
    }
}
