<?php

namespace App\Imports\Jobs\Eminia;

use App\Imports\Jobs\AbstractImportJob;
use App\Imports\Pairing\EminiaPairingManager;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;
use Carbon\Carbon;

class EminiaImportJob extends AbstractImportJob
{
    protected string $pairingManagerClass = EminiaPairingManager::class;

    protected function getPropertyValueChangesForUpdate(): array
    {
        return [
            PropertyTag::SUPPLIER_TAG() => PageIds::getEminiaSupplier(),
            PropertyTag::EMINIA_PRICE_WITHOUT_VAT_TAG() => $this->price,
            PropertyTag::EMINIA_STOCK_BALANCE_TAG() => $this->stockBalance,
            PropertyTag::EMINIA_LATEST_IMPORT_TAG() => Carbon::now()->toDateTimeString(),
        ];
    }

    protected function getPropertyValueChangesForCreate(): array
    {
        return [
            PropertyTag::TITLE_TAG() => $this->supplierCode . ' ' . $this->title,
            PropertyTag::ESHOP_ROLLER_PRODUCER_TAG() => PageIds::getProducerFiatOriginal(),
            PropertyTag::SUPPLIER_TAG() => PageIds::getEminiaSupplier(),
            PropertyTag::EMINIA_SUPPLIER_CODE_TAG() => $this->supplierCode,
            PropertyTag::EMINIA_PRICE_WITHOUT_VAT_TAG() => $this->price,
            PropertyTag::EMINIA_STOCK_BALANCE_TAG() => $this->stockBalance,
            PropertyTag::EMINIA_OE_NUMBER_TAG() => $this->supplierCode,
            PropertyTag::EMINIA_LATEST_IMPORT_TAG() => Carbon::now()->toDateTimeString(),
        ];
    }

    protected function getParentPageIdForCreate(): ?int
    {
        return PageIds::getFiatCategory();
    }
}
