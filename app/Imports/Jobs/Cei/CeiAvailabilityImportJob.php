<?php

namespace App\Imports\Jobs\Cei;

use App\Imports;
use App\Imports\Pairing\CeiPairingManager;
use App\Imports\Pairing\MotorservicePairingManager;
use App\Jobs\Traits\DispatchableAtMidnight;
use App\Logger\ImportsLogger;
use Buxus\Error\ErrorReporter;
use Buxus\Page\PageInterface;
use Buxus\Util\PropertyTag;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use ProductFactory;

class CeiAvailabilityImportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, DispatchableAtMidnight;

    protected $item;
    protected $importId;

    protected $last;

    /**
     * @var \Buxus\Logger\Logger
     */
    protected $logger;

    public function __construct($item, $importId, $last = false)
    {
        $this->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));

        $this->item = $item;
        $this->importId = $importId;
        $this->last = $last;
    }

    public function handle()
    {
        $this->logger = (new ImportsLogger)->getLogger();
        $this->setImportToProcessing();

        try {
            $item = $this->item;

            $pairing = new CeiPairingManager($item);
            $page = $pairing->getPages(substr(trim($item[0]), 0, 6));

            if ($page instanceof PageInterface) {
                $this->updatePage($page, $item);
            }

            if ($this->last) {
                $this->setImportToDone();
            }
        } catch (\Exception $e) {
            ErrorReporter::reportSilent($e);
            Imports::find($this->importId)->increment('errors');
        }
    }

    protected function getProductUpdateData($item)
    {
        if ($item[1] == 'S') {
            $quantity = (int)trim($item[2]);
        } else {
            $quantity = 0;
        }

        if ($quantity <= 0) {
            $quantity = 0;
        }

        return [
            PropertyTag::CEI_STOCK_BALANCE_TAG() => $quantity,
            PropertyTag::CEI_LATEST_STOCK_BALANCE_IMPORT_TAG() => Carbon::now()->toDateTimeString(),
        ];
    }

    protected function updatePage(PageInterface $page, $item)
    {
        $productData = $this->getProductUpdateData($item);

        $product = ProductFactory::get($page->getPageId());
        $product->fillPropertiesAndSave($productData);

        $this->logger->info("[MOTORSERVICE AVAILABILITY] Updated page with ID: {$page->getPageId()}, stock {$productData[PropertyTag::MOTORSERVICE_STOCK_BALANCE_TAG()]}");
        Imports::find($this->importId)->increment('updates_processed');
    }

    protected function setImportToDone()
    {
        $import = Imports::find($this->importId);
        $import->status = Imports::DONE;
        $import->save();
    }

    protected function setImportToProcessing()
    {
        $import = Imports::find($this->importId);

        if ($import->status != Imports::DELAYED) {
            return;
        }

        $import->status = Imports::STILL_RUNNING;
        $import->save();
    }
}
