<?php

namespace App\Imports\Jobs\AugustinGroup;

use App\Logger\ImportsLogger;
use Buxus\Core\Constants;
use Buxus\Error\ErrorReporter;
use Buxus\Page\PageInterface;
use Buxus\Util\PropertyTag;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class AugustinGroupStockAndPricingJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $number;
    protected $price;
    protected $on_stock;
    protected $bulky;

    /**
     * @var \Buxus\Logger\Logger
     */
    protected $logger;


    public function __construct($number, $on_stock, $price = null, $bulky = null)
    {
        $this->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));

        $this->number = $number;
        $this->price = $price;
        $this->on_stock = $on_stock;
        $this->bulky = $bulky;
    }

    public function handle()
    {
        $this->logger = (new ImportsLogger)->getLogger();

        try {
            $page = \PageFactory::builder()
                ->wherePropertyValue(PropertyTag::AUGUSTIN_GROUP_NUMBER_TAG(), trim($this->number))
                ->first();

            if (!empty($page)) {
                $this->updatePage($page);
            }
        } catch (\Exception $e) {
            ErrorReporter::reportSilent($e);
        }
    }

    protected function updatePage(PageInterface $page)
    {
        if ($this->price > 0 && $this->price != null) {
            $page->setValue(PropertyTag::AUGUSTIN_GROUP_PRICE_WITHOUT_VAT_TAG(), $this->price);
        }

        $page->setValue(PropertyTag::AUGUSTIN_GROUP_BULKY_PART_TAG(), $this->bulky == 1 ? Constants::C_True_Char : Constants::C_False_Char);
        $page->setValue(PropertyTag::AUGUSTIN_GROUP_STOCK_BALANCE_TAG(), $this->on_stock);
        $page->setValue(PropertyTag::AUGUSTIN_GROUP_LATEST_IMPORT_TAG(), Carbon::now()->toDateTimeString());
        $page->save(false);

        $this->logger->info("[AUGUSTIN GROUP] Changing price and stock of page with ID: {$page->getPageId()}, price {$this->price}, stock {$this->on_stock}");
    }

}
