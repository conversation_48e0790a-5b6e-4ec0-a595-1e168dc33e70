<?php

namespace App\Imports\Jobs\AugustinGroup;

use App\Http\Livewire\AugustinGroupImport;
use App\Imports;
use App\Logger\ImportsLogger;
use Buxus\Ciselniky\ValueInterface;
use Buxus\Core\Constants;
use Buxus\Error\ErrorReporter;
use Buxus\Page\PageInterface;
use Buxus\Util\PropertyTag;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RemovePriceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $supplier;
    protected $code;
    protected $propertyTag;
    protected $import_id;
    /**
     * @var \Buxus\Logger\Logger
     */
    protected $logger;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($code, $import_id)
    {
        $this->code = $code;

        $this->propertyTag = config('imports.' . AugustinGroupImport::CONFIG_KEY . '.price_property_tag');
        $this->import_id = $import_id;
    }
    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->logger = (new ImportsLogger)->getLogger();

        try {
            $page = \PageFactory::builder()
                ->wherePropertyValue(PropertyTag::AUGUSTIN_GROUP_NUMBER_TAG(), $this->code)
                ->first();

            if ($page instanceof PageInterface) {
                $page->setPageStateId(Constants::C_passive_page_state_id);
                $page->setValue(PropertyTag::AUGUSTIN_GROUP_STOCK_BALANCE_TAG(), null);
                $mutex = new \malkusch\lock\mutex\FlockMutex(fopen(__FILE__, 'rb'));
                $mutex->synchronized(function () use ($page) {
                    $page->save();
                });

                $this->logger->info("\n[AUGUSTIN GROUP] Removing product price: {$this->code}, page ID: {$page->getPageId()}, from price: {$page->getValue(PropertyTag::AUGUSTIN_GROUP_PRICE_WITHOUT_VAT_TAG())}");

                Imports::find($this->import_id)->increment('deletes_processed');
            }
        } catch (\Exception $e) {
            ErrorReporter::reportSilent($e);
            Imports::find($this->import_id)->increment('errors');
        }
    }
}
