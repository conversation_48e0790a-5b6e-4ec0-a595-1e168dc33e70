<?php

namespace App\Imports\Jobs\Motorservice;

use App\Eshop\Product\GroupedProducts;
use App\Imports;
use App\Imports\Helper\CategoryHelper;
use App\Logger\ImportsLogger;
use App\Product\Codes\ProductCodesChangedEvent;
use Buxus\Core\Constants;
use Buxus\Error\ErrorReporter;
use Buxus\Page\PageInterface;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;
use BuxusEvent;
use Carbon\Carbon;
use Ciselniky;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use PageTypesConstantsIDs;
use ProductFactory;

class MotorserviceImportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $item;
    protected $importId;

    protected $last;

    /**
     * @var \Buxus\Logger\Logger
     */
    protected $logger;

    public function __construct($item, $importId, $last = false)
    {
        $this->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));

        $this->item = $item;
        $this->importId = $importId;
        $this->last = $last;
    }

    public function handle()
    {
        $this->logger = (new ImportsLogger)->getLogger();

        try {
            $item = $this->item;

            $pairing = new Imports\Pairing\MotorservicePairingManager($item);
            $page = $pairing->getPages($item['ms_motor_cislo']);

            if ($page instanceof PageInterface) {
                $this->updatePage($page, $item);
            } else {
                $this->createPage($item);
            }

            if ($this->last) {
                $this->setImportToDone();
            }
        } catch (\Exception $e) {
            ErrorReporter::reportSilent($e);
            Imports::find($this->importId)->increment('errors');
        }
    }

    protected function getProductData($item)
    {
        return [
            PropertyTag::MOTORSERVICE_SUPPLIER_CODE_TAG() => trim($item['ms_motor_cislo']),
            PropertyTag::MOTORSERVICE_PRICE_WITHOUT_VAT_TAG() => trim($item['cena']),
            PropertyTag::ESHOP_ROLLER_PRODUCER_TAG() => Ciselniky::get('product_catalog.producer')->getValueByName(trim($item['znacka']))->getId(),
            PropertyTag::MOTORSERVICE_EAN_TAG() => trim($item['ean']),
            PropertyTag::SUPPLIER_TAG() => PageIds::getMotorserviceSupplier(),
            PropertyTag::MOTORSERVICE_LATEST_IMPORT_TAG() => Carbon::now()->toDateTimeString(),
            'parent_page_id' => PageIds::getNezaradene(),
        ];
    }

    protected function getProductUpdateData($item)
    {
        return [
            PropertyTag::MOTORSERVICE_SUPPLIER_CODE_TAG() => trim($item['ms_motor_cislo']),
            PropertyTag::MOTORSERVICE_PRICE_WITHOUT_VAT_TAG() => trim($item['cena']),
            PropertyTag::ESHOP_ROLLER_PRODUCER_TAG() => Ciselniky::get('product_catalog.producer')->getValueByName(trim($item['znacka']))->getId(),
            PropertyTag::MOTORSERVICE_EAN_TAG() => trim($item['ean']),
            PropertyTag::SUPPLIER_TAG() => PageIds::getMotorserviceSupplier(),
            PropertyTag::MOTORSERVICE_LATEST_IMPORT_TAG() => Carbon::now()->toDateTimeString(),
        ];
    }

    protected function createPage($item)
    {
        $productData = $this->getProductData($item);

        $page = \PageFactory::create($productData['parent_page_id'], PageTypesConstantsIDs::ESHOP_PRODUCT_ID());
        $page->setPageName($productData['name']);

        $mutex = new \malkusch\lock\mutex\FlockMutex(fopen(__FILE__, 'rb'));
        $mutex->synchronized(function () use ($page) {
            $page->save(false);
        });

        $product = ProductFactory::get($page->getPageId());
        $product->fillPropertiesAndSave($productData);

        GroupedProducts::processForCode($productData[PropertyTag::MOTORSERVICE_SUPPLIER_CODE_TAG()]);
        $event = new ProductCodesChangedEvent($page);
        BuxusEvent::fire($event);

        $this->logger->info("[MOTORSERVICE] Created page with ID: {$page->getPageId()}, price {$productData[PropertyTag::MOTORSERVICE_PRICE_WITHOUT_VAT_TAG()]}, stock {$productData[PropertyTag::MOTORSERVICE_STOCK_BALANCE_TAG()]}");
        Imports::find($this->importId)->increment('creates_processed');
    }

    protected function updatePage(PageInterface $page, $item)
    {
        $productData = $this->getProductUpdateData($item);

        $product = ProductFactory::get($page->getPageId());
        $product->fillPropertiesAndSave($productData);

        $event = new ProductCodesChangedEvent($page);
        BuxusEvent::fire($event);

        $this->logger->info("[MOTORSERVICE] Updated page with ID: {$page->getPageId()}, price {$productData[PropertyTag::MOTORSERVICE_PRICE_WITHOUT_VAT_TAG()]}, stock {$productData[PropertyTag::MOTORSERVICE_STOCK_BALANCE_TAG()]}");
        Imports::find($this->importId)->increment('updates_processed');
    }

    protected function setImportToDone()
    {
        $import = Imports::find($this->importId);
        $import->status = Imports::DONE;
        $import->save();
    }
}
