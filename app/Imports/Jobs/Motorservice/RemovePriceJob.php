<?php

namespace App\Imports\Jobs\Motorservice;

use App\Http\Livewire\MotorserviceImport;
use App\Imports;
use App\Imports\Pairing\MotorservicePairingManager;
use App\Logger\ImportsLogger;
use Buxus\Ciselniky\ValueInterface;
use Buxus\Error\ErrorReporter;
use Buxus\Page\PageInterface;
use Buxus\Util\PropertyTag;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RemovePriceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $supplier;
    protected $code;
    protected $propertyTag;
    protected $import_id;
    /**
     * @var \Buxus\Logger\Logger
     */
    protected $logger;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($code, $import_id)
    {
        $this->code = $code;

        $this->propertyTag = config('imports.' . MotorserviceImport::CONFIG_KEY . '.price_property_tag');
        $this->import_id = $import_id;

        $this->onQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->logger = (new ImportsLogger)->getLogger();

        try {
            $manager = new MotorservicePairingManager();
            $page = $manager->getPages($this->code);

            $price = $page->getValue($this->propertyTag);

            if ($page instanceof PageInterface) {
                $page->setValue($this->propertyTag, null);
                $mutex = new \malkusch\lock\mutex\FlockMutex(fopen(__FILE__, 'rb'));
                $mutex->synchronized(function () use ($page) {
                    $page->save(false);
                });

                $this->logger->info("\n[" . self::class . "] Removing product price: {$this->code}, page ID: {$page->getPageId()}, from price: {$price}");

                Imports::find($this->import_id)->increment('deletes_processed');
            }
        } catch (\Throwable $e) {
            ErrorReporter::reportSilent($e);
            Imports::find($this->import_id)->increment('errors');
        }
    }
}
