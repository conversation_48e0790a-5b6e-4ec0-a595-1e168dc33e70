<?php

namespace App\Imports\Jobs\SpecialTurbo;

use App\Imports;
use App\Imports\Helper\CategoryHelper;
use App\Imports\Jobs\AbstractImportJob;
use App\Imports\Pairing\SpecialTurboPairingManager;
use App\Logger\ImportsLogger;
use Buxus\Core\Constants;
use Buxus\Error\ErrorReporter;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;
use Carbon\Carbon;

class SpecialTurboImportJob extends AbstractImportJob
{
    protected string $pairingManagerClass = SpecialTurboPairingManager::class;

    protected function getPropertyValueChangesForUpdate(): array
    {
        $properties = [];

        if ($this->shouldHaveDeposit()) {
            $properties[PropertyTag::IS_REFURBISHED_PART_TAG()] = Constants::C_True_Char;
            $properties[PropertyTag::ESHOP_EUR_DEPOSIT_AMOUNT_WITHOUT_VAT_TAG()] = $this->getDepositValue();
            $properties[PropertyTag::ONIX_DEPOSIT_CODE_TAG()] = '013024';
        } else {
            $properties[PropertyTag::IS_REFURBISHED_PART_TAG()] = Constants::C_False_Char;
            $properties[PropertyTag::ESHOP_EUR_DEPOSIT_AMOUNT_WITHOUT_VAT_TAG()] = null;
            $properties[PropertyTag::ONIX_DEPOSIT_CODE_TAG()] = null;
        }

        return array_merge($properties, [
            PropertyTag::ALTERNATIVE_PRICE_WITHOUT_VAT_TAG() => null,
            PropertyTag::ALTERNATIVE_PRICES_CODE_TAG() => null,
            PropertyTag::ALTERNATIVE_PRICES_MAIN_OE_CODE_TAG() => null,
            PropertyTag::ALTERNATIVE_PRICES_OE_CODES_TAG() => null,
            PropertyTag::TITLE_TAG() => $this->supplierCode . ' ' . $this->title . ' ' . $this->additionalProperties['nazov_2'] . ' ' . $this->additionalProperties['nazov_3'],
            PropertyTag::ESHOP_ROLLER_PRODUCER_TAG() => $this->getProducerId(),
            PropertyTag::SUPPLIER_TAG() => PageIds::getSpecialTurboSupplier(),
            PropertyTag::SPECIAL_TURBO_SUPPLIER_CODE_TAG() => $this->supplierCode,
            PropertyTag::SPECIAL_TURBO_STOCK_CODE_TAG() => $this->getStockCode(),
            PropertyTag::SPECIAL_TURBO_PRICE_WITHOUT_VAT_TAG() => $this->price,
            PropertyTag::SPECIAL_TURBO_LATEST_IMPORT_TAG() => Carbon::now()->toDateTimeString(),
        ]);
    }

    protected function getPropertyValueChangesForCreate(): array
    {
        $properties = [];

        if ($this->shouldHaveDeposit()) {
            $properties[PropertyTag::IS_REFURBISHED_PART_TAG()] = Constants::C_True_Char;
            $properties[PropertyTag::ESHOP_EUR_DEPOSIT_AMOUNT_WITHOUT_VAT_TAG()] = $this->getDepositValue();
        } else {
            $properties[PropertyTag::IS_REFURBISHED_PART_TAG()] = Constants::C_False_Char;
            $properties[PropertyTag::ESHOP_EUR_DEPOSIT_AMOUNT_WITHOUT_VAT_TAG()] = null;
        }

        return array_merge($properties, [
            PropertyTag::TITLE_TAG() => $this->supplierCode . ' ' . $this->title . ' ' . $this->additionalProperties['nazov_2'] . ' ' . $this->additionalProperties['nazov_3'],
            PropertyTag::ESHOP_ROLLER_PRODUCER_TAG() => $this->getProducerId(),
            PropertyTag::SUPPLIER_TAG() => PageIds::getSpecialTurboSupplier(),
            PropertyTag::SPECIAL_TURBO_SUPPLIER_CODE_TAG() => $this->supplierCode,
            PropertyTag::SPECIAL_TURBO_STOCK_CODE_TAG() => $this->getStockCode(),
            PropertyTag::SPECIAL_TURBO_PRICE_WITHOUT_VAT_TAG() => $this->price,
            PropertyTag::SPECIAL_TURBO_LATEST_IMPORT_TAG() => Carbon::now()->toDateTimeString(),
        ]);
    }

    public function handle()
    {
        $this->logger = (new ImportsLogger)->getLogger();

        try {
            /** @var SpecialTurboPairingManager $manager */
            $manager = new $this->pairingManagerClass();
            $manager->setStockCode($this->getStockCode());
            $page = $manager->getPages($this->getStockCode());

            if (!empty($page)) {
                $this->updatePage($page);

                if ($this->last) {
                    $this->setImportToDone();
                }

                return;
            }
        } catch (\Exception $e) {
            if ($this->importId) {
                Imports::find($this->importId)->increment('errors');
            }
            ErrorReporter::reportSilent($e);
            $this->logger->error($e->getMessage());
        }

        parent::handle();
    }

    protected function getParentPageIdForCreate(): ?int
    {
        return PageIds::getNezaradene();
    }

    protected function getProducerId(): int
    {
        $producer = $this->additionalProperties['vyrobca'];

        $producer = trim($producer);
        return \Ciselniky::get('product_catalog.producer')->getValueByName($producer)->getId();
    }

    protected function shouldHaveDeposit(): bool
    {
        return (float)$this->additionalProperties['vratna_zaloha'] > 0;
    }

    protected function getDepositValue(): float
    {
        return (float)$this->additionalProperties['vratna_zaloha'];
    }

    protected function getStockCode(): ?string
    {
        return $this->additionalProperties['sklad'];
    }
}
