<?php

namespace App\Imports\Components;

use App\Imports;
use Buxus\Livewire\Component;

abstract class AbstractImportIndexComponent extends Component
{
    protected Imports $manager;
    protected array $imports;
    protected ?string $importsFunction;

    public function __construct($id = null)
    {
        parent::__construct($id);

        $this->manager = new Imports();

        if (isset($this->importsFunction)) {
            $this->imports = $this->manager->{$this->importsFunction}();
        }
    }

    public function render()
    {
        return view('imports.default-import-index', [
            'imports' => $this->imports
        ]);
    }
}