<?php

namespace App\Imports\Components;

use Illuminate\Http\UploadedFile;
use Livewire\TemporaryUploadedFile;

abstract class AbstractImportWithCrossfileComponent extends AbstractImportComponent
{
    /**
     * @var TemporaryUploadedFile|UploadedFile|null|string
     */
    public $crossfile;
    protected string $crossfileProcessorClass;
    protected bool $shouldQueueCrossfile = false;


    protected ?array $rules = [
        'file' => 'required_without:crossfile',
        'crossfile' => 'required_without:file'
    ];

    public function create($delayed = false)
    {
        parent::create($delayed);

        if ($this->crossfile) {
            $name = $this->crossfile->getClientOriginalName();
            $extension = pathinfo($name, PATHINFO_EXTENSION);

            $crossfilePath = \Storage::disk('local')->putFileAs(dirname($this->fileLocation), $this->crossfile, 'crossfile.' . $extension);

            $processor = new $this->crossfileProcessorClass($delayed);

            if ($this->shouldQueueCrossfile) {
                $processor->queue($crossfilePath)->allOnQueue('buxus_iveco_big_db_import_' . env('DB_DATABASE'));
            } else {
                $processor->import($crossfilePath);
            }
        }
    }

    public function render()
    {
        return view('imports.default-import-with-crossfile');
    }
}