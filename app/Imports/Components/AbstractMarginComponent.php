<?php

namespace App\Imports\Components;

use App\MarginLevel;
use App\Supplier;
use Buxus\Livewire\Component;

abstract class AbstractMarginComponent extends Component
{
    public $price_from;
    public $margin;
    public $margin_eu;
    public $producer;
    protected $type;

    protected $rules = [
        'price_from' => 'required|numeric|min:0',
        'margin' => 'required|numeric|min:0',
        'margin_eu' => 'required|numeric|min:0',
    ];

    public function render()
    {
        return view('margin.default-margin');
    }

    public function create()
    {
        $this->validate();

        MarginLevel::create([
            'price_from' => $this->price_from,
            'margin' => $this->margin,
            'margin_eu' => $this->margin_eu,
            'producer_id' => $this->producer->id,
            'type' => $this->type,
        ]);

        session()->flash('success', 'Cenová hladina pridaná úspešne.');

        $this->emit('marginLevelEdited');
    }

    public function changeStatus()
    {
        $this->producer->price_levels_on = !$this->producer->price_levels_on;
        $this->producer->save();

        if ($this->producer->price_levels_on) {
            session()->flash('success', 'Cenové hladiny boli zapnuté úspešne.');
        } else {
            session()->flash('success', 'Cenové hladiny boli vypnuté úspešne.');
        }
    }
}