<?php

namespace App\Imports\Pairing;

use Buxus\Page\PageInterface;
use Illuminate\Support\Collection;

abstract class BasePairingManager
{
    public const METHOD_GET_FIRST = 'first';
    public const METHOD_GET_ALL = 'get';

    protected string $method;

    public function __construct($method = self::METHOD_GET_FIRST)
    {
        if (in_array($method, [self::METHOD_GET_FIRST, self::METHOD_GET_ALL])) {
            $this->method = $method;
        } else {
            $this->method = self::METHOD_GET_FIRST;
        }
    }

    /**
     * @param string $code
     * @return Collection|PageInterface|PageInterface[]|null
     */
    public function getPages(string $code)
    {
        $pages = collect([]);

        foreach ($this->getPropertiesForPairing() as $property) {
            $page = \PageFactory::builder()
                ->wherePropertyValue($property, $code)
                ->{$this->method}();

            if ($page instanceof PageInterface) {
                $pages->push($page);
            } else {
                $pages = $pages->merge($page);
            }

            $pages = $pages->filter(function ($page) {
                return $page instanceof PageInterface;
            });

            if ($this->method == self::METHOD_GET_FIRST && !empty($pages)) {
                if ($pages instanceof PageInterface) {
                    return $pages;
                }

                return $pages->first();
            }
        }

        return $pages;
    }


    abstract public function getPropertiesForPairing(): ?array;

    /**
     * @return string
     */
    public function getMethod(): string
    {
        return $this->method;
    }

    /**
     * @param string $method
     */
    public function setMethod(string $method): void
    {
        $this->method = $method;
    }


}
