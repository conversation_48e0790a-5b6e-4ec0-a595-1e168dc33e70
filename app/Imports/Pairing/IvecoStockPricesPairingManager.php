<?php

namespace App\Imports\Pairing;

use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;

class IvecoStockPricesPairingManager extends BasePairingManager
{
    public function getPropertiesForPairing(): ?array
    {
        return [
            PropertyTag::ONIX_MAIN_CODE_TAG(),
            PropertyTag::IVECO_BIG_DB_IMPORT_CODE_TAG(),
        ];
    }

    public function getPages(string $code)
    {
        $pages = parent::getPages($code);

        return $pages->filter(function ($page) {
            return $page->getValue(PropertyTag::ESHOP_ROLLER_PRODUCER_TAG()) == PageIds::getProducerIvecoOriginal();
        });
    }
}
