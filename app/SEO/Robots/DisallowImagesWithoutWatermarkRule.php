<?php

namespace App\SEO\Robots;

use Buxus\SEO\Robots\RobotsTxtRuleCollection;
use Buxus\SEO\Robots\RobotsTxtRulesInterface;
use Buxus\SEO\Robots\RobotsTxtRulesProviderInterface;

class DisallowImagesWithoutWatermarkRule implements RobotsTxtRulesProviderInterface
{
    /**
     * @inheritDoc
     */
    public function getRules(): RobotsTxtRulesInterface
    {
        $collection = new RobotsTxtRuleCollection();
        $collection->userAgent('Googlebot-Image');
        $collection->disallow('/buxus/images/*');
        $collection->allow('/buxus/images/cache/*');

        return $collection;
    }
}