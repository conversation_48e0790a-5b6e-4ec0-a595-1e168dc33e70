<?php

namespace App\SEO;

use Buxus\Page\PageInterface;
use Buxus\SEO\AlternateLinks\AlternateLink;
use Buxus\SEO\AlternateLinks\AlternateLinksGenerator;
use Buxus\Util\Url;
use Illuminate\Http\Request;

class RinopartsAlternateLinksGenerator extends AlternateLinksGenerator
{
    protected function getAlternateLinks(PageInterface $page, Request $request): array
    {
        $links = [];
        $urlParams = http_build_query($request->query->all());

        foreach (\BuxusSite::getActiveSites() as $site) {
            \BuxusSite::executeInSiteContext($site, function () use ($page, $urlParams, &$links, $site) {

                if (!$this->isPageAllowedForCurrentDomain($page)) {
                    return;
                }

                $links[] = new AlternateLink(
                    Url::staticUrl(
                        $page->getUrl($urlParams)
                    ),
                    \Trans::getISO639_1_Code()
                );
            });
        }

        return $links;
    }
}
