<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DeliveryAddress extends Model
{
    use HasFactory;

    protected $table = 'tblDeliveryAddresses';

    protected $fillable = [
        'fullname',
        'webuser_id',
        'company_name',
        'delivery_phone',
        'street',
        'city',
        'zip',
        'country'
    ];

    public function isValid()
    {
        if (empty($this->fullname) && empty($this->company_name)) {
            return false;
        }

        if (empty($this->street)) {
            return false;
        }

        if (empty($this->city)) {
            return false;
        }

        if (empty($this->zip)) {
            return false;
        }

        return true;
    }
}
