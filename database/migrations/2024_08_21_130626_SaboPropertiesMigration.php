<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2024-08-21 13:06:26
 * Property generator: property=sabo_supplier_code,sabo_price_without_vat,sabo_stock_balance,sabo_oe_number,sabo_oe_numbers,sabo_latest_import
 */
class SaboPropertiesMigration extends AbstractMigration
{
    public function up()
    {
        // property: Sabo - Dodávateľský kód(sabo_supplier_code)
        $propertySaboSupplierCode = $this->propertyManager()->propertyExistsByTag('sabo_supplier_code');
        if ($propertySaboSupplierCode === false) {
            $propertySaboSupplierCode = new Property();
            $propertySaboSupplierCode->setTag('sabo_supplier_code');
            $propertySaboSupplierCode->setDescription('');
            $propertySaboSupplierCode->setExtendedDescription('');
            $propertySaboSupplierCode->setName('Sabo - Dodávateľský kód');
            $propertySaboSupplierCode->setClassId(4);
            $propertySaboSupplierCode->setShowType(null);
            $propertySaboSupplierCode->setShowTypeTag('text');
            $propertySaboSupplierCode->setValueType('oneline_text');
            $propertySaboSupplierCode->setDefaultValue('');
            $propertySaboSupplierCode->setMultiOperations(false);
            $propertySaboSupplierCode->setInputString('');
            $propertySaboSupplierCode->setAttribute('tab', 'Sabo');
            $propertySaboSupplierCode->setAttribute('size', '60');
            $propertySaboSupplierCode->setAttribute('maxlength', '');
            $propertySaboSupplierCode->setAttribute('readonly', 'F');
            $propertySaboSupplierCode->setAttribute('pattern', '');
            $propertySaboSupplierCode->setAttribute('inherit_value', 'F');
            $propertySaboSupplierCode->setAttribute('onchange-js', '');
            $propertySaboSupplierCode->setAttribute('onkeyup-js', '');
            $propertySaboSupplierCode->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertySaboSupplierCode);
        } else {
            $this->writeLine('Property with tag sabo_supplier_code already exists');
            $this->setDataKey('property_sabo_supplier_code_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('sabo_supplier_code', 'eshop_product', false);
        }

        // property: Sabo - Cena bez DPH(sabo_price_without_vat)
        $propertySaboPriceWithoutVat = $this->propertyManager()->propertyExistsByTag('sabo_price_without_vat');
        if ($propertySaboPriceWithoutVat === false) {
            $propertySaboPriceWithoutVat = new Property();
            $propertySaboPriceWithoutVat->setTag('sabo_price_without_vat');
            $propertySaboPriceWithoutVat->setDescription('');
            $propertySaboPriceWithoutVat->setExtendedDescription('');
            $propertySaboPriceWithoutVat->setName('Sabo - Cena bez DPH');
            $propertySaboPriceWithoutVat->setClassId(4);
            $propertySaboPriceWithoutVat->setShowType(null);
            $propertySaboPriceWithoutVat->setShowTypeTag('text');
            $propertySaboPriceWithoutVat->setValueType('oneline_text');
            $propertySaboPriceWithoutVat->setDefaultValue('');
            $propertySaboPriceWithoutVat->setMultiOperations(false);
            $propertySaboPriceWithoutVat->setInputString('');
            $propertySaboPriceWithoutVat->setAttribute('tab', 'Sabo');
            $propertySaboPriceWithoutVat->setAttribute('size', '60');
            $propertySaboPriceWithoutVat->setAttribute('maxlength', '');
            $propertySaboPriceWithoutVat->setAttribute('readonly', 'F');
            $propertySaboPriceWithoutVat->setAttribute('pattern', '');
            $propertySaboPriceWithoutVat->setAttribute('inherit_value', 'F');
            $propertySaboPriceWithoutVat->setAttribute('onchange-js', '');
            $propertySaboPriceWithoutVat->setAttribute('onkeyup-js', '');
            $propertySaboPriceWithoutVat->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertySaboPriceWithoutVat);
        } else {
            $this->writeLine('Property with tag sabo_price_without_vat already exists');
            $this->setDataKey('property_sabo_price_without_vat_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('sabo_price_without_vat', 'eshop_product', false);
        }

        // property: Sabo - Skladová zásoba(sabo_stock_balance)
        $propertySaboStockBalance = $this->propertyManager()->propertyExistsByTag('sabo_stock_balance');
        if ($propertySaboStockBalance === false) {
            $propertySaboStockBalance = new Property();
            $propertySaboStockBalance->setTag('sabo_stock_balance');
            $propertySaboStockBalance->setDescription('');
            $propertySaboStockBalance->setExtendedDescription('');
            $propertySaboStockBalance->setName('Sabo - Skladová zásoba');
            $propertySaboStockBalance->setClassId(4);
            $propertySaboStockBalance->setShowType(null);
            $propertySaboStockBalance->setShowTypeTag('text');
            $propertySaboStockBalance->setValueType('oneline_text');
            $propertySaboStockBalance->setDefaultValue('');
            $propertySaboStockBalance->setMultiOperations(false);
            $propertySaboStockBalance->setInputString('');
            $propertySaboStockBalance->setAttribute('tab', 'Sabo');
            $propertySaboStockBalance->setAttribute('size', '60');
            $propertySaboStockBalance->setAttribute('maxlength', '');
            $propertySaboStockBalance->setAttribute('readonly', 'F');
            $propertySaboStockBalance->setAttribute('pattern', '');
            $propertySaboStockBalance->setAttribute('inherit_value', 'F');
            $propertySaboStockBalance->setAttribute('onchange-js', '');
            $propertySaboStockBalance->setAttribute('onkeyup-js', '');
            $propertySaboStockBalance->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertySaboStockBalance);
        } else {
            $this->writeLine('Property with tag sabo_stock_balance already exists');
            $this->setDataKey('property_sabo_stock_balance_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('sabo_stock_balance', 'eshop_product', false);
        }

        // property: Sabo - OE number(sabo_oe_number)
        $propertySaboOeNumber = $this->propertyManager()->propertyExistsByTag('sabo_oe_number');
        if ($propertySaboOeNumber === false) {
            $propertySaboOeNumber = new Property();
            $propertySaboOeNumber->setTag('sabo_oe_number');
            $propertySaboOeNumber->setDescription('');
            $propertySaboOeNumber->setExtendedDescription('');
            $propertySaboOeNumber->setName('Sabo - OE number');
            $propertySaboOeNumber->setClassId(4);
            $propertySaboOeNumber->setShowType(null);
            $propertySaboOeNumber->setShowTypeTag('text');
            $propertySaboOeNumber->setValueType('oneline_text');
            $propertySaboOeNumber->setDefaultValue('');
            $propertySaboOeNumber->setMultiOperations(false);
            $propertySaboOeNumber->setInputString('');
            $propertySaboOeNumber->setAttribute('tab', 'Sabo');
            $propertySaboOeNumber->setAttribute('size', '60');
            $propertySaboOeNumber->setAttribute('maxlength', '');
            $propertySaboOeNumber->setAttribute('readonly', 'F');
            $propertySaboOeNumber->setAttribute('pattern', '');
            $propertySaboOeNumber->setAttribute('inherit_value', 'F');
            $propertySaboOeNumber->setAttribute('onchange-js', '');
            $propertySaboOeNumber->setAttribute('onkeyup-js', '');
            $propertySaboOeNumber->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertySaboOeNumber);
        } else {
            $this->writeLine('Property with tag sabo_oe_number already exists');
            $this->setDataKey('property_sabo_oe_number_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('sabo_oe_number', 'eshop_product', false);
        }

        // property: Sabo - OE numbers(sabo_oe_numbers)
        $propertySaboOeNumbers = $this->propertyManager()->propertyExistsByTag('sabo_oe_numbers');
        if ($propertySaboOeNumbers === false) {
            $propertySaboOeNumbers = new Property();
            $propertySaboOeNumbers->setTag('sabo_oe_numbers');
            $propertySaboOeNumbers->setDescription('');
            $propertySaboOeNumbers->setExtendedDescription('');
            $propertySaboOeNumbers->setName('Sabo - OE numbers');
            $propertySaboOeNumbers->setClassId(4);
            $propertySaboOeNumbers->setShowType(null);
            $propertySaboOeNumbers->setShowTypeTag('text');
            $propertySaboOeNumbers->setValueType('oneline_text');
            $propertySaboOeNumbers->setDefaultValue('');
            $propertySaboOeNumbers->setMultiOperations(false);
            $propertySaboOeNumbers->setInputString('');
            $propertySaboOeNumbers->setAttribute('tab', 'Sabo');
            $propertySaboOeNumbers->setAttribute('size', '60');
            $propertySaboOeNumbers->setAttribute('maxlength', '');
            $propertySaboOeNumbers->setAttribute('readonly', 'F');
            $propertySaboOeNumbers->setAttribute('pattern', '');
            $propertySaboOeNumbers->setAttribute('inherit_value', 'F');
            $propertySaboOeNumbers->setAttribute('onchange-js', '');
            $propertySaboOeNumbers->setAttribute('onkeyup-js', '');
            $propertySaboOeNumbers->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertySaboOeNumbers);
        } else {
            $this->writeLine('Property with tag sabo_oe_numbers already exists');
            $this->setDataKey('property_sabo_oe_numbers_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('sabo_oe_numbers', 'eshop_product', false);
        }

        // property: Sabo - Posledný import(sabo_latest_import)
        $propertySaboLatestImport = $this->propertyManager()->propertyExistsByTag('sabo_latest_import');
        if ($propertySaboLatestImport === false) {
            $propertySaboLatestImport = new Property();
            $propertySaboLatestImport->setTag('sabo_latest_import');
            $propertySaboLatestImport->setDescription('');
            $propertySaboLatestImport->setExtendedDescription('');
            $propertySaboLatestImport->setName('Sabo - Posledný import');
            $propertySaboLatestImport->setClassId(4);
            $propertySaboLatestImport->setShowType(null);
            $propertySaboLatestImport->setShowTypeTag('text');
            $propertySaboLatestImport->setValueType('oneline_text');
            $propertySaboLatestImport->setDefaultValue('');
            $propertySaboLatestImport->setMultiOperations(false);
            $propertySaboLatestImport->setInputString('');
            $propertySaboLatestImport->setAttribute('tab', 'Sabo');
            $propertySaboLatestImport->setAttribute('size', '60');
            $propertySaboLatestImport->setAttribute('maxlength', '');
            $propertySaboLatestImport->setAttribute('readonly', 'F');
            $propertySaboLatestImport->setAttribute('pattern', '');
            $propertySaboLatestImport->setAttribute('inherit_value', 'F');
            $propertySaboLatestImport->setAttribute('onchange-js', '');
            $propertySaboLatestImport->setAttribute('onkeyup-js', '');
            $propertySaboLatestImport->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertySaboLatestImport);
        } else {
            $this->writeLine('Property with tag sabo_latest_import already exists');
            $this->setDataKey('property_sabo_latest_import_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('sabo_latest_import', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Sabo - Posledný import(sabo_latest_import)
        $propertySaboLatestImport = $this->propertyManager()->propertyExistsByTag('sabo_latest_import');
        if (($propertySaboLatestImport !== false) && ($this->getDataKey('property_sabo_latest_import_existed') === null)) {
            $this->propertyManager()->removeProperty($propertySaboLatestImport);
        }

        // remove property: Sabo - OE numbers(sabo_oe_numbers)
        $propertySaboOeNumbers = $this->propertyManager()->propertyExistsByTag('sabo_oe_numbers');
        if (($propertySaboOeNumbers !== false) && ($this->getDataKey('property_sabo_oe_numbers_existed') === null)) {
            $this->propertyManager()->removeProperty($propertySaboOeNumbers);
        }

        // remove property: Sabo - OE number(sabo_oe_number)
        $propertySaboOeNumber = $this->propertyManager()->propertyExistsByTag('sabo_oe_number');
        if (($propertySaboOeNumber !== false) && ($this->getDataKey('property_sabo_oe_number_existed') === null)) {
            $this->propertyManager()->removeProperty($propertySaboOeNumber);
        }

        // remove property: Sabo - Skladová zásoba(sabo_stock_balance)
        $propertySaboStockBalance = $this->propertyManager()->propertyExistsByTag('sabo_stock_balance');
        if (($propertySaboStockBalance !== false) && ($this->getDataKey('property_sabo_stock_balance_existed') === null)) {
            $this->propertyManager()->removeProperty($propertySaboStockBalance);
        }

        // remove property: Sabo - Cena bez DPH(sabo_price_without_vat)
        $propertySaboPriceWithoutVat = $this->propertyManager()->propertyExistsByTag('sabo_price_without_vat');
        if (($propertySaboPriceWithoutVat !== false) && ($this->getDataKey('property_sabo_price_without_vat_existed') === null)) {
            $this->propertyManager()->removeProperty($propertySaboPriceWithoutVat);
        }

        // remove property: Sabo - Dodávateľský kód(sabo_supplier_code)
        $propertySaboSupplierCode = $this->propertyManager()->propertyExistsByTag('sabo_supplier_code');
        if (($propertySaboSupplierCode !== false) && ($this->getDataKey('property_sabo_supplier_code_existed') === null)) {
            $this->propertyManager()->removeProperty($propertySaboSupplierCode);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
