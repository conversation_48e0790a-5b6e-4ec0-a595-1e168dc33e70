<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2022-02-10 11:42:18
 * Property generator: property=promo_btn_text_cz,promo_btn_text_en,promo_text_cz,promo_text_en
 */
class PromoboxLanguagePropertiesMigration extends AbstractMigration
{
    public function up()
    {
        // property: Text tlačidla promobox [CZ](promo_btn_text_cz)
        $propertyPromoBtnTextCz = $this->propertyManager()->propertyExistsByTag('promo_btn_text_cz');
        if ($propertyPromoBtnTextCz === false) {
            $propertyPromoBtnTextCz = new Property();
            $propertyPromoBtnTextCz->setTag('promo_btn_text_cz');
            $propertyPromoBtnTextCz->setDescription('Text, ktorý sa zobrazí v tlačidle v promoboxe. Doporučuje sa používať max. 15 znakov pre správna zobrazenie na rôznych zariadeniach.');
            $propertyPromoBtnTextCz->setExtendedDescription('');
            $propertyPromoBtnTextCz->setName('Text tlačidla promobox [CZ]');
            $propertyPromoBtnTextCz->setClassId(4);
            $propertyPromoBtnTextCz->setShowType(null);
            $propertyPromoBtnTextCz->setShowTypeTag('text');
            $propertyPromoBtnTextCz->setValueType('oneline_text');
            $propertyPromoBtnTextCz->setDefaultValue('');
            $propertyPromoBtnTextCz->setMultiOperations(false);
            $propertyPromoBtnTextCz->setInputString('');
            $propertyPromoBtnTextCz->setAttribute('tab', 'CZ');
            $propertyPromoBtnTextCz->setAttribute('size', '60');
            $propertyPromoBtnTextCz->setAttribute('maxlength', '30');
            $propertyPromoBtnTextCz->setAttribute('readonly', 'F');
            $propertyPromoBtnTextCz->setAttribute('pattern', '');
            $propertyPromoBtnTextCz->setAttribute('inherit_value', 'F');
            $propertyPromoBtnTextCz->setAttribute('onchange-js', '');
            $propertyPromoBtnTextCz->setAttribute('onkeyup-js', '');
            $propertyPromoBtnTextCz->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyPromoBtnTextCz);
        } else {
            $this->writeLine('Property with tag promo_btn_text_cz already exists');
            $this->setDataKey('property_promo_btn_text_cz_existed', true);
        }
        if ($this->pageTypeExists('layout_element_promobox')) {
            $this->addPropertyToPageType('promo_btn_text_cz', 'layout_element_promobox', false);
        }

        // property: Text tlačidla promobox [EN](promo_btn_text_en)
        $propertyPromoBtnTextEn = $this->propertyManager()->propertyExistsByTag('promo_btn_text_en');
        if ($propertyPromoBtnTextEn === false) {
            $propertyPromoBtnTextEn = new Property();
            $propertyPromoBtnTextEn->setTag('promo_btn_text_en');
            $propertyPromoBtnTextEn->setDescription('Text, ktorý sa zobrazí v tlačidle v promoboxe. Doporučuje sa používať max. 15 znakov pre správna zobrazenie na rôznych zariadeniach.');
            $propertyPromoBtnTextEn->setExtendedDescription('');
            $propertyPromoBtnTextEn->setName('Text tlačidla promobox [EN]');
            $propertyPromoBtnTextEn->setClassId(4);
            $propertyPromoBtnTextEn->setShowType(null);
            $propertyPromoBtnTextEn->setShowTypeTag('text');
            $propertyPromoBtnTextEn->setValueType('oneline_text');
            $propertyPromoBtnTextEn->setDefaultValue('');
            $propertyPromoBtnTextEn->setMultiOperations(false);
            $propertyPromoBtnTextEn->setInputString('');
            $propertyPromoBtnTextEn->setAttribute('tab', 'EN');
            $propertyPromoBtnTextEn->setAttribute('size', '60');
            $propertyPromoBtnTextEn->setAttribute('maxlength', '30');
            $propertyPromoBtnTextEn->setAttribute('readonly', 'F');
            $propertyPromoBtnTextEn->setAttribute('pattern', '');
            $propertyPromoBtnTextEn->setAttribute('inherit_value', 'F');
            $propertyPromoBtnTextEn->setAttribute('onchange-js', '');
            $propertyPromoBtnTextEn->setAttribute('onkeyup-js', '');
            $propertyPromoBtnTextEn->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyPromoBtnTextEn);
        } else {
            $this->writeLine('Property with tag promo_btn_text_en already exists');
            $this->setDataKey('property_promo_btn_text_en_existed', true);
        }
        if ($this->pageTypeExists('layout_element_promobox')) {
            $this->addPropertyToPageType('promo_btn_text_en', 'layout_element_promobox', false);
        }

        // property: Promobox text [CZ](promo_text_cz)
        $propertyPromoTextCz = $this->propertyManager()->propertyExistsByTag('promo_text_cz');
        if ($propertyPromoTextCz === false) {
            $propertyPromoTextCz = new Property();
            $propertyPromoTextCz->setTag('promo_text_cz');
            $propertyPromoTextCz->setDescription('Text, ktorý sa zobrazí v promoboxe.');
            $propertyPromoTextCz->setExtendedDescription('');
            $propertyPromoTextCz->setName('Promobox text [CZ]');
            $propertyPromoTextCz->setClassId(4);
            $propertyPromoTextCz->setShowType(null);
            $propertyPromoTextCz->setShowTypeTag('text');
            $propertyPromoTextCz->setValueType('oneline_text');
            $propertyPromoTextCz->setDefaultValue('');
            $propertyPromoTextCz->setMultiOperations(false);
            $propertyPromoTextCz->setInputString('');
            $propertyPromoTextCz->setAttribute('tab', 'CZ');
            $propertyPromoTextCz->setAttribute('size', '60');
            $propertyPromoTextCz->setAttribute('maxlength', '');
            $propertyPromoTextCz->setAttribute('readonly', 'F');
            $propertyPromoTextCz->setAttribute('pattern', '');
            $propertyPromoTextCz->setAttribute('inherit_value', 'F');
            $propertyPromoTextCz->setAttribute('onchange-js', '');
            $propertyPromoTextCz->setAttribute('onkeyup-js', '');
            $propertyPromoTextCz->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyPromoTextCz);
        } else {
            $this->writeLine('Property with tag promo_text_cz already exists');
            $this->setDataKey('property_promo_text_cz_existed', true);
        }
        if ($this->pageTypeExists('layout_element_promobox')) {
            $this->addPropertyToPageType('promo_text_cz', 'layout_element_promobox', false);
        }

        // property: Promobox text [EN](promo_text_en)
        $propertyPromoTextEn = $this->propertyManager()->propertyExistsByTag('promo_text_en');
        if ($propertyPromoTextEn === false) {
            $propertyPromoTextEn = new Property();
            $propertyPromoTextEn->setTag('promo_text_en');
            $propertyPromoTextEn->setDescription('Text, ktorý sa zobrazí v promoboxe.');
            $propertyPromoTextEn->setExtendedDescription('');
            $propertyPromoTextEn->setName('Promobox text [EN]');
            $propertyPromoTextEn->setClassId(4);
            $propertyPromoTextEn->setShowType(null);
            $propertyPromoTextEn->setShowTypeTag('text');
            $propertyPromoTextEn->setValueType('oneline_text');
            $propertyPromoTextEn->setDefaultValue('');
            $propertyPromoTextEn->setMultiOperations(false);
            $propertyPromoTextEn->setInputString('');
            $propertyPromoTextEn->setAttribute('tab', 'EN');
            $propertyPromoTextEn->setAttribute('size', '60');
            $propertyPromoTextEn->setAttribute('maxlength', '');
            $propertyPromoTextEn->setAttribute('readonly', 'F');
            $propertyPromoTextEn->setAttribute('pattern', '');
            $propertyPromoTextEn->setAttribute('inherit_value', 'F');
            $propertyPromoTextEn->setAttribute('onchange-js', '');
            $propertyPromoTextEn->setAttribute('onkeyup-js', '');
            $propertyPromoTextEn->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyPromoTextEn);
        } else {
            $this->writeLine('Property with tag promo_text_en already exists');
            $this->setDataKey('property_promo_text_en_existed', true);
        }
        if ($this->pageTypeExists('layout_element_promobox')) {
            $this->addPropertyToPageType('promo_text_en', 'layout_element_promobox', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Promobox text [EN](promo_text_en)
        $propertyPromoTextEn = $this->propertyManager()->propertyExistsByTag('promo_text_en');
        if (($propertyPromoTextEn !== false) && ($this->getDataKey('property_promo_text_en_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyPromoTextEn);
        }

        // remove property: Promobox text [CZ](promo_text_cz)
        $propertyPromoTextCz = $this->propertyManager()->propertyExistsByTag('promo_text_cz');
        if (($propertyPromoTextCz !== false) && ($this->getDataKey('property_promo_text_cz_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyPromoTextCz);
        }

        // remove property: Text tlačidla promobox [EN](promo_btn_text_en)
        $propertyPromoBtnTextEn = $this->propertyManager()->propertyExistsByTag('promo_btn_text_en');
        if (($propertyPromoBtnTextEn !== false) && ($this->getDataKey('property_promo_btn_text_en_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyPromoBtnTextEn);
        }

        // remove property: Text tlačidla promobox [CZ](promo_btn_text_cz)
        $propertyPromoBtnTextCz = $this->propertyManager()->propertyExistsByTag('promo_btn_text_cz');
        if (($propertyPromoBtnTextCz !== false) && ($this->getDataKey('property_promo_btn_text_cz_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyPromoBtnTextCz);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
