<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Automatic generation from (buxus_rinoparts_test) at 2022-01-27 13:09:49
 */
class OnixEnclosures extends AbstractMigration
{
    public function up()
    {
        Schema::create('onix_enclosures', function (Blueprint $table) {
            $table->bigIncrements('enclosure_record_id');
            $table->bigInteger('doc_record_id');
            $table->datetime('inserted');
            $table->datetime('onix_date_changed');
            $table->datetime('onix_date_document');
            $table->integer('partner_id');
            $table->integer('enclosure_type_id');
            $table->string('path');
            $table->string('code');
            $table->string('vs');
            $table->float('sum');
            $table->string('curr');
            $table->string('extension');
            $table->string('commission');
        });
    }

    public function down()
    {
        Schema::dropIfExists('onix_enclosures');
    }
}
