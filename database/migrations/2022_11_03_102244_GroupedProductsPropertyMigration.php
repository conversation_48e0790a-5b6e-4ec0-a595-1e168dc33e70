<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2022-11-03 10:22:44
 * Property generator: property=grouped_products
 */
class GroupedProductsPropertyMigration extends AbstractMigration
{
    public function up()
    {
        // property: Produkty skupiny(grouped_products)
        $propertyGroupedProducts = $this->propertyManager()->propertyExistsByTag('grouped_products');
        if ($propertyGroupedProducts === false) {
            $propertyGroupedProducts = new Property();
            $propertyGroupedProducts->setTag('grouped_products');
            $propertyGroupedProducts->setDescription('');
            $propertyGroupedProducts->setExtendedDescription('');
            $propertyGroupedProducts->setName('Produkty skupiny');
            $propertyGroupedProducts->setClassId(5);
            $propertyGroupedProducts->setShowType(null);
            $propertyGroupedProducts->setShowTypeTag('page_list');
            $propertyGroupedProducts->setValueType('page_list');
            $propertyGroupedProducts->setDefaultValue('');
            $propertyGroupedProducts->setMultiOperations(false);
            $propertyGroupedProducts->setInputString('');
            $propertyGroupedProducts->setAttribute('tab', '');
            $propertyGroupedProducts->setAttribute('root_page_id', '');
            $propertyGroupedProducts->setAttribute('page_type_id', '');
            $propertyGroupedProducts->setAttribute('default_sort', 'tblPages.sort_date_time');
            $propertyGroupedProducts->setAttribute('advanced_mode', 'T');
            $propertyGroupedProducts->setAttribute('external_url', 'T');
            $propertyGroupedProducts->setAttribute('max_items', '');
            $propertyGroupedProducts->setAttribute('middle_col_width', '');
            $propertyGroupedProducts->setAttribute('inherit_value', 'F');
            $propertyGroupedProducts->setAttribute('apply_user_rights', 'T');
            $propertyGroupedProducts->setAttribute('property_for_link_name', 'title');
            $propertyGroupedProducts->setAttribute('properties_for_search', 'tblPages.page_name,title');
            $propertyGroupedProducts->setAttribute('parent_page_type_id', '');
            $propertyGroupedProducts->setAttribute('options', []);
            $this->propertyManager()->saveProperty($propertyGroupedProducts);
        } else {
            $this->writeLine('Property with tag grouped_products already exists');
            $this->setDataKey('property_grouped_products_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('grouped_products', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Produkty skupiny(grouped_products)
        $propertyGroupedProducts = $this->propertyManager()->propertyExistsByTag('grouped_products');
        if (($propertyGroupedProducts !== false) && ($this->getDataKey('property_grouped_products_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyGroupedProducts);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
