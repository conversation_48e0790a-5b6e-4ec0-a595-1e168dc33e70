<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2022-11-25 14:08:16
 * Property generator: property=mec_diesel_price_without_vat,mec_diesel_supplier_code,mec_diesel_latest_import,mec_diesel_oe_number,mec_diesel_oe_numbers,mec_diesel_stock_balance_italy,mec_diesel_stock_balance_czech
 */
class MecDieselProperties extends AbstractMigration
{
    public function up()
    {
        // property: Cena bez DPH(mec_diesel_price_without_vat)
        $propertyMecDieselPriceWithoutVat = $this->propertyManager()->propertyExistsByTag('mec_diesel_price_without_vat');
        if ($propertyMecDieselPriceWithoutVat === false) {
            $propertyMecDieselPriceWithoutVat = new Property();
            $propertyMecDieselPriceWithoutVat->setTag('mec_diesel_price_without_vat');
            $propertyMecDieselPriceWithoutVat->setDescription('');
            $propertyMecDieselPriceWithoutVat->setExtendedDescription('');
            $propertyMecDieselPriceWithoutVat->setName('Cena bez DPH');
            $propertyMecDieselPriceWithoutVat->setClassId(4);
            $propertyMecDieselPriceWithoutVat->setShowType(null);
            $propertyMecDieselPriceWithoutVat->setShowTypeTag('text');
            $propertyMecDieselPriceWithoutVat->setValueType('oneline_text');
            $propertyMecDieselPriceWithoutVat->setDefaultValue('');
            $propertyMecDieselPriceWithoutVat->setMultiOperations(false);
            $propertyMecDieselPriceWithoutVat->setInputString('');
            $propertyMecDieselPriceWithoutVat->setAttribute('tab', 'Mec-Diesel');
            $propertyMecDieselPriceWithoutVat->setAttribute('size', '60');
            $propertyMecDieselPriceWithoutVat->setAttribute('maxlength', '');
            $propertyMecDieselPriceWithoutVat->setAttribute('readonly', 'F');
            $propertyMecDieselPriceWithoutVat->setAttribute('pattern', '');
            $propertyMecDieselPriceWithoutVat->setAttribute('inherit_value', 'F');
            $propertyMecDieselPriceWithoutVat->setAttribute('onchange-js', '');
            $propertyMecDieselPriceWithoutVat->setAttribute('onkeyup-js', '');
            $propertyMecDieselPriceWithoutVat->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyMecDieselPriceWithoutVat);
        } else {
            $this->writeLine('Property with tag mec_diesel_price_without_vat already exists');
            $this->setDataKey('property_mec_diesel_price_without_vat_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('mec_diesel_price_without_vat', 'eshop_product', false);
        }
        if ($this->pageTypeExists('base_product')) {
            $this->addPropertyToPageType('mec_diesel_price_without_vat', 'base_product', false);
        }

        // property: Dodávateľský kód(mec_diesel_supplier_code)
        $propertyMecDieselSupplierCode = $this->propertyManager()->propertyExistsByTag('mec_diesel_supplier_code');
        if ($propertyMecDieselSupplierCode === false) {
            $propertyMecDieselSupplierCode = new Property();
            $propertyMecDieselSupplierCode->setTag('mec_diesel_supplier_code');
            $propertyMecDieselSupplierCode->setDescription('');
            $propertyMecDieselSupplierCode->setExtendedDescription('');
            $propertyMecDieselSupplierCode->setName('Dodávateľský kód');
            $propertyMecDieselSupplierCode->setClassId(4);
            $propertyMecDieselSupplierCode->setShowType(null);
            $propertyMecDieselSupplierCode->setShowTypeTag('text');
            $propertyMecDieselSupplierCode->setValueType('oneline_text');
            $propertyMecDieselSupplierCode->setDefaultValue('');
            $propertyMecDieselSupplierCode->setMultiOperations(false);
            $propertyMecDieselSupplierCode->setInputString('');
            $propertyMecDieselSupplierCode->setAttribute('tab', 'Mec-Diesel');
            $propertyMecDieselSupplierCode->setAttribute('size', '60');
            $propertyMecDieselSupplierCode->setAttribute('maxlength', '');
            $propertyMecDieselSupplierCode->setAttribute('readonly', 'F');
            $propertyMecDieselSupplierCode->setAttribute('pattern', '');
            $propertyMecDieselSupplierCode->setAttribute('inherit_value', 'F');
            $propertyMecDieselSupplierCode->setAttribute('onchange-js', '');
            $propertyMecDieselSupplierCode->setAttribute('onkeyup-js', '');
            $propertyMecDieselSupplierCode->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyMecDieselSupplierCode);
        } else {
            $this->writeLine('Property with tag mec_diesel_supplier_code already exists');
            $this->setDataKey('property_mec_diesel_supplier_code_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('mec_diesel_supplier_code', 'eshop_product', false);
        }

        // property: Mec-Diesel - posledný import(mec_diesel_latest_import)
        $propertyMecDieselLatestImport = $this->propertyManager()->propertyExistsByTag('mec_diesel_latest_import');
        if ($propertyMecDieselLatestImport === false) {
            $propertyMecDieselLatestImport = new Property();
            $propertyMecDieselLatestImport->setTag('mec_diesel_latest_import');
            $propertyMecDieselLatestImport->setDescription('');
            $propertyMecDieselLatestImport->setExtendedDescription('');
            $propertyMecDieselLatestImport->setName('Mec-Diesel - posledný import');
            $propertyMecDieselLatestImport->setClassId(4);
            $propertyMecDieselLatestImport->setShowType(null);
            $propertyMecDieselLatestImport->setShowTypeTag('text');
            $propertyMecDieselLatestImport->setValueType('oneline_text');
            $propertyMecDieselLatestImport->setDefaultValue('');
            $propertyMecDieselLatestImport->setMultiOperations(false);
            $propertyMecDieselLatestImport->setInputString('');
            $propertyMecDieselLatestImport->setAttribute('tab', 'Mec-Diesel');
            $propertyMecDieselLatestImport->setAttribute('size', '60');
            $propertyMecDieselLatestImport->setAttribute('maxlength', '');
            $propertyMecDieselLatestImport->setAttribute('readonly', 'F');
            $propertyMecDieselLatestImport->setAttribute('pattern', '');
            $propertyMecDieselLatestImport->setAttribute('inherit_value', 'F');
            $propertyMecDieselLatestImport->setAttribute('onchange-js', '');
            $propertyMecDieselLatestImport->setAttribute('onkeyup-js', '');
            $propertyMecDieselLatestImport->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyMecDieselLatestImport);
        } else {
            $this->writeLine('Property with tag mec_diesel_latest_import already exists');
            $this->setDataKey('property_mec_diesel_latest_import_existed', true);
        }
        if ($this->pageTypeExists('mec_diesel_product')) {
            $this->addPropertyToPageType('mec_diesel_latest_import', 'mec_diesel_product', false);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('mec_diesel_latest_import', 'eshop_product', false);
        }

        // property: OE number(mec_diesel_oe_number)
        $propertyMecDieselOeNumber = $this->propertyManager()->propertyExistsByTag('mec_diesel_oe_number');
        if ($propertyMecDieselOeNumber === false) {
            $propertyMecDieselOeNumber = new Property();
            $propertyMecDieselOeNumber->setTag('mec_diesel_oe_number');
            $propertyMecDieselOeNumber->setDescription('');
            $propertyMecDieselOeNumber->setExtendedDescription('');
            $propertyMecDieselOeNumber->setName('OE number');
            $propertyMecDieselOeNumber->setClassId(4);
            $propertyMecDieselOeNumber->setShowType(null);
            $propertyMecDieselOeNumber->setShowTypeTag('text');
            $propertyMecDieselOeNumber->setValueType('oneline_text');
            $propertyMecDieselOeNumber->setDefaultValue('');
            $propertyMecDieselOeNumber->setMultiOperations(false);
            $propertyMecDieselOeNumber->setInputString('');
            $propertyMecDieselOeNumber->setAttribute('tab', 'Mec-Diesel');
            $propertyMecDieselOeNumber->setAttribute('size', '60');
            $propertyMecDieselOeNumber->setAttribute('maxlength', '');
            $propertyMecDieselOeNumber->setAttribute('readonly', 'F');
            $propertyMecDieselOeNumber->setAttribute('pattern', '');
            $propertyMecDieselOeNumber->setAttribute('inherit_value', 'F');
            $propertyMecDieselOeNumber->setAttribute('onchange-js', '');
            $propertyMecDieselOeNumber->setAttribute('onkeyup-js', '');
            $propertyMecDieselOeNumber->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyMecDieselOeNumber);
        } else {
            $this->writeLine('Property with tag mec_diesel_oe_number already exists');
            $this->setDataKey('property_mec_diesel_oe_number_existed', true);
        }
        if ($this->pageTypeExists('mec_diesel_product')) {
            $this->addPropertyToPageType('mec_diesel_oe_number', 'mec_diesel_product', false);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('mec_diesel_oe_number', 'eshop_product', false);
        }

        // property: OE numbers(mec_diesel_oe_numbers)
        $propertyMecDieselOeNumbers = $this->propertyManager()->propertyExistsByTag('mec_diesel_oe_numbers');
        if ($propertyMecDieselOeNumbers === false) {
            $propertyMecDieselOeNumbers = new Property();
            $propertyMecDieselOeNumbers->setTag('mec_diesel_oe_numbers');
            $propertyMecDieselOeNumbers->setDescription('');
            $propertyMecDieselOeNumbers->setExtendedDescription('');
            $propertyMecDieselOeNumbers->setName('OE numbers');
            $propertyMecDieselOeNumbers->setClassId(4);
            $propertyMecDieselOeNumbers->setShowType(null);
            $propertyMecDieselOeNumbers->setShowTypeTag('text');
            $propertyMecDieselOeNumbers->setValueType('oneline_text');
            $propertyMecDieselOeNumbers->setDefaultValue('');
            $propertyMecDieselOeNumbers->setMultiOperations(false);
            $propertyMecDieselOeNumbers->setInputString('');
            $propertyMecDieselOeNumbers->setAttribute('tab', 'Mec-Diesel');
            $propertyMecDieselOeNumbers->setAttribute('size', '60');
            $propertyMecDieselOeNumbers->setAttribute('maxlength', '');
            $propertyMecDieselOeNumbers->setAttribute('readonly', 'F');
            $propertyMecDieselOeNumbers->setAttribute('pattern', '');
            $propertyMecDieselOeNumbers->setAttribute('inherit_value', 'F');
            $propertyMecDieselOeNumbers->setAttribute('onchange-js', '');
            $propertyMecDieselOeNumbers->setAttribute('onkeyup-js', '');
            $propertyMecDieselOeNumbers->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyMecDieselOeNumbers);
        } else {
            $this->writeLine('Property with tag mec_diesel_oe_numbers already exists');
            $this->setDataKey('property_mec_diesel_oe_numbers_existed', true);
        }
        if ($this->pageTypeExists('mec_diesel_product')) {
            $this->addPropertyToPageType('mec_diesel_oe_numbers', 'mec_diesel_product', false);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('mec_diesel_oe_numbers', 'eshop_product', false);
        }

        // property: Skladová zásoba [Taliansko](mec_diesel_stock_balance_italy)
        $propertyMecDieselStockBalanceItaly = $this->propertyManager()->propertyExistsByTag('mec_diesel_stock_balance_italy');
        if ($propertyMecDieselStockBalanceItaly === false) {
            $propertyMecDieselStockBalanceItaly = new Property();
            $propertyMecDieselStockBalanceItaly->setTag('mec_diesel_stock_balance_italy');
            $propertyMecDieselStockBalanceItaly->setDescription('');
            $propertyMecDieselStockBalanceItaly->setExtendedDescription('');
            $propertyMecDieselStockBalanceItaly->setName('Skladová zásoba [Taliansko]');
            $propertyMecDieselStockBalanceItaly->setClassId(4);
            $propertyMecDieselStockBalanceItaly->setShowType(null);
            $propertyMecDieselStockBalanceItaly->setShowTypeTag('text');
            $propertyMecDieselStockBalanceItaly->setValueType('oneline_text');
            $propertyMecDieselStockBalanceItaly->setDefaultValue('');
            $propertyMecDieselStockBalanceItaly->setMultiOperations(false);
            $propertyMecDieselStockBalanceItaly->setInputString('');
            $propertyMecDieselStockBalanceItaly->setAttribute('tab', 'Mec-Diesel');
            $propertyMecDieselStockBalanceItaly->setAttribute('size', '60');
            $propertyMecDieselStockBalanceItaly->setAttribute('maxlength', '');
            $propertyMecDieselStockBalanceItaly->setAttribute('readonly', 'F');
            $propertyMecDieselStockBalanceItaly->setAttribute('pattern', '');
            $propertyMecDieselStockBalanceItaly->setAttribute('inherit_value', 'F');
            $propertyMecDieselStockBalanceItaly->setAttribute('onchange-js', '');
            $propertyMecDieselStockBalanceItaly->setAttribute('onkeyup-js', '');
            $propertyMecDieselStockBalanceItaly->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyMecDieselStockBalanceItaly);
        } else {
            $this->writeLine('Property with tag mec_diesel_stock_balance_italy already exists');
            $this->setDataKey('property_mec_diesel_stock_balance_italy_existed', true);
        }
        if ($this->pageTypeExists('mec_diesel_product')) {
            $this->addPropertyToPageType('mec_diesel_stock_balance_italy', 'mec_diesel_product', false);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('mec_diesel_stock_balance_italy', 'eshop_product', false);
        }

        // property: Skladová zásoba [Česko](mec_diesel_stock_balance_czech)
        $propertyMecDieselStockBalanceCzech = $this->propertyManager()->propertyExistsByTag('mec_diesel_stock_balance_czech');
        if ($propertyMecDieselStockBalanceCzech === false) {
            $propertyMecDieselStockBalanceCzech = new Property();
            $propertyMecDieselStockBalanceCzech->setTag('mec_diesel_stock_balance_czech');
            $propertyMecDieselStockBalanceCzech->setDescription('');
            $propertyMecDieselStockBalanceCzech->setExtendedDescription('');
            $propertyMecDieselStockBalanceCzech->setName('Skladová zásoba [Česko]');
            $propertyMecDieselStockBalanceCzech->setClassId(4);
            $propertyMecDieselStockBalanceCzech->setShowType(null);
            $propertyMecDieselStockBalanceCzech->setShowTypeTag('text');
            $propertyMecDieselStockBalanceCzech->setValueType('oneline_text');
            $propertyMecDieselStockBalanceCzech->setDefaultValue('');
            $propertyMecDieselStockBalanceCzech->setMultiOperations(false);
            $propertyMecDieselStockBalanceCzech->setInputString('');
            $propertyMecDieselStockBalanceCzech->setAttribute('tab', 'Mec-Diesel');
            $propertyMecDieselStockBalanceCzech->setAttribute('size', '60');
            $propertyMecDieselStockBalanceCzech->setAttribute('maxlength', '');
            $propertyMecDieselStockBalanceCzech->setAttribute('readonly', 'F');
            $propertyMecDieselStockBalanceCzech->setAttribute('pattern', '');
            $propertyMecDieselStockBalanceCzech->setAttribute('inherit_value', 'F');
            $propertyMecDieselStockBalanceCzech->setAttribute('onchange-js', '');
            $propertyMecDieselStockBalanceCzech->setAttribute('onkeyup-js', '');
            $propertyMecDieselStockBalanceCzech->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyMecDieselStockBalanceCzech);
        } else {
            $this->writeLine('Property with tag mec_diesel_stock_balance_czech already exists');
            $this->setDataKey('property_mec_diesel_stock_balance_czech_existed', true);
        }
        if ($this->pageTypeExists('mec_diesel_product')) {
            $this->addPropertyToPageType('mec_diesel_stock_balance_czech', 'mec_diesel_product', false);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('mec_diesel_stock_balance_czech', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Skladová zásoba [Česko](mec_diesel_stock_balance_czech)
        $propertyMecDieselStockBalanceCzech = $this->propertyManager()->propertyExistsByTag('mec_diesel_stock_balance_czech');
        if (($propertyMecDieselStockBalanceCzech !== false) && ($this->getDataKey('property_mec_diesel_stock_balance_czech_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyMecDieselStockBalanceCzech);
        }

        // remove property: Skladová zásoba [Taliansko](mec_diesel_stock_balance_italy)
        $propertyMecDieselStockBalanceItaly = $this->propertyManager()->propertyExistsByTag('mec_diesel_stock_balance_italy');
        if (($propertyMecDieselStockBalanceItaly !== false) && ($this->getDataKey('property_mec_diesel_stock_balance_italy_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyMecDieselStockBalanceItaly);
        }

        // remove property: OE numbers(mec_diesel_oe_numbers)
        $propertyMecDieselOeNumbers = $this->propertyManager()->propertyExistsByTag('mec_diesel_oe_numbers');
        if (($propertyMecDieselOeNumbers !== false) && ($this->getDataKey('property_mec_diesel_oe_numbers_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyMecDieselOeNumbers);
        }

        // remove property: OE number(mec_diesel_oe_number)
        $propertyMecDieselOeNumber = $this->propertyManager()->propertyExistsByTag('mec_diesel_oe_number');
        if (($propertyMecDieselOeNumber !== false) && ($this->getDataKey('property_mec_diesel_oe_number_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyMecDieselOeNumber);
        }

        // remove property: Mec-Diesel - posledný import(mec_diesel_latest_import)
        $propertyMecDieselLatestImport = $this->propertyManager()->propertyExistsByTag('mec_diesel_latest_import');
        if (($propertyMecDieselLatestImport !== false) && ($this->getDataKey('property_mec_diesel_latest_import_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyMecDieselLatestImport);
        }

        // remove property: Dodávateľský kód(mec_diesel_supplier_code)
        $propertyMecDieselSupplierCode = $this->propertyManager()->propertyExistsByTag('mec_diesel_supplier_code');
        if (($propertyMecDieselSupplierCode !== false) && ($this->getDataKey('property_mec_diesel_supplier_code_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyMecDieselSupplierCode);
        }

        // remove property: Cena bez DPH(mec_diesel_price_without_vat)
        $propertyMecDieselPriceWithoutVat = $this->propertyManager()->propertyExistsByTag('mec_diesel_price_without_vat');
        if (($propertyMecDieselPriceWithoutVat !== false) && ($this->getDataKey('property_mec_diesel_price_without_vat_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyMecDieselPriceWithoutVat);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
