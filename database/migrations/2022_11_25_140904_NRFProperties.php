<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2022-11-25 14:09:04
 * Property generator: property=nrf_supplier_code,nrf_oe_number,nrf_oe_numbers,nrf_latest_import,nrf_price_without_vat
 */
class NRFProperties extends AbstractMigration
{
    public function up()
    {
        // property: NRF Dodávateľský kód(nrf_supplier_code)
        $propertyNrfSupplierCode = $this->propertyManager()->propertyExistsByTag('nrf_supplier_code');
        if ($propertyNrfSupplierCode === false) {
            $propertyNrfSupplierCode = new Property();
            $propertyNrfSupplierCode->setTag('nrf_supplier_code');
            $propertyNrfSupplierCode->setDescription('');
            $propertyNrfSupplierCode->setExtendedDescription('');
            $propertyNrfSupplierCode->setName('NRF Dodávateľský kód');
            $propertyNrfSupplierCode->setClassId(4);
            $propertyNrfSupplierCode->setShowType(null);
            $propertyNrfSupplierCode->setShowTypeTag('text');
            $propertyNrfSupplierCode->setValueType('oneline_text');
            $propertyNrfSupplierCode->setDefaultValue('');
            $propertyNrfSupplierCode->setMultiOperations(false);
            $propertyNrfSupplierCode->setInputString('');
            $propertyNrfSupplierCode->setAttribute('tab', 'NRF');
            $propertyNrfSupplierCode->setAttribute('size', '60');
            $propertyNrfSupplierCode->setAttribute('maxlength', '');
            $propertyNrfSupplierCode->setAttribute('readonly', 'F');
            $propertyNrfSupplierCode->setAttribute('pattern', '');
            $propertyNrfSupplierCode->setAttribute('inherit_value', 'F');
            $propertyNrfSupplierCode->setAttribute('onchange-js', '');
            $propertyNrfSupplierCode->setAttribute('onkeyup-js', '');
            $propertyNrfSupplierCode->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyNrfSupplierCode);
        } else {
            $this->writeLine('Property with tag nrf_supplier_code already exists');
            $this->setDataKey('property_nrf_supplier_code_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('nrf_supplier_code', 'eshop_product', false);
        }

        // property: NRF - OE number(nrf_oe_number)
        $propertyNrfOeNumber = $this->propertyManager()->propertyExistsByTag('nrf_oe_number');
        if ($propertyNrfOeNumber === false) {
            $propertyNrfOeNumber = new Property();
            $propertyNrfOeNumber->setTag('nrf_oe_number');
            $propertyNrfOeNumber->setDescription('');
            $propertyNrfOeNumber->setExtendedDescription('');
            $propertyNrfOeNumber->setName('NRF - OE number');
            $propertyNrfOeNumber->setClassId(4);
            $propertyNrfOeNumber->setShowType(null);
            $propertyNrfOeNumber->setShowTypeTag('text');
            $propertyNrfOeNumber->setValueType('oneline_text');
            $propertyNrfOeNumber->setDefaultValue('');
            $propertyNrfOeNumber->setMultiOperations(false);
            $propertyNrfOeNumber->setInputString('');
            $propertyNrfOeNumber->setAttribute('tab', 'NRF');
            $propertyNrfOeNumber->setAttribute('size', '60');
            $propertyNrfOeNumber->setAttribute('maxlength', '');
            $propertyNrfOeNumber->setAttribute('readonly', 'F');
            $propertyNrfOeNumber->setAttribute('pattern', '');
            $propertyNrfOeNumber->setAttribute('inherit_value', 'F');
            $propertyNrfOeNumber->setAttribute('onchange-js', '');
            $propertyNrfOeNumber->setAttribute('onkeyup-js', '');
            $propertyNrfOeNumber->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyNrfOeNumber);
        } else {
            $this->writeLine('Property with tag nrf_oe_number already exists');
            $this->setDataKey('property_nrf_oe_number_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('nrf_oe_number', 'eshop_product', false);
        }

        // property: NRF - OE numbers(nrf_oe_numbers)
        $propertyNrfOeNumbers = $this->propertyManager()->propertyExistsByTag('nrf_oe_numbers');
        if ($propertyNrfOeNumbers === false) {
            $propertyNrfOeNumbers = new Property();
            $propertyNrfOeNumbers->setTag('nrf_oe_numbers');
            $propertyNrfOeNumbers->setDescription('');
            $propertyNrfOeNumbers->setExtendedDescription('');
            $propertyNrfOeNumbers->setName('NRF - OE numbers');
            $propertyNrfOeNumbers->setClassId(4);
            $propertyNrfOeNumbers->setShowType(null);
            $propertyNrfOeNumbers->setShowTypeTag('text');
            $propertyNrfOeNumbers->setValueType('oneline_text');
            $propertyNrfOeNumbers->setDefaultValue('');
            $propertyNrfOeNumbers->setMultiOperations(false);
            $propertyNrfOeNumbers->setInputString('');
            $propertyNrfOeNumbers->setAttribute('tab', 'NRF');
            $propertyNrfOeNumbers->setAttribute('size', '60');
            $propertyNrfOeNumbers->setAttribute('maxlength', '');
            $propertyNrfOeNumbers->setAttribute('readonly', 'F');
            $propertyNrfOeNumbers->setAttribute('pattern', '');
            $propertyNrfOeNumbers->setAttribute('inherit_value', 'F');
            $propertyNrfOeNumbers->setAttribute('onchange-js', '');
            $propertyNrfOeNumbers->setAttribute('onkeyup-js', '');
            $propertyNrfOeNumbers->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyNrfOeNumbers);
        } else {
            $this->writeLine('Property with tag nrf_oe_numbers already exists');
            $this->setDataKey('property_nrf_oe_numbers_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('nrf_oe_numbers', 'eshop_product', false);
        }

        // property: NRF - posledný import(nrf_latest_import)
        $propertyNrfLatestImport = $this->propertyManager()->propertyExistsByTag('nrf_latest_import');
        if ($propertyNrfLatestImport === false) {
            $propertyNrfLatestImport = new Property();
            $propertyNrfLatestImport->setTag('nrf_latest_import');
            $propertyNrfLatestImport->setDescription('');
            $propertyNrfLatestImport->setExtendedDescription('');
            $propertyNrfLatestImport->setName('NRF - posledný import');
            $propertyNrfLatestImport->setClassId(4);
            $propertyNrfLatestImport->setShowType(null);
            $propertyNrfLatestImport->setShowTypeTag('text');
            $propertyNrfLatestImport->setValueType('oneline_text');
            $propertyNrfLatestImport->setDefaultValue('');
            $propertyNrfLatestImport->setMultiOperations(false);
            $propertyNrfLatestImport->setInputString('');
            $propertyNrfLatestImport->setAttribute('tab', 'NRF');
            $propertyNrfLatestImport->setAttribute('size', '60');
            $propertyNrfLatestImport->setAttribute('maxlength', '');
            $propertyNrfLatestImport->setAttribute('readonly', 'F');
            $propertyNrfLatestImport->setAttribute('pattern', '');
            $propertyNrfLatestImport->setAttribute('inherit_value', 'F');
            $propertyNrfLatestImport->setAttribute('onchange-js', '');
            $propertyNrfLatestImport->setAttribute('onkeyup-js', '');
            $propertyNrfLatestImport->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyNrfLatestImport);
        } else {
            $this->writeLine('Property with tag nrf_latest_import already exists');
            $this->setDataKey('property_nrf_latest_import_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('nrf_latest_import', 'eshop_product', false);
        }

        // property: NRF - Cena bez DPH(nrf_price_without_vat)
        $propertyNrfPriceWithoutVat = $this->propertyManager()->propertyExistsByTag('nrf_price_without_vat');
        if ($propertyNrfPriceWithoutVat === false) {
            $propertyNrfPriceWithoutVat = new Property();
            $propertyNrfPriceWithoutVat->setTag('nrf_price_without_vat');
            $propertyNrfPriceWithoutVat->setDescription('');
            $propertyNrfPriceWithoutVat->setExtendedDescription('');
            $propertyNrfPriceWithoutVat->setName('NRF - Cena bez DPH');
            $propertyNrfPriceWithoutVat->setClassId(4);
            $propertyNrfPriceWithoutVat->setShowType(null);
            $propertyNrfPriceWithoutVat->setShowTypeTag('text');
            $propertyNrfPriceWithoutVat->setValueType('oneline_text');
            $propertyNrfPriceWithoutVat->setDefaultValue('');
            $propertyNrfPriceWithoutVat->setMultiOperations(false);
            $propertyNrfPriceWithoutVat->setInputString('');
            $propertyNrfPriceWithoutVat->setAttribute('tab', 'NRF');
            $propertyNrfPriceWithoutVat->setAttribute('size', '60');
            $propertyNrfPriceWithoutVat->setAttribute('maxlength', '');
            $propertyNrfPriceWithoutVat->setAttribute('readonly', 'F');
            $propertyNrfPriceWithoutVat->setAttribute('pattern', '');
            $propertyNrfPriceWithoutVat->setAttribute('inherit_value', 'F');
            $propertyNrfPriceWithoutVat->setAttribute('onchange-js', '');
            $propertyNrfPriceWithoutVat->setAttribute('onkeyup-js', '');
            $propertyNrfPriceWithoutVat->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyNrfPriceWithoutVat);
        } else {
            $this->writeLine('Property with tag nrf_price_without_vat already exists');
            $this->setDataKey('property_nrf_price_without_vat_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('nrf_price_without_vat', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: NRF - Cena bez DPH(nrf_price_without_vat)
        $propertyNrfPriceWithoutVat = $this->propertyManager()->propertyExistsByTag('nrf_price_without_vat');
        if (($propertyNrfPriceWithoutVat !== false) && ($this->getDataKey('property_nrf_price_without_vat_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyNrfPriceWithoutVat);
        }

        // remove property: NRF - posledný import(nrf_latest_import)
        $propertyNrfLatestImport = $this->propertyManager()->propertyExistsByTag('nrf_latest_import');
        if (($propertyNrfLatestImport !== false) && ($this->getDataKey('property_nrf_latest_import_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyNrfLatestImport);
        }

        // remove property: NRF - OE numbers(nrf_oe_numbers)
        $propertyNrfOeNumbers = $this->propertyManager()->propertyExistsByTag('nrf_oe_numbers');
        if (($propertyNrfOeNumbers !== false) && ($this->getDataKey('property_nrf_oe_numbers_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyNrfOeNumbers);
        }

        // remove property: NRF - OE number(nrf_oe_number)
        $propertyNrfOeNumber = $this->propertyManager()->propertyExistsByTag('nrf_oe_number');
        if (($propertyNrfOeNumber !== false) && ($this->getDataKey('property_nrf_oe_number_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyNrfOeNumber);
        }

        // remove property: NRF Dodávateľský kód(nrf_supplier_code)
        $propertyNrfSupplierCode = $this->propertyManager()->propertyExistsByTag('nrf_supplier_code');
        if (($propertyNrfSupplierCode !== false) && ($this->getDataKey('property_nrf_supplier_code_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyNrfSupplierCode);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
