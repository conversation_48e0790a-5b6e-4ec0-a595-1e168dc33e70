<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Automatic generation from (rinoparts) at 2025-03-07 08:47:38
 */
class MakeEnclosureColumnsNullable extends AbstractMigration
{
    public function up()
    {
        // update table onix_enclosures
        Schema::table('onix_enclosures', function (Blueprint $table) {
            $table->datetime('onix_date_document')->nullable()->change();
            $table->string('path')->nullable()->change();
            $table->string('extension')->nullable()->change();
        });
    }

    public function down()
    {
        // revert changes to table onix_enclosures
        Schema::table('onix_enclosures', function (Blueprint $table) {
            //
        });
    }
}
