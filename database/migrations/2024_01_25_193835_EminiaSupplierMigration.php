<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (rinoparts) at 2024-01-25 19:38:35
 */
class EminiaSupplierMigration extends AbstractMigration
{
    public function dependencies()
    {
        return [
            EminiaPagesMigration::class,
        ];
    }

    public function up()
    {
        \App\Supplier::create([
            'name' => 'EMINIA-SUPPLIER',
            'producer_ciselnik_id' => \Buxus\Util\PageIds::getEminiaSupplier(),
            'price_levels_on' => 1,
        ]);
    }

    public function down()
    {
    }
}
