<?php
namespace Krabica\Migrations;

use Buxus\Migration\AbstractMigration;

class Demo extends AbstractMigration {
    public function up()
    {
        // set dizajn
        $page_id = $this->getPageIdByTag('dizajn');
        if ($page_id === NULL) {
            throw new \Exception('Unable to find dizajn page');
        }

        $dizajn = \PageFactory::get($page_id);
        $dizajn->setPropertyValue('brand_logo_small', 'demo_content/logo/buxus_logo.png');
        $dizajn->setPropertyValue('brand_logo_middle', 'demo_content/logo/logo_stredne.png');
        $dizajn->setPropertyValue('brand_logo', 'demo_content/logo/logo.png');
//        $dizajn->setPropertyValue('page_background_image', 'demo_content/pozadie/pozadie3.png');
        //$dizajn->setPropertyValue('page_background_image', 'demo_content/pozadie/pozadie3.png');
        $dizajn->save();

        // set menu
        $page_id = $this->getPageIdByTag('menu');
        if ($page_id === NULL) {
            throw new \Exception('Unable to find menu page');
        }
        $menu = \PageFactory::get($page_id);
        $menu->setValue('page_list_menu_top', array(
            array (
                'to_page_id' => $this->getPageIdByTag('o_nas'),
                'order_index' => 1,
            ),
            array (
                'to_page_id' => $this->getPageIdByTag('vseobecne_obchodne_podmienky'),
                'order_index' => 2,
            ),
        ));
        $menu->save();
    }

    public function down()
    {
        // set dizajn
        $page_id = $this->getPageIdByTag('dizajn');
        if ($page_id === NULL) {
            throw new \Exception('Unable to find dizajn page');
        }

        $dizajn = \PageFactory::get($page_id);
        $dizajn->setPropertyValue('brand_logo_small', '');
        $dizajn->setPropertyValue('brand_logo_middle', '');
        $dizajn->setPropertyValue('brand_logo', '');
        $dizajn->setPropertyValue('page_background_image', '');
        $dizajn->save();

        // set menu
        $page_id = $this->getPageIdByTag('menu');
        if ($page_id === NULL) {
            throw new \Exception('Unable to find menu page');
        }
        $menu = \PageFactory::get($page_id);
        $menu->setPropertyValue('page_list_menu_top', array());
        $menu->save();
    }
}