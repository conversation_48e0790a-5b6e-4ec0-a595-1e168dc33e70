<?php
namespace Krabica\Migrations;

use Buxus\Migration\AbstractMigration;
use Buxus\Util\PageIds;

class ContactSettings extends AbstractMigration {
    public function up() {
        // page type: Nastavenia (settings)
        $page_type_settings = $this->pageTypesManager()->pageTypeExistsByTag('settings');
        if ($page_type_settings === false) {
            $page_type_settings = new \Buxus\PageType\PageType();
            $page_type_settings->setTag('settings');
            $page_type_settings->setName('Nastavenia');
            $page_type_settings->setPageClassId('1');
            $page_type_settings->setDefaultTemplateId('2');
            $page_type_settings->setDeleteTrigger('');
            $page_type_settings->setIncludeInSync('1');
            $page_type_settings->setPageDetailsLayout('');
            $page_type_settings->setPageSortTypeTag('sort_date_time');
            $page_type_settings->setPageTypeOrder('999');
            $page_type_settings->setPostmoveTrigger('');
            $page_type_settings->setPostsubmitTrigger('');
            $page_type_settings->setPresubmitTrigger('');
            $page_type_settings->setParent(NULL);

        } else {
            $this->writeLine('Page type with tag settings already exists');
            $this->setDataKey('page_type_settings_existed', true);
        }
        if ($this->pageTypeExists('main_page')) {
            $page_type_settings->addSuperiorPageType($this->getPageTypeByTag('main_page'));
        }
        if ($this->pageTypeExists('eshop_catalog')) {
            $page_type_settings->addSuperiorPageType($this->getPageTypeByTag('eshop_catalog'));
        }
        if ($this->pageTypeExists('settings')) {
            $page_type_settings->addSuperiorPageType($this->getPageTypeByTag('settings'));
        }
        if ($this->pageTypeExists('folder')) {
            $page_type_settings->addSuperiorPageType($this->getPageTypeByTag('folder'));
        }
        $this->pageTypesManager()->savePageType($page_type_settings);
        // set template on MAIN PAGE index::error404
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('settings'), 'index', 'error404');

        // property: Ulica(company_street)
        $property_company_street = $this->propertyManager()->propertyExistsByTag('company_street');
        if ($property_company_street === false) {
            $property_company_street = new \Buxus\Property\Types\Input();
            $property_company_street->setTag('company_street');
            $property_company_street->setDescription('Ulica spoločnosti');
            $property_company_street->setExtendedDescription('');
            $property_company_street->setName('Ulica');
            $property_company_street->setClassId('4');
            $property_company_street->setShowType(NULL);
            $property_company_street->setShowTypeTag('text');
            $property_company_street->setValueType('oneline_text');
            $property_company_street->setDefaultValue('');
            $property_company_street->setMultiOperations(false);
            $property_company_street->setInputString(NULL);
            $property_company_street->setAttribute('tab', 'Kontaktn&eacute; &uacute;daje');
            $property_company_street->setAttribute('size', '60');
            $property_company_street->setAttribute('maxlength', '');
            $property_company_street->setAttribute('readonly', 'F');
            $property_company_street->setAttribute('pattern', '');
            $property_company_street->setAttribute('inherit_value', 'F');
            $property_company_street->setAttribute('onchange-js', '');
            $property_company_street->setAttribute('onkeyup-js', '');
            $property_company_street->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_company_street);

        } else {
            $this->writeLine('Property with tag company_street already exists');
            $this->setDataKey('property_company_street_existed', true);
        }

        // property: Číslo(company_street_number)
        $property_company_street_number = $this->propertyManager()->propertyExistsByTag('company_street_number');
        if ($property_company_street_number === false) {
            $property_company_street_number = new \Buxus\Property\Types\Input();
            $property_company_street_number->setTag('company_street_number');
            $property_company_street_number->setDescription('číslo domu');
            $property_company_street_number->setExtendedDescription('');
            $property_company_street_number->setName('Číslo');
            $property_company_street_number->setClassId('4');
            $property_company_street_number->setShowType(NULL);
            $property_company_street_number->setShowTypeTag('text');
            $property_company_street_number->setValueType('oneline_text');
            $property_company_street_number->setDefaultValue('');
            $property_company_street_number->setMultiOperations(false);
            $property_company_street_number->setInputString(NULL);
            $property_company_street_number->setAttribute('tab', 'Kontaktn&eacute; &uacute;daje');
            $property_company_street_number->setAttribute('size', '60');
            $property_company_street_number->setAttribute('maxlength', '');
            $property_company_street_number->setAttribute('readonly', 'F');
            $property_company_street_number->setAttribute('pattern', '');
            $property_company_street_number->setAttribute('inherit_value', 'F');
            $property_company_street_number->setAttribute('onchange-js', '');
            $property_company_street_number->setAttribute('onkeyup-js', '');
            $property_company_street_number->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_company_street_number);

        } else {
            $this->writeLine('Property with tag company_street_number already exists');
            $this->setDataKey('property_company_street_number_existed', true);
        }

        // property: PSČ(company_zip_code)
        $property_company_zip_code = $this->propertyManager()->propertyExistsByTag('company_zip_code');
        if ($property_company_zip_code === false) {
            $property_company_zip_code = new \Buxus\Property\Types\Input();
            $property_company_zip_code->setTag('company_zip_code');
            $property_company_zip_code->setDescription('Poštové smerovacie číslo');
            $property_company_zip_code->setExtendedDescription('');
            $property_company_zip_code->setName('PSČ');
            $property_company_zip_code->setClassId('4');
            $property_company_zip_code->setShowType(NULL);
            $property_company_zip_code->setShowTypeTag('text');
            $property_company_zip_code->setValueType('oneline_text');
            $property_company_zip_code->setDefaultValue('');
            $property_company_zip_code->setMultiOperations(false);
            $property_company_zip_code->setInputString(NULL);
            $property_company_zip_code->setAttribute('tab', 'Kontaktn&eacute; &uacute;daje');
            $property_company_zip_code->setAttribute('size', '60');
            $property_company_zip_code->setAttribute('maxlength', '');
            $property_company_zip_code->setAttribute('readonly', 'F');
            $property_company_zip_code->setAttribute('pattern', '');
            $property_company_zip_code->setAttribute('inherit_value', 'F');
            $property_company_zip_code->setAttribute('onchange-js', '');
            $property_company_zip_code->setAttribute('onkeyup-js', '');
            $property_company_zip_code->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_company_zip_code);

        } else {
            $this->writeLine('Property with tag company_zip_code already exists');
            $this->setDataKey('property_company_zip_code_existed', true);
        }

        // property: Mesto(company_city)
        $property_company_city = $this->propertyManager()->propertyExistsByTag('company_city');
        if ($property_company_city === false) {
            $property_company_city = new \Buxus\Property\Types\Input();
            $property_company_city->setTag('company_city');
            $property_company_city->setDescription('Mesto spoločnosti.');
            $property_company_city->setExtendedDescription('');
            $property_company_city->setName('Mesto');
            $property_company_city->setClassId('4');
            $property_company_city->setShowType(NULL);
            $property_company_city->setShowTypeTag('text');
            $property_company_city->setValueType('oneline_text');
            $property_company_city->setDefaultValue('');
            $property_company_city->setMultiOperations(false);
            $property_company_city->setInputString(NULL);
            $property_company_city->setAttribute('tab', 'Kontaktn&eacute; &uacute;daje');
            $property_company_city->setAttribute('size', '60');
            $property_company_city->setAttribute('maxlength', '');
            $property_company_city->setAttribute('readonly', 'F');
            $property_company_city->setAttribute('pattern', '');
            $property_company_city->setAttribute('inherit_value', 'F');
            $property_company_city->setAttribute('onchange-js', '');
            $property_company_city->setAttribute('onkeyup-js', '');
            $property_company_city->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_company_city);

        } else {
            $this->writeLine('Property with tag company_city already exists');
            $this->setDataKey('property_company_city_existed', true);
        }

        // property: Email(company_email)
        $property_company_email = $this->propertyManager()->propertyExistsByTag('company_email');
        if ($property_company_email === false) {
            $property_company_email = new \Buxus\Property\Types\Input();
            $property_company_email->setTag('company_email');
            $property_company_email->setDescription('Email spoločnosti');
            $property_company_email->setExtendedDescription('');
            $property_company_email->setName('Email');
            $property_company_email->setClassId('4');
            $property_company_email->setShowType(NULL);
            $property_company_email->setShowTypeTag('text');
            $property_company_email->setValueType('oneline_text');
            $property_company_email->setDefaultValue('');
            $property_company_email->setMultiOperations(false);
            $property_company_email->setInputString(NULL);
            $property_company_email->setAttribute('tab', 'Kontaktn&eacute; &uacute;daje');
            $property_company_email->setAttribute('size', '60');
            $property_company_email->setAttribute('maxlength', '');
            $property_company_email->setAttribute('readonly', 'F');
            $property_company_email->setAttribute('pattern', '');
            $property_company_email->setAttribute('inherit_value', 'F');
            $property_company_email->setAttribute('onchange-js', '');
            $property_company_email->setAttribute('onkeyup-js', '');
            $property_company_email->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_company_email);

        } else {
            $this->writeLine('Property with tag company_email already exists');
            $this->setDataKey('property_company_email_existed', true);
        }

        // property: Telefón(company_telephone)
        $property_company_telephone = $this->propertyManager()->propertyExistsByTag('company_telephone');
        if ($property_company_telephone === false) {
            $property_company_telephone = new \Buxus\Property\Types\Input();
            $property_company_telephone->setTag('company_telephone');
            $property_company_telephone->setDescription('Telefón spoločnosti');
            $property_company_telephone->setExtendedDescription('');
            $property_company_telephone->setName('Telefón');
            $property_company_telephone->setClassId('4');
            $property_company_telephone->setShowType(NULL);
            $property_company_telephone->setShowTypeTag('text');
            $property_company_telephone->setValueType('oneline_text');
            $property_company_telephone->setDefaultValue('');
            $property_company_telephone->setMultiOperations(false);
            $property_company_telephone->setInputString(NULL);
            $property_company_telephone->setAttribute('tab', 'Kontaktn&eacute; &uacute;daje');
            $property_company_telephone->setAttribute('size', '60');
            $property_company_telephone->setAttribute('maxlength', '');
            $property_company_telephone->setAttribute('readonly', 'F');
            $property_company_telephone->setAttribute('pattern', '');
            $property_company_telephone->setAttribute('inherit_value', 'F');
            $property_company_telephone->setAttribute('onchange-js', '');
            $property_company_telephone->setAttribute('onkeyup-js', '');
            $property_company_telephone->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_company_telephone);

        } else {
            $this->writeLine('Property with tag company_telephone already exists');
            $this->setDataKey('property_company_telephone_existed', true);
        }

        // property: IČO(company_ico)
        $property_company_ico = $this->propertyManager()->propertyExistsByTag('company_ico');
        if ($property_company_ico === false) {
            $property_company_ico = new \Buxus\Property\Types\Input();
            $property_company_ico->setTag('company_ico');
            $property_company_ico->setDescription('IČO spoločnosti');
            $property_company_ico->setExtendedDescription('');
            $property_company_ico->setName('IČO');
            $property_company_ico->setClassId('4');
            $property_company_ico->setShowType(NULL);
            $property_company_ico->setShowTypeTag('text');
            $property_company_ico->setValueType('oneline_text');
            $property_company_ico->setDefaultValue('');
            $property_company_ico->setMultiOperations(false);
            $property_company_ico->setInputString(NULL);
            $property_company_ico->setAttribute('tab', 'Kontaktn&eacute; &uacute;daje');
            $property_company_ico->setAttribute('size', '60');
            $property_company_ico->setAttribute('maxlength', '');
            $property_company_ico->setAttribute('readonly', 'F');
            $property_company_ico->setAttribute('pattern', '');
            $property_company_ico->setAttribute('inherit_value', 'F');
            $property_company_ico->setAttribute('onchange-js', '');
            $property_company_ico->setAttribute('onkeyup-js', '');
            $property_company_ico->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_company_ico);

        } else {
            $this->writeLine('Property with tag company_ico already exists');
            $this->setDataKey('property_company_ico_existed', true);
        }

        // property: DIČ(company_dic)
        $property_company_dic = $this->propertyManager()->propertyExistsByTag('company_dic');
        if ($property_company_dic === false) {
            $property_company_dic = new \Buxus\Property\Types\Input();
            $property_company_dic->setTag('company_dic');
            $property_company_dic->setDescription('DIČ spoločnosti');
            $property_company_dic->setExtendedDescription('');
            $property_company_dic->setName('DIČ');
            $property_company_dic->setClassId('4');
            $property_company_dic->setShowType(NULL);
            $property_company_dic->setShowTypeTag('text');
            $property_company_dic->setValueType('oneline_text');
            $property_company_dic->setDefaultValue('');
            $property_company_dic->setMultiOperations(false);
            $property_company_dic->setInputString(NULL);
            $property_company_dic->setAttribute('tab', 'Kontaktn&eacute; &uacute;daje');
            $property_company_dic->setAttribute('size', '60');
            $property_company_dic->setAttribute('maxlength', '');
            $property_company_dic->setAttribute('readonly', 'F');
            $property_company_dic->setAttribute('pattern', '');
            $property_company_dic->setAttribute('inherit_value', 'F');
            $property_company_dic->setAttribute('onchange-js', '');
            $property_company_dic->setAttribute('onkeyup-js', '');
            $property_company_dic->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_company_dic);

        } else {
            $this->writeLine('Property with tag company_dic already exists');
            $this->setDataKey('property_company_dic_existed', true);
        }

        // property: IČ DPH(company_ic_dph)
        $property_company_ic_dph = $this->propertyManager()->propertyExistsByTag('company_ic_dph');
        if ($property_company_ic_dph === false) {
            $property_company_ic_dph = new \Buxus\Property\Types\Input();
            $property_company_ic_dph->setTag('company_ic_dph');
            $property_company_ic_dph->setDescription('IČ DPH spoločnosti');
            $property_company_ic_dph->setExtendedDescription('');
            $property_company_ic_dph->setName('IČ DPH');
            $property_company_ic_dph->setClassId('4');
            $property_company_ic_dph->setShowType(NULL);
            $property_company_ic_dph->setShowTypeTag('text');
            $property_company_ic_dph->setValueType('oneline_text');
            $property_company_ic_dph->setDefaultValue('');
            $property_company_ic_dph->setMultiOperations(false);
            $property_company_ic_dph->setInputString(NULL);
            $property_company_ic_dph->setAttribute('tab', 'Kontaktn&eacute; &uacute;daje');
            $property_company_ic_dph->setAttribute('size', '60');
            $property_company_ic_dph->setAttribute('maxlength', '');
            $property_company_ic_dph->setAttribute('readonly', 'F');
            $property_company_ic_dph->setAttribute('pattern', '');
            $property_company_ic_dph->setAttribute('inherit_value', 'F');
            $property_company_ic_dph->setAttribute('onchange-js', '');
            $property_company_ic_dph->setAttribute('onkeyup-js', '');
            $property_company_ic_dph->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_company_ic_dph);

        } else {
            $this->writeLine('Property with tag company_ic_dph already exists');
            $this->setDataKey('property_company_ic_dph_existed', true);
        }

        // property: Slogan(catchword)
        $property_catchword = $this->propertyManager()->propertyExistsByTag('catchword');
        if ($property_catchword === false) {
            $property_catchword = new \Buxus\Property\Types\Input();
            $property_catchword->setTag('catchword');
            $property_catchword->setDescription('Slogan webu');
            $property_catchword->setExtendedDescription('');
            $property_catchword->setName('Slogan');
            $property_catchword->setClassId('4');
            $property_catchword->setShowType(NULL);
            $property_catchword->setShowTypeTag('text');
            $property_catchword->setValueType('oneline_text');
            $property_catchword->setDefaultValue('Lorem ipsum');
            $property_catchword->setMultiOperations(false);
            $property_catchword->setInputString(NULL);
            $property_catchword->setAttribute('tab', '');
            $property_catchword->setAttribute('size', '60');
            $property_catchword->setAttribute('maxlength', '');
            $property_catchword->setAttribute('readonly', 'F');
            $property_catchword->setAttribute('pattern', '');
            $property_catchword->setAttribute('inherit_value', 'F');
            $property_catchword->setAttribute('onchange-js', '');
            $property_catchword->setAttribute('onkeyup-js', '');
            $property_catchword->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_catchword);

        } else {
            $this->writeLine('Property with tag catchword already exists');
            $this->setDataKey('property_catchword_existed', true);
        }

        // page type: Nastavenie údajov (contact_data_settings)
        $page_type_contact_data_settings = $this->pageTypesManager()->pageTypeExistsByTag('contact_data_settings');
        if ($page_type_contact_data_settings === false) {
            $page_type_contact_data_settings = new \Buxus\PageType\PageType();
            $page_type_contact_data_settings->setTag('contact_data_settings');
            $page_type_contact_data_settings->setName('Nastavenie údajov');
            $page_type_contact_data_settings->setPageClassId('1');
            $page_type_contact_data_settings->setDefaultTemplateId('2');
            $page_type_contact_data_settings->setDeleteTrigger('');
            $page_type_contact_data_settings->setIncludeInSync(NULL);
            $page_type_contact_data_settings->setPageDetailsLayout('');
            $page_type_contact_data_settings->setPageSortTypeTag('sort_date_time');
            $page_type_contact_data_settings->setPageTypeOrder('0');
            $page_type_contact_data_settings->setPostmoveTrigger('');
            $page_type_contact_data_settings->setPostsubmitTrigger('');
            $page_type_contact_data_settings->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('settings');
            $page_type_contact_data_settings->setParent($parent);

        } else {
            $this->writeLine('Page type with tag contact_data_settings already exists');
            $this->setDataKey('page_type_contact_data_settings_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('company_street');
        $property_id = $property->getId();
        $tmp = $page_type_contact_data_settings->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('1');
            $tmp->setRequired(false);
            $page_type_contact_data_settings->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('company_street_number');
        $property_id = $property->getId();
        $tmp = $page_type_contact_data_settings->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('2');
            $tmp->setRequired(false);
            $page_type_contact_data_settings->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('company_zip_code');
        $property_id = $property->getId();
        $tmp = $page_type_contact_data_settings->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('3');
            $tmp->setRequired(false);
            $page_type_contact_data_settings->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('company_city');
        $property_id = $property->getId();
        $tmp = $page_type_contact_data_settings->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('4');
            $tmp->setRequired(false);
            $page_type_contact_data_settings->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('company_email');
        $property_id = $property->getId();
        $tmp = $page_type_contact_data_settings->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('5');
            $tmp->setRequired(false);
            $page_type_contact_data_settings->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('company_telephone');
        $property_id = $property->getId();
        $tmp = $page_type_contact_data_settings->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('6');
            $tmp->setRequired(false);
            $page_type_contact_data_settings->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('company_ico');
        $property_id = $property->getId();
        $tmp = $page_type_contact_data_settings->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('7');
            $tmp->setRequired(false);
            $page_type_contact_data_settings->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('company_dic');
        $property_id = $property->getId();
        $tmp = $page_type_contact_data_settings->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('8');
            $tmp->setRequired(false);
            $page_type_contact_data_settings->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('company_ic_dph');
        $property_id = $property->getId();
        $tmp = $page_type_contact_data_settings->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('9');
            $tmp->setRequired(false);
            $page_type_contact_data_settings->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('catchword');
        $property_id = $property->getId();
        $tmp = $page_type_contact_data_settings->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('10');
            $tmp->setRequired(false);
            $page_type_contact_data_settings->addPropertyItem($tmp);
        }
        if ($this->pageTypeExists('settings')) {
            $page_type_contact_data_settings->addSuperiorPageType($this->getPageTypeByTag('settings'));
        }
        if ($this->pageTypeExists('folder')) {
            $page_type_contact_data_settings->addSuperiorPageType($this->getPageTypeByTag('folder'));
        }
        $this->pageTypesManager()->savePageType($page_type_contact_data_settings);

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // page: Nastavenie údajov(ID: 492 TAG: nastavenie_udajov)
        $page_id = $this->getPageIdByTag('nastavenie_udajov');
        if ($page_id === NULL) {
            $page_type = $this->pageTypesManager()->getPageTypeByTag('contact_data_settings');
            $page_492 = \PageFactory::create($this->getPageIdByTag('admin_settings'), $page_type->getId());
        } else {
            $page_492 = \PageFactory::get($page_id);
        }
        $page_492->setPageName('Nastavenie údajov');
        $page_492->setPageTag('nastavenie_udajov');
        $page_492->setPageStateId('2');
        $page_492->setPageClassId('1');
        $page_492->setPropertyValue('catchword', 'Komplexné riešenie pre váš e-shop');
        $page_492->setPropertyValue('company_telephone', '88 0160 123 456');
        $page_492->save();

        // regenerate page tags
        PageIds::generatePageTagsList();
    }

    public function down() {
        // remove page: Nastavenie údajov (nastavenie_udajov)
        $page_id = $this->getPageIdByTag('nastavenie_udajov');
        if ($page_id !== NULL) {
            \PageFactory::get($page_id)->delete();
        }

        // regenerate page tags
        PageIds::generatePageTagsList();

        // remove page type: Nastavenie údajov (contact_data_settings)
        $page_type_contact_data_settings = $this->pageTypesManager()->pageTypeExistsByTag('contact_data_settings');
        if (($page_type_contact_data_settings != false) && (is_null($this->getDataKey('page_type_contact_data_settings_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_contact_data_settings);
        }

        // remove property: Slogan(catchword)
        $property_catchword = $this->propertyManager()->propertyExistsByTag('catchword');
        if ($property_catchword != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_catchword);
            if ((is_null($this->getDataKey('property_catchword_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_catchword);
            }
        }

        // remove property: IČ DPH(company_ic_dph)
        $property_company_ic_dph = $this->propertyManager()->propertyExistsByTag('company_ic_dph');
        if ($property_company_ic_dph != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_company_ic_dph);
            if ((is_null($this->getDataKey('property_company_ic_dph_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_company_ic_dph);
            }
        }

        // remove property: DIČ(company_dic)
        $property_company_dic = $this->propertyManager()->propertyExistsByTag('company_dic');
        if ($property_company_dic != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_company_dic);
            if ((is_null($this->getDataKey('property_company_dic_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_company_dic);
            }
        }

        // remove property: IČO(company_ico)
        $property_company_ico = $this->propertyManager()->propertyExistsByTag('company_ico');
        if ($property_company_ico != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_company_ico);
            if ((is_null($this->getDataKey('property_company_ico_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_company_ico);
            }
        }

        // remove property: Telefón(company_telephone)
        $property_company_telephone = $this->propertyManager()->propertyExistsByTag('company_telephone');
        if ($property_company_telephone != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_company_telephone);
            if ((is_null($this->getDataKey('property_company_telephone_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_company_telephone);
            }
        }

        // remove property: Email(company_email)
        $property_company_email = $this->propertyManager()->propertyExistsByTag('company_email');
        if ($property_company_email != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_company_email);
            if ((is_null($this->getDataKey('property_company_email_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_company_email);
            }
        }

        // remove property: Mesto(company_city)
        $property_company_city = $this->propertyManager()->propertyExistsByTag('company_city');
        if ($property_company_city != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_company_city);
            if ((is_null($this->getDataKey('property_company_city_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_company_city);
            }
        }

        // remove property: PSČ(company_zip_code)
        $property_company_zip_code = $this->propertyManager()->propertyExistsByTag('company_zip_code');
        if ($property_company_zip_code != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_company_zip_code);
            if ((is_null($this->getDataKey('property_company_zip_code_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_company_zip_code);
            }
        }

        // remove property: Číslo(company_street_number)
        $property_company_street_number = $this->propertyManager()->propertyExistsByTag('company_street_number');
        if ($property_company_street_number != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_company_street_number);
            if ((is_null($this->getDataKey('property_company_street_number_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_company_street_number);
            }
        }

        // remove property: Ulica(company_street)
        $property_company_street = $this->propertyManager()->propertyExistsByTag('company_street');
        if ($property_company_street != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_company_street);
            if ((is_null($this->getDataKey('property_company_street_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_company_street);
            }
        }

        // remove page type: Nastavenia (settings)
        $page_type_settings = $this->pageTypesManager()->pageTypeExistsByTag('settings');
        if (($page_type_settings != false) && (is_null($this->getDataKey('page_type_settings_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_settings);
        }

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

        // regenerate property constants
        $this->propertyManager()->generateConstants();

    }

}
