<?php
namespace Krabica\Migrations;

use Buxus\Migration\AbstractMigration;
use Buxus\PageType\PageType;
use Buxus\PageType\PageTypePropertyItem;
use Buxus\Property\Property;

/**
 * Automatic generation from (krabica3) at 2015-12-01 15:24:59
 * PageType generator: page_type=article,rubric,section
 */

class PublishingPageTypes extends AbstractMigration {
    public function up() {
        // property: Titulok(title)
        $property_title = $this->propertyManager()->propertyExistsByTag('title');
        if ($property_title === false) {
            $property_title = new \Buxus\Property\Types\Input();
            $property_title->setTag('title');
            $property_title->setDescription('Štandardný názov stránky. Text, ktorý sa zobrazí v záložke prehliadača. Podľa SEO odporúčaní max. 65 znakov.');
            $property_title->setExtendedDescription('');
            $property_title->setName('Titulok');
            $property_title->setClassId('4');
            $property_title->setShowType(NULL);
            $property_title->setShowTypeTag('text');
            $property_title->setValueType('oneline_text');
            $property_title->setDefaultValue('');
            $property_title->setMultiOperations(false);
            $property_title->setInputString(NULL);
            $property_title->setAttribute('tab', '');
            $property_title->setAttribute('size', '60');
            $property_title->setAttribute('maxlength', '');
            $property_title->setAttribute('readonly', 'F');
            $property_title->setAttribute('pattern', '');
            $property_title->setAttribute('inherit_value', 'F');
            $property_title->setAttribute('onchange-js', '');
            $property_title->setAttribute('onkeyup-js', '');
            $property_title->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_title);
        } else {
            $this->writeLine('Property with tag title already exists');
            $this->setDataKey('property_title_existed', true);
        }

        // property: SEO URL name(seo_url_name)
        $property_seo_url_name = $this->propertyManager()->propertyExistsByTag('seo_url_name');
        if ($property_seo_url_name === false) {
            $property_seo_url_name = new Property();
            $property_seo_url_name->setTag('seo_url_name');
            $property_seo_url_name->setDescription('Podľa SEO odporúčaní max. 35 znakov.');
            $property_seo_url_name->setExtendedDescription('');
            $property_seo_url_name->setName('SEO URL name');
            $property_seo_url_name->setClassId('4');
            $property_seo_url_name->setShowType(NULL);
            $property_seo_url_name->setShowTypeTag('seo_url_name');
            $property_seo_url_name->setValueType('seo_url_name');
            $property_seo_url_name->setDefaultValue('');
            $property_seo_url_name->setMultiOperations(false);
            $property_seo_url_name->setInputString(NULL);
            $property_seo_url_name->setAttribute('tab', 'SEO');
            $property_seo_url_name->setAttribute('size', '80');
            $property_seo_url_name->setAttribute('onchange-js', '');
            $property_seo_url_name->setAttribute('onkeyup-js', '');
            $property_seo_url_name->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_seo_url_name);
        } else {
            $this->writeLine('Property with tag seo_url_name already exists');
            $this->setDataKey('property_seo_url_name_existed', true);
        }

        // page type: Stránka (page)
        $page_type_page = $this->pageTypesManager()->pageTypeExistsByTag('page');
        if ($page_type_page === false) {
            $page_type_page = new PageType();
            $page_type_page->setTag('page');
            $page_type_page->setName('Stránka');
            $page_type_page->setPageClassId('1');
            $page_type_page->setDefaultTemplateId('2');
            $page_type_page->setDeleteTrigger('');
            $page_type_page->setIncludeInSync(NULL);
            $page_type_page->setPageDetailsLayout('');
            $page_type_page->setPageSortTypeTag('sort_date_time');
            $page_type_page->setPageTypeOrder('0');
            $page_type_page->setPostmoveTrigger('');
            $page_type_page->setPostsubmitTrigger('');
            $page_type_page->setPresubmitTrigger('');
            $page_type_page->setParent(NULL);
        } else {
            $this->writeLine('Page type with tag page already exists');
            $this->setDataKey('page_type_page_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('title');
        $property_id = $property->getId();
        $tmp = $page_type_page->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder('1');
            $tmp->setRequired(false);
            $page_type_page->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('seo_url_name');
        $property_id = $property->getId();
        $tmp = $page_type_page->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder('2');
            $tmp->setRequired(false);
            $page_type_page->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($page_type_page);
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('page'), 'index', 'error404');

        // property: Anotácia(annotation)
        $property_annotation = $this->propertyManager()->propertyExistsByTag('annotation');
        if ($property_annotation === false) {
            $property_annotation = new \Buxus\Property\Types\Textarea();
            $property_annotation->setTag('annotation');
            $property_annotation->setDescription('Štandardná anotácia stránky.');
            $property_annotation->setExtendedDescription('');
            $property_annotation->setName('Anotácia');
            $property_annotation->setClassId('4');
            $property_annotation->setShowType(NULL);
            $property_annotation->setShowTypeTag('textarea');
            $property_annotation->setValueType('multiline_text');
            $property_annotation->setDefaultValue('');
            $property_annotation->setMultiOperations(false);
            $property_annotation->setInputString(NULL);
            $property_annotation->setAttribute('tab', '');
            $property_annotation->setAttribute('cols', '60');
            $property_annotation->setAttribute('rows', '');
            $property_annotation->setAttribute('dhtml-edit', '1');
            $property_annotation->setAttribute('dhtml-configuration', 'full');
            $property_annotation->setAttribute('import-word', '0');
            $property_annotation->setAttribute('auto', '1');
            $property_annotation->setAttribute('inherit_value', 'F');
            $property_annotation->setAttribute('onchange-js', '');
            $property_annotation->setAttribute('onkeyup-js', '');
            $property_annotation->setAttribute('onkeydown-js', '');
            $property_annotation->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($property_annotation);
        } else {
            $this->writeLine('Property with tag annotation already exists');
            $this->setDataKey('property_annotation_existed', true);
        }

        // property: Text(text)
        $property_text = $this->propertyManager()->propertyExistsByTag('text');
        if ($property_text === false) {
            $property_text = new \Buxus\Property\Types\Textarea();
            $property_text->setTag('text');
            $property_text->setDescription('Štandardný text stránky.');
            $property_text->setExtendedDescription('');
            $property_text->setName('Text');
            $property_text->setClassId('4');
            $property_text->setShowType(NULL);
            $property_text->setShowTypeTag('textarea');
            $property_text->setValueType('multiline_text');
            $property_text->setDefaultValue('');
            $property_text->setMultiOperations(false);
            $property_text->setInputString(NULL);
            $property_text->setAttribute('tab', '');
            $property_text->setAttribute('cols', '60');
            $property_text->setAttribute('rows', '');
            $property_text->setAttribute('dhtml-edit', '1');
            $property_text->setAttribute('dhtml-configuration', 'full');
            $property_text->setAttribute('import-word', '0');
            $property_text->setAttribute('auto', '1');
            $property_text->setAttribute('inherit_value', 'F');
            $property_text->setAttribute('onchange-js', '');
            $property_text->setAttribute('onkeyup-js', '');
            $property_text->setAttribute('onkeydown-js', '');
            $property_text->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($property_text);
        } else {
            $this->writeLine('Property with tag text already exists');
            $this->setDataKey('property_text_existed', true);
        }

        // page type: Článok (article)
        $page_type_article = $this->pageTypesManager()->pageTypeExistsByTag('article');
        if ($page_type_article === false) {
            $page_type_article = new PageType();
            $page_type_article->setTag('article');
            $page_type_article->setName('Článok');
            $page_type_article->setPageClassId('1');
            $page_type_article->setDefaultTemplateId('2');
            $page_type_article->setDeleteTrigger('');
            $page_type_article->setIncludeInSync('0');
            $page_type_article->setPageDetailsLayout('');
            $page_type_article->setPageSortTypeTag('page_name');
            $page_type_article->setPageTypeOrder('10');
            $page_type_article->setPostmoveTrigger('');
            $page_type_article->setPostsubmitTrigger('');
            $page_type_article->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('page');
            $page_type_article->setParent($parent);
        } else {
            $this->writeLine('Page type with tag article already exists');
            $this->setDataKey('page_type_article_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('annotation');
        $property_id = $property->getId();
        $tmp = $page_type_article->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder('1');
            $tmp->setRequired(false);
            $page_type_article->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('text');
        $property_id = $property->getId();
        $tmp = $page_type_article->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder('2');
            $tmp->setRequired(false);
            $page_type_article->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($page_type_article);
        // set template on MAIN PAGE index::index
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('article'), 'index', 'index');

        // page type: Kategórie (page_category)
        $page_type_page_category = $this->pageTypesManager()->pageTypeExistsByTag('page_category');
        if ($page_type_page_category === false) {
            $page_type_page_category = new PageType();
            $page_type_page_category->setTag('page_category');
            $page_type_page_category->setName('Kategórie');
            $page_type_page_category->setPageClassId('1');
            $page_type_page_category->setDefaultTemplateId('2');
            $page_type_page_category->setDeleteTrigger('');
            $page_type_page_category->setIncludeInSync(NULL);
            $page_type_page_category->setPageDetailsLayout('');
            $page_type_page_category->setPageSortTypeTag('sort_date_time');
            $page_type_page_category->setPageTypeOrder('0');
            $page_type_page_category->setPostmoveTrigger('');
            $page_type_page_category->setPostsubmitTrigger('');
            $page_type_page_category->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('page');
            $page_type_page_category->setParent($parent);
        } else {
            $this->writeLine('Page type with tag page_category already exists');
            $this->setDataKey('page_type_page_category_existed', true);
        }
        $this->pageTypesManager()->savePageType($page_type_page_category);
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('page_category'), 'index', 'list');

        // page type: Rubrika (rubric)
        $page_type_rubric = $this->pageTypesManager()->pageTypeExistsByTag('rubric');
        if ($page_type_rubric === false) {
            $page_type_rubric = new PageType();
            $page_type_rubric->setTag('rubric');
            $page_type_rubric->setName('Rubrika');
            $page_type_rubric->setPageClassId('1');
            $page_type_rubric->setDefaultTemplateId('2');
            $page_type_rubric->setDeleteTrigger('');
            $page_type_rubric->setIncludeInSync('0');
            $page_type_rubric->setPageDetailsLayout('');
            $page_type_rubric->setPageSortTypeTag('sort_date_time');
            $page_type_rubric->setPageTypeOrder('5');
            $page_type_rubric->setPostmoveTrigger('');
            $page_type_rubric->setPostsubmitTrigger('');
            $page_type_rubric->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('page_category');
            $page_type_rubric->setParent($parent);
        } else {
            $this->writeLine('Page type with tag rubric already exists');
            $this->setDataKey('page_type_rubric_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('annotation');
        $property_id = $property->getId();
        $tmp = $page_type_rubric->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder('2');
            $tmp->setRequired(false);
            $page_type_rubric->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('text');
        $property_id = $property->getId();
        $tmp = $page_type_rubric->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder('3');
            $tmp->setRequired(false);
            $page_type_rubric->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($page_type_rubric);
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('rubric'), 'index', 'list');

        // page type: Sekcia (section)
        $page_type_section = $this->pageTypesManager()->pageTypeExistsByTag('section');
        if ($page_type_section === false) {
            $page_type_section = new PageType();
            $page_type_section->setTag('section');
            $page_type_section->setName('Sekcia');
            $page_type_section->setPageClassId('1');
            $page_type_section->setDefaultTemplateId('2');
            $page_type_section->setDeleteTrigger('');
            $page_type_section->setIncludeInSync('0');
            $page_type_section->setPageDetailsLayout('');
            $page_type_section->setPageSortTypeTag('sort_date_time');
            $page_type_section->setPageTypeOrder('1');
            $page_type_section->setPostmoveTrigger('');
            $page_type_section->setPostsubmitTrigger('');
            $page_type_section->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('page_category');
            $page_type_section->setParent($parent);
        } else {
            $this->writeLine('Page type with tag section already exists');
            $this->setDataKey('page_type_section_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('annotation');
        $property_id = $property->getId();
        $tmp = $page_type_section->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder('1');
            $tmp->setRequired(false);
            $page_type_section->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('text');
        $property_id = $property->getId();
        $tmp = $page_type_section->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder('2');
            $tmp->setRequired(false);
            $page_type_section->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($page_type_section);
        // set template on MAIN PAGE index::list
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('section'), 'index', 'list');

        if ($this->pageTypeExists('horne_menu')) {
            $this->addPageTypeSuperiorPageType('article', 'horne_menu');
        }
        if ($this->pageTypeExists('container')) {
            $this->addPageTypeSuperiorPageType('article', 'container');
        }
        if ($this->pageTypeExists('folder')) {
            $this->addPageTypeSuperiorPageType('article', 'folder');
        }
        if ($this->pageTypeExists('rubric')) {
            $this->addPageTypeSuperiorPageType('article', 'rubric');
        }
        if ($this->pageTypeExists('section')) {
            $this->addPageTypeSuperiorPageType('article', 'section');
        }
        if ($this->pageTypeExists('main_page')) {
            $this->addPageTypeSuperiorPageType('article', 'main_page');
        }
        if ($this->pageTypeExists('folder')) {
            $this->addPageTypeSuperiorPageType('page_category', 'folder');
        }
        if ($this->pageTypeExists('main_page')) {
            $this->addPageTypeSuperiorPageType('page_category', 'main_page');
        }
        if ($this->pageTypeExists('section')) {
            $this->addPageTypeSuperiorPageType('rubric', 'section');
        }
        if ($this->pageTypeExists('horne_menu')) {
            $this->addPageTypeSuperiorPageType('section', 'horne_menu');
        }
        if ($this->pageTypeExists('page_category')) {
            $this->addPageTypeSuperiorPageType('section', 'page_category');
        }
        if ($this->pageTypeExists('folder')) {
            $this->addPageTypeSuperiorPageType('section', 'folder');
        }
        if ($this->pageTypeExists('main_page')) {
            $this->addPageTypeSuperiorPageType('section', 'main_page');
        }

        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

    }

    public function down() {
        // remove page type: Sekcia (section)
        $page_type_section = $this->pageTypesManager()->pageTypeExistsByTag('section');
        if (($page_type_section != false) && (is_null($this->getDataKey('page_type_section_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_section);
        }

        // remove page type: Rubrika (rubric)
        $page_type_rubric = $this->pageTypesManager()->pageTypeExistsByTag('rubric');
        if (($page_type_rubric != false) && (is_null($this->getDataKey('page_type_rubric_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_rubric);
        }

        // remove page type: Kategórie (page_category)
        $page_type_page_category = $this->pageTypesManager()->pageTypeExistsByTag('page_category');
        if (($page_type_page_category != false) && (is_null($this->getDataKey('page_type_page_category_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_page_category);
        }

        // remove page type: Článok (article)
        $page_type_article = $this->pageTypesManager()->pageTypeExistsByTag('article');
        if (($page_type_article != false) && (is_null($this->getDataKey('page_type_article_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_article);
        }

        // remove property: Text(text)
        $property_text = $this->propertyManager()->propertyExistsByTag('text');
        if ($property_text != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_text);
            if ((is_null($this->getDataKey('property_text_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_text);
            }
        }

        // remove property: Anotácia(annotation)
        $property_annotation = $this->propertyManager()->propertyExistsByTag('annotation');
        if ($property_annotation != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_annotation);
            if ((is_null($this->getDataKey('property_annotation_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_annotation);
            }
        }

        // remove page type: Stránka (page)
        $page_type_page = $this->pageTypesManager()->pageTypeExistsByTag('page');
        if (($page_type_page != false) && (is_null($this->getDataKey('page_type_page_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_page);
        }

        // remove property: SEO URL name(seo_url_name)
        $property_seo_url_name = $this->propertyManager()->propertyExistsByTag('seo_url_name');
        if ($property_seo_url_name != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_seo_url_name);
            if ((is_null($this->getDataKey('property_seo_url_name_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_seo_url_name);
            }
        }

        // remove property: Titulok(title)
        $property_title = $this->propertyManager()->propertyExistsByTag('title');
        if ($property_title != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_title);
            if ((is_null($this->getDataKey('property_title_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_title);
            }
        }

        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

    }

}
