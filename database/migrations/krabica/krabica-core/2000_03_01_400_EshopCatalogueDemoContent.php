<?php

namespace Krabica\Migrations;

use Buxus\Migration\AbstractMigration;
use Buxus\Page\PageInterface;
use Buxus\Util\PageIds;

class EshopCatalogueDemoContent extends AbstractMigration
{
    public function dependencies()
    {
        return array(
            \Eshop\Catalog\Migrations\EshopCatalogue::class,
            \Eshop\Catalog\Migrations\VariantProductType::class,
            \PhotoGallery\Migrations\PhotoGallery::class,
        );
    }

    public function up()
    {
        // page: Katalóg produktov(ID: 38 TAG: Katalóg produktov)
        $catalogue_page_id = $this->getPageIdByTag('eshop_catalog');
        if ($catalogue_page_id === NULL) {
            throw new \Exception('Unable to find: Katalóg produktov');
        }

        // page: Hríby(ID: 175 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_category');
        $page_175 = \PageFactory::create($catalogue_page_id, $page_type->getId());
        $page_175->setPageName('Hríby');
        $page_175->setPageTag(false);
        $page_175->setPageStateId('1');
        $page_175->setPageClassId('1');
        $page_175->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_175->setValue('title', 'Hríby');
        $page_175->setValue('seo_url_name', '/katalog-produktov/hriby');
        $page_175->setValue('eshop_vat_rate', '20');
        $page_175->save();

        // page: Muchotrávka(ID: 176 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_product');
        $page_176 = \PageFactory::create($page_175->getPageId(), $page_type->getId());
        $page_176->setPageName('Muchotrávka');
        $page_176->setPageTag(false);
        $page_176->setPageStateId('1');
        $page_176->setPageClassId('1');
        $page_176->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_176->setValue('title', 'Muchotrávka');
        $page_176->setValue('seo_url_name', '/katalog-produktov/hriby/muchotravka');
        $page_176->setValue('photo_album_thumbnail', 'demo_content/obrazky_produktov_detail/muchotravka1.jpg');
        $page_176->setValue('eshop_eur_price_without_vat', '1.5');
        $page_176->setValue('eshop_product_akcia', 'F');
        $page_176->setValue('availability', '3');
        $page_176->setValue('eshop_detail_text', '<p>Cras vel molestie tortor. Nullam pulvinar metus mauris, sed fringilla ex laoreet et. Maecenas rutrum viverra tincidunt. Nam et arcu tellus. Aliquam ut iaculis orci, at lobortis eros. Pellentesque ligula purus, laoreet eget nisl non, condimentum dapibus lacus. Proin velit erat, convallis eget ultricies quis, venenatis vel ipsum. Curabitur ultrices laoreet mauris, id lobortis massa eleifend et. Morbi finibus felis sit amet bibendum bibendum. Etiam dapibus at nunc et aliquam.</p>');
        $page_176->setValue('eshop_vat_rate', '20');
        // set template on MAIN PAGE eshop_catalog::product
        $page_176->save();

        $this->createVariants($page_176);

        // page: demo_content/obrazky_produktov_detail/muchotravka3.jpg(ID: 179 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_179 = \PageFactory::create($page_176->getPageId(), $page_type->getId());
        $page_179->setPageName('demo_content/obrazky_produktov_detail/muchotravka3.jpg');
        $page_179->setPageTag(false);
        $page_179->setPageStateId('1');
        $page_179->setPageClassId('1');
        $page_179->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_179->setValue('title', 'Muchotravka3');
        $page_179->setValue('photo_file', 'demo_content/obrazky_produktov_detail/muchotravka3.jpg');
        $page_179->setValue('photo_alt', 'Muchotrávka - Muchotravka3');
        $page_179->save();

        // page: demo_content/obrazky_produktov_detail/muchotravka2.jpg(ID: 178 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_178 = \PageFactory::create($page_176->getPageId(), $page_type->getId());
        $page_178->setPageName('demo_content/obrazky_produktov_detail/muchotravka2.jpg');
        $page_178->setPageTag(false);
        $page_178->setPageStateId('1');
        $page_178->setPageClassId('1');
        $page_178->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_178->setValue('title', 'Muchotravka2');
        $page_178->setValue('photo_file', 'demo_content/obrazky_produktov_detail/muchotravka2.jpg');
        $page_178->setValue('photo_alt', 'Muchotrávka - Muchotravka2');
        $page_178->save();

        // page: demo_content/obrazky_produktov_detail/muchotravka1.jpg(ID: 177 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_177 = \PageFactory::create($page_176->getPageId(), $page_type->getId());
        $page_177->setPageName('demo_content/obrazky_produktov_detail/muchotravka1.jpg');
        $page_177->setPageTag(false);
        $page_177->setPageStateId('1');
        $page_177->setPageClassId('1');
        $page_177->setSortDateTime(date('Y-m-d H:i:s', time() + 2));
        $page_177->setValue('title', 'Muchotravka1');
        $page_177->setValue('photo_file', 'demo_content/obrazky_produktov_detail/muchotravka1.jpg');
        $page_177->setValue('photo_alt', 'Muchotrávka - Muchotravka1');
        $page_177->save();

        // page: Šampióny(ID: 180 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_product');
        $page_180 = \PageFactory::create($page_175->getPageId(), $page_type->getId());
        $page_180->setPageName('Šampióny');
        $page_180->setPageTag(false);
        $page_180->setPageStateId('1');
        $page_180->setPageClassId('1');
        $page_180->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_180->setValue('title', 'Šampióny');
        $page_180->setValue('seo_url_name', '/katalog-produktov/hriby/sampiony');
        $page_180->setValue('photo_album_thumbnail', 'demo_content/obrazky_produktov_detail/sampiony3.png');
        $page_180->setValue('eshop_eur_price_without_vat', '145');
        $page_180->setValue('eshop_product_akcia', 'T');
        $page_180->setValue('eshop_eur_action_price_without_vat', '99');
        $page_180->setValue('availability', '1');
        $page_180->setValue('eshop_detail_text', '<p>Cras vel molestie tortor. Nullam pulvinar metus mauris, sed fringilla ex laoreet et. Maecenas rutrum viverra tincidunt. Nam et arcu tellus. Aliquam ut iaculis orci, at lobortis eros. Pellentesque ligula purus, laoreet eget nisl non, condimentum dapibus lacus. Proin velit erat, convallis eget ultricies quis, venenatis vel ipsum. Curabitur ultrices laoreet mauris, id lobortis massa eleifend et. Morbi finibus felis sit amet bibendum bibendum. Etiam dapibus at nunc et aliquam.</p>');
        $page_180->setValue('eshop_vat_rate', '20');
        // set template on MAIN PAGE eshop_catalog::product
        $page_180->save();

        $this->createVariants($page_180);

        // page: demo_content/obrazky_produktov_detail/sampiony3.png(ID: 183 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_183 = \PageFactory::create($page_180->getPageId(), $page_type->getId());
        $page_183->setPageName('demo_content/obrazky_produktov_detail/sampiony3.png');
        $page_183->setPageTag(false);
        $page_183->setPageStateId('1');
        $page_183->setPageClassId('1');
        $page_183->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_183->setValue('photo_file', 'demo_content/obrazky_produktov_detail/sampiony3.png');
        $page_183->setValue('photo_alt', 'Šampióny - Sampiony3');
        $page_183->setValue('title', 'Sampiony3');
        $page_183->save();

        // page: demo_content/obrazky_produktov_detail/sampiony2.jpg(ID: 182 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_182 = \PageFactory::create($page_180->getPageId(), $page_type->getId());
        $page_182->setPageName('demo_content/obrazky_produktov_detail/sampiony2.jpg');
        $page_182->setPageTag(false);
        $page_182->setPageStateId('1');
        $page_182->setPageClassId('1');
        $page_182->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_182->setValue('title', 'Sampiony2');
        $page_182->setValue('photo_file', 'demo_content/obrazky_produktov_detail/sampiony2.jpg');
        $page_182->setValue('photo_alt', 'Šampióny - Sampiony2');
        $page_182->save();

        // page: demo_content/obrazky_produktov_detail/sampiony1.jpg(ID: 181 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_181 = \PageFactory::create($page_180->getPageId(), $page_type->getId());
        $page_181->setPageName('demo_content/obrazky_produktov_detail/sampiony1.jpg');
        $page_181->setPageTag(false);
        $page_181->setPageStateId('1');
        $page_181->setPageClassId('1');
        $page_181->setSortDateTime(date('Y-m-d H:i:s', time() + 2));
        $page_181->setValue('title', 'Sampiony1');
        $page_181->setValue('photo_file', 'demo_content/obrazky_produktov_detail/sampiony1.jpg');
        $page_181->setValue('photo_alt', 'Šampióny - Sampiony1');
        $page_181->save();

        // page: Hríb dubový(ID: 184 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_product');
        $page_184 = \PageFactory::create($page_175->getPageId(), $page_type->getId());
        $page_184->setPageName('Hríb dubový');
        $page_184->setPageTag(false);
        $page_184->setPageStateId('1');
        $page_184->setPageClassId('1');
        $page_184->setSortDateTime(date('Y-m-d H:i:s', time() + 2));
        $page_184->setValue('title', 'Hríb dubový');
        $page_184->setValue('eshop_eur_price_without_vat', '47');
        $page_184->setValue('eshop_product_akcia', 'F');
        $page_184->setValue('availability', '2');
        $page_184->setValue('eshop_detail_text', '<p><span>Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Quisque eget fermentum odio. Vestibulum eleifend congue posuere. Integer dignissim sed odio sit amet eleifend. Vestibulum euismod orci sapien, quis auctor augue feugiat vel. Duis eu sapien elit. Duis massa ante, commodo id luctus non, pulvinar sed mi. Sed a nulla ac ligula vulputate convallis. Quisque eget finibus nibh, non dignissim elit. Ut sagittis finibus erat, quis finibus mauris dapibus nec.</span></p>');
        $page_184->setValue('seo_url_name', '/katalog-produktov/hriby/hrib-dubovy');
        $page_184->setValue('photo_album_thumbnail', 'demo_content/obrazky_produktov_detail/dubak1.jpg');
        $page_184->setValue('eshop_vat_rate', '20');
        // set template on MAIN PAGE eshop_catalog::product
        $page_184->save();

        $this->createVariants($page_184);

        // page: demo_content/obrazky_produktov_detail/dubak3.jpg(ID: 187 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_187 = \PageFactory::create($page_184->getPageId(), $page_type->getId());
        $page_187->setPageName('demo_content/obrazky_produktov_detail/dubak3.jpg');
        $page_187->setPageTag(false);
        $page_187->setPageStateId('1');
        $page_187->setPageClassId('1');
        $page_187->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_187->setValue('photo_file', 'demo_content/obrazky_produktov_detail/dubak3.jpg');
        $page_187->setValue('photo_alt', 'Hríb dubový - Dubak3');
        $page_187->setValue('title', 'Dubak3');
        $page_187->save();

        // page: demo_content/obrazky_produktov_detail/dubak2.jpg(ID: 186 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_186 = \PageFactory::create($page_184->getPageId(), $page_type->getId());
        $page_186->setPageName('demo_content/obrazky_produktov_detail/dubak2.jpg');
        $page_186->setPageTag(false);
        $page_186->setPageStateId('1');
        $page_186->setPageClassId('1');
        $page_186->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_186->setValue('photo_file', 'demo_content/obrazky_produktov_detail/dubak2.jpg');
        $page_186->setValue('photo_alt', 'Hríb dubový - Dubak2');
        $page_186->setValue('title', 'Dubak2');
        $page_186->save();

        // page: demo_content/obrazky_produktov_detail/dubak1.jpg(ID: 185 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_185 = \PageFactory::create($page_184->getPageId(), $page_type->getId());
        $page_185->setPageName('demo_content/obrazky_produktov_detail/dubak1.jpg');
        $page_185->setPageTag(false);
        $page_185->setPageStateId('1');
        $page_185->setPageClassId('1');
        $page_185->setSortDateTime(date('Y-m-d H:i:s', time() + 2));
        $page_185->setValue('photo_file', 'demo_content/obrazky_produktov_detail/dubak1.jpg');
        $page_185->setValue('photo_alt', 'Hríb dubový - Dubak1');
        $page_185->setValue('title', 'Dubak1');
        $page_185->save();

        // page: Zelenina(ID: 83 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_category');
        $page_83 = \PageFactory::create($catalogue_page_id, $page_type->getId());
        $page_83->setPageName('Zelenina');
        $page_83->setPageTag('');
        $page_83->setPageStateId('1');
        $page_83->setPageClassId('1');
        $page_83->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_83->setValue('title', 'Zelenina');
        $page_83->setValue('eshop_vat_rate', '20');
        $page_83->save();

        // page: Uhorka(ID: 84 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_product');
        $page_84 = \PageFactory::create($page_83->getPageId(), $page_type->getId());
        $page_84->setPageName('Uhorka');
        $page_84->setPageTag(false);
        $page_84->setPageStateId('1');
        $page_84->setPageClassId('1');
        $page_84->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_84->setValue('title', 'Uhorka');
        $page_84->setValue('seo_url_name', '/katalog-produktov/zelenina/uhorka');
        $page_84->setValue('photo_album_thumbnail', 'demo_content/obrazky_produktov_detail/uhorka1.jpg');
        $page_84->setValue('eshop_vat_rate', '20');
        $page_84->setValue('eshop_eur_price_without_vat', '47');
        $page_84->setValue('eshop_product_akcia', 'F');
        $page_84->setValue('availability', '1');
        $page_84->setValue('eshop_detail_text', '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc cursus risus vitae risus dapibus, in lobortis velit auctor. Cras scelerisque sapien hendrerit, congue nunc ut, eleifend purus. Proin pellentesque sem elit, sed auctor dolor convallis accumsan. Maecenas tincidunt ex sed tempor lobortis. Nunc pretium pellentesque nibh, vel volutpat lectus tempor gravida. Vivamus molestie elementum mattis. Vivamus blandit aliquet purus, nec bibendum lorem egestas nec. Donec pellentesque porttitor convallis. Mauris sed quam hendrerit, bibendum nibh nec, aliquam purus. Nam laoreet congue quam, sit amet vestibulum ex lobortis et. Vivamus a orci et eros sagittis mattis sed eget urna. Cras sem risus, mattis sit amet lobortis eu, tincidunt et ipsum. Curabitur sit amet ipsum a nisi sodales hendrerit sed eu purus. Integer tincidunt vel erat ac vehicula. Nulla ligula eros, molestie sit amet sagittis sit amet, interdum ut nisi.&nbsp;</p>');
        // set template on MAIN PAGE eshop_catalog::product
        $page_84->save();

        $this->createVariants($page_84);

        // page: demo_content/obrazky_produktov_detail/uhorka3.jpg(ID: 86 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_86 = \PageFactory::create($page_84->getPageId(), $page_type->getId());
        $page_86->setPageName('demo_content/obrazky_produktov_detail/uhorka3.jpg');
        $page_86->setPageTag('');
        $page_86->setPageStateId('1');
        $page_86->setPageClassId('1');
        $page_86->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_86->setValue('title', 'Uhorka3');
        $page_86->setValue('photo_file', 'demo_content/obrazky_produktov_detail/uhorka3.jpg');
        $page_86->setValue('photo_alt', 'Uhorka - Uhorka3');
        $page_86->save();

        // page: demo_content/obrazky_produktov_detail/uhorka2.jpg(ID: 188 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_188 = \PageFactory::create($page_84->getPageId(), $page_type->getId());
        $page_188->setPageName('demo_content/obrazky_produktov_detail/uhorka2.jpg');
        $page_188->setPageTag(false);
        $page_188->setPageStateId('1');
        $page_188->setPageClassId('1');
        $page_188->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_188->setValue('photo_alt', 'Uhorka - Uhorka2');
        $page_188->setValue('photo_file', 'demo_content/obrazky_produktov_detail/uhorka2.jpg');
        $page_188->setValue('title', 'Uhorka2');
        $page_188->save();

        // page: demo_content/obrazky_produktov_detail/uhorka1.jpg(ID: 87 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_87 = \PageFactory::create($page_84->getPageId(), $page_type->getId());
        $page_87->setPageName('demo_content/obrazky_produktov_detail/uhorka1.jpg');
        $page_87->setPageTag('');
        $page_87->setPageStateId('1');
        $page_87->setPageClassId('1');
        $page_87->setSortDateTime(date('Y-m-d H:i:s', time() + 2));
        $page_87->setValue('title', 'Uhorka1');
        $page_87->setValue('photo_file', 'demo_content/obrazky_produktov_detail/uhorka1.jpg');
        $page_87->setValue('photo_alt', 'Uhorka - Uhorka1');
        $page_87->save();

        // page: Reďkovka(ID: 88 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_product');
        $page_88 = \PageFactory::create($page_83->getPageId(), $page_type->getId());
        $page_88->setPageName('Reďkovka');
        $page_88->setPageTag('');
        $page_88->setPageStateId('1');
        $page_88->setPageClassId('1');
        $page_88->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_88->setValue('title', 'Reďkovka');
        $page_88->setValue('seo_url_name', '/katalog-produktov/zelenina/redkovka');
        $page_88->setValue('photo_album_thumbnail', 'demo_content/obrazky_produktov_detail/redkovka2.jpg');
        $page_88->setValue('eshop_vat_rate', '20');
        $page_88->setValue('eshop_eur_price_without_vat', '49');
        $page_88->setValue('eshop_product_akcia', 'T');
        $page_88->setValue('eshop_eur_action_price_without_vat', '45');
        $page_88->setValue('availability', '1');
        // set template on MAIN PAGE eshop_catalog::product
        $page_88->save();

        $this->createVariants($page_88);

        // page: demo_content/obrazky_produktov_detail/redkovka3.jpg(ID: 89 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_89 = \PageFactory::create($page_88->getPageId(), $page_type->getId());
        $page_89->setPageName('demo_content/obrazky_produktov_detail/redkovka3.jpg');
        $page_89->setPageTag('');
        $page_89->setPageStateId('1');
        $page_89->setPageClassId('1');
        $page_89->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_89->setValue('title', 'Redkovka3');
        $page_89->setValue('photo_file', 'demo_content/obrazky_produktov_detail/redkovka3.jpg');
        $page_89->setValue('photo_alt', 'Reďkovka - Redkovka3');
        $page_89->save();

        // page: demo_content/obrazky_produktov_detail/redkovka2.jpg(ID: 90 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_90 = \PageFactory::create($page_88->getPageId(), $page_type->getId());
        $page_90->setPageName('demo_content/obrazky_produktov_detail/redkovka2.jpg');
        $page_90->setPageTag('');
        $page_90->setPageStateId('1');
        $page_90->setPageClassId('1');
        $page_90->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_90->setValue('title', 'Redkovka2');
        $page_90->setValue('photo_file', 'demo_content/obrazky_produktov_detail/redkovka2.jpg');
        $page_90->setValue('photo_alt', 'Reďkovka - Redkovka2');
        $page_90->save();

        // page: demo_content/obrazky_produktov_detail/redkovka1.jpg(ID: 91 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_91 = \PageFactory::create($page_88->getPageId(), $page_type->getId());
        $page_91->setPageName('demo_content/obrazky_produktov_detail/redkovka1.jpg');
        $page_91->setPageTag('');
        $page_91->setPageStateId('1');
        $page_91->setPageClassId('1');
        $page_91->setSortDateTime(date('Y-m-d H:i:s', time() + 2));
        $page_91->setValue('title', 'Redkovka1');
        $page_91->setValue('photo_file', 'demo_content/obrazky_produktov_detail/redkovka1.jpg');
        $page_91->setValue('photo_alt', 'Reďkovka - Redkovka1');
        $page_91->save();

        // page: Paprika(ID: 92 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_product');
        $page_92 = \PageFactory::create($page_83->getPageId(), $page_type->getId());
        $page_92->setPageName('Paprika');
        $page_92->setPageTag('');
        $page_92->setPageStateId('1');
        $page_92->setPageClassId('1');
        $page_92->setSortDateTime(date('Y-m-d H:i:s', time() + 2));
        $page_92->setValue('title', 'Paprika');
        $page_92->setValue('seo_url_name', '/katalog-produktov/zelenina/paprika');
        $page_92->setValue('photo_album_thumbnail', 'demo_content/obrazky_produktov_detail/paprika1.jpg');
        $page_92->setValue('eshop_vat_rate', '20');
        $page_92->setValue('eshop_eur_price_without_vat', '98');
        $page_92->setValue('eshop_product_akcia', 'T');
        $page_92->setValue('eshop_eur_action_price_without_vat', '74');
        $page_92->setValue('availability', '1');
        // set template on MAIN PAGE eshop_catalog::product
        $page_92->save();

        $this->createVariants($page_92);

        // page: demo_content/obrazky_produktov_detail/paprika3.jpg(ID: 93 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_93 = \PageFactory::create($page_92->getPageId(), $page_type->getId());
        $page_93->setPageName('demo_content/obrazky_produktov_detail/paprika3.jpg');
        $page_93->setPageTag('');
        $page_93->setPageStateId('1');
        $page_93->setPageClassId('1');
        $page_93->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_93->setValue('title', 'Paprika3');
        $page_93->setValue('photo_file', 'demo_content/obrazky_produktov_detail/paprika3.jpg');
        $page_93->setValue('photo_alt', 'Paprika - Paprika3');
        $page_93->save();

        // page: demo_content/obrazky_produktov_detail/paprika2.jpg(ID: 94 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_94 = \PageFactory::create($page_92->getPageId(), $page_type->getId());
        $page_94->setPageName('demo_content/obrazky_produktov_detail/paprika2.jpg');
        $page_94->setPageTag('');
        $page_94->setPageStateId('1');
        $page_94->setPageClassId('1');
        $page_94->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_94->setValue('title', 'Paprika2');
        $page_94->setValue('photo_file', 'demo_content/obrazky_produktov_detail/paprika2.jpg');
        $page_94->setValue('photo_alt', 'Paprika - Paprika2');
        $page_94->save();

        // page: demo_content/obrazky_produktov_detail/paprika1.jpg(ID: 95 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_95 = \PageFactory::create($page_92->getPageId(), $page_type->getId());
        $page_95->setPageName('demo_content/obrazky_produktov_detail/paprika1.jpg');
        $page_95->setPageTag('');
        $page_95->setPageStateId('1');
        $page_95->setPageClassId('1');
        $page_95->setSortDateTime(date('Y-m-d H:i:s', time() + 2));
        $page_95->setValue('title', 'Paprika1');
        $page_95->setValue('photo_file', 'demo_content/obrazky_produktov_detail/paprika1.jpg');
        $page_95->setValue('photo_alt', 'Paprika - Paprika1');
        $page_95->save();

        // page: Chutná zelenina(ID: 166 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_subcategory');
        $page_166 = \PageFactory::create($page_83->getPageId(), $page_type->getId());
        $page_166->setPageName('Chutná zelenina');
        $page_166->setPageTag(false);
        $page_166->setPageStateId('1');
        $page_166->setPageClassId('1');
        $page_166->setSortDateTime(date('Y-m-d H:i:s', time() + 3));
        $page_166->setValue('title', 'Chutná zelenina');
        $page_166->setValue('seo_url_name', '/katalog-produktov/zelenina/chutna-zelenina');
        $page_166->setValue('eshop_vat_rate', '20');
        $page_166->setValue('promo_bg_image', 'demo_content/obrazky_produktov_detail/uhorka2.jpg');
        $page_166->save();

        // page: Mrkva(ID: 167 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_product');
        $page_167 = \PageFactory::create($page_166->getPageId(), $page_type->getId());
        $page_167->setPageName('Mrkva');
        $page_167->setPageTag(false);
        $page_167->setPageStateId('1');
        $page_167->setPageClassId('1');
        $page_167->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_167->setValue('title', 'Mrkva');
        $page_167->setValue('seo_url_name', '/katalog-produktov/zelenina/chutna-zelenina/mrkva');
        $page_167->setValue('photo_album_thumbnail', 'demo_content/obrazky_produktov_detail/carrot1.jpg');
        $page_167->setValue('eshop_eur_price_without_vat', '4.50');
        $page_167->setValue('eshop_product_akcia', 'T');
        $page_167->setValue('availability', '2');
        $page_167->setValue('eshop_detail_text', '<p><span>Morbi accumsan, ante eu dapibus fringilla, tellus leo pharetra arcu, sed faucibus turpis lacus eu lectus. Phasellus eu ante feugiat, ullamcorper nisi id, viverra nunc. Nullam tincidunt ante in porttitor rutrum. Proin sit amet tortor elit. Nam mattis condimentum semper. Quisque pellentesque eget nisl in rutrum. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Etiam ut orci eros. Curabitur semper tortor nisl, quis dictum odio pharetra in. Vivamus suscipit eros ut congue convallis. Curabitur dictum quam id urna dignissim condimentum. Ut ut pellentesque turpis. Praesent mattis velit vel leo malesuada imperdiet. Vivamus molestie lacinia justo, ac ultrices purus fringilla a. Donec sit amet ipsum dapibus, interdum est nec, consectetur quam.</span></p>');
        $page_167->setValue('eshop_vat_rate', '20');
        // set template on MAIN PAGE eshop_catalog::product
        $page_167->save();

        $this->createVariants($page_167);

        // page: demo_content/obrazky_produktov_detail/carrot3.png(ID: 170 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_170 = \PageFactory::create($page_167->getPageId(), $page_type->getId());
        $page_170->setPageName('demo_content/obrazky_produktov_detail/carrot3.png');
        $page_170->setPageTag(false);
        $page_170->setPageStateId('1');
        $page_170->setPageClassId('1');
        $page_170->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_170->setValue('title', 'Mrkva');
        $page_170->setValue('photo_file', 'demo_content/obrazky_produktov_detail/carrot3.png');
        $page_170->setValue('photo_alt', 'Mrkva - Carrot3');
        $page_170->save();

        // page: demo_content/obrazky_produktov_detail/carrot2.jpg(ID: 169 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_169 = \PageFactory::create($page_167->getPageId(), $page_type->getId());
        $page_169->setPageName('demo_content/obrazky_produktov_detail/carrot2.jpg');
        $page_169->setPageTag(false);
        $page_169->setPageStateId('1');
        $page_169->setPageClassId('1');
        $page_169->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_169->setValue('title', 'Mrkva');
        $page_169->setValue('photo_file', 'demo_content/obrazky_produktov_detail/carrot2.jpg');
        $page_169->setValue('photo_alt', 'Mrkva - Carrot2');
        $page_169->save();

        // page: demo_content/obrazky_produktov_detail/carrot1.jpg(ID: 168 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_168 = \PageFactory::create($page_167->getPageId(), $page_type->getId());
        $page_168->setPageName('demo_content/obrazky_produktov_detail/carrot1.jpg');
        $page_168->setPageTag(false);
        $page_168->setPageStateId('1');
        $page_168->setPageClassId('1');
        $page_168->setSortDateTime(date('Y-m-d H:i:s', time() + 2));
        $page_168->setValue('title', 'Mrkva');
        $page_168->setValue('photo_file', 'demo_content/obrazky_produktov_detail/carrot1.jpg');
        $page_168->setValue('photo_alt', 'Mrkva - Carrot1');
        $page_168->save();

        // page: Kaleráb(ID: 171 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_product');
        $page_171 = \PageFactory::create($page_166->getPageId(), $page_type->getId());
        $page_171->setPageName('Kaleráb');
        $page_171->setPageTag(false);
        $page_171->setPageStateId('1');
        $page_171->setPageClassId('1');
        $page_171->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_171->setValue('title', 'Kaleráb');
        $page_171->setValue('seo_url_name', '/katalog-produktov/zelenina/chutna-zelenina/kalerab');
        $page_171->setValue('photo_album_thumbnail', 'demo_content/obrazky_produktov_detail/kalerab1.jpg');
        $page_171->setValue('eshop_eur_price_without_vat', '48');
        $page_171->setValue('eshop_product_akcia', 'F');
        $page_171->setValue('availability', '2');
        $page_171->setValue('eshop_detail_text', '<p><span>Cras vel molestie tortor. Nullam pulvinar metus mauris, sed fringilla ex laoreet et. Maecenas rutrum viverra tincidunt. Nam et arcu tellus. Aliquam ut iaculis orci, at lobortis eros. Pellentesque ligula purus, laoreet eget nisl non, condimentum dapibus lacus. Proin velit erat, convallis eget ultricies quis, venenatis vel ipsum. Curabitur ultrices laoreet mauris, id lobortis massa eleifend et. Morbi finibus felis sit amet bibendum bibendum. Etiam dapibus at nunc et aliquam.</span></p>');
        $page_171->setValue('eshop_vat_rate', '20');
        // set template on MAIN PAGE eshop_catalog::product
        $page_171->save();

        $this->createVariants($page_171);

        // page: demo_content/obrazky_produktov_detail/kalerab3.jpg(ID: 174 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_174 = \PageFactory::create($page_171->getPageId(), $page_type->getId());
        $page_174->setPageName('demo_content/obrazky_produktov_detail/kalerab3.jpg');
        $page_174->setPageTag(false);
        $page_174->setPageStateId('1');
        $page_174->setPageClassId('1');
        $page_174->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_174->setValue('title', 'Kalerab3');
        $page_174->setValue('photo_file', 'demo_content/obrazky_produktov_detail/kalerab3.jpg');
        $page_174->setValue('photo_alt', 'Kaleráb - Kalerab3');
        $page_174->save();

        // page: demo_content/obrazky_produktov_detail/kalerab2.jpg(ID: 173 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_173 = \PageFactory::create($page_171->getPageId(), $page_type->getId());
        $page_173->setPageName('demo_content/obrazky_produktov_detail/kalerab2.jpg');
        $page_173->setPageTag(false);
        $page_173->setPageStateId('1');
        $page_173->setPageClassId('1');
        $page_173->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_173->setValue('title', 'Kalerab2');
        $page_173->setValue('photo_file', 'demo_content/obrazky_produktov_detail/kalerab2.jpg');
        $page_173->setValue('photo_alt', 'Kaleráb - Kalerab2');
        $page_173->save();

        // page: demo_content/obrazky_produktov_detail/kalerab1.jpg(ID: 172 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_172 = \PageFactory::create($page_171->getPageId(), $page_type->getId());
        $page_172->setPageName('demo_content/obrazky_produktov_detail/kalerab1.jpg');
        $page_172->setPageTag(false);
        $page_172->setPageStateId('1');
        $page_172->setPageClassId('1');
        $page_172->setSortDateTime(date('Y-m-d H:i:s', time() + 2));
        $page_172->setValue('title', 'Kalerab1');
        $page_172->setValue('photo_file', 'demo_content/obrazky_produktov_detail/kalerab1.jpg');
        $page_172->setValue('photo_alt', 'Kaleráb - Kalerab1');
        $page_172->save();

        // page: Ovocie(ID: 96 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_category');
        $page_96 = \PageFactory::create($catalogue_page_id, $page_type->getId());
        $page_96->setPageName('Ovocie');
        $page_96->setPageTag('');
        $page_96->setPageStateId('1');
        $page_96->setPageClassId('1');
        $page_96->setSortDateTime(date('Y-m-d H:i:s', time() + 2));
        $page_96->setValue('title', 'Ovocie');
        $page_96->setValue('eshop_vat_rate', '20');
        $page_96->save();

        // page: Broskyne(ID: 97 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_product');
        $page_97 = \PageFactory::create($page_96->getPageId(), $page_type->getId());
        $page_97->setPageName('Broskyne');
        $page_97->setPageTag('');
        $page_97->setPageStateId('1');
        $page_97->setPageClassId('1');
        $page_97->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_97->setValue('title', 'Broskyne');
        $page_97->setValue('seo_url_name', '/ovocie/broskyne');
        $page_97->setValue('photo_album_thumbnail', 'demo_content/obrazky_produktov_detail/broskyne_velke2.png');
        $page_97->setValue('eshop_vat_rate', '20');
        $page_97->setValue('eshop_eur_price_without_vat', '25');
        $page_97->setValue('eshop_product_akcia', 'T');
        $page_97->setValue('eshop_eur_action_price_without_vat', '20');
        $page_97->setValue('availability', '3');
        $page_97->setValue('eshop_detail_text', base64_decode('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'));
        // set template on MAIN PAGE eshop_catalog::product
        $page_97->save();

        $this->createVariants($page_97);

        // page: demo_content/obrazky_produktov_detail/broskyne_velke3.png(ID: 98 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_98 = \PageFactory::create($page_97->getPageId(), $page_type->getId());
        $page_98->setPageName('demo_content/obrazky_produktov_detail/broskyne_velke3.png');
        $page_98->setPageTag('');
        $page_98->setPageStateId('1');
        $page_98->setPageClassId('1');
        $page_98->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_98->setValue('title', 'Broskyne velke3');
        $page_98->setValue('photo_file', 'demo_content/obrazky_produktov_detail/broskyne_velke3.png');
        $page_98->setValue('photo_alt', 'Broskyne - Broskyne velke3');
        $page_98->save();

        // page: demo_content/obrazky_produktov_detail/broskyne_velke2.png(ID: 99 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_99 = \PageFactory::create($page_97->getPageId(), $page_type->getId());
        $page_99->setPageName('demo_content/obrazky_produktov_detail/broskyne_velke2.png');
        $page_99->setPageTag('');
        $page_99->setPageStateId('1');
        $page_99->setPageClassId('1');
        $page_99->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_99->setValue('title', 'Broskyne velke2');
        $page_99->setValue('photo_file', 'demo_content/obrazky_produktov_detail/broskyne_velke2.png');
        $page_99->setValue('photo_alt', 'Broskyne - Broskyne velke2');
        $page_99->save();

        // page: demo_content/obrazky_produktov_detail/broskyne_velke1.png(ID: 100 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_100 = \PageFactory::create($page_97->getPageId(), $page_type->getId());
        $page_100->setPageName('demo_content/obrazky_produktov_detail/broskyne_velke1.png');
        $page_100->setPageTag('');
        $page_100->setPageStateId('1');
        $page_100->setPageClassId('1');
        $page_100->setSortDateTime(date('Y-m-d H:i:s', time() + 2));
        $page_100->setValue('title', 'Broskyne velke1');
        $page_100->setValue('photo_file', 'demo_content/obrazky_produktov_detail/broskyne_velke1.png');
        $page_100->setValue('photo_alt', 'Broskyne - Broskyne velke1');
        $page_100->save();

        // page: Jablká(ID: 105 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_product');
        $page_105 = \PageFactory::create($page_96->getPageId(), $page_type->getId());
        $page_105->setPageName('Jablká');
        $page_105->setPageTag('');
        $page_105->setPageStateId('1');
        $page_105->setPageClassId('1');
        $page_105->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_105->setValue('title', 'Jablká');
        $page_105->setValue('seo_url_name', '/ovocie/jablka');
        $page_105->setValue('photo_album_thumbnail', 'demo_content/obrazky_produktov_detail/jablko_velke1.png');
        $page_105->setValue('eshop_vat_rate', '20');
        $page_105->setValue('eshop_eur_price_without_vat', '1.5');
        $page_105->setValue('eshop_product_akcia', 'F');
        $page_105->setValue('availability', '1');
        $page_105->setValue('eshop_detail_text', base64_decode('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'));
        // set template on MAIN PAGE eshop_catalog::product
        $page_105->save();

        $this->createVariants($page_105);

        // page: demo_content/obrazky_produktov_detail/jablko_velke3.png(ID: 106 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_106 = \PageFactory::create($page_105->getPageId(), $page_type->getId());
        $page_106->setPageName('demo_content/obrazky_produktov_detail/jablko_velke3.png');
        $page_106->setPageTag('');
        $page_106->setPageStateId('1');
        $page_106->setPageClassId('1');
        $page_106->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_106->setValue('title', 'Jablko velke3');
        $page_106->setValue('photo_file', 'demo_content/obrazky_produktov_detail/jablko_velke3.png');
        $page_106->setValue('photo_alt', 'Jablká - Jablko velke3');
        $page_106->save();

        // page: demo_content/obrazky_produktov_detail/jablko_velke2.png(ID: 107 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_107 = \PageFactory::create($page_105->getPageId(), $page_type->getId());
        $page_107->setPageName('demo_content/obrazky_produktov_detail/jablko_velke2.png');
        $page_107->setPageTag('');
        $page_107->setPageStateId('1');
        $page_107->setPageClassId('1');
        $page_107->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_107->setValue('title', 'Jablko velke2');
        $page_107->setValue('photo_file', 'demo_content/obrazky_produktov_detail/jablko_velke2.png');
        $page_107->setValue('photo_alt', 'Jablká - Jablko velke2');
        $page_107->save();

        // page: demo_content/obrazky_produktov_detail/jablko_velke1.png(ID: 108 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_108 = \PageFactory::create($page_105->getPageId(), $page_type->getId());
        $page_108->setPageName('demo_content/obrazky_produktov_detail/jablko_velke1.png');
        $page_108->setPageTag('');
        $page_108->setPageStateId('1');
        $page_108->setPageClassId('1');
        $page_108->setSortDateTime(date('Y-m-d H:i:s', time() + 2));
        $page_108->setValue('title', 'Jablko velke1');
        $page_108->setValue('photo_file', 'demo_content/obrazky_produktov_detail/jablko_velke1.png');
        $page_108->setValue('photo_alt', 'Jablká - Jablko velke1');
        $page_108->save();

        // page: Maliny(ID: 109 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_product');
        $page_109 = \PageFactory::create($page_96->getPageId(), $page_type->getId());
        $page_109->setPageName('Maliny');
        $page_109->setPageTag('');
        $page_109->setPageStateId('1');
        $page_109->setPageClassId('1');
        $page_109->setSortDateTime(date('Y-m-d H:i:s', time() + 2));
        $page_109->setValue('title', 'Maliny');
        $page_109->setValue('seo_url_name', '/ovocie/maliny');
        $page_109->setValue('photo_album_thumbnail', 'demo_content/obrazky_produktov_detail/maliny_velke1.png');
        $page_109->setValue('eshop_vat_rate', '20');
        $page_109->setValue('eshop_eur_price_without_vat', '4');
        $page_109->setValue('eshop_product_akcia', 'F');
        $page_109->setValue('availability', '1');
        $page_109->setValue('eshop_detail_text', base64_decode('TG9yZW0gaXBzdW0gZG9sb3Igc2l0IGFtZXQsIGNvbnNlY3RldHVyIGFkaXBpc2NpbmcgZWxpdC4gTnVuYyBjdXJzdXMgcmlzdXMgdml0YWUgcmlzdXMgZGFwaWJ1cywgaW4gbG9ib3J0aXMgdmVsaXQgYXVjdG9yLiBDcmFzIHNjZWxlcmlzcXVlIHNhcGllbiBoZW5kcmVyaXQsIGNvbmd1ZSBudW5jIHV0LCBlbGVpZmVuZCBwdXJ1cy4gUHJvaW4gcGVsbGVudGVzcXVlIHNlbSBlbGl0LCBzZWQgYXVjdG9yIGRvbG9yIGNvbnZhbGxpcyBhY2N1bXNhbi4gTWFlY2VuYXMgdGluY2lkdW50IGV4IHNlZCB0ZW1wb3IgbG9ib3J0aXMuIE51bmMgcHJldGl1bSBwZWxsZW50ZXNxdWUgbmliaCwgdmVsIHZvbHV0cGF0IGxlY3R1cyB0ZW1wb3IgZ3JhdmlkYS4gVml2YW11cyBtb2xlc3RpZSBlbGVtZW50dW0gbWF0dGlzLiBWaXZhbXVzIGJsYW5kaXQgYWxpcXVldCBwdXJ1cywgbmVjIGJpYmVuZHVtIGxvcmVtIGVnZXN0YXMgbmVjLiBEb25lYyBwZWxsZW50ZXNxdWUgcG9ydHRpdG9yIGNvbnZhbGxpcy4gTWF1cmlzIHNlZCBxdWFtIGhlbmRyZXJpdCwgYmliZW5kdW0gbmliaCBuZWMsIGFsaXF1YW0gcHVydXMuIE5hbSBsYW9yZWV0IGNvbmd1ZSBxdWFtLCBzaXQgYW1ldCB2ZXN0aWJ1bHVtIGV4IGxvYm9ydGlzIGV0LiBWaXZhbXVzIGEgb3JjaSBldCBlcm9zIHNhZ2l0dGlzIG1hdHRpcyBzZWQgZWdldCB1cm5hLiBDcmFzIHNlbSByaXN1cywgbWF0dGlzIHNpdCBhbWV0IGxvYm9ydGlzIGV1LCB0aW5jaWR1bnQgZXQgaXBzdW0uIEN1cmFiaXR1ciBzaXQgYW1ldCBpcHN1bSBhIG5pc2kgc29kYWxlcyBoZW5kcmVyaXQgc2VkIGV1IHB1cnVzLg0KDQpJbnRlZ2VyIHRpbmNpZHVudCB2ZWwgZXJhdCBhYyB2ZWhpY3VsYS4gTnVsbGEgbGlndWxhIGVyb3MsIG1vbGVzdGllIHNpdCBhbWV0IHNhZ2l0dGlzIHNpdCBhbWV0LCBpbnRlcmR1bSB1dCBuaXNpLiBDdXJhYml0dXIgdmVzdGlidWx1bSBsYW9yZWV0IGVyYXQgbm9uIHNlbXBlci4gU2VkIHZlaGljdWxhIG5vbiBlbGl0IGEgY29uc2VjdGV0dXIuIFN1c3BlbmRpc3NlIGVnZXQgbWF0dGlzIHZlbGl0LiBOdWxsYW0gc29sbGljaXR1ZGluIGp1c3RvIHVsdHJpY2VzIG5lcXVlIGxhb3JlZXQgcnV0cnVtLiBOdWxsYSBub24gb2RpbyBldSBleCBiaWJlbmR1bSBjb25kaW1lbnR1bS4gU2VkIG1hZ25hIGFyY3UsIHNhZ2l0dGlzIHNlZCBtZXR1cyBpbiwgZnJpbmdpbGxhIHRlbXB1cyBsb3JlbS4gTnVsbGFtIGFjIG9kaW8gbGVvLiBQaGFzZWxsdXMgbmVjIGVyb3Mgbm9uIG1hc3NhIHVsbGFtY29ycGVyIHRpbmNpZHVudC4gTnVsbGFtIGVnZXQgbnVsbGEgaWQgdmVsaXQgYXVjdG9yIGV1aXNtb2QgZXQgYXQgbGVvLiBEb25lYyBldCBlZmZpY2l0dXIgbWkuIFNlZCB0cmlzdGlxdWUgZXggdmVsIGRvbG9yIHB1bHZpbmFyIGFsaXF1YW0uIE5hbSBwaGFyZXRyYSBzY2VsZXJpc3F1ZSBsZWN0dXMsIHNpdCBhbWV0IGNvbmRpbWVudHVtIGV4IHZlaGljdWxhIHZpdGFlLiBNYWVjZW5hcyBvcmNpIGV4LCBwb3J0dGl0b3IgYWMgbmVxdWUgbmVjLCBjb252YWxsaXMgb3JuYXJlIG9kaW8uIFByb2luIHNvZGFsZXMgcnV0cnVtIHB1cnVzIHNpdCBhbWV0IGFsaXF1YW0uDQoNClNlZCBtYWduYSBhdWd1ZSwgcHVsdmluYXIgdmVsIGVsZW1lbnR1bSB2ZWwsIGdyYXZpZGEgbGFvcmVldCB2ZWxpdC4gRXRpYW0gc2VkIGV4IHBlbGxlbnRlc3F1ZSwgdWx0cmljaWVzIGRvbG9yIHN1c2NpcGl0LCB1bHRyaWNlcyBmZWxpcy4gTnVsbGEgZmFjaWxpc2kuIFN1c3BlbmRpc3NlIHZpdGFlIGVyYXQgZXQgdXJuYSBtb2xsaXMgbW9sbGlzLiBBZW5lYW4gbGFvcmVldCBkaWN0dW0gc2FwaWVuLiBNYXVyaXMgdmFyaXVzIHZlbmVuYXRpcyBkdWksIGFjIHZlc3RpYnVsdW0gb3JjaSB2aXZlcnJhIHV0LiBOdWxsYW0gcnV0cnVtIGdyYXZpZGEgbWV0dXMgc2l0IGFtZXQgdm9sdXRwYXQuIFBlbGxlbnRlc3F1ZSBzZWQgbGFjdXMgZWdldCBsYWN1cyBzYWdpdHRpcyBwb3N1ZXJlIGluIG5vbiBsZWN0dXMuIFZlc3RpYnVsdW0gc2l0IGFtZXQgZGlnbmlzc2ltIGp1c3RvLCB2ZWwgZWdlc3RhcyBsaWJlcm8uIFByb2luIGVsZWlmZW5kIHJpc3VzIGV1IHZlbGl0IG1vbGVzdGllLCBldCBiaWJlbmR1bSBlc3QgcnV0cnVtLiBNYWVjZW5hcyBsYW9yZWV0IGxhb3JlZXQgbmlzaSBub24gaWFjdWxpcy4gU2VkIHBvcnRhIG51bmMgaWQgc2FwaWVuIHBvcnR0aXRvciwgZWZmaWNpdHVyIHN1c2NpcGl0IHJpc3VzIHRlbXBvci4gTnVuYyB1dCBmZXJtZW50dW0gbnVsbGEu'));
        // set template on MAIN PAGE eshop_catalog::product
        $page_109->save();

        $this->createVariants($page_109);

        // page: demo_content/obrazky_produktov_detail/maliny_velke3.png(ID: 110 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_110 = \PageFactory::create($page_109->getPageId(), $page_type->getId());
        $page_110->setPageName('demo_content/obrazky_produktov_detail/maliny_velke3.png');
        $page_110->setPageTag('');
        $page_110->setPageStateId('1');
        $page_110->setPageClassId('1');
        $page_110->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_110->setValue('title', 'Maliny velke3');
        $page_110->setValue('photo_file', 'demo_content/obrazky_produktov_detail/maliny_velke3.png');
        $page_110->setValue('photo_alt', 'Maliny - Maliny velke3');
        $page_110->save();

        // page: demo_content/obrazky_produktov_detail/maliny_velke2.png(ID: 111 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_111 = \PageFactory::create($page_109->getPageId(), $page_type->getId());
        $page_111->setPageName('demo_content/obrazky_produktov_detail/maliny_velke2.png');
        $page_111->setPageTag('');
        $page_111->setPageStateId('1');
        $page_111->setPageClassId('1');
        $page_111->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_111->setValue('title', 'Maliny velke2');
        $page_111->setValue('photo_file', 'demo_content/obrazky_produktov_detail/maliny_velke2.png');
        $page_111->setValue('photo_alt', 'Maliny - Maliny velke2');
        $page_111->save();

        // page: demo_content/obrazky_produktov_detail/maliny_velke1.png(ID: 112 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_112 = \PageFactory::create($page_109->getPageId(), $page_type->getId());
        $page_112->setPageName('demo_content/obrazky_produktov_detail/maliny_velke1.png');
        $page_112->setPageTag('');
        $page_112->setPageStateId('1');
        $page_112->setPageClassId('1');
        $page_112->setSortDateTime(date('Y-m-d H:i:s', time() + 2));
        $page_112->setValue('title', 'Maliny velke1');
        $page_112->setValue('photo_file', 'demo_content/obrazky_produktov_detail/maliny_velke1.png');
        $page_112->setValue('photo_alt', 'Maliny - Maliny velke1');
        $page_112->save();

        // page: Čerstvé(ID: 113 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_subcategory');
        $page_113 = \PageFactory::create($page_96->getPageId(), $page_type->getId());
        $page_113->setPageName('Čerstvé');
        $page_113->setPageTag('');
        $page_113->setPageStateId('1');
        $page_113->setPageClassId('1');
        $page_113->setSortDateTime(date('Y-m-d H:i:s', time() + 3));
        $page_113->setValue('title', 'Čerstvé');
        $page_113->setValue('seo_url_name', '/katalog-produktov/ovocie/cerstve');
        $page_113->setValue('eshop_vat_rate', '20');
        $page_113->setValue('promo_bg_image', 'demo_content/obrazky_produktov_detail/jablko_velke1.png');
        $page_113->save();

        // page: Kiwi(ID: 114 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_product');
        $page_114 = \PageFactory::create($page_113->getPageId(), $page_type->getId());
        $page_114->setPageName('Kiwi');
        $page_114->setPageTag('');
        $page_114->setPageStateId('1');
        $page_114->setPageClassId('1');
        $page_114->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_114->setValue('title', 'Kiwi');
        $page_114->setValue('seo_url_name', '/ovocie/kiwi');
        $page_114->setValue('photo_album_thumbnail', 'demo_content/obrazky_produktov_detail/kiwi_velke1.png');
        $page_114->setValue('eshop_vat_rate', '20');
        $page_114->setValue('eshop_eur_price_without_vat', '10');
        $page_114->setValue('eshop_product_akcia', 'T');
        $page_114->setValue('eshop_eur_action_price_without_vat', '5');
        $page_114->setValue('availability', '3');
        $page_114->setValue('eshop_detail_text', base64_decode('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'));
        // set template on MAIN PAGE eshop_catalog::product
        $page_114->save();

        $this->createVariants($page_114);

        // page: demo_content/obrazky_produktov_detail/kiwi_velke3.png(ID: 115 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_115 = \PageFactory::create($page_114->getPageId(), $page_type->getId());
        $page_115->setPageName('demo_content/obrazky_produktov_detail/kiwi_velke3.png');
        $page_115->setPageTag('');
        $page_115->setPageStateId('1');
        $page_115->setPageClassId('1');
        $page_115->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_115->setValue('title', 'Kiwi velke3');
        $page_115->setValue('photo_file', 'demo_content/obrazky_produktov_detail/kiwi_velke3.png');
        $page_115->setValue('photo_alt', 'Kiwi - Kiwi velke3');
        $page_115->save();

        // page: demo_content/obrazky_produktov_detail/kiwi_velke2.png(ID: 116 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_116 = \PageFactory::create($page_114->getPageId(), $page_type->getId());
        $page_116->setPageName('demo_content/obrazky_produktov_detail/kiwi_velke2.png');
        $page_116->setPageTag('');
        $page_116->setPageStateId('1');
        $page_116->setPageClassId('1');
        $page_116->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_116->setValue('title', 'Kiwi velke2');
        $page_116->setValue('photo_file', 'demo_content/obrazky_produktov_detail/kiwi_velke2.png');
        $page_116->setValue('photo_alt', 'Kiwi - Kiwi velke2');
        $page_116->save();

        // page: demo_content/obrazky_produktov_detail/kiwi_velke1.png(ID: 117 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_117 = \PageFactory::create($page_114->getPageId(), $page_type->getId());
        $page_117->setPageName('demo_content/obrazky_produktov_detail/kiwi_velke1.png');
        $page_117->setPageTag('');
        $page_117->setPageStateId('1');
        $page_117->setPageClassId('1');
        $page_117->setSortDateTime(date('Y-m-d H:i:s', time() + 2));
        $page_117->setValue('title', 'Kiwi velke1');
        $page_117->setValue('photo_file', 'demo_content/obrazky_produktov_detail/kiwi_velke1.png');
        $page_117->setValue('photo_alt', 'Kiwi - Kiwi velke1');
        $page_117->save();

        // page: Hrozno(ID: 118 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_product');
        $page_118 = \PageFactory::create($page_113->getPageId(), $page_type->getId());
        $page_118->setPageName('Hrozno');
        $page_118->setPageTag('');
        $page_118->setPageStateId('1');
        $page_118->setPageClassId('1');
        $page_118->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_118->setValue('title', 'Hrozno');
        $page_118->setValue('seo_url_name', '/ovocie/hrozno');
        $page_118->setValue('photo_album_thumbnail', 'demo_content/obrazky_produktov_detail/hrozno_velke2.png');
        $page_118->setValue('eshop_vat_rate', '20');
        $page_118->setValue('eshop_eur_price_without_vat', '2');
        $page_118->setValue('eshop_product_akcia', 'F');
        $page_118->setValue('availability', '1');
        $page_118->setValue('eshop_detail_text', base64_decode('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'));
        // set template on MAIN PAGE eshop_catalog::product
        $page_118->save();

        $this->createVariants($page_118);

        // page: demo_content/obrazky_produktov_detail/hrozno_velke3.png(ID: 119 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_119 = \PageFactory::create($page_118->getPageId(), $page_type->getId());
        $page_119->setPageName('demo_content/obrazky_produktov_detail/hrozno_velke3.png');
        $page_119->setPageTag('');
        $page_119->setPageStateId('1');
        $page_119->setPageClassId('1');
        $page_119->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_119->setValue('title', 'Hrozno velke3');
        $page_119->setValue('photo_file', 'demo_content/obrazky_produktov_detail/hrozno_velke3.png');
        $page_119->setValue('photo_alt', 'Hrozno - Hrozno velke3');
        $page_119->save();

        // page: demo_content/obrazky_produktov_detail/hrozno_velke2.png(ID: 120 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_120 = \PageFactory::create($page_118->getPageId(), $page_type->getId());
        $page_120->setPageName('demo_content/obrazky_produktov_detail/hrozno_velke2.png');
        $page_120->setPageTag('');
        $page_120->setPageStateId('1');
        $page_120->setPageClassId('1');
        $page_120->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_120->setValue('title', 'Hrozno velke2');
        $page_120->setValue('photo_file', 'demo_content/obrazky_produktov_detail/hrozno_velke2.png');
        $page_120->setValue('photo_alt', 'Hrozno - Hrozno velke2');
        $page_120->save();

        // page: demo_content/obrazky_produktov_detail/hrozno_velke1.png(ID: 121 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_121 = \PageFactory::create($page_118->getPageId(), $page_type->getId());
        $page_121->setPageName('demo_content/obrazky_produktov_detail/hrozno_velke1.png');
        $page_121->setPageTag('');
        $page_121->setPageStateId('1');
        $page_121->setPageClassId('1');
        $page_121->setSortDateTime(date('Y-m-d H:i:s', time() + 2));
        $page_121->setValue('title', 'Hrozno velke1');
        $page_121->setValue('photo_file', 'demo_content/obrazky_produktov_detail/hrozno_velke1.png');
        $page_121->setValue('photo_alt', 'Hrozno - Hrozno velke1');
        $page_121->save();

        // page: Pokazené(ID: 165 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_subcategory');
        $page_165 = \PageFactory::create($page_96->getPageId(), $page_type->getId());
        $page_165->setPageName('Pokazené');
        $page_165->setPageTag(false);
        $page_165->setPageStateId('1');
        $page_165->setPageClassId('1');
        $page_165->setSortDateTime(date('Y-m-d H:i:s', time() + 4));
        $page_165->setValue('title', 'Pokazené');
        $page_165->setValue('seo_url_name', '/katalog-produktov/ovocie/pokazene');
        $page_165->setValue('eshop_vat_rate', '20');
        $page_165->setValue('promo_bg_image', 'demo_content/obrazky_produktov_detail/broskyne_velke3.png');
        $page_165->save();

        // page: Černice(ID: 101 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_product');
        $page_101 = \PageFactory::create($page_165->getPageId(), $page_type->getId());
        $page_101->setPageName('Černice');
        $page_101->setPageTag('');
        $page_101->setPageStateId('1');
        $page_101->setPageClassId('1');
        $page_101->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_101->setValue('title', 'Černice');
        $page_101->setValue('seo_url_name', '/ovocie/cernice');
        $page_101->setValue('photo_album_thumbnail', 'demo_content/obrazky_produktov_detail/cernice_velke1.png');
        $page_101->setValue('eshop_vat_rate', '20');
        $page_101->setValue('eshop_eur_price_without_vat', '7');
        $page_101->setValue('eshop_product_akcia', 'F');
        $page_101->setValue('availability', '2');
        $page_101->setValue('eshop_detail_text', base64_decode('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'));
        // set template on MAIN PAGE eshop_catalog::product
        $page_101->save();

        $this->createVariants($page_101);

        // page: demo_content/obrazky_produktov_detail/cernice_velke3.png(ID: 102 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_102 = \PageFactory::create($page_101->getPageId(), $page_type->getId());
        $page_102->setPageName('demo_content/obrazky_produktov_detail/cernice_velke3.png');
        $page_102->setPageTag('');
        $page_102->setPageStateId('1');
        $page_102->setPageClassId('1');
        $page_102->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_102->setValue('title', 'Cernice velke3');
        $page_102->setValue('photo_file', 'demo_content/obrazky_produktov_detail/cernice_velke3.png');
        $page_102->setValue('photo_alt', 'Černice - Cernice velke3');
        $page_102->save();

        // page: demo_content/obrazky_produktov_detail/cernice_velke2.png(ID: 103 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_103 = \PageFactory::create($page_101->getPageId(), $page_type->getId());
        $page_103->setPageName('demo_content/obrazky_produktov_detail/cernice_velke2.png');
        $page_103->setPageTag('');
        $page_103->setPageStateId('1');
        $page_103->setPageClassId('1');
        $page_103->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_103->setValue('title', 'Cernice velke2');
        $page_103->setValue('photo_file', 'demo_content/obrazky_produktov_detail/cernice_velke2.png');
        $page_103->setValue('photo_alt', 'Černice - Cernice velke2');
        $page_103->save();

        // page: demo_content/obrazky_produktov_detail/cernice_velke1.png(ID: 104 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('photo');
        $page_104 = \PageFactory::create($page_101->getPageId(), $page_type->getId());
        $page_104->setPageName('demo_content/obrazky_produktov_detail/cernice_velke1.png');
        $page_104->setPageTag('');
        $page_104->setPageStateId('1');
        $page_104->setPageClassId('1');
        $page_104->setSortDateTime(date('Y-m-d H:i:s', time() + 2));
        $page_104->setValue('title', 'Cernice velke1');
        $page_104->setValue('photo_file', 'demo_content/obrazky_produktov_detail/cernice_velke1.png');
        $page_104->setValue('photo_alt', 'Černice - Cernice velke1');
        $page_104->save();

        // regenerate page tags
        PageIds::generatePageTagsList();

        // page: Katalóg produktov(ID: 38 TAG: Katalóg produktov)
        $dizajn_page_id = $this->getPageIdByTag('dizajn');
        if (!empty($dizajn_page_id)) {
            $dizajn = \PageFactory::get($dizajn_page_id);
            $dizajn->setValue('sale_sticker_image', 'demo_content/eshop/akcia.png');
            $dizajn->save();
        }

        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('eshop_product'), 'product-catalog', 'product');
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('eshop_category'), 'product-catalog', 'product-list');
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('photo'), 'index', 'index');
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('eshop_subcategory'), 'product-catalog', 'product-list');
        
        // regenerate page tags
        PageIds::generatePageTagsList();
    }

    public function down()
    {
        // remove page: Katalóg produktov (Katalóg produktov)
        $page_id = $this->getPageIdByTag('eshop_catalog');
        if ($page_id !== NULL) {
            \PageFactory::get($page_id)->delete();
        }

        // regenerate page tags
        PageIds::generatePageTagsList();
    }

    protected function createVariants(PageInterface $parent)
    {
        $variants = ['S', 'M', 'L', 'XL', 'XXL'];

        $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_product_variant');

        $sort = time();
        $price = $parent->getValue('eshop_eur_price_without_vat');


        foreach ($variants as $size) {
            $page = \PageFactory::create($parent->getPageId(), $page_type->getId());
            $page->setPageName($size);
            $page->setPageTag('');
            $page->setPageStateId('1');
            $page->setPageClassId('1');
            $page->setSortDateTime(date('Y-m-d H:i:s', $sort));
            $page->setValue('title', $parent->getValue('title') . ' ' . $size);
            $page->setValue('eshop_eur_price_without_vat', $price);
            $page->setValue('size', $size);
            $page->save();

            $price += (rand(0, 100) / 100);

            $sort--;
        }
    }
}
