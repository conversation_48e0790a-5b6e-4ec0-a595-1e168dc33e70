<?php
namespace Krabica\Migrations;

use Buxus\Migration\AbstractMigration;
use Buxus\Util\PageIds;

class InfoStrankyContent extends AbstractMigration {
    public function up() {
        $page_ids = array();

        // page: Info stránky(ID: 443 TAG: InfoPages)
        $page_id = $this->getPageIdByTag('info_pages');
        $root_created = false;
        if ($page_id === NULL) {
            $page_type = $this->pageTypesManager()->getPageTypeByTag('folder');
            $page_443 = \PageFactory::create($this->getPageIdByTag('root_page'), $page_type->getId());
            $root_created = true;
        } else {
            $page_443 = \PageFactory::get($page_id);
        }
        $page_443->setPageName('Info stránky');
        $page_443->setPageTag('info_pages');
        $page_443->setPageStateId('1');
        $page_443->setPageClassId('1');
        $page_443->save();

        if ($root_created) {
            $page_ids[] = $page_443->getPageId();
        }

        // page: Všeobecné obchodné podmienky(ID: 52 TAG: Všeobecné podmienky na predaj tovaru)
        $page_type = $this->pageTypesManager()->getPageTypeByTag('article');
        $page_52 = \PageFactory::create($page_443->getPageId(), $page_type->getId());
        $page_52->setPageName('Všeobecné obchodné podmienky');
        $page_52->setPageTag('vseobecne_obchodne_podmienky');
        $page_52->setPageStateId('1');
        $page_52->setPageClassId('1');
        $page_52->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_52->setPropertyValue('title', 'Všeobecné obchodné podmienky');
        $page_52->setPropertyValue('text', base64_decode('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'));
        // set template on MAIN PAGE index::index
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('article'), 'index', 'index');
        $page_52->save();

        $page_ids[] = $page_52->getPageId();

        // page: O nás(ID: 502 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('article');
        $page_502 = \PageFactory::create($page_443->getPageId(), $page_type->getId());
        $page_502->setPageName('O nás');
        $page_502->setPageTag('o_nas');
        $page_502->setPageStateId('1');
        $page_502->setPageClassId('1');
        $page_502->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_502->setPropertyValue('title', 'O nás');
        $page_502->setPropertyValue('text', base64_decode('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'));
        // set template on MAIN PAGE index::index
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('article'), 'index', 'index');
        $page_502->save();

        $page_ids[] = $page_502->getPageId();

        $this->setDataKey('page_ids', $page_ids);

        // regenerate page tags
        PageIds::generatePageTagsList();

    }

    public function down() {
        $page_ids = $this->getDataKey('page_ids');

        foreach ($page_ids as $page_id) {
            try {
                $page = \PageFactory::get($page_id);
                if ($page !== null) {
                    $page->delete();
                }
            } catch (\Exception $e) {
                $this->output->writeln('<info>Page ID ' . $page_id . ' not found</info>');
            }
        }

        // regenerate page tags
        PageIds::generatePageTagsList();

    }

}
