<?php
namespace Krabica\Migrations;

use Buxus\Migration\AbstractMigration;
use Buxus\Translate\Exception\Exception;
use Buxus\Util\PageIds;

class AddImageForSubcategory extends AbstractMigration {
//    public function dependencies() {
//        return array(
//            '\DynamicCategory\Migrations\DynamicCategories',
//        );
//    }

    const PAGE_TAG = 'eshop_subcategory';

    public function up() {
        $page = $this->getPageTypeByTag(static::PAGE_TAG);
        if ($page === NULL) {
            throw new Exception('Page type with tag "'. static::PAGE_TAG .'" does not exist.');
        }

        $property = $this->propertyManager()->getPropertyByTag('promo_bg_image');
        $property_id = $property->getId();
        $tmp = $page->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $page->addPropertyItem($tmp);
        }

        $this->pageTypesManager()->savePageType($page);
    }

    public function down() {
        $page = $this->getPageTypeByTag(static::PAGE_TAG);
        if ($page === NULL) {
            throw new Exception('Page type with tag "'. static::PAGE_TAG .'" does not exist.');
        }

        $page->removePropertyItemById($this->propertyManager()->getPropertyByTag('promo_bg_image')->getId());
        $this->pageTypesManager()->savePageType($page);
    }

}