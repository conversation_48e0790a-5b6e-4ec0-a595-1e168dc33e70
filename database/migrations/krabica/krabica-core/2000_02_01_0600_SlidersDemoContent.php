<?php
namespace Krabica\Migrations;

use Buxus\Migration\AbstractMigration;
use Buxus\Util\PageIds;

class SlidersDemoContent extends AbstractMigration {
    public function dependencies() {
        return array(
            '\Layout\Migrations\LayoutSliders',
        );
    }

    public function up() {
        $page_ids = array();

        // page: Zoznam banerov(ID: 102 TAG: banners_list)
        $page_id = $this->getPageIdByTag('banners_list');
        if ($page_id === NULL) {
            $page_type = $this->pageTypesManager()->getPageTypeByTag('layout_element_banner_list');
            $page_102 = \PageFactory::create($this->getPageIdByTag('homepage'), $page_type->getId());
        } else {
            $page_102 = \PageFactory::get($page_id);
        }
        $page_102->setPageName('Zoznam bannerov');
        $page_102->setPageTag('banners_list');
        $page_102->setPageStateId('1');
        $page_102->setPageClassId('1');
        $page_102->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_102->save();

        $page_ids[] = $page_102->getPageId();

        // page: Prvotriedna kvalita(ID: 104 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('layout_element_slider_banner');
        $page_104 = \PageFactory::create($page_102->getPageId(), $page_type->getId());
        $page_104->setPageName('Prvotriedna kvalita');
        $page_104->setPageTag(false);
        $page_104->setPageStateId('1');
        $page_104->setPageClassId('1');
        $page_104->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_104->setPropertyValue('text', base64_decode('PGgyPlNsb2dhbiBha2NpZSAyPC9oMj4NCjxwPkxvcmVtIGlwc3VtIGRvbG9yIHNpdCBhbWV0LCBjb25zZWN0ZXR1ciBhZGlwaXNjaW5nIGVsaXQuIEluIGludGVyZHVtIGRpZ25pc3NpbSBlcm9zLCBpbiBmYWNpbGlzaXMgbGVvIHVsdHJpY2VzIGJpYmVuZHVtLiBTdXNwZW5kaXNzZSBwb3RlbnRpLjwvcD4NCjx1bD4NCjxsaT5Mb3JlbSBpcHN1bTwvbGk+DQo8bGk+TG9yZW0gaXBzdW08L2xpPg0KPGxpPkxvcmVtIGlwc3VtPC9saT4NCjwvdWw+'));
        $page_104->setPropertyValue('slider_text_button_label', 'Chcem ochutnať');
        $page_104->setPropertyValue('slider_text_sticker', 'demo_content/slider/fruit2.jpg');
        $page_104->setPropertyValue('promobox_background', '#000000');
        $page_104->setPropertyValue('slider_text_background', 'T');
        $page_104->setPropertyValue('background_opacity', '70');
        $page_104->setPropertyValue('info_text', 'Vyskúšajte ovocie z našej ponuky');
        $page_104->setPropertyValue('heading_text', 'Prvotriedna kvalita');
        $page_104->save();

        $page_ids[] = $page_104->getPageId();

        // page: Čerstvé ovocie pre Vás(ID: 455 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('layout_element_slider_banner');
        $page_455 = \PageFactory::create($page_102->getPageId(), $page_type->getId());
        $page_455->setPageName('Čerstvé ovocie pre Vás');
        $page_455->setPageTag(false);
        $page_455->setPageStateId('1');
        $page_455->setPageClassId('1');
        $page_455->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_455->setPropertyValue('text', base64_decode('PGgyPlNsb2dhbiBha2NpZSAyPC9oMj4NCjxwPkxvcmVtIGlwc3VtIGRvbG9yIHNpdCBhbWV0LCBjb25zZWN0ZXR1ciBhZGlwaXNjaW5nIGVsaXQuIEluIGludGVyZHVtIGRpZ25pc3NpbSBlcm9zLCBpbiBmYWNpbGlzaXMgbGVvIHVsdHJpY2VzIGJpYmVuZHVtLiBTdXNwZW5kaXNzZSBwb3RlbnRpLjwvcD4NCjx1bD4NCjxsaT5Mb3JlbSBpcHN1bTwvbGk+DQo8bGk+TG9yZW0gaXBzdW08L2xpPg0KPGxpPkxvcmVtIGlwc3VtPC9saT4NCjwvdWw+'));
        $page_455->setPropertyValue('slider_text_button_label', 'Chcem ochutnať');
        $page_455->setPropertyValue('slider_text_sticker', 'demo_content/slider/fruit1.jpg');
        $page_455->setPropertyValue('promobox_background', '#000000');
        $page_455->setPropertyValue('slider_text_background', 'T');
        $page_455->setPropertyValue('background_opacity', '70');
        $page_455->setPropertyValue('info_text', 'Ponúkame čerstvé nestriekané ovocie priamo zo záhrady, v bio kvalite!');
        $page_455->setPropertyValue('heading_text', 'Čerstvé ovocie pre Vás');
        $page_455->save();

        $page_ids[] = $page_455->getPageId();

        $this->setData($page_ids);

        // regenerate page tags
        PageIds::generatePageTagsList();
    }

    public function down() {
        $page_ids = $this->getData();

        foreach ($page_ids as $page_id) {
            try {
                $page = \PageFactory::get($page_id);
                if ($page) {
                    $page->delete();
                }
            } catch (\Exception $e) {
                $this->output->writeln('<info>Page ID ' . $page_id . ' npt found</info>');
            }
        }

        // regenerate page tags
        PageIds::generatePageTagsList();
    }
}