<?php

use Buxus\Migration\AbstractMigration;


/**
 * Automatic generation from (buxus7_clean_install) at 2019-01-04 11:07:30
 */
class SetSeoTitleMainTemplates extends AbstractMigration
{
    public function dependencies()
    {
        return [
            \Buxus\Migrations\BuxusMainPage::class,
            AddSeoSettingsPage::class,
            \Krabica\Migrations\METAProperties::class
        ];
    }

    public function up()
    {
        $page = PageFactory::get(\Buxus\Util\PageIds::getPageId('root_page'));
        $tree_property_factory = \TreeProperty\TreePropertyFactory::getInstance();
        $property = $this->propertyManager()->getPropertyByTag('meta_title');
        $tree_property = $tree_property_factory->get($property->getTag());

        $page_types = $this->propertyManager()->getSupportedPageTypesForPropertyID($property->getId());

        $migration_data = serialize($tree_property->getPageTypesData($page->getPageId()));
        $this->setDataKey('root_template_properties', $migration_data);

        $page_types_data = collect($tree_property->getPageTypesData($page->getPageId()));
        $page_templates = $page_types_data->keyBy('page_type_id');

        $should_update = $this->resolveIfShouldUpdate($page_templates, $page_types);

        if ($should_update) {
            foreach ($page_types as $page_type_id) {
                $old_value = $page_templates[$page_type_id]['value'];

                if (empty($old_value) || $old_value == '<buxus-prop>title</buxus-prop>') {
                    $default_value = '<buxus-prop>title</buxus-prop> • <buxus-func>prop("seo_title_brand","seo_settings")</buxus-func>';
                    $input['value_' . $page_type_id] = $default_value;
                }
            }

            $tree_property->savePageTypesData($page->getPageId(), $input);
        }

        $this->setDataKey('root_template_properties_updated', $should_update);
    }

    protected function resolveIfShouldUpdate($page_templates, $pageTypes)
    {
        $should_update = true;
        foreach ($pageTypes as $pageTypeId) {
            $old_value = $page_templates[$pageTypeId]['value'];
            if (!empty($old_value) && $old_value != '<buxus-prop>title</buxus-prop>') {
                $should_update = false;
                return $should_update;
            }
        }
        return $should_update;
    }

    public function down()
    {
        $page = PageFactory::get(\Buxus\Util\PageIds::getPageId('root_page'));
        $tree_property_factory = \TreeProperty\TreePropertyFactory::getInstance();
        $property = $this->propertyManager()->getPropertyByTag('meta_title');
        $tree_property = $tree_property_factory->get($property->getTag());

        $pageTypes = $this->propertyManager()->getSupportedPageTypesForPropertyID($property->getId());

        foreach ($pageTypes as $pageTypeId) {
            $input['inherit_' . $pageTypeId] = true;
        }

        $tree_property->savePageTypesData($page->getPageId(), $input);
    }
}
