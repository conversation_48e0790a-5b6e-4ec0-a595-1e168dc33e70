<?php
namespace Krabica\Migrations;

use Buxus\Migration\AbstractMigration;
use Buxus\Page\PageUtils;
use Buxus\Util\PageIds;

class LayoutDemo extends AbstractMigration {
    public function dependencies() {
        return array(
            '\Layout\Migrations\LayoutBasic',
            '\Layout\Migrations\LayoutDynamicCategories',
        );
    }

    public function up() {
        $homepage_id = $this->getPageIdByTag('homepage');

        $sort_time = PageUtils::getEldestSubpageSortDateTime($this->getPageIdByTag('homepage'));

        // page: Akciové produkty na HP(ID: 151 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('layout_element_dynamic_query');
        $page_151 = \PageFactory::create($homepage_id, $page_type->getId());
        $page_151->setPageName('Akciové produkty');
        $page_151->setPageTag(false);
        $page_151->setPageStateId('1');
        $page_151->setPageClassId('1');
        $page_151->setSortDateTime(date('Y-m-d H:i:s', $sort_time - 30));
        $page_151->setPropertyValue('title', 'Akciové produkty');
        $page_151->setPropertyValue('query', '{"type":"eshop_product_akcia","data":null,"inv":"0"}');
        $page_151->setPropertyValue('item_count', '4');
        $page_151->setPropertyValue('item_css_class', 'col-sm-3');
        $page_151->save();

        // page: jumbotron(ID: 27 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('layout_element_jumbotron');
        $page_27 = \PageFactory::create($homepage_id, $page_type->getId());
        $page_27->setPageName('Príklad veľkého bloku textu');
        $page_27->setPageTag('');
        $page_27->setPageStateId('1');
        $page_27->setPageClassId('1');
        $page_27->setSortDateTime(date('Y-m-d H:i:s', $sort_time - 20));
        $page_27->setPropertyValue('title', 'BUXUS E-Shop');
        $page_27->setPropertyValue('text', '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec vitae molestie nulla. Integer vitae dolor non mauris bibendum consequat. Mauris accumsan, augue lacinia euismod blandit, mi mi semper massa, sit amet fermentum lacus justo in nisi. Praesent viverra felis vitae sollicitudin dictum. </p>');
        $page_27->save();

        // regenerate page tags
        PageIds::generatePageTagsList();
    }

    public function down() {

        // regenerate page tags
        PageIds::generatePageTagsList();

    }

}
