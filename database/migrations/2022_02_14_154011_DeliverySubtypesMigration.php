<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;
use Buxus\PageType\PageType;
use Buxus\PageType\PageTypePropertyItem;

/**
 * Automatic generation from (buxus_rinoparts_test) at 2022-02-14 15:40:11
 * PageType generator: page_type=delivery_subtype
 * Page generator: page_id=137074
 */
class DeliverySubtypesMigration extends AbstractMigration
{
    public function up()
    {
        // property: Titulok(title)
        $propertyTitle = $this->propertyManager()->propertyExistsByTag('title');
        if ($propertyTitle === false) {
            $propertyTitle = new Property();
            $propertyTitle->setTag('title');
            $propertyTitle->setDescription('Štandardný názov stránky. Text, ktorý sa zobrazí v záložke prehliadača. Podľa SEO odporúčaní max. 65 znakov.');
            $propertyTitle->setExtendedDescription('');
            $propertyTitle->setName('Titulok');
            $propertyTitle->setClassId(4);
            $propertyTitle->setShowType(null);
            $propertyTitle->setShowTypeTag('text');
            $propertyTitle->setValueType('oneline_text');
            $propertyTitle->setDefaultValue('');
            $propertyTitle->setMultiOperations(false);
            $propertyTitle->setInputString(null);
            $propertyTitle->setAttribute('tab', '');
            $propertyTitle->setAttribute('size', '60');
            $propertyTitle->setAttribute('maxlength', '');
            $propertyTitle->setAttribute('readonly', 'F');
            $propertyTitle->setAttribute('pattern', '');
            $propertyTitle->setAttribute('inherit_value', 'F');
            $propertyTitle->setAttribute('onchange-js', '');
            $propertyTitle->setAttribute('onkeyup-js', '');
            $propertyTitle->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyTitle);
        } else {
            $this->writeLine('Property with tag title already exists');
            $this->setDataKey('property_title_existed', true);
        }

        // property: Titulok [CZ](title_cz)
        $propertyTitleCz = $this->propertyManager()->propertyExistsByTag('title_cz');
        if ($propertyTitleCz === false) {
            $propertyTitleCz = new Property();
            $propertyTitleCz->setTag('title_cz');
            $propertyTitleCz->setDescription('Štandardný názov stránky. Text, ktorý sa zobrazí v záložke prehliadača. Podľa SEO odporúčaní max. 65 znakov.');
            $propertyTitleCz->setExtendedDescription('');
            $propertyTitleCz->setName('Titulok [CZ]');
            $propertyTitleCz->setClassId(4);
            $propertyTitleCz->setShowType(null);
            $propertyTitleCz->setShowTypeTag('text');
            $propertyTitleCz->setValueType('oneline_text');
            $propertyTitleCz->setDefaultValue('');
            $propertyTitleCz->setMultiOperations(false);
            $propertyTitleCz->setInputString('');
            $propertyTitleCz->setAttribute('tab', 'CZ');
            $propertyTitleCz->setAttribute('size', '60');
            $propertyTitleCz->setAttribute('maxlength', '');
            $propertyTitleCz->setAttribute('readonly', 'F');
            $propertyTitleCz->setAttribute('pattern', '');
            $propertyTitleCz->setAttribute('inherit_value', 'F');
            $propertyTitleCz->setAttribute('onchange-js', '');
            $propertyTitleCz->setAttribute('onkeyup-js', '');
            $propertyTitleCz->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyTitleCz);
        } else {
            $this->writeLine('Property with tag title_cz already exists');
            $this->setDataKey('property_title_cz_existed', true);
        }

        // property: Titulok [EN](title_en)
        $propertyTitleEn = $this->propertyManager()->propertyExistsByTag('title_en');
        if ($propertyTitleEn === false) {
            $propertyTitleEn = new Property();
            $propertyTitleEn->setTag('title_en');
            $propertyTitleEn->setDescription('Štandardný názov stránky. Text, ktorý sa zobrazí v záložke prehliadača. Podľa SEO odporúčaní max. 65 znakov.');
            $propertyTitleEn->setExtendedDescription('');
            $propertyTitleEn->setName('Titulok [EN]');
            $propertyTitleEn->setClassId(4);
            $propertyTitleEn->setShowType(null);
            $propertyTitleEn->setShowTypeTag('text');
            $propertyTitleEn->setValueType('oneline_text');
            $propertyTitleEn->setDefaultValue('');
            $propertyTitleEn->setMultiOperations(false);
            $propertyTitleEn->setInputString('');
            $propertyTitleEn->setAttribute('tab', 'EN');
            $propertyTitleEn->setAttribute('size', '60');
            $propertyTitleEn->setAttribute('maxlength', '');
            $propertyTitleEn->setAttribute('readonly', 'F');
            $propertyTitleEn->setAttribute('pattern', '');
            $propertyTitleEn->setAttribute('inherit_value', 'F');
            $propertyTitleEn->setAttribute('onchange-js', '');
            $propertyTitleEn->setAttribute('onkeyup-js', '');
            $propertyTitleEn->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyTitleEn);
        } else {
            $this->writeLine('Property with tag title_en already exists');
            $this->setDataKey('property_title_en_existed', true);
        }

        // property: Nezobrazovať na doméne(hide_on_domain)
        $propertyHideOnDomain = $this->propertyManager()->propertyExistsByTag('hide_on_domain');
        if ($propertyHideOnDomain === false) {
            $propertyHideOnDomain = new Property();
            $propertyHideOnDomain->setTag('hide_on_domain');
            $propertyHideOnDomain->setDescription('Ak je táto vlastnosť zaškrtnutá, daný produkt, kategória sa nebude zobrazovať na doméne.');
            $propertyHideOnDomain->setExtendedDescription('');
            $propertyHideOnDomain->setName('Nezobrazovať na doméne');
            $propertyHideOnDomain->setClassId(4);
            $propertyHideOnDomain->setShowType(null);
            $propertyHideOnDomain->setShowTypeTag('checkbox');
            $propertyHideOnDomain->setValueType('logical_value');
            $propertyHideOnDomain->setDefaultValue('F');
            $propertyHideOnDomain->setMultiOperations(false);
            $propertyHideOnDomain->setInputString('');
            $propertyHideOnDomain->setAttribute('tab', '');
            $propertyHideOnDomain->setAttribute('on_value', 'T');
            $propertyHideOnDomain->setAttribute('off_value', 'F');
            $propertyHideOnDomain->setAttribute('onclick-js', '');
            $propertyHideOnDomain->setAttribute('inherit_value', 'F');
            $propertyHideOnDomain->setAttribute('readonly', 'F');
            $this->propertyManager()->saveProperty($propertyHideOnDomain);
        } else {
            $this->writeLine('Property with tag hide_on_domain already exists');
            $this->setDataKey('property_hide_on_domain_existed', true);
        }

        // property: Nezobrazovať na doméne [CZ](hide_on_domain_cz)
        $propertyHideOnDomainCz = $this->propertyManager()->propertyExistsByTag('hide_on_domain_cz');
        if ($propertyHideOnDomainCz === false) {
            $propertyHideOnDomainCz = new Property();
            $propertyHideOnDomainCz->setTag('hide_on_domain_cz');
            $propertyHideOnDomainCz->setDescription('Ak je táto vlastnosť zaškrtnutá, daný produkt, kategória sa nebude zobrazovať na doméne.');
            $propertyHideOnDomainCz->setExtendedDescription('');
            $propertyHideOnDomainCz->setName('Nezobrazovať na doméne [CZ]');
            $propertyHideOnDomainCz->setClassId(4);
            $propertyHideOnDomainCz->setShowType(null);
            $propertyHideOnDomainCz->setShowTypeTag('checkbox');
            $propertyHideOnDomainCz->setValueType('logical_value');
            $propertyHideOnDomainCz->setDefaultValue('F');
            $propertyHideOnDomainCz->setMultiOperations(false);
            $propertyHideOnDomainCz->setInputString('');
            $propertyHideOnDomainCz->setAttribute('tab', 'CZ');
            $propertyHideOnDomainCz->setAttribute('on_value', 'T');
            $propertyHideOnDomainCz->setAttribute('off_value', 'F');
            $propertyHideOnDomainCz->setAttribute('onclick-js', '');
            $propertyHideOnDomainCz->setAttribute('inherit_value', 'F');
            $propertyHideOnDomainCz->setAttribute('readonly', 'F');
            $this->propertyManager()->saveProperty($propertyHideOnDomainCz);
        } else {
            $this->writeLine('Property with tag hide_on_domain_cz already exists');
            $this->setDataKey('property_hide_on_domain_cz_existed', true);
        }

        // property: Nezobrazovať na doméne [EN](hide_on_domain_en)
        $propertyHideOnDomainEn = $this->propertyManager()->propertyExistsByTag('hide_on_domain_en');
        if ($propertyHideOnDomainEn === false) {
            $propertyHideOnDomainEn = new Property();
            $propertyHideOnDomainEn->setTag('hide_on_domain_en');
            $propertyHideOnDomainEn->setDescription('Ak je táto vlastnosť zaškrtnutá, daný produkt, kategória sa nebude zobrazovať na doméne.');
            $propertyHideOnDomainEn->setExtendedDescription('');
            $propertyHideOnDomainEn->setName('Nezobrazovať na doméne [EN]');
            $propertyHideOnDomainEn->setClassId(4);
            $propertyHideOnDomainEn->setShowType(null);
            $propertyHideOnDomainEn->setShowTypeTag('checkbox');
            $propertyHideOnDomainEn->setValueType('logical_value');
            $propertyHideOnDomainEn->setDefaultValue('F');
            $propertyHideOnDomainEn->setMultiOperations(false);
            $propertyHideOnDomainEn->setInputString('');
            $propertyHideOnDomainEn->setAttribute('tab', 'EN');
            $propertyHideOnDomainEn->setAttribute('on_value', 'T');
            $propertyHideOnDomainEn->setAttribute('off_value', 'F');
            $propertyHideOnDomainEn->setAttribute('onclick-js', '');
            $propertyHideOnDomainEn->setAttribute('inherit_value', 'F');
            $propertyHideOnDomainEn->setAttribute('readonly', 'F');
            $this->propertyManager()->saveProperty($propertyHideOnDomainEn);
        } else {
            $this->writeLine('Property with tag hide_on_domain_en already exists');
            $this->setDataKey('property_hide_on_domain_en_existed', true);
        }

        // property: Tag(eshop_tag)
        $propertyEshopTag = $this->propertyManager()->propertyExistsByTag('eshop_tag');
        if ($propertyEshopTag === false) {
            $propertyEshopTag = new Property();
            $propertyEshopTag->setTag('eshop_tag');
            $propertyEshopTag->setDescription('Tag stránky, slúži na identifikovanie účelu stránky.');
            $propertyEshopTag->setExtendedDescription(null);
            $propertyEshopTag->setName('Tag');
            $propertyEshopTag->setClassId(4);
            $propertyEshopTag->setShowType(null);
            $propertyEshopTag->setShowTypeTag('text');
            $propertyEshopTag->setValueType('oneline_text');
            $propertyEshopTag->setDefaultValue('');
            $propertyEshopTag->setMultiOperations(false);
            $propertyEshopTag->setInputString(null);
            $propertyEshopTag->setAttribute('tab', '');
            $propertyEshopTag->setAttribute('size', '25');
            $propertyEshopTag->setAttribute('maxlength', '');
            $propertyEshopTag->setAttribute('readonly', 'F');
            $propertyEshopTag->setAttribute('pattern', '');
            $propertyEshopTag->setAttribute('inherit_value', 'F');
            $propertyEshopTag->setAttribute('onchange-js', '');
            $propertyEshopTag->setAttribute('onkeyup-js', '');
            $propertyEshopTag->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyEshopTag);
        } else {
            $this->writeLine('Property with tag eshop_tag already exists');
            $this->setDataKey('property_eshop_tag_existed', true);
        }

        // page type: Podtyp dopravy (delivery_subtype)
        $pageTypeDeliverySubtype = $this->pageTypesManager()->pageTypeExistsByTag('delivery_subtype');
        if ($pageTypeDeliverySubtype === false) {
            $pageTypeDeliverySubtype = new PageType();
            $pageTypeDeliverySubtype->setTag('delivery_subtype');
            $pageTypeDeliverySubtype->setName('Podtyp dopravy');
            $pageTypeDeliverySubtype->setPageClassId(1);
            $pageTypeDeliverySubtype->setDefaultTemplateId(1);
            $pageTypeDeliverySubtype->setDeleteTrigger(null);
            $pageTypeDeliverySubtype->setIncludeInSync(null);
            $pageTypeDeliverySubtype->setPageDetailsLayout('');
            $pageTypeDeliverySubtype->setPageSortTypeTag('sort_date_time');
            $pageTypeDeliverySubtype->setPageTypeOrder(0);
            $pageTypeDeliverySubtype->setPostmoveTrigger(null);
            $pageTypeDeliverySubtype->setPostsubmitTrigger(null);
            $pageTypeDeliverySubtype->setPresubmitTrigger(null);
            $pageTypeDeliverySubtype->setParent(null);
        } else {
            $this->writeLine('Page type with tag delivery_subtype already exists');
            $this->setDataKey('page_type_delivery_subtype_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('title');
        $propertyId = $property->getId();
        $tmp = $pageTypeDeliverySubtype->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(1);
            $tmp->setRequired(false);
            $pageTypeDeliverySubtype->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('title_cz');
        $propertyId = $property->getId();
        $tmp = $pageTypeDeliverySubtype->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(2);
            $tmp->setRequired(false);
            $pageTypeDeliverySubtype->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('title_en');
        $propertyId = $property->getId();
        $tmp = $pageTypeDeliverySubtype->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(3);
            $tmp->setRequired(false);
            $pageTypeDeliverySubtype->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('hide_on_domain');
        $propertyId = $property->getId();
        $tmp = $pageTypeDeliverySubtype->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(4);
            $tmp->setRequired(false);
            $pageTypeDeliverySubtype->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('hide_on_domain_cz');
        $propertyId = $property->getId();
        $tmp = $pageTypeDeliverySubtype->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(5);
            $tmp->setRequired(false);
            $pageTypeDeliverySubtype->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('hide_on_domain_en');
        $propertyId = $property->getId();
        $tmp = $pageTypeDeliverySubtype->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(6);
            $tmp->setRequired(false);
            $pageTypeDeliverySubtype->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('eshop_tag');
        $propertyId = $property->getId();
        $tmp = $pageTypeDeliverySubtype->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(7);
            $tmp->setRequired(false);
            $pageTypeDeliverySubtype->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($pageTypeDeliverySubtype);

        if ($this->pageTypeExists('eshop_transport_type')) {
            $this->addPageTypeSuperiorPageType('delivery_subtype', 'eshop_transport_type');
        }

        // page: Kuriérska služba(ID: 137074 TAG: courier)
        $pageId = $this->getPageIdByTag('courier');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('eshop_transport_type');
            $page137074 = \PageFactory::create($this->getPageIdByTag('eshop_delivery_types'), $pageType->getId());
        } else {
            $page137074 = \PageFactory::get($pageId);
        }
        $page137074->setPageName('Kuriérska služba');
        $page137074->setPageTag('courier');
        $page137074->setPageStateId(1);
        $page137074->setPageClassId(1);
        $page137074->setValue('exchange_rate_cz', '');
        $page137074->setValue('title', 'Kuriérska služba');
        $page137074->setValue('title_en', 'Courier service');
        $page137074->setValue('title_cz', 'Kurýrní služba');
        $page137074->setValue('eshop_tag', 'courier');
        $page137074->setValue('eshop_description', 'Zásielka Vám bude doručená na druhý pracovný deň po vyexpedovaní');
        $page137074->setValue('eshop_eur_price_including_vat', '4');
        $page137074->setValue('testing_active', 'F');
        $page137074->setValue('minimal_price_for_free_delivery', '');
        $page137074->setValue('eshop_eur_price_including_vat_cz', '150');
        $page137074->save();

        // page: UPS(ID: 655813 TAG: )
        $pageType = $this->pageTypesManager()->getPageTypeByTag('delivery_subtype');
        $page655813 = \PageFactory::create($page137074->getPageId(), $pageType->getId());
        $page655813->setPageName('UPS');
        $page655813->setPageTag(null);
        $page655813->setPageStateId('1');
        $page655813->setPageClassId(1);
        $page655813->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page655813->setValue('title', 'UPS');
        $page655813->setValue('title_cz', '');
        $page655813->setValue('title_en', '');
        $page655813->setValue('hide_on_domain', 'F');
        $page655813->setValue('hide_on_domain_cz', 'T');
        $page655813->setValue('hide_on_domain_en', 'T');
        $page655813->setValue('eshop_tag', 'ups');
        $page655813->save();

        // page: DPD(ID: 655814 TAG: )
        $pageType = $this->pageTypesManager()->getPageTypeByTag('delivery_subtype');
        $page655814 = \PageFactory::create($page137074->getPageId(), $pageType->getId());
        $page655814->setPageName('DPD');
        $page655814->setPageTag(null);
        $page655814->setPageStateId('1');
        $page655814->setPageClassId(1);
        $page655814->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page655814->setValue('title', 'DPD');
        $page655814->setValue('title_cz', '');
        $page655814->setValue('title_en', '');
        $page655814->setValue('hide_on_domain', 'F');
        $page655814->setValue('hide_on_domain_cz', 'T');
        $page655814->setValue('hide_on_domain_en', 'T');
        $page655814->setValue('eshop_tag', 'dpd');
        $page655814->save();

        // page: PPL(ID: 655815 TAG: )
        $pageType = $this->pageTypesManager()->getPageTypeByTag('delivery_subtype');
        $page655815 = \PageFactory::create($page137074->getPageId(), $pageType->getId());
        $page655815->setPageName('PPL');
        $page655815->setPageTag(null);
        $page655815->setPageStateId('1');
        $page655815->setPageClassId(1);
        $page655815->setSortDateTime(date('Y-m-d H:i:s', time() + 2));
        $page655815->setValue('title', 'PPL');
        $page655815->setValue('title_cz', '');
        $page655815->setValue('title_en', '');
        $page655815->setValue('hide_on_domain', 'T');
        $page655815->setValue('hide_on_domain_cz', 'F');
        $page655815->setValue('hide_on_domain_en', 'T');
        $page655815->setValue('eshop_tag', 'ppl');
        $page655815->save();

        // page: Zavolejsikurýra(ID: 655816 TAG: )
        $pageType = $this->pageTypesManager()->getPageTypeByTag('delivery_subtype');
        $page655816 = \PageFactory::create($page137074->getPageId(), $pageType->getId());
        $page655816->setPageName('Zavolejsikurýra');
        $page655816->setPageTag(null);
        $page655816->setPageStateId('1');
        $page655816->setPageClassId(1);
        $page655816->setSortDateTime(date('Y-m-d H:i:s', time() + 3));
        $page655816->setValue('title', 'Zavolejsikurýra');
        $page655816->setValue('title_cz', '');
        $page655816->setValue('title_en', '');
        $page655816->setValue('hide_on_domain', 'T');
        $page655816->setValue('hide_on_domain_cz', 'F');
        $page655816->setValue('hide_on_domain_en', 'T');
        $page655816->setValue('eshop_tag', 'zavolejsikuryra');
        $page655816->save();

        // page: GLS(ID: 655817 TAG: )
        $pageType = $this->pageTypesManager()->getPageTypeByTag('delivery_subtype');
        $page655817 = \PageFactory::create($page137074->getPageId(), $pageType->getId());
        $page655817->setPageName('GLS');
        $page655817->setPageTag(null);
        $page655817->setPageStateId('1');
        $page655817->setPageClassId(1);
        $page655817->setSortDateTime(date('Y-m-d H:i:s', time() + 4));
        $page655817->setValue('title', 'GLS');
        $page655817->setValue('title_cz', '');
        $page655817->setValue('title_en', '');
        $page655817->setValue('hide_on_domain', 'T');
        $page655817->setValue('hide_on_domain_cz', 'T');
        $page655817->setValue('hide_on_domain_en', 'F');
        $page655817->setValue('eshop_tag', 'gls');
        $page655817->save();

        $page137074->setValue('eshop_payment_type', [
            ]);
        $page137074->save();
        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();
    }

    public function down()
    {
        // remove page: Kuriérska služba (courier)
        $pageId = $this->getPageIdByTag('courier');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }

        // remove page type: Podtyp dopravy (delivery_subtype)
        $pageTypeDeliverySubtype = $this->pageTypesManager()->pageTypeExistsByTag('delivery_subtype');
        if (($pageTypeDeliverySubtype != false) && (is_null($this->getDataKey('page_type_delivery_subtype_existed')))) {
            $this->pageTypesManager()->removePageType($pageTypeDeliverySubtype);
        }

        // remove property: Tag(eshop_tag)
        $propertyEshopTag = $this->propertyManager()->propertyExistsByTag('eshop_tag');
        if (($propertyEshopTag !== false) && ($this->getDataKey('property_eshop_tag_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyEshopTag);
        }

        // remove property: Nezobrazovať na doméne [EN](hide_on_domain_en)
        $propertyHideOnDomainEn = $this->propertyManager()->propertyExistsByTag('hide_on_domain_en');
        if (($propertyHideOnDomainEn !== false) && ($this->getDataKey('property_hide_on_domain_en_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyHideOnDomainEn);
        }

        // remove property: Nezobrazovať na doméne [CZ](hide_on_domain_cz)
        $propertyHideOnDomainCz = $this->propertyManager()->propertyExistsByTag('hide_on_domain_cz');
        if (($propertyHideOnDomainCz !== false) && ($this->getDataKey('property_hide_on_domain_cz_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyHideOnDomainCz);
        }

        // remove property: Nezobrazovať na doméne(hide_on_domain)
        $propertyHideOnDomain = $this->propertyManager()->propertyExistsByTag('hide_on_domain');
        if (($propertyHideOnDomain !== false) && ($this->getDataKey('property_hide_on_domain_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyHideOnDomain);
        }

        // remove property: Titulok [EN](title_en)
        $propertyTitleEn = $this->propertyManager()->propertyExistsByTag('title_en');
        if (($propertyTitleEn !== false) && ($this->getDataKey('property_title_en_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyTitleEn);
        }

        // remove property: Titulok [CZ](title_cz)
        $propertyTitleCz = $this->propertyManager()->propertyExistsByTag('title_cz');
        if (($propertyTitleCz !== false) && ($this->getDataKey('property_title_cz_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyTitleCz);
        }

        // remove property: Titulok(title)
        $propertyTitle = $this->propertyManager()->propertyExistsByTag('title');
        if (($propertyTitle !== false) && ($this->getDataKey('property_title_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyTitle);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();
    }
}
