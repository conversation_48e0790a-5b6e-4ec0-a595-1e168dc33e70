<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2023-03-10 09:38:45
 * Page generator: page_id=746043,746044
 * Property generator: property=febi_bilstein_supplier_code,febi_bilstein_oe_number,febi_bilstein_oe_numbers,febi_bilstein_latest_import,febi_bilstein_price_without_vat,febi_bilstein_stock_balance
 */
class FebiBilsteinPropertiesAndPagesMigration extends AbstractMigration
{
    public function up()
    {
        // page: FEBI Bilstein(ID: 746043 TAG: febi_bilstein_settings)
        $pageId = $this->getPageIdByTag('febi_bilstein_settings');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('supplier_catalog_settings');
            $page746043 = \PageFactory::create($this->getPageIdByTag('suppliers_settings'), $pageType->getId());
        } else {
            $page746043 = \PageFactory::get($pageId);
        }
        $page746043->setPageName('FEBI Bilstein');
        $page746043->setPageTag('febi_bilstein_settings');
        $page746043->setPageStateId('2');
        $page746043->setPageClassId(1);
        $page746043->setValue('transport_surcharge', '10');
        $page746043->save();

        // page: FEBI Bilstein 1(ID: 746044 TAG: febi_bilstein_supplier)
        $pageId = $this->getPageIdByTag('febi_bilstein_supplier');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('rolller_item_supplier');
            $page746044 = \PageFactory::create($this->getPageIdByTag('ciselnik-dodavatel'), $pageType->getId());
        } else {
            $page746044 = \PageFactory::get($pageId);
        }
        $page746044->setPageName('FEBI Bilstein 1');
        $page746044->setPageTag('febi_bilstein_supplier');
        $page746044->setPageStateId('1');
        $page746044->setPageClassId(1);
        $page746044->setValue('title', 'FEBI Bilstein');
        $page746044->setValue('title_en', '');
        $page746044->setValue('title_cz', '');
        $page746044->setValue('image', '');
        $page746044->setValue('delivery_time', '');
        $page746044->setValue('delivery_time_cz', '');
        $page746044->setValue('delivery_time_en', '');
        $page746044->save();

        // property: FEBI Bilstein Dodávateľský kód(febi_bilstein_supplier_code)
        $propertyFebiBilsteinSupplierCode = $this->propertyManager()->propertyExistsByTag('febi_bilstein_supplier_code');
        if ($propertyFebiBilsteinSupplierCode === false) {
            $propertyFebiBilsteinSupplierCode = new Property();
            $propertyFebiBilsteinSupplierCode->setTag('febi_bilstein_supplier_code');
            $propertyFebiBilsteinSupplierCode->setDescription('');
            $propertyFebiBilsteinSupplierCode->setExtendedDescription('');
            $propertyFebiBilsteinSupplierCode->setName('FEBI Bilstein Dodávateľský kód');
            $propertyFebiBilsteinSupplierCode->setClassId(4);
            $propertyFebiBilsteinSupplierCode->setShowType(null);
            $propertyFebiBilsteinSupplierCode->setShowTypeTag('text');
            $propertyFebiBilsteinSupplierCode->setValueType('oneline_text');
            $propertyFebiBilsteinSupplierCode->setDefaultValue('');
            $propertyFebiBilsteinSupplierCode->setMultiOperations(false);
            $propertyFebiBilsteinSupplierCode->setInputString('');
            $propertyFebiBilsteinSupplierCode->setAttribute('tab', 'FEBI Bilstein');
            $propertyFebiBilsteinSupplierCode->setAttribute('size', '60');
            $propertyFebiBilsteinSupplierCode->setAttribute('maxlength', '');
            $propertyFebiBilsteinSupplierCode->setAttribute('readonly', 'F');
            $propertyFebiBilsteinSupplierCode->setAttribute('pattern', '');
            $propertyFebiBilsteinSupplierCode->setAttribute('inherit_value', 'F');
            $propertyFebiBilsteinSupplierCode->setAttribute('onchange-js', '');
            $propertyFebiBilsteinSupplierCode->setAttribute('onkeyup-js', '');
            $propertyFebiBilsteinSupplierCode->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyFebiBilsteinSupplierCode);
        } else {
            $this->writeLine('Property with tag febi_bilstein_supplier_code already exists');
            $this->setDataKey('property_febi_bilstein_supplier_code_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('febi_bilstein_supplier_code', 'eshop_product', false);
        }

        // property: FEBI Bilstein - OE number(febi_bilstein_oe_number)
        $propertyFebiBilsteinOeNumber = $this->propertyManager()->propertyExistsByTag('febi_bilstein_oe_number');
        if ($propertyFebiBilsteinOeNumber === false) {
            $propertyFebiBilsteinOeNumber = new Property();
            $propertyFebiBilsteinOeNumber->setTag('febi_bilstein_oe_number');
            $propertyFebiBilsteinOeNumber->setDescription('');
            $propertyFebiBilsteinOeNumber->setExtendedDescription('');
            $propertyFebiBilsteinOeNumber->setName('FEBI Bilstein - OE number');
            $propertyFebiBilsteinOeNumber->setClassId(4);
            $propertyFebiBilsteinOeNumber->setShowType(null);
            $propertyFebiBilsteinOeNumber->setShowTypeTag('text');
            $propertyFebiBilsteinOeNumber->setValueType('oneline_text');
            $propertyFebiBilsteinOeNumber->setDefaultValue('');
            $propertyFebiBilsteinOeNumber->setMultiOperations(false);
            $propertyFebiBilsteinOeNumber->setInputString('');
            $propertyFebiBilsteinOeNumber->setAttribute('tab', 'FEBI Bilstein');
            $propertyFebiBilsteinOeNumber->setAttribute('size', '60');
            $propertyFebiBilsteinOeNumber->setAttribute('maxlength', '');
            $propertyFebiBilsteinOeNumber->setAttribute('readonly', 'F');
            $propertyFebiBilsteinOeNumber->setAttribute('pattern', '');
            $propertyFebiBilsteinOeNumber->setAttribute('inherit_value', 'F');
            $propertyFebiBilsteinOeNumber->setAttribute('onchange-js', '');
            $propertyFebiBilsteinOeNumber->setAttribute('onkeyup-js', '');
            $propertyFebiBilsteinOeNumber->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyFebiBilsteinOeNumber);
        } else {
            $this->writeLine('Property with tag febi_bilstein_oe_number already exists');
            $this->setDataKey('property_febi_bilstein_oe_number_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('febi_bilstein_oe_number', 'eshop_product', false);
        }

        // property: FEBI Bilstein - OE numbers(febi_bilstein_oe_numbers)
        $propertyFebiBilsteinOeNumbers = $this->propertyManager()->propertyExistsByTag('febi_bilstein_oe_numbers');
        if ($propertyFebiBilsteinOeNumbers === false) {
            $propertyFebiBilsteinOeNumbers = new Property();
            $propertyFebiBilsteinOeNumbers->setTag('febi_bilstein_oe_numbers');
            $propertyFebiBilsteinOeNumbers->setDescription('');
            $propertyFebiBilsteinOeNumbers->setExtendedDescription('');
            $propertyFebiBilsteinOeNumbers->setName('FEBI Bilstein - OE numbers');
            $propertyFebiBilsteinOeNumbers->setClassId(4);
            $propertyFebiBilsteinOeNumbers->setShowType(null);
            $propertyFebiBilsteinOeNumbers->setShowTypeTag('text');
            $propertyFebiBilsteinOeNumbers->setValueType('oneline_text');
            $propertyFebiBilsteinOeNumbers->setDefaultValue('');
            $propertyFebiBilsteinOeNumbers->setMultiOperations(false);
            $propertyFebiBilsteinOeNumbers->setInputString('');
            $propertyFebiBilsteinOeNumbers->setAttribute('tab', 'FEBI Bilstein');
            $propertyFebiBilsteinOeNumbers->setAttribute('size', '60');
            $propertyFebiBilsteinOeNumbers->setAttribute('maxlength', '');
            $propertyFebiBilsteinOeNumbers->setAttribute('readonly', 'F');
            $propertyFebiBilsteinOeNumbers->setAttribute('pattern', '');
            $propertyFebiBilsteinOeNumbers->setAttribute('inherit_value', 'F');
            $propertyFebiBilsteinOeNumbers->setAttribute('onchange-js', '');
            $propertyFebiBilsteinOeNumbers->setAttribute('onkeyup-js', '');
            $propertyFebiBilsteinOeNumbers->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyFebiBilsteinOeNumbers);
        } else {
            $this->writeLine('Property with tag febi_bilstein_oe_numbers already exists');
            $this->setDataKey('property_febi_bilstein_oe_numbers_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('febi_bilstein_oe_numbers', 'eshop_product', false);
        }

        // property: FEBI Bilstein - posledný import(febi_bilstein_latest_import)
        $propertyFebiBilsteinLatestImport = $this->propertyManager()->propertyExistsByTag('febi_bilstein_latest_import');
        if ($propertyFebiBilsteinLatestImport === false) {
            $propertyFebiBilsteinLatestImport = new Property();
            $propertyFebiBilsteinLatestImport->setTag('febi_bilstein_latest_import');
            $propertyFebiBilsteinLatestImport->setDescription('');
            $propertyFebiBilsteinLatestImport->setExtendedDescription('');
            $propertyFebiBilsteinLatestImport->setName('FEBI Bilstein - posledný import');
            $propertyFebiBilsteinLatestImport->setClassId(4);
            $propertyFebiBilsteinLatestImport->setShowType(null);
            $propertyFebiBilsteinLatestImport->setShowTypeTag('text');
            $propertyFebiBilsteinLatestImport->setValueType('oneline_text');
            $propertyFebiBilsteinLatestImport->setDefaultValue('');
            $propertyFebiBilsteinLatestImport->setMultiOperations(false);
            $propertyFebiBilsteinLatestImport->setInputString('');
            $propertyFebiBilsteinLatestImport->setAttribute('tab', 'FEBI Bilstein');
            $propertyFebiBilsteinLatestImport->setAttribute('size', '60');
            $propertyFebiBilsteinLatestImport->setAttribute('maxlength', '');
            $propertyFebiBilsteinLatestImport->setAttribute('readonly', 'F');
            $propertyFebiBilsteinLatestImport->setAttribute('pattern', '');
            $propertyFebiBilsteinLatestImport->setAttribute('inherit_value', 'F');
            $propertyFebiBilsteinLatestImport->setAttribute('onchange-js', '');
            $propertyFebiBilsteinLatestImport->setAttribute('onkeyup-js', '');
            $propertyFebiBilsteinLatestImport->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyFebiBilsteinLatestImport);
        } else {
            $this->writeLine('Property with tag febi_bilstein_latest_import already exists');
            $this->setDataKey('property_febi_bilstein_latest_import_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('febi_bilstein_latest_import', 'eshop_product', false);
        }

        // property: FEBI Bilstein - Cena bez DPH(febi_bilstein_price_without_vat)
        $propertyFebiBilsteinPriceWithoutVat = $this->propertyManager()->propertyExistsByTag('febi_bilstein_price_without_vat');
        if ($propertyFebiBilsteinPriceWithoutVat === false) {
            $propertyFebiBilsteinPriceWithoutVat = new Property();
            $propertyFebiBilsteinPriceWithoutVat->setTag('febi_bilstein_price_without_vat');
            $propertyFebiBilsteinPriceWithoutVat->setDescription('');
            $propertyFebiBilsteinPriceWithoutVat->setExtendedDescription('');
            $propertyFebiBilsteinPriceWithoutVat->setName('FEBI Bilstein - Cena bez DPH');
            $propertyFebiBilsteinPriceWithoutVat->setClassId(4);
            $propertyFebiBilsteinPriceWithoutVat->setShowType(null);
            $propertyFebiBilsteinPriceWithoutVat->setShowTypeTag('text');
            $propertyFebiBilsteinPriceWithoutVat->setValueType('oneline_text');
            $propertyFebiBilsteinPriceWithoutVat->setDefaultValue('');
            $propertyFebiBilsteinPriceWithoutVat->setMultiOperations(false);
            $propertyFebiBilsteinPriceWithoutVat->setInputString('');
            $propertyFebiBilsteinPriceWithoutVat->setAttribute('tab', 'FEBI Bilstein');
            $propertyFebiBilsteinPriceWithoutVat->setAttribute('size', '60');
            $propertyFebiBilsteinPriceWithoutVat->setAttribute('maxlength', '');
            $propertyFebiBilsteinPriceWithoutVat->setAttribute('readonly', 'F');
            $propertyFebiBilsteinPriceWithoutVat->setAttribute('pattern', '');
            $propertyFebiBilsteinPriceWithoutVat->setAttribute('inherit_value', 'F');
            $propertyFebiBilsteinPriceWithoutVat->setAttribute('onchange-js', '');
            $propertyFebiBilsteinPriceWithoutVat->setAttribute('onkeyup-js', '');
            $propertyFebiBilsteinPriceWithoutVat->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyFebiBilsteinPriceWithoutVat);
        } else {
            $this->writeLine('Property with tag febi_bilstein_price_without_vat already exists');
            $this->setDataKey('property_febi_bilstein_price_without_vat_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('febi_bilstein_price_without_vat', 'eshop_product', false);
        }

        // property: FEBI Bilstein - Skladová zásoba(febi_bilstein_stock_balance)
        $propertyFebiBilsteinStockBalance = $this->propertyManager()->propertyExistsByTag('febi_bilstein_stock_balance');
        if ($propertyFebiBilsteinStockBalance === false) {
            $propertyFebiBilsteinStockBalance = new Property();
            $propertyFebiBilsteinStockBalance->setTag('febi_bilstein_stock_balance');
            $propertyFebiBilsteinStockBalance->setDescription('');
            $propertyFebiBilsteinStockBalance->setExtendedDescription('');
            $propertyFebiBilsteinStockBalance->setName('FEBI Bilstein - Skladová zásoba');
            $propertyFebiBilsteinStockBalance->setClassId(4);
            $propertyFebiBilsteinStockBalance->setShowType(null);
            $propertyFebiBilsteinStockBalance->setShowTypeTag('text');
            $propertyFebiBilsteinStockBalance->setValueType('oneline_text');
            $propertyFebiBilsteinStockBalance->setDefaultValue('');
            $propertyFebiBilsteinStockBalance->setMultiOperations(false);
            $propertyFebiBilsteinStockBalance->setInputString('');
            $propertyFebiBilsteinStockBalance->setAttribute('tab', 'FEBI Bilstein');
            $propertyFebiBilsteinStockBalance->setAttribute('size', '60');
            $propertyFebiBilsteinStockBalance->setAttribute('maxlength', '');
            $propertyFebiBilsteinStockBalance->setAttribute('readonly', 'F');
            $propertyFebiBilsteinStockBalance->setAttribute('pattern', '');
            $propertyFebiBilsteinStockBalance->setAttribute('inherit_value', 'F');
            $propertyFebiBilsteinStockBalance->setAttribute('onchange-js', '');
            $propertyFebiBilsteinStockBalance->setAttribute('onkeyup-js', '');
            $propertyFebiBilsteinStockBalance->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyFebiBilsteinStockBalance);
        } else {
            $this->writeLine('Property with tag febi_bilstein_stock_balance already exists');
            $this->setDataKey('property_febi_bilstein_stock_balance_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('febi_bilstein_stock_balance', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: FEBI Bilstein - Skladová zásoba(febi_bilstein_stock_balance)
        $propertyFebiBilsteinStockBalance = $this->propertyManager()->propertyExistsByTag('febi_bilstein_stock_balance');
        if (($propertyFebiBilsteinStockBalance !== false) && ($this->getDataKey('property_febi_bilstein_stock_balance_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyFebiBilsteinStockBalance);
        }

        // remove property: FEBI Bilstein - Cena bez DPH(febi_bilstein_price_without_vat)
        $propertyFebiBilsteinPriceWithoutVat = $this->propertyManager()->propertyExistsByTag('febi_bilstein_price_without_vat');
        if (($propertyFebiBilsteinPriceWithoutVat !== false) && ($this->getDataKey('property_febi_bilstein_price_without_vat_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyFebiBilsteinPriceWithoutVat);
        }

        // remove property: FEBI Bilstein - posledný import(febi_bilstein_latest_import)
        $propertyFebiBilsteinLatestImport = $this->propertyManager()->propertyExistsByTag('febi_bilstein_latest_import');
        if (($propertyFebiBilsteinLatestImport !== false) && ($this->getDataKey('property_febi_bilstein_latest_import_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyFebiBilsteinLatestImport);
        }

        // remove property: FEBI Bilstein - OE numbers(febi_bilstein_oe_numbers)
        $propertyFebiBilsteinOeNumbers = $this->propertyManager()->propertyExistsByTag('febi_bilstein_oe_numbers');
        if (($propertyFebiBilsteinOeNumbers !== false) && ($this->getDataKey('property_febi_bilstein_oe_numbers_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyFebiBilsteinOeNumbers);
        }

        // remove property: FEBI Bilstein - OE number(febi_bilstein_oe_number)
        $propertyFebiBilsteinOeNumber = $this->propertyManager()->propertyExistsByTag('febi_bilstein_oe_number');
        if (($propertyFebiBilsteinOeNumber !== false) && ($this->getDataKey('property_febi_bilstein_oe_number_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyFebiBilsteinOeNumber);
        }

        // remove property: FEBI Bilstein Dodávateľský kód(febi_bilstein_supplier_code)
        $propertyFebiBilsteinSupplierCode = $this->propertyManager()->propertyExistsByTag('febi_bilstein_supplier_code');
        if (($propertyFebiBilsteinSupplierCode !== false) && ($this->getDataKey('property_febi_bilstein_supplier_code_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyFebiBilsteinSupplierCode);
        }

        // remove page: FEBI Bilstein 1 (febi_bilstein_supplier)
        $pageId = $this->getPageIdByTag('febi_bilstein_supplier');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }

        // remove page: FEBI Bilstein (febi_bilstein_settings)
        $pageId = $this->getPageIdByTag('febi_bilstein_settings');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
