<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (buxus_rinoparts_test) at 2022-02-21 13:31:31
 * Page generator: page_id=655818
 */
class RegistrationCountryPickPageMigration extends AbstractMigration
{
    public function up()
    {
        // page: Registrácia - výber krajiny(ID: 655818 TAG: registration_country)
        $pageId = $this->getPageIdByTag('registration_country');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('service_page');
            $page655818 = \PageFactory::create($this->getPageIdByTag('authentification'), $pageType->getId());
        } else {
            $page655818 = \PageFactory::get($pageId);
        }
        $page655818->setPageName('Registrácia - výber krajiny');
        $page655818->setPageTag('registration_country');
        $page655818->setPageStateId('1');
        $page655818->setPageClassId(1);
        $page655818->setValue('title', 'Registrácia - výber krajiny');
        $page655818->setValue('title_en', 'Registration - pick your country');
        $page655818->setValue('title_cz', 'Registrace - výběr krajiny');
        $page655818->setValue('text', '<p>Pred registráciou je potrebné si vybrať krajinu.</p>');
        $page655818->setValue('text_en', '');
        $page655818->setValue('text_cz', '');
        $page655818->setValue('seo_url_name', '/registracia-vyber-krajiny');
        $page655818->setValue('meta_title', '');
        $page655818->setValue('seo_url_name_en', '/registration-pick-your-country');
        $page655818->setValue('meta_title_en', '');
        $page655818->setValue('seo_url_name_cz', '/registrace-vyber-krajiny');
        $page655818->setValue('meta_title_cz', '');
        // set template authentication::registration-country
        $page655818->getPageTemplate()->setController('authentication');
        $page655818->getPageTemplate()->setAction('registration-country');
        $page655818->save();
    }

    public function down()
    {
        // remove page: Registrácia - výber krajiny (registration_country)
        $pageId = $this->getPageIdByTag('registration_country');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }
    }
}
