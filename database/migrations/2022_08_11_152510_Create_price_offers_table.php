<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Automatic generation from (rinoparts) at 2022-08-11 15:25:10
 */
class Create_price_offers_table extends AbstractMigration
{
    public function up()
    {
        // create table tblPriceOffers
        Schema::create('tblPriceOffers', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('saved_by');
            $table->string('saved_for');
            $table->string('tag');
            $table->string('state');
            $table->timestamps();
        });
    }

    public function down()
    {
        // drop table tblPriceOffers
        Schema::dropIfExists('tblPriceOffers');
    }
}
