<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (rinoparts) at 2024-08-07 09:59:33
 */
class AddedDoNotSendInvoiceRemindersSimpleEntityMigration extends AbstractMigration
{
    public function up()
    {
        \DB::table('tblSimpleEntities')
            ->insert([
                'entity_tag' => 'do_not_send_invoice_reminders',
                'entity_name' => 'Neposielať upomienky na faktúry',
                'entity_type_tag' => 'web_user_option'
            ]);
    }

    public function down()
    {
        \DB::table('tblSimpleEntities')
            ->where('entity_tag', 'do_not_send_invoice_reminders')
            ->delete();
    }
}
