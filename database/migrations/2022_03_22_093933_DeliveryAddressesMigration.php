<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (buxus_rinoparts_test) at 2022-03-22 09:39:33
 * Page generator: page_id=655826
 */
class DeliveryAddressesMigration extends AbstractMigration
{
    public function up()
    {
        // page: Dodacie adresy(ID: 655826 TAG: delivery_addresses)
        $pageId = $this->getPageIdByTag('delivery_addresses');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('service_page');
            $page655826 = \PageFactory::create($this->getPageIdByTag('auth_klientska_zona'), $pageType->getId());
        } else {
            $page655826 = \PageFactory::get($pageId);
        }
        $page655826->setPageName('Dodacie adresy');
        $page655826->setPageTag('delivery_addresses');
        $page655826->setPageStateId(1);
        $page655826->setPageClassId(1);
        $page655826->setValue('title', 'Dodacie adresy');
        $page655826->setValue('title_en', 'Dodacie adresy');
        $page655826->setValue('title_cz', 'Dodacie adresy');
        $page655826->setValue('text', '');
        $page655826->setValue('text_en', '');
        $page655826->setValue('text_cz', '');
        $page655826->setValue('seo_url_name', null);
        $page655826->setValue('meta_title', '');
        $page655826->setValue('seo_url_name_en', null);
        $page655826->setValue('meta_title_en', '');
        $page655826->setValue('seo_url_name_cz', null);
        $page655826->setValue('meta_title_cz', '');
        // set template user::addresses
        $page655826->getPageTemplate()->setController('user');
        $page655826->getPageTemplate()->setAction('addresses');
        $page655826->save();
    }

    public function down()
    {
        // remove page: Dodacie adresy (delivery_addresses)
        $pageId = $this->getPageIdByTag('delivery_addresses');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }
    }
}
