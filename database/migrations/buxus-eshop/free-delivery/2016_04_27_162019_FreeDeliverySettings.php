<?php

use Buxus\Migration\AbstractMigration;
use Buxus\PageType\PageType;
use Buxus\Property\Property;
use Buxus\PageType\PageTypePropertyItem;
use Buxus\Util\PageIds;

/**
 * Automatic generation from (buxus_predeti_live) at 2016-04-27 16:20:19
 * PageType generator: page_type=eshop_properties
 * Page generator: page_id=12548
 * Property generator: property=free_shipping_limit
 */
class FreeDeliverySettings extends AbstractMigration
{
    public function up()
    {
        // page type: Nastavenia (settings)
        $page_type_settings = $this->pageTypesManager()->pageTypeExistsByTag('settings');
        if ($page_type_settings === false) {
            $page_type_settings = new PageType();
            $page_type_settings->setTag('settings');
            $page_type_settings->setName('Nastavenia');
            $page_type_settings->setPageClassId('1');
            $page_type_settings->setDefaultTemplateId('2');
            $page_type_settings->setDeleteTrigger('');
            $page_type_settings->setIncludeInSync('1');
            $page_type_settings->setPageDetailsLayout('');
            $page_type_settings->setPageSortTypeTag('sort_date_time');
            $page_type_settings->setPageTypeOrder('400');
            $page_type_settings->setPostmoveTrigger('');
            $page_type_settings->setPostsubmitTrigger('');
            $page_type_settings->setPresubmitTrigger('');
            $page_type_settings->setParent(null);
        } else {
            $this->writeLine('Page type with tag settings already exists');
            $this->setDataKey('page_type_settings_existed', true);
        }
        $this->pageTypesManager()->savePageType($page_type_settings);
        // set template on MAIN PAGE index::error404
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('settings'), 'index', 'error404');

        // page type: Nastavenia - eshop (settings_eshop)
        $page_type_settings_eshop = $this->pageTypesManager()->pageTypeExistsByTag('settings_eshop');
        if ($page_type_settings_eshop === false) {
            $page_type_settings_eshop = new PageType();
            $page_type_settings_eshop->setTag('settings_eshop');
            $page_type_settings_eshop->setName('Nastavenia - eshop');
            $page_type_settings_eshop->setPageClassId('1');
            $page_type_settings_eshop->setDefaultTemplateId('2');
            $page_type_settings_eshop->setDeleteTrigger('');
            $page_type_settings_eshop->setIncludeInSync(null);
            $page_type_settings_eshop->setPageDetailsLayout('');
            $page_type_settings_eshop->setPageSortTypeTag('sort_date_time');
            $page_type_settings_eshop->setPageTypeOrder('0');
            $page_type_settings_eshop->setPostmoveTrigger('');
            $page_type_settings_eshop->setPostsubmitTrigger('');
            $page_type_settings_eshop->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('settings');
            $page_type_settings_eshop->setParent($parent);
        } else {
            $this->writeLine('Page type with tag settings_eshop already exists');
            $this->setDataKey('page_type_settings_eshop_existed', true);
        }
        $this->pageTypesManager()->savePageType($page_type_settings_eshop);

        // property: Limit dopravy zadarmo(free_shipping_limit)
        $property_free_shipping_limit = $this->propertyManager()->propertyExistsByTag('free_shipping_limit');
        if ($property_free_shipping_limit === false) {
            $property_free_shipping_limit = new Property();
            $property_free_shipping_limit->setTag('free_shipping_limit');
            $property_free_shipping_limit->setDescription('Limit v defaultnej mene (s DPH), od ktorého je doprava zadarmo');
            $property_free_shipping_limit->setExtendedDescription('');
            $property_free_shipping_limit->setName('Limit dopravy zadarmo');
            $property_free_shipping_limit->setClassId('4');
            $property_free_shipping_limit->setShowType(null);
            $property_free_shipping_limit->setShowTypeTag('number');
            $property_free_shipping_limit->setValueType('number');
            $property_free_shipping_limit->setDefaultValue('');
            $property_free_shipping_limit->setMultiOperations(false);
            $property_free_shipping_limit->setInputString('');
            $property_free_shipping_limit->setAttribute('tab', '');
            $property_free_shipping_limit->setAttribute('size', '10');
            $property_free_shipping_limit->setAttribute('min', '0');
            $property_free_shipping_limit->setAttribute('max', '');
            $property_free_shipping_limit->setAttribute('step', '1');
            $property_free_shipping_limit->setAttribute('inherit_value', 'F');
            $property_free_shipping_limit->setAttribute('onchange-js', '');
            $property_free_shipping_limit->setAttribute('onkeyup-js', '');
            $property_free_shipping_limit->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_free_shipping_limit);
        } else {
            $this->writeLine('Property with tag free_shipping_limit already exists');
            $this->setDataKey('property_free_shipping_limit_existed', true);
        }
        if ($this->pageTypeExists('eshop_properties')) {
            $this->addPropertyToPageType('free_shipping_limit', 'eshop_properties', false);
        }

        // page type: Vlastnosti eshopu (eshop_properties)
        $page_type_eshop_properties = $this->pageTypesManager()->pageTypeExistsByTag('eshop_properties');
        if ($page_type_eshop_properties === false) {
            $page_type_eshop_properties = new PageType();
            $page_type_eshop_properties->setTag('eshop_properties');
            $page_type_eshop_properties->setName('Vlastnosti eshopu');
            $page_type_eshop_properties->setPageClassId('1');
            $page_type_eshop_properties->setDefaultTemplateId('1');
            $page_type_eshop_properties->setDeleteTrigger('');
            $page_type_eshop_properties->setIncludeInSync(null);
            $page_type_eshop_properties->setPageDetailsLayout('');
            $page_type_eshop_properties->setPageSortTypeTag('sort_date_time');
            $page_type_eshop_properties->setPageTypeOrder('0');
            $page_type_eshop_properties->setPostmoveTrigger('');
            $page_type_eshop_properties->setPostsubmitTrigger('');
            $page_type_eshop_properties->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('settings_eshop');
            $page_type_eshop_properties->setParent($parent);
        } else {
            $this->writeLine('Page type with tag eshop_properties already exists');
            $this->setDataKey('page_type_eshop_properties_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('free_shipping_limit');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_properties->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder('1');
            $tmp->setRequired(false);
            $page_type_eshop_properties->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($page_type_eshop_properties);

        if ($this->pageTypeExists('eshop_catalog')) {
            $this->addPageTypeSuperiorPageType('settings', 'eshop_catalog');
        }
        if ($this->pageTypeExists('settings')) {
            $this->addPageTypeSuperiorPageType('settings', 'settings');
        }
        if ($this->pageTypeExists('folder')) {
            $this->addPageTypeSuperiorPageType('settings', 'folder');
        }
        if ($this->pageTypeExists('main_page')) {
            $this->addPageTypeSuperiorPageType('settings', 'main_page');
        }
        if ($this->pageTypeExists('settings')) {
            $this->addPageTypeSuperiorPageType('eshop_properties', 'settings');
        }

        // page: Nastavenie vlastností eshopu(ID: 12548 TAG: eshop_properties_settings)
        $page_id = $this->getPageIdByTag('eshop_properties_settings');
        if ($page_id === null) {
            $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_properties');
            $page_12548 = \PageFactory::create($this->getPageIdByTag('Nastavenia e-shopu'), $page_type->getId());
        } else {
            $page_12548 = \PageFactory::get($page_id);
        }
        $page_12548->setPageName('Nastavenie vlastností eshopu');
        $page_12548->setPageTag('eshop_properties_settings');
        $page_12548->setPageStateId('2');
        $page_12548->setPageClassId('1');
        $page_12548->setPropertyValue('free_shipping_limit', '100');
        $page_12548->save();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page tags
        PageIds::generatePageTagsList();

    }

    public function down()
    {
        // remove page: Nastavenie vlastností eshopu (eshop_properties_settings)
        $page_id = $this->getPageIdByTag('eshop_properties_settings');
        if ($page_id !== null) {
            \PageFactory::get($page_id)->delete();
        }

        // remove page type: Vlastnosti eshopu (eshop_properties)
        $page_type_eshop_properties = $this->pageTypesManager()->pageTypeExistsByTag('eshop_properties');
        if (($page_type_eshop_properties != false) && (is_null($this->getDataKey('page_type_eshop_properties_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_eshop_properties);
        }

        // remove property: Limit dopravy zadarmo(free_shipping_limit)
        $property_free_shipping_limit = $this->propertyManager()->propertyExistsByTag('free_shipping_limit');
        if ($property_free_shipping_limit != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_free_shipping_limit);
            if ((is_null($this->getDataKey('property_free_shipping_limit_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_free_shipping_limit);
            }
        }

        // remove page type: Nastavenia - eshop (settings_eshop)
        $page_type_settings_eshop = $this->pageTypesManager()->pageTypeExistsByTag('settings_eshop');
        if (($page_type_settings_eshop != false) && (is_null($this->getDataKey('page_type_settings_eshop_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_settings_eshop);
        }

        // remove page type: Nastavenia (settings)
        $page_type_settings = $this->pageTypesManager()->pageTypeExistsByTag('settings');
        if (($page_type_settings != false) && (is_null($this->getDataKey('page_type_settings_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_settings);
        }

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page tags
        PageIds::generatePageTagsList();

    }

}
