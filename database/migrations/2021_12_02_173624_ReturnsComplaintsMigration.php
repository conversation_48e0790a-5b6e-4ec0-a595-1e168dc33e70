<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (rinoparts) at 2021-12-02 17:36:22
 * Page generator: page_id=376194,376195,376197
 */
class ReturnsComplaintsMigration extends AbstractMigration
{
    public function up()
    {
        // page: Vrátenie tovaru(ID: 376194 TAG: returns)
        $pageId = $this->getPageIdByTag('returns');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('service_page');
            $page376194 = \PageFactory::create($this->getPageIdByTag('auth_klientska_zona'), $pageType->getId());
        } else {
            $page376194 = \PageFactory::get($pageId);
        }
        $page376194->setPageName('Vrátenie tovaru');
        $page376194->setPageTag('returns');
        $page376194->setPageStateId('1');
        $page376194->setPageClassId(1);
        $page376194->setValue('title', 'Vrátenie tovaru');
        $page376194->setValue('title_en', '');
        $page376194->setValue('title_cz', '');
        $page376194->setValue('text', base64_decode('PHVsPg0KPGxpPlRvdmFyIG3DtMW+ZXRlIHZyw6F0acWlIGRvIDE0IGRuw60gb2QgemFrw7pwZW5pYS48L2xpPg0KPGxpPlRvdmFyIG5hIG9iamVkbsOhdmt1IHNhIG5lZMOhIHZyw6F0acWlLjwvbGk+DQo8bGk+RWxla3RyaWNrw6kgZGllbHkgc2EgbmVkYWrDuiB2csOhdGnFpS48L2xpPg0KPC91bD4='));
        $page376194->setValue('text_en', '');
        $page376194->setValue('text_cz', '');
        $page376194->setValue('seo_url_name', '/moj-ucet/vratenie-tovaru');
        $page376194->setValue('meta_title', '');
        $page376194->setValue('seo_url_name_en', '/moj-ucet-1/vratenie-tovaru');
        $page376194->setValue('meta_title_en', '');
        $page376194->setValue('seo_url_name_cz', '/moj-ucet-2/vratenie-tovaru');
        $page376194->setValue('meta_title_cz', '');
        // set template user::returns
        $page376194->getPageTemplate()->setController('user');
        $page376194->getPageTemplate()->setAction('returns');
        $page376194->save();

        // page: Reklamácie(ID: 376195 TAG: complaints)
        $pageId = $this->getPageIdByTag('complaints');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('service_page');
            $page376195 = \PageFactory::create($this->getPageIdByTag('auth_klientska_zona'), $pageType->getId());
        } else {
            $page376195 = \PageFactory::get($pageId);
        }
        $page376195->setPageName('Reklamácie');
        $page376195->setPageTag('complaints');
        $page376195->setPageStateId('1');
        $page376195->setPageClassId(1);
        $page376195->setValue('title', 'Reklamácie');
        $page376195->setValue('title_en', '');
        $page376195->setValue('title_cz', '');
        $page376195->setValue('text', '');
        $page376195->setValue('text_en', '');
        $page376195->setValue('text_cz', '');
        $page376195->setValue('seo_url_name', '/moj-ucet/reklamacie');
        $page376195->setValue('meta_title', '');
        $page376195->setValue('seo_url_name_en', '/moj-ucet-1/reklamacie');
        $page376195->setValue('meta_title_en', '');
        $page376195->setValue('seo_url_name_cz', '/moj-ucet-2/reklamacie');
        $page376195->setValue('meta_title_cz', '');
        // set template user::complaints
        $page376195->getPageTemplate()->setController('user');
        $page376195->getPageTemplate()->setAction('complaints');
        $page376195->save();

        // page: Reklamačný mail 1(ID: 376197 TAG: complaints_email_html)
        $pageId = $this->getPageIdByTag('complaints_email_html');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('basic_email');
            $page376197 = \PageFactory::create($this->getPageIdByTag('E-maily shop modulu'), $pageType->getId());
        } else {
            $page376197 = \PageFactory::get($pageId);
        }
        $page376197->setPageName('Reklamačný mail 1');
        $page376197->setPageTag('complaints_email_html');
        $page376197->setPageStateId('1');
        $page376197->setPageClassId(1);
        $page376197->setValue('email_sender', '');
        $page376197->setValue('email_recipients', '<EMAIL>');
        $page376197->setValue('email_subject', '');
        $page376197->setValue('mail_embed_images', 'F');
        $page376197->setValue('email_body_html', '<p>Reklamacia {{INVOICE_NUMBER}}</p>');
        $page376197->setValue('rendered_email_bottom_text', '');
        $page376197->setValue('attachment_list', []);
        // set template on MAIN PAGE index::error404
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('basic_email'), 'index', 'error404');
        $page376197->save();
    }

    public function down()
    {
        // remove page: Reklamačný mail 1 (complaints_email_html)
        $pageId = $this->getPageIdByTag('complaints_email_html');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }

        // remove page: Reklamácie (complaints)
        $pageId = $this->getPageIdByTag('complaints');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }

        // remove page: Vrátenie tovaru (returns)
        $pageId = $this->getPageIdByTag('returns');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }
    }
}
