<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2024-01-17 10:22:08
 * Property generator: property=iveco_original_stock_eur_without_vat,iveco_latest_price_import
 */
class IvecoStockPricesPropertiesMigration extends AbstractMigration
{
    public function up()
    {
        // property: Iveco Original Stock EUR bez DPH(iveco_original_stock_eur_without_vat)
        $propertyIvecoOriginalStockEurWithoutVat = $this->propertyManager()->propertyExistsByTag('iveco_original_stock_eur_without_vat');
        if ($propertyIvecoOriginalStockEurWithoutVat === false) {
            $propertyIvecoOriginalStockEurWithoutVat = new Property();
            $propertyIvecoOriginalStockEurWithoutVat->setTag('iveco_original_stock_eur_without_vat');
            $propertyIvecoOriginalStockEurWithoutVat->setDescription('');
            $propertyIvecoOriginalStockEurWithoutVat->setExtendedDescription('');
            $propertyIvecoOriginalStockEurWithoutVat->setName('Iveco Original Stock EUR bez DPH');
            $propertyIvecoOriginalStockEurWithoutVat->setClassId(4);
            $propertyIvecoOriginalStockEurWithoutVat->setShowType(null);
            $propertyIvecoOriginalStockEurWithoutVat->setShowTypeTag('text');
            $propertyIvecoOriginalStockEurWithoutVat->setValueType('oneline_text');
            $propertyIvecoOriginalStockEurWithoutVat->setDefaultValue('');
            $propertyIvecoOriginalStockEurWithoutVat->setMultiOperations(false);
            $propertyIvecoOriginalStockEurWithoutVat->setInputString('');
            $propertyIvecoOriginalStockEurWithoutVat->setAttribute('tab', 'Iveco Stock Prices');
            $propertyIvecoOriginalStockEurWithoutVat->setAttribute('size', '60');
            $propertyIvecoOriginalStockEurWithoutVat->setAttribute('maxlength', '');
            $propertyIvecoOriginalStockEurWithoutVat->setAttribute('readonly', 'F');
            $propertyIvecoOriginalStockEurWithoutVat->setAttribute('pattern', '');
            $propertyIvecoOriginalStockEurWithoutVat->setAttribute('inherit_value', 'F');
            $propertyIvecoOriginalStockEurWithoutVat->setAttribute('onchange-js', '');
            $propertyIvecoOriginalStockEurWithoutVat->setAttribute('onkeyup-js', '');
            $propertyIvecoOriginalStockEurWithoutVat->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyIvecoOriginalStockEurWithoutVat);
        } else {
            $this->writeLine('Property with tag iveco_original_stock_eur_without_vat already exists');
            $this->setDataKey('property_iveco_original_stock_eur_without_vat_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('iveco_original_stock_eur_without_vat', 'eshop_product', false);
        }

        // property: Iveco Stock - Posledný import ceny(iveco_latest_price_import)
        $propertyIvecoLatestPriceImport = $this->propertyManager()->propertyExistsByTag('iveco_latest_price_import');
        if ($propertyIvecoLatestPriceImport === false) {
            $propertyIvecoLatestPriceImport = new Property();
            $propertyIvecoLatestPriceImport->setTag('iveco_latest_price_import');
            $propertyIvecoLatestPriceImport->setDescription('');
            $propertyIvecoLatestPriceImport->setExtendedDescription('');
            $propertyIvecoLatestPriceImport->setName('Iveco Stock - Posledný import ceny');
            $propertyIvecoLatestPriceImport->setClassId(4);
            $propertyIvecoLatestPriceImport->setShowType(null);
            $propertyIvecoLatestPriceImport->setShowTypeTag('text');
            $propertyIvecoLatestPriceImport->setValueType('oneline_text');
            $propertyIvecoLatestPriceImport->setDefaultValue('');
            $propertyIvecoLatestPriceImport->setMultiOperations(false);
            $propertyIvecoLatestPriceImport->setInputString('');
            $propertyIvecoLatestPriceImport->setAttribute('tab', 'Iveco Stock Prices');
            $propertyIvecoLatestPriceImport->setAttribute('size', '60');
            $propertyIvecoLatestPriceImport->setAttribute('maxlength', '');
            $propertyIvecoLatestPriceImport->setAttribute('readonly', 'F');
            $propertyIvecoLatestPriceImport->setAttribute('pattern', '');
            $propertyIvecoLatestPriceImport->setAttribute('inherit_value', 'F');
            $propertyIvecoLatestPriceImport->setAttribute('onchange-js', '');
            $propertyIvecoLatestPriceImport->setAttribute('onkeyup-js', '');
            $propertyIvecoLatestPriceImport->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyIvecoLatestPriceImport);
        } else {
            $this->writeLine('Property with tag iveco_latest_price_import already exists');
            $this->setDataKey('property_iveco_latest_price_import_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('iveco_latest_price_import', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Iveco Stock - Posledný import ceny(iveco_latest_price_import)
        $propertyIvecoLatestPriceImport = $this->propertyManager()->propertyExistsByTag('iveco_latest_price_import');
        if (($propertyIvecoLatestPriceImport !== false) && ($this->getDataKey('property_iveco_latest_price_import_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyIvecoLatestPriceImport);
        }

        // remove property: Iveco Original Stock EUR bez DPH(iveco_original_stock_eur_without_vat)
        $propertyIvecoOriginalStockEurWithoutVat = $this->propertyManager()->propertyExistsByTag('iveco_original_stock_eur_without_vat');
        if (($propertyIvecoOriginalStockEurWithoutVat !== false) && ($this->getDataKey('property_iveco_original_stock_eur_without_vat_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyIvecoOriginalStockEurWithoutVat);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
