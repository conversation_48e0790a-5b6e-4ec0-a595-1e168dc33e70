<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Automatic generation from (rinoparts) at 2021-10-28 08:42:28
 * Translation generator: sections=sk.cart
 */
class ProducerCiselnikIdNullable extends AbstractMigration
{
    public function up()
    {
        Schema::table('producers', function (Blueprint $table){
            $table->dropUnique(['producer_ciselnik_id']);
            $table->unsignedBigInteger('producer_ciselnik_id')->nullable()->change();
        });
    }

    public function down()
    {
        Schema::table('producers', function (Blueprint $table){
            $table->unsignedBigInteger('producer_ciselnik_id')->unique()->change();
            $table->unsignedBigInteger('producer_ciselnik_id')->nullable(false)->change();
        });
    }
}
