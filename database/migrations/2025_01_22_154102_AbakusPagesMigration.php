<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (rinoparts) at 2024-08-21 13:08:02
 * Page generator: page_id=1721094,1726569
 */
class AbakusPagesMigration extends AbstractMigration
{
    public function up()
    {
        // page: Abakus(ID: 1721094 TAG: abakus_supplier)
        $pageId = $this->getPageIdByTag('abakus_supplier');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('rolller_item_supplier');
            $page1721094 = \PageFactory::create($this->getPageIdByTag('ciselnik-dodavatel'), $pageType->getId());
        } else {
            $page1721094 = \PageFactory::get($pageId);
        }
        $page1721094->setPageName('Abakus');
        $page1721094->setPageTag('abakus_supplier');
        $page1721094->setPageStateId('1');
        $page1721094->setPageClassId(1);
        $page1721094->setValue('title', 'Abakus');
        $page1721094->setValue('title_en', '');
        $page1721094->setValue('title_cz', '');
        $page1721094->setValue('image', '');
        $page1721094->setValue('delivery_time', '');
        $page1721094->setValue('delivery_time_cz', '');
        $page1721094->setValue('delivery_time_en', '');
        $page1721094->save();

        // page: Abakus 1(ID: 1726569 TAG: abakus_settings)
        $pageId = $this->getPageIdByTag('abakus_settings');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('supplier_catalog_settings');
            $page1726569 = \PageFactory::create($this->getPageIdByTag('suppliers_settings'), $pageType->getId());
        } else {
            $page1726569 = \PageFactory::get($pageId);
        }
        $page1726569->setPageName('Abakus 1');
        $page1726569->setPageTag('abakus_settings');
        $page1726569->setPageStateId('2');
        $page1726569->setPageClassId(1);
        $page1726569->setValue('transport_surcharge', '4');
        $page1726569->save();
    }

    public function down()
    {
        // remove page: Abakus 1 (abakus_settings)
        $pageId = $this->getPageIdByTag('abakus_settings');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }

        // remove page: Abakus (abakus_supplier)
        $pageId = $this->getPageIdByTag('abakus_supplier');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }
    }
}
