<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2022-06-29 12:30:13
 * Property generator: property=content_after_cz,content_after_en
 */
class ContentAfterLanguagePropertiesMigration extends AbstractMigration
{
    public function up()
    {
        // property: Content after [CZ](content_after_cz)
        $propertyContentAfterCz = $this->propertyManager()->propertyExistsByTag('content_after_cz');
        if ($propertyContentAfterCz === false) {
            $propertyContentAfterCz = new Property();
            $propertyContentAfterCz->setTag('content_after_cz');
            $propertyContentAfterCz->setDescription('Obsah na koniec');
            $propertyContentAfterCz->setExtendedDescription('');
            $propertyContentAfterCz->setName('Content after [CZ]');
            $propertyContentAfterCz->setClassId(4);
            $propertyContentAfterCz->setShowType(null);
            $propertyContentAfterCz->setShowTypeTag('textarea');
            $propertyContentAfterCz->setValueType('multiline_text');
            $propertyContentAfterCz->setDefaultValue('');
            $propertyContentAfterCz->setMultiOperations(false);
            $propertyContentAfterCz->setInputString('');
            $propertyContentAfterCz->setAttribute('tab', 'CZ');
            $propertyContentAfterCz->setAttribute('cols', '60');
            $propertyContentAfterCz->setAttribute('rows', '3');
            $propertyContentAfterCz->setAttribute('dhtml-edit', '1');
            $propertyContentAfterCz->setAttribute('dhtml-configuration', 'full_no_p');
            $propertyContentAfterCz->setAttribute('import-word', '0');
            $propertyContentAfterCz->setAttribute('auto', '');
            $propertyContentAfterCz->setAttribute('inherit_value', 'F');
            $propertyContentAfterCz->setAttribute('onchange-js', '');
            $propertyContentAfterCz->setAttribute('onkeyup-js', '');
            $propertyContentAfterCz->setAttribute('onkeydown-js', '');
            $propertyContentAfterCz->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($propertyContentAfterCz);
        } else {
            $this->writeLine('Property with tag content_after_cz already exists');
            $this->setDataKey('property_content_after_cz_existed', true);
        }
        if ($this->pageTypeExists('layout_element_container')) {
            $this->addPropertyToPageType('content_after_cz', 'layout_element_container', false);
        }
        if ($this->pageTypeExists('layout_element_menu_link')) {
            $this->addPropertyToPageType('content_after_cz', 'layout_element_menu_link', false);
        }
        if ($this->pageTypeExists('footer_column_links_text')) {
            $this->addPropertyToPageType('content_after_cz', 'footer_column_links_text', false);
        }

        // property: Content after [EN](content_after_en)
        $propertyContentAfterEn = $this->propertyManager()->propertyExistsByTag('content_after_en');
        if ($propertyContentAfterEn === false) {
            $propertyContentAfterEn = new Property();
            $propertyContentAfterEn->setTag('content_after_en');
            $propertyContentAfterEn->setDescription('Obsah na koniec');
            $propertyContentAfterEn->setExtendedDescription('');
            $propertyContentAfterEn->setName('Content after [EN]');
            $propertyContentAfterEn->setClassId(4);
            $propertyContentAfterEn->setShowType(null);
            $propertyContentAfterEn->setShowTypeTag('textarea');
            $propertyContentAfterEn->setValueType('multiline_text');
            $propertyContentAfterEn->setDefaultValue('');
            $propertyContentAfterEn->setMultiOperations(false);
            $propertyContentAfterEn->setInputString('');
            $propertyContentAfterEn->setAttribute('tab', 'EN');
            $propertyContentAfterEn->setAttribute('cols', '60');
            $propertyContentAfterEn->setAttribute('rows', '3');
            $propertyContentAfterEn->setAttribute('dhtml-edit', '1');
            $propertyContentAfterEn->setAttribute('dhtml-configuration', 'full_no_p');
            $propertyContentAfterEn->setAttribute('import-word', '0');
            $propertyContentAfterEn->setAttribute('auto', '');
            $propertyContentAfterEn->setAttribute('inherit_value', 'F');
            $propertyContentAfterEn->setAttribute('onchange-js', '');
            $propertyContentAfterEn->setAttribute('onkeyup-js', '');
            $propertyContentAfterEn->setAttribute('onkeydown-js', '');
            $propertyContentAfterEn->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($propertyContentAfterEn);
        } else {
            $this->writeLine('Property with tag content_after_en already exists');
            $this->setDataKey('property_content_after_en_existed', true);
        }
        if ($this->pageTypeExists('layout_element_container')) {
            $this->addPropertyToPageType('content_after_en', 'layout_element_container', false);
        }
        if ($this->pageTypeExists('layout_element_menu_link')) {
            $this->addPropertyToPageType('content_after_en', 'layout_element_menu_link', false);
        }
        if ($this->pageTypeExists('footer_column_links_text')) {
            $this->addPropertyToPageType('content_after_en', 'footer_column_links_text', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Content after [EN](content_after_en)
        $propertyContentAfterEn = $this->propertyManager()->propertyExistsByTag('content_after_en');
        if (($propertyContentAfterEn !== false) && ($this->getDataKey('property_content_after_en_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyContentAfterEn);
        }

        // remove property: Content after [CZ](content_after_cz)
        $propertyContentAfterCz = $this->propertyManager()->propertyExistsByTag('content_after_cz');
        if (($propertyContentAfterCz !== false) && ($this->getDataKey('property_content_after_cz_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyContentAfterCz);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
