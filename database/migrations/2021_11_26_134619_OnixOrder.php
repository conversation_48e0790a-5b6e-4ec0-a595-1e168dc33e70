<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Automatic generation from (buxus_rinoparts_test) at 2021-11-26 13:46:19
 */
class OnixOrder extends AbstractMigration
{
    public function up()
    {
        Schema::table('onix_requests', function (Blueprint $table) {
            $table->unsignedBigInteger('order_id')->nullable()->default(null);
        });
    }

    public function down()
    {
        Schema::table('onix_requests', function (Blueprint $table) {
            $table->dropColumn('order_id');
        });
    }
}
