<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2024-01-26 13:38:06
 * Property generator: property=martex_supplier_code,martex_price_without_vat,martex_stock_balance,martex_oe_number,martex_oe_numbers,martex_latest_import
 */
class MartexPropertiesMigration extends AbstractMigration
{
    public function up()
    {
        // property: Martex - Dodávateľský kód(martex_supplier_code)
        $propertyMartexSupplierCode = $this->propertyManager()->propertyExistsByTag('martex_supplier_code');
        if ($propertyMartexSupplierCode === false) {
            $propertyMartexSupplierCode = new Property();
            $propertyMartexSupplierCode->setTag('martex_supplier_code');
            $propertyMartexSupplierCode->setDescription('');
            $propertyMartexSupplierCode->setExtendedDescription('');
            $propertyMartexSupplierCode->setName('Martex - Dodávateľský kód');
            $propertyMartexSupplierCode->setClassId(4);
            $propertyMartexSupplierCode->setShowType(null);
            $propertyMartexSupplierCode->setShowTypeTag('text');
            $propertyMartexSupplierCode->setValueType('oneline_text');
            $propertyMartexSupplierCode->setDefaultValue('');
            $propertyMartexSupplierCode->setMultiOperations(false);
            $propertyMartexSupplierCode->setInputString('');
            $propertyMartexSupplierCode->setAttribute('tab', 'Martex');
            $propertyMartexSupplierCode->setAttribute('size', '60');
            $propertyMartexSupplierCode->setAttribute('maxlength', '');
            $propertyMartexSupplierCode->setAttribute('readonly', 'F');
            $propertyMartexSupplierCode->setAttribute('pattern', '');
            $propertyMartexSupplierCode->setAttribute('inherit_value', 'F');
            $propertyMartexSupplierCode->setAttribute('onchange-js', '');
            $propertyMartexSupplierCode->setAttribute('onkeyup-js', '');
            $propertyMartexSupplierCode->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyMartexSupplierCode);
        } else {
            $this->writeLine('Property with tag martex_supplier_code already exists');
            $this->setDataKey('property_martex_supplier_code_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('martex_supplier_code', 'eshop_product', false);
        }

        // property: Martex - Cena bez DPH(martex_price_without_vat)
        $propertyMartexPriceWithoutVat = $this->propertyManager()->propertyExistsByTag('martex_price_without_vat');
        if ($propertyMartexPriceWithoutVat === false) {
            $propertyMartexPriceWithoutVat = new Property();
            $propertyMartexPriceWithoutVat->setTag('martex_price_without_vat');
            $propertyMartexPriceWithoutVat->setDescription('');
            $propertyMartexPriceWithoutVat->setExtendedDescription('');
            $propertyMartexPriceWithoutVat->setName('Martex - Cena bez DPH');
            $propertyMartexPriceWithoutVat->setClassId(4);
            $propertyMartexPriceWithoutVat->setShowType(null);
            $propertyMartexPriceWithoutVat->setShowTypeTag('text');
            $propertyMartexPriceWithoutVat->setValueType('oneline_text');
            $propertyMartexPriceWithoutVat->setDefaultValue('');
            $propertyMartexPriceWithoutVat->setMultiOperations(false);
            $propertyMartexPriceWithoutVat->setInputString('');
            $propertyMartexPriceWithoutVat->setAttribute('tab', 'Martex');
            $propertyMartexPriceWithoutVat->setAttribute('size', '60');
            $propertyMartexPriceWithoutVat->setAttribute('maxlength', '');
            $propertyMartexPriceWithoutVat->setAttribute('readonly', 'F');
            $propertyMartexPriceWithoutVat->setAttribute('pattern', '');
            $propertyMartexPriceWithoutVat->setAttribute('inherit_value', 'F');
            $propertyMartexPriceWithoutVat->setAttribute('onchange-js', '');
            $propertyMartexPriceWithoutVat->setAttribute('onkeyup-js', '');
            $propertyMartexPriceWithoutVat->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyMartexPriceWithoutVat);
        } else {
            $this->writeLine('Property with tag martex_price_without_vat already exists');
            $this->setDataKey('property_martex_price_without_vat_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('martex_price_without_vat', 'eshop_product', false);
        }

        // property: Martex - Skladová zásoba(martex_stock_balance)
        $propertyMartexStockBalance = $this->propertyManager()->propertyExistsByTag('martex_stock_balance');
        if ($propertyMartexStockBalance === false) {
            $propertyMartexStockBalance = new Property();
            $propertyMartexStockBalance->setTag('martex_stock_balance');
            $propertyMartexStockBalance->setDescription('');
            $propertyMartexStockBalance->setExtendedDescription('');
            $propertyMartexStockBalance->setName('Martex - Skladová zásoba');
            $propertyMartexStockBalance->setClassId(4);
            $propertyMartexStockBalance->setShowType(null);
            $propertyMartexStockBalance->setShowTypeTag('text');
            $propertyMartexStockBalance->setValueType('oneline_text');
            $propertyMartexStockBalance->setDefaultValue('');
            $propertyMartexStockBalance->setMultiOperations(false);
            $propertyMartexStockBalance->setInputString('');
            $propertyMartexStockBalance->setAttribute('tab', 'Martex');
            $propertyMartexStockBalance->setAttribute('size', '60');
            $propertyMartexStockBalance->setAttribute('maxlength', '');
            $propertyMartexStockBalance->setAttribute('readonly', 'F');
            $propertyMartexStockBalance->setAttribute('pattern', '');
            $propertyMartexStockBalance->setAttribute('inherit_value', 'F');
            $propertyMartexStockBalance->setAttribute('onchange-js', '');
            $propertyMartexStockBalance->setAttribute('onkeyup-js', '');
            $propertyMartexStockBalance->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyMartexStockBalance);
        } else {
            $this->writeLine('Property with tag martex_stock_balance already exists');
            $this->setDataKey('property_martex_stock_balance_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('martex_stock_balance', 'eshop_product', false);
        }

        // property: Martex - OE number(martex_oe_number)
        $propertyMartexOeNumber = $this->propertyManager()->propertyExistsByTag('martex_oe_number');
        if ($propertyMartexOeNumber === false) {
            $propertyMartexOeNumber = new Property();
            $propertyMartexOeNumber->setTag('martex_oe_number');
            $propertyMartexOeNumber->setDescription('');
            $propertyMartexOeNumber->setExtendedDescription('');
            $propertyMartexOeNumber->setName('Martex - OE number');
            $propertyMartexOeNumber->setClassId(4);
            $propertyMartexOeNumber->setShowType(null);
            $propertyMartexOeNumber->setShowTypeTag('text');
            $propertyMartexOeNumber->setValueType('oneline_text');
            $propertyMartexOeNumber->setDefaultValue('');
            $propertyMartexOeNumber->setMultiOperations(false);
            $propertyMartexOeNumber->setInputString('');
            $propertyMartexOeNumber->setAttribute('tab', 'Martex');
            $propertyMartexOeNumber->setAttribute('size', '60');
            $propertyMartexOeNumber->setAttribute('maxlength', '');
            $propertyMartexOeNumber->setAttribute('readonly', 'F');
            $propertyMartexOeNumber->setAttribute('pattern', '');
            $propertyMartexOeNumber->setAttribute('inherit_value', 'F');
            $propertyMartexOeNumber->setAttribute('onchange-js', '');
            $propertyMartexOeNumber->setAttribute('onkeyup-js', '');
            $propertyMartexOeNumber->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyMartexOeNumber);
        } else {
            $this->writeLine('Property with tag martex_oe_number already exists');
            $this->setDataKey('property_martex_oe_number_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('martex_oe_number', 'eshop_product', false);
        }

        // property: Martex - OE numbers(martex_oe_numbers)
        $propertyMartexOeNumbers = $this->propertyManager()->propertyExistsByTag('martex_oe_numbers');
        if ($propertyMartexOeNumbers === false) {
            $propertyMartexOeNumbers = new Property();
            $propertyMartexOeNumbers->setTag('martex_oe_numbers');
            $propertyMartexOeNumbers->setDescription('');
            $propertyMartexOeNumbers->setExtendedDescription('');
            $propertyMartexOeNumbers->setName('Martex - OE numbers');
            $propertyMartexOeNumbers->setClassId(4);
            $propertyMartexOeNumbers->setShowType(null);
            $propertyMartexOeNumbers->setShowTypeTag('text');
            $propertyMartexOeNumbers->setValueType('oneline_text');
            $propertyMartexOeNumbers->setDefaultValue('');
            $propertyMartexOeNumbers->setMultiOperations(false);
            $propertyMartexOeNumbers->setInputString('');
            $propertyMartexOeNumbers->setAttribute('tab', 'Martex');
            $propertyMartexOeNumbers->setAttribute('size', '60');
            $propertyMartexOeNumbers->setAttribute('maxlength', '');
            $propertyMartexOeNumbers->setAttribute('readonly', 'F');
            $propertyMartexOeNumbers->setAttribute('pattern', '');
            $propertyMartexOeNumbers->setAttribute('inherit_value', 'F');
            $propertyMartexOeNumbers->setAttribute('onchange-js', '');
            $propertyMartexOeNumbers->setAttribute('onkeyup-js', '');
            $propertyMartexOeNumbers->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyMartexOeNumbers);
        } else {
            $this->writeLine('Property with tag martex_oe_numbers already exists');
            $this->setDataKey('property_martex_oe_numbers_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('martex_oe_numbers', 'eshop_product', false);
        }

        // property: Martex - Posledný import(martex_latest_import)
        $propertyMartexLatestImport = $this->propertyManager()->propertyExistsByTag('martex_latest_import');
        if ($propertyMartexLatestImport === false) {
            $propertyMartexLatestImport = new Property();
            $propertyMartexLatestImport->setTag('martex_latest_import');
            $propertyMartexLatestImport->setDescription('');
            $propertyMartexLatestImport->setExtendedDescription('');
            $propertyMartexLatestImport->setName('Martex - Posledný import');
            $propertyMartexLatestImport->setClassId(4);
            $propertyMartexLatestImport->setShowType(null);
            $propertyMartexLatestImport->setShowTypeTag('text');
            $propertyMartexLatestImport->setValueType('oneline_text');
            $propertyMartexLatestImport->setDefaultValue('');
            $propertyMartexLatestImport->setMultiOperations(false);
            $propertyMartexLatestImport->setInputString('');
            $propertyMartexLatestImport->setAttribute('tab', 'Martex');
            $propertyMartexLatestImport->setAttribute('size', '60');
            $propertyMartexLatestImport->setAttribute('maxlength', '');
            $propertyMartexLatestImport->setAttribute('readonly', 'F');
            $propertyMartexLatestImport->setAttribute('pattern', '');
            $propertyMartexLatestImport->setAttribute('inherit_value', 'F');
            $propertyMartexLatestImport->setAttribute('onchange-js', '');
            $propertyMartexLatestImport->setAttribute('onkeyup-js', '');
            $propertyMartexLatestImport->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyMartexLatestImport);
        } else {
            $this->writeLine('Property with tag martex_latest_import already exists');
            $this->setDataKey('property_martex_latest_import_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('martex_latest_import', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Martex - Posledný import(martex_latest_import)
        $propertyMartexLatestImport = $this->propertyManager()->propertyExistsByTag('martex_latest_import');
        if (($propertyMartexLatestImport !== false) && ($this->getDataKey('property_martex_latest_import_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyMartexLatestImport);
        }

        // remove property: Martex - OE numbers(martex_oe_numbers)
        $propertyMartexOeNumbers = $this->propertyManager()->propertyExistsByTag('martex_oe_numbers');
        if (($propertyMartexOeNumbers !== false) && ($this->getDataKey('property_martex_oe_numbers_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyMartexOeNumbers);
        }

        // remove property: Martex - OE number(martex_oe_number)
        $propertyMartexOeNumber = $this->propertyManager()->propertyExistsByTag('martex_oe_number');
        if (($propertyMartexOeNumber !== false) && ($this->getDataKey('property_martex_oe_number_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyMartexOeNumber);
        }

        // remove property: Martex - Skladová zásoba(martex_stock_balance)
        $propertyMartexStockBalance = $this->propertyManager()->propertyExistsByTag('martex_stock_balance');
        if (($propertyMartexStockBalance !== false) && ($this->getDataKey('property_martex_stock_balance_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyMartexStockBalance);
        }

        // remove property: Martex - Cena bez DPH(martex_price_without_vat)
        $propertyMartexPriceWithoutVat = $this->propertyManager()->propertyExistsByTag('martex_price_without_vat');
        if (($propertyMartexPriceWithoutVat !== false) && ($this->getDataKey('property_martex_price_without_vat_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyMartexPriceWithoutVat);
        }

        // remove property: Martex - Dodávateľský kód(martex_supplier_code)
        $propertyMartexSupplierCode = $this->propertyManager()->propertyExistsByTag('martex_supplier_code');
        if (($propertyMartexSupplierCode !== false) && ($this->getDataKey('property_martex_supplier_code_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyMartexSupplierCode);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
