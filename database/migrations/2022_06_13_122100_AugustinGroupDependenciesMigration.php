<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;
use Buxus\PageType\PageType;
use Buxus\PageType\PageTypePropertyItem;

/**
 * Automatic generation from (rinoparts) at 2022-06-13 12:20:59
 * PageType generator: page_type=supplier_catalog_settings
 * Page generator: page_id=741316,741079
 * Property generator: property=all_codes,transport_surcharge,augustin_group_number,augustin_group_oe_number,augustin_group_oe_numbers,product_weight,augustin_group_stock_balance,augustin_group_price_without_vat,augustin_group_latest_import
 */
class AugustinGroupDependenciesMigration extends AbstractMigration
{
    public function up()
    {
        // property: Prirážka - doprava [%](transport_surcharge)
        $propertyTransportSurcharge = $this->propertyManager()->propertyExistsByTag('transport_surcharge');
        if ($propertyTransportSurcharge === false) {
            $propertyTransportSurcharge = new Property();
            $propertyTransportSurcharge->setTag('transport_surcharge');
            $propertyTransportSurcharge->setDescription('');
            $propertyTransportSurcharge->setExtendedDescription('');
            $propertyTransportSurcharge->setName('Prirážka - doprava [%]');
            $propertyTransportSurcharge->setClassId(4);
            $propertyTransportSurcharge->setShowType(null);
            $propertyTransportSurcharge->setShowTypeTag('text');
            $propertyTransportSurcharge->setValueType('oneline_text');
            $propertyTransportSurcharge->setDefaultValue('');
            $propertyTransportSurcharge->setMultiOperations(false);
            $propertyTransportSurcharge->setInputString('');
            $propertyTransportSurcharge->setAttribute('tab', '');
            $propertyTransportSurcharge->setAttribute('size', '60');
            $propertyTransportSurcharge->setAttribute('maxlength', '');
            $propertyTransportSurcharge->setAttribute('readonly', 'F');
            $propertyTransportSurcharge->setAttribute('pattern', '');
            $propertyTransportSurcharge->setAttribute('inherit_value', 'F');
            $propertyTransportSurcharge->setAttribute('onchange-js', '');
            $propertyTransportSurcharge->setAttribute('onkeyup-js', '');
            $propertyTransportSurcharge->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyTransportSurcharge);
        } else {
            $this->writeLine('Property with tag transport_surcharge already exists');
            $this->setDataKey('property_transport_surcharge_existed', true);
        }
        if ($this->pageTypeExists('supplier_catalog_settings')) {
            $this->addPropertyToPageType('transport_surcharge', 'supplier_catalog_settings', false);
        }

        // page type: Dodávateľské cenníky - nastavenia (supplier_catalog_settings)
        $pageTypeSupplierCatalogSettings = $this->pageTypesManager()->pageTypeExistsByTag('supplier_catalog_settings');
        if ($pageTypeSupplierCatalogSettings === false) {
            $pageTypeSupplierCatalogSettings = new PageType();
            $pageTypeSupplierCatalogSettings->setTag('supplier_catalog_settings');
            $pageTypeSupplierCatalogSettings->setName('Dodávateľské cenníky - nastavenia');
            $pageTypeSupplierCatalogSettings->setPageClassId(1);
            $pageTypeSupplierCatalogSettings->setDefaultTemplateId(1);
            $pageTypeSupplierCatalogSettings->setDeleteTrigger(null);
            $pageTypeSupplierCatalogSettings->setIncludeInSync(null);
            $pageTypeSupplierCatalogSettings->setPageDetailsLayout('');
            $pageTypeSupplierCatalogSettings->setPageSortTypeTag('sort_date_time');
            $pageTypeSupplierCatalogSettings->setPageTypeOrder(0);
            $pageTypeSupplierCatalogSettings->setPostmoveTrigger(null);
            $pageTypeSupplierCatalogSettings->setPostsubmitTrigger(null);
            $pageTypeSupplierCatalogSettings->setPresubmitTrigger(null);
            $pageTypeSupplierCatalogSettings->setParent(null);
        } else {
            $this->writeLine('Page type with tag supplier_catalog_settings already exists');
            $this->setDataKey('page_type_supplier_catalog_settings_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('transport_surcharge');
        $propertyId = $property->getId();
        $tmp = $pageTypeSupplierCatalogSettings->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(1);
            $tmp->setRequired(false);
            $pageTypeSupplierCatalogSettings->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($pageTypeSupplierCatalogSettings);

        if ($this->pageTypeExists('folder')) {
            $this->addPageTypeSuperiorPageType('supplier_catalog_settings', 'folder');
        }

        // page: Augustin Group 2(ID: 741316 TAG: augustin_group_supplier)
        $pageId = $this->getPageIdByTag('augustin_group_supplier');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('rolller_item_supplier');
            $page741316 = \PageFactory::create($this->getPageIdByTag('ciselnik-dodavatel'), $pageType->getId());
        } else {
            $page741316 = \PageFactory::get($pageId);
        }
        $page741316->setPageName('Augustin Group 2');
        $page741316->setPageTag('augustin_group_supplier');
        $page741316->setPageStateId(2);
        $page741316->setPageClassId(1);
        $page741316->setValue('title', 'Augustin Group');
        $page741316->setValue('title_en', '');
        $page741316->setValue('title_cz', '');
        $page741316->setValue('image', '');
        $page741316->setValue('delivery_time', '');
        $page741316->setValue('delivery_time_cz', '');
        $page741316->setValue('delivery_time_en', '');
        $page741316->save();

        // page: Augustin Group 1(ID: 741079 TAG: augustin_group_settings)
        $pageId = $this->getPageIdByTag('augustin_group_settings');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('supplier_catalog_settings');
            $page741079 = \PageFactory::create($this->getPageIdByTag('suppliers_settings'), $pageType->getId());
        } else {
            $page741079 = \PageFactory::get($pageId);
        }
        $page741079->setPageName('Augustin Group 1');
        $page741079->setPageTag('augustin_group_settings');
        $page741079->setPageStateId(2);
        $page741079->setPageClassId(1);
        $page741079->setValue('transport_surcharge', '10');
        $page741079->save();

        // property: Všetky kódy produktu(all_codes)
        $propertyAllCodes = $this->propertyManager()->propertyExistsByTag('all_codes');
        if ($propertyAllCodes === false) {
            $propertyAllCodes = new Property();
            $propertyAllCodes->setTag('all_codes');
            $propertyAllCodes->setDescription('');
            $propertyAllCodes->setExtendedDescription('');
            $propertyAllCodes->setName('Všetky kódy produktu');
            $propertyAllCodes->setClassId(4);
            $propertyAllCodes->setShowType(null);
            $propertyAllCodes->setShowTypeTag('textarea');
            $propertyAllCodes->setValueType('multiline_text');
            $propertyAllCodes->setDefaultValue('');
            $propertyAllCodes->setMultiOperations(false);
            $propertyAllCodes->setInputString('');
            $propertyAllCodes->setAttribute('tab', '');
            $propertyAllCodes->setAttribute('cols', '60');
            $propertyAllCodes->setAttribute('rows', '3');
            $propertyAllCodes->setAttribute('dhtml-edit', '0');
            $propertyAllCodes->setAttribute('dhtml-configuration', '');
            $propertyAllCodes->setAttribute('import-word', '0');
            $propertyAllCodes->setAttribute('auto', '');
            $propertyAllCodes->setAttribute('inherit_value', 'F');
            $propertyAllCodes->setAttribute('onchange-js', '');
            $propertyAllCodes->setAttribute('onkeyup-js', '');
            $propertyAllCodes->setAttribute('onkeydown-js', '');
            $propertyAllCodes->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($propertyAllCodes);
        } else {
            $this->writeLine('Property with tag all_codes already exists');
            $this->setDataKey('property_all_codes_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('all_codes', 'eshop_product', false);
        }

        // property: Number(augustin_group_number)
        $propertyAugustinGroupNumber = $this->propertyManager()->propertyExistsByTag('augustin_group_number');
        if ($propertyAugustinGroupNumber === false) {
            $propertyAugustinGroupNumber = new Property();
            $propertyAugustinGroupNumber->setTag('augustin_group_number');
            $propertyAugustinGroupNumber->setDescription('');
            $propertyAugustinGroupNumber->setExtendedDescription('');
            $propertyAugustinGroupNumber->setName('Number');
            $propertyAugustinGroupNumber->setClassId(4);
            $propertyAugustinGroupNumber->setShowType(null);
            $propertyAugustinGroupNumber->setShowTypeTag('text');
            $propertyAugustinGroupNumber->setValueType('oneline_text');
            $propertyAugustinGroupNumber->setDefaultValue('');
            $propertyAugustinGroupNumber->setMultiOperations(false);
            $propertyAugustinGroupNumber->setInputString('');
            $propertyAugustinGroupNumber->setAttribute('tab', 'Augustin Group');
            $propertyAugustinGroupNumber->setAttribute('size', '60');
            $propertyAugustinGroupNumber->setAttribute('maxlength', '');
            $propertyAugustinGroupNumber->setAttribute('readonly', 'F');
            $propertyAugustinGroupNumber->setAttribute('pattern', '');
            $propertyAugustinGroupNumber->setAttribute('inherit_value', 'F');
            $propertyAugustinGroupNumber->setAttribute('onchange-js', '');
            $propertyAugustinGroupNumber->setAttribute('onkeyup-js', '');
            $propertyAugustinGroupNumber->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyAugustinGroupNumber);
        } else {
            $this->writeLine('Property with tag augustin_group_number already exists');
            $this->setDataKey('property_augustin_group_number_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('augustin_group_number', 'eshop_product', false);
        }

        // property: OE number(augustin_group_oe_number)
        $propertyAugustinGroupOeNumber = $this->propertyManager()->propertyExistsByTag('augustin_group_oe_number');
        if ($propertyAugustinGroupOeNumber === false) {
            $propertyAugustinGroupOeNumber = new Property();
            $propertyAugustinGroupOeNumber->setTag('augustin_group_oe_number');
            $propertyAugustinGroupOeNumber->setDescription('');
            $propertyAugustinGroupOeNumber->setExtendedDescription('');
            $propertyAugustinGroupOeNumber->setName('OE number');
            $propertyAugustinGroupOeNumber->setClassId(4);
            $propertyAugustinGroupOeNumber->setShowType(null);
            $propertyAugustinGroupOeNumber->setShowTypeTag('text');
            $propertyAugustinGroupOeNumber->setValueType('oneline_text');
            $propertyAugustinGroupOeNumber->setDefaultValue('');
            $propertyAugustinGroupOeNumber->setMultiOperations(false);
            $propertyAugustinGroupOeNumber->setInputString('');
            $propertyAugustinGroupOeNumber->setAttribute('tab', 'Augustin Group');
            $propertyAugustinGroupOeNumber->setAttribute('size', '60');
            $propertyAugustinGroupOeNumber->setAttribute('maxlength', '');
            $propertyAugustinGroupOeNumber->setAttribute('readonly', 'F');
            $propertyAugustinGroupOeNumber->setAttribute('pattern', '');
            $propertyAugustinGroupOeNumber->setAttribute('inherit_value', 'F');
            $propertyAugustinGroupOeNumber->setAttribute('onchange-js', '');
            $propertyAugustinGroupOeNumber->setAttribute('onkeyup-js', '');
            $propertyAugustinGroupOeNumber->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyAugustinGroupOeNumber);
        } else {
            $this->writeLine('Property with tag augustin_group_oe_number already exists');
            $this->setDataKey('property_augustin_group_oe_number_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('augustin_group_oe_number', 'eshop_product', false);
        }

        // property: OE numbers(augustin_group_oe_numbers)
        $propertyAugustinGroupOeNumbers = $this->propertyManager()->propertyExistsByTag('augustin_group_oe_numbers');
        if ($propertyAugustinGroupOeNumbers === false) {
            $propertyAugustinGroupOeNumbers = new Property();
            $propertyAugustinGroupOeNumbers->setTag('augustin_group_oe_numbers');
            $propertyAugustinGroupOeNumbers->setDescription('');
            $propertyAugustinGroupOeNumbers->setExtendedDescription('');
            $propertyAugustinGroupOeNumbers->setName('OE numbers');
            $propertyAugustinGroupOeNumbers->setClassId(4);
            $propertyAugustinGroupOeNumbers->setShowType(null);
            $propertyAugustinGroupOeNumbers->setShowTypeTag('textarea');
            $propertyAugustinGroupOeNumbers->setValueType('multiline_text');
            $propertyAugustinGroupOeNumbers->setDefaultValue('');
            $propertyAugustinGroupOeNumbers->setMultiOperations(false);
            $propertyAugustinGroupOeNumbers->setInputString('');
            $propertyAugustinGroupOeNumbers->setAttribute('tab', 'Augustin Group');
            $propertyAugustinGroupOeNumbers->setAttribute('cols', '60');
            $propertyAugustinGroupOeNumbers->setAttribute('rows', '3');
            $propertyAugustinGroupOeNumbers->setAttribute('dhtml-edit', '0');
            $propertyAugustinGroupOeNumbers->setAttribute('dhtml-configuration', 'full');
            $propertyAugustinGroupOeNumbers->setAttribute('import-word', '0');
            $propertyAugustinGroupOeNumbers->setAttribute('auto', '');
            $propertyAugustinGroupOeNumbers->setAttribute('inherit_value', 'F');
            $propertyAugustinGroupOeNumbers->setAttribute('onchange-js', '');
            $propertyAugustinGroupOeNumbers->setAttribute('onkeyup-js', '');
            $propertyAugustinGroupOeNumbers->setAttribute('onkeydown-js', '');
            $propertyAugustinGroupOeNumbers->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($propertyAugustinGroupOeNumbers);
        } else {
            $this->writeLine('Property with tag augustin_group_oe_numbers already exists');
            $this->setDataKey('property_augustin_group_oe_numbers_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('augustin_group_oe_numbers', 'eshop_product', false);
        }

        // property: Hmotnosť(product_weight)
        $propertyProductWeight = $this->propertyManager()->propertyExistsByTag('product_weight');
        if ($propertyProductWeight === false) {
            $propertyProductWeight = new Property();
            $propertyProductWeight->setTag('product_weight');
            $propertyProductWeight->setDescription('');
            $propertyProductWeight->setExtendedDescription('');
            $propertyProductWeight->setName('Hmotnosť');
            $propertyProductWeight->setClassId(4);
            $propertyProductWeight->setShowType(null);
            $propertyProductWeight->setShowTypeTag('text');
            $propertyProductWeight->setValueType('oneline_text');
            $propertyProductWeight->setDefaultValue('');
            $propertyProductWeight->setMultiOperations(false);
            $propertyProductWeight->setInputString('');
            $propertyProductWeight->setAttribute('tab', '');
            $propertyProductWeight->setAttribute('size', '60');
            $propertyProductWeight->setAttribute('maxlength', '');
            $propertyProductWeight->setAttribute('readonly', 'F');
            $propertyProductWeight->setAttribute('pattern', '');
            $propertyProductWeight->setAttribute('inherit_value', 'F');
            $propertyProductWeight->setAttribute('onchange-js', '');
            $propertyProductWeight->setAttribute('onkeyup-js', '');
            $propertyProductWeight->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyProductWeight);
        } else {
            $this->writeLine('Property with tag product_weight already exists');
            $this->setDataKey('property_product_weight_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('product_weight', 'eshop_product', false);
        }

        // property: Stav externého skladu(augustin_group_stock_balance)
        $propertyAugustinGroupStockBalance = $this->propertyManager()->propertyExistsByTag('augustin_group_stock_balance');
        if ($propertyAugustinGroupStockBalance === false) {
            $propertyAugustinGroupStockBalance = new Property();
            $propertyAugustinGroupStockBalance->setTag('augustin_group_stock_balance');
            $propertyAugustinGroupStockBalance->setDescription('');
            $propertyAugustinGroupStockBalance->setExtendedDescription('');
            $propertyAugustinGroupStockBalance->setName('Stav externého skladu');
            $propertyAugustinGroupStockBalance->setClassId(4);
            $propertyAugustinGroupStockBalance->setShowType(null);
            $propertyAugustinGroupStockBalance->setShowTypeTag('text');
            $propertyAugustinGroupStockBalance->setValueType('oneline_text');
            $propertyAugustinGroupStockBalance->setDefaultValue('');
            $propertyAugustinGroupStockBalance->setMultiOperations(false);
            $propertyAugustinGroupStockBalance->setInputString('');
            $propertyAugustinGroupStockBalance->setAttribute('tab', 'Augustin Group');
            $propertyAugustinGroupStockBalance->setAttribute('size', '60');
            $propertyAugustinGroupStockBalance->setAttribute('maxlength', '');
            $propertyAugustinGroupStockBalance->setAttribute('readonly', 'F');
            $propertyAugustinGroupStockBalance->setAttribute('pattern', '');
            $propertyAugustinGroupStockBalance->setAttribute('inherit_value', 'F');
            $propertyAugustinGroupStockBalance->setAttribute('onchange-js', '');
            $propertyAugustinGroupStockBalance->setAttribute('onkeyup-js', '');
            $propertyAugustinGroupStockBalance->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyAugustinGroupStockBalance);
        } else {
            $this->writeLine('Property with tag augustin_group_stock_balance already exists');
            $this->setDataKey('property_augustin_group_stock_balance_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('augustin_group_stock_balance', 'eshop_product', false);
        }

        // property: Cena EUR bez DPH(augustin_group_price_without_vat)
        $propertyAugustinGroupPriceWithoutVat = $this->propertyManager()->propertyExistsByTag('augustin_group_price_without_vat');
        if ($propertyAugustinGroupPriceWithoutVat === false) {
            $propertyAugustinGroupPriceWithoutVat = new Property();
            $propertyAugustinGroupPriceWithoutVat->setTag('augustin_group_price_without_vat');
            $propertyAugustinGroupPriceWithoutVat->setDescription('');
            $propertyAugustinGroupPriceWithoutVat->setExtendedDescription('');
            $propertyAugustinGroupPriceWithoutVat->setName('Cena EUR bez DPH');
            $propertyAugustinGroupPriceWithoutVat->setClassId(4);
            $propertyAugustinGroupPriceWithoutVat->setShowType(null);
            $propertyAugustinGroupPriceWithoutVat->setShowTypeTag('text');
            $propertyAugustinGroupPriceWithoutVat->setValueType('oneline_text');
            $propertyAugustinGroupPriceWithoutVat->setDefaultValue('');
            $propertyAugustinGroupPriceWithoutVat->setMultiOperations(false);
            $propertyAugustinGroupPriceWithoutVat->setInputString('');
            $propertyAugustinGroupPriceWithoutVat->setAttribute('tab', 'Augustin Group');
            $propertyAugustinGroupPriceWithoutVat->setAttribute('size', '60');
            $propertyAugustinGroupPriceWithoutVat->setAttribute('maxlength', '');
            $propertyAugustinGroupPriceWithoutVat->setAttribute('readonly', 'F');
            $propertyAugustinGroupPriceWithoutVat->setAttribute('pattern', '');
            $propertyAugustinGroupPriceWithoutVat->setAttribute('inherit_value', 'F');
            $propertyAugustinGroupPriceWithoutVat->setAttribute('onchange-js', '');
            $propertyAugustinGroupPriceWithoutVat->setAttribute('onkeyup-js', '');
            $propertyAugustinGroupPriceWithoutVat->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyAugustinGroupPriceWithoutVat);
        } else {
            $this->writeLine('Property with tag augustin_group_price_without_vat already exists');
            $this->setDataKey('property_augustin_group_price_without_vat_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('augustin_group_price_without_vat', 'eshop_product', false);
        }

        // property: Augustin Group - posledný import(augustin_group_latest_import)
        $propertyAugustinGroupLatestImport = $this->propertyManager()->propertyExistsByTag('augustin_group_latest_import');
        if ($propertyAugustinGroupLatestImport === false) {
            $propertyAugustinGroupLatestImport = new Property();
            $propertyAugustinGroupLatestImport->setTag('augustin_group_latest_import');
            $propertyAugustinGroupLatestImport->setDescription('');
            $propertyAugustinGroupLatestImport->setExtendedDescription('');
            $propertyAugustinGroupLatestImport->setName('Augustin Group - posledný import');
            $propertyAugustinGroupLatestImport->setClassId(4);
            $propertyAugustinGroupLatestImport->setShowType(null);
            $propertyAugustinGroupLatestImport->setShowTypeTag('text');
            $propertyAugustinGroupLatestImport->setValueType('oneline_text');
            $propertyAugustinGroupLatestImport->setDefaultValue('');
            $propertyAugustinGroupLatestImport->setMultiOperations(false);
            $propertyAugustinGroupLatestImport->setInputString('');
            $propertyAugustinGroupLatestImport->setAttribute('tab', 'Augustin Group');
            $propertyAugustinGroupLatestImport->setAttribute('size', '60');
            $propertyAugustinGroupLatestImport->setAttribute('maxlength', '');
            $propertyAugustinGroupLatestImport->setAttribute('readonly', 'F');
            $propertyAugustinGroupLatestImport->setAttribute('pattern', '');
            $propertyAugustinGroupLatestImport->setAttribute('inherit_value', 'F');
            $propertyAugustinGroupLatestImport->setAttribute('onchange-js', '');
            $propertyAugustinGroupLatestImport->setAttribute('onkeyup-js', '');
            $propertyAugustinGroupLatestImport->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyAugustinGroupLatestImport);
        } else {
            $this->writeLine('Property with tag augustin_group_latest_import already exists');
            $this->setDataKey('property_augustin_group_latest_import_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('augustin_group_latest_import', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Augustin Group - posledný import(augustin_group_latest_import)
        $propertyAugustinGroupLatestImport = $this->propertyManager()->propertyExistsByTag('augustin_group_latest_import');
        if (($propertyAugustinGroupLatestImport !== false) && ($this->getDataKey('property_augustin_group_latest_import_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyAugustinGroupLatestImport);
        }

        // remove property: Cena EUR bez DPH(augustin_group_price_without_vat)
        $propertyAugustinGroupPriceWithoutVat = $this->propertyManager()->propertyExistsByTag('augustin_group_price_without_vat');
        if (($propertyAugustinGroupPriceWithoutVat !== false) && ($this->getDataKey('property_augustin_group_price_without_vat_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyAugustinGroupPriceWithoutVat);
        }

        // remove property: Stav externého skladu(augustin_group_stock_balance)
        $propertyAugustinGroupStockBalance = $this->propertyManager()->propertyExistsByTag('augustin_group_stock_balance');
        if (($propertyAugustinGroupStockBalance !== false) && ($this->getDataKey('property_augustin_group_stock_balance_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyAugustinGroupStockBalance);
        }

        // remove property: Hmotnosť(product_weight)
        $propertyProductWeight = $this->propertyManager()->propertyExistsByTag('product_weight');
        if (($propertyProductWeight !== false) && ($this->getDataKey('property_product_weight_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyProductWeight);
        }

        // remove property: OE numbers(augustin_group_oe_numbers)
        $propertyAugustinGroupOeNumbers = $this->propertyManager()->propertyExistsByTag('augustin_group_oe_numbers');
        if (($propertyAugustinGroupOeNumbers !== false) && ($this->getDataKey('property_augustin_group_oe_numbers_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyAugustinGroupOeNumbers);
        }

        // remove property: OE number(augustin_group_oe_number)
        $propertyAugustinGroupOeNumber = $this->propertyManager()->propertyExistsByTag('augustin_group_oe_number');
        if (($propertyAugustinGroupOeNumber !== false) && ($this->getDataKey('property_augustin_group_oe_number_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyAugustinGroupOeNumber);
        }

        // remove property: Number(augustin_group_number)
        $propertyAugustinGroupNumber = $this->propertyManager()->propertyExistsByTag('augustin_group_number');
        if (($propertyAugustinGroupNumber !== false) && ($this->getDataKey('property_augustin_group_number_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyAugustinGroupNumber);
        }

        // remove property: Všetky kódy produktu(all_codes)
        $propertyAllCodes = $this->propertyManager()->propertyExistsByTag('all_codes');
        if (($propertyAllCodes !== false) && ($this->getDataKey('property_all_codes_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyAllCodes);
        }

        // remove page: Augustin Group 1 (augustin_group_settings)
        $pageId = $this->getPageIdByTag('augustin_group_settings');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }

        // remove page: Augustin Group 2 (augustin_group_supplier)
        $pageId = $this->getPageIdByTag('augustin_group_supplier');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }

        // remove page type: Dodávateľské cenníky - nastavenia (supplier_catalog_settings)
        $pageTypeSupplierCatalogSettings = $this->pageTypesManager()->pageTypeExistsByTag('supplier_catalog_settings');
        if (($pageTypeSupplierCatalogSettings != false) && (is_null($this->getDataKey('page_type_supplier_catalog_settings_existed')))) {
            $this->pageTypesManager()->removePageType($pageTypeSupplierCatalogSettings);
        }

        // remove property: Prirážka - doprava [%](transport_surcharge)
        $propertyTransportSurcharge = $this->propertyManager()->propertyExistsByTag('transport_surcharge');
        if (($propertyTransportSurcharge !== false) && ($this->getDataKey('property_transport_surcharge_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyTransportSurcharge);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();
    }
}
