<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2022-04-21 16:49:43
 * Property generator: property=delivery_time_cz,delivery_time_en
 */
class DeliveryTimeLangProperties extends AbstractMigration
{
    public function up()
    {
        // property: Doba dodania [CZ](delivery_time_cz)
        $propertyDeliveryTimeCz = $this->propertyManager()->propertyExistsByTag('delivery_time_cz');
        if ($propertyDeliveryTimeCz === false) {
            $propertyDeliveryTimeCz = new Property();
            $propertyDeliveryTimeCz->setTag('delivery_time_cz');
            $propertyDeliveryTimeCz->setDescription('');
            $propertyDeliveryTimeCz->setExtendedDescription('');
            $propertyDeliveryTimeCz->setName('Doba dodania [CZ]');
            $propertyDeliveryTimeCz->setClassId(4);
            $propertyDeliveryTimeCz->setShowType(null);
            $propertyDeliveryTimeCz->setShowTypeTag('text');
            $propertyDeliveryTimeCz->setValueType('oneline_text');
            $propertyDeliveryTimeCz->setDefaultValue('');
            $propertyDeliveryTimeCz->setMultiOperations(false);
            $propertyDeliveryTimeCz->setInputString('');
            $propertyDeliveryTimeCz->setAttribute('tab', 'CZ');
            $propertyDeliveryTimeCz->setAttribute('size', '60');
            $propertyDeliveryTimeCz->setAttribute('maxlength', '');
            $propertyDeliveryTimeCz->setAttribute('readonly', 'F');
            $propertyDeliveryTimeCz->setAttribute('pattern', '');
            $propertyDeliveryTimeCz->setAttribute('inherit_value', 'F');
            $propertyDeliveryTimeCz->setAttribute('onchange-js', '');
            $propertyDeliveryTimeCz->setAttribute('onkeyup-js', '');
            $propertyDeliveryTimeCz->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyDeliveryTimeCz);
        } else {
            $this->writeLine('Property with tag delivery_time_cz already exists');
            $this->setDataKey('property_delivery_time_cz_existed', true);
        }
        if ($this->pageTypeExists('rolller_item_supplier')) {
            $this->addPropertyToPageType('delivery_time_cz', 'rolller_item_supplier', false);
        }

        // property: Doba dodania [EN](delivery_time_en)
        $propertyDeliveryTimeEn = $this->propertyManager()->propertyExistsByTag('delivery_time_en');
        if ($propertyDeliveryTimeEn === false) {
            $propertyDeliveryTimeEn = new Property();
            $propertyDeliveryTimeEn->setTag('delivery_time_en');
            $propertyDeliveryTimeEn->setDescription('');
            $propertyDeliveryTimeEn->setExtendedDescription('');
            $propertyDeliveryTimeEn->setName('Doba dodania [EN]');
            $propertyDeliveryTimeEn->setClassId(4);
            $propertyDeliveryTimeEn->setShowType(null);
            $propertyDeliveryTimeEn->setShowTypeTag('text');
            $propertyDeliveryTimeEn->setValueType('oneline_text');
            $propertyDeliveryTimeEn->setDefaultValue('');
            $propertyDeliveryTimeEn->setMultiOperations(false);
            $propertyDeliveryTimeEn->setInputString('');
            $propertyDeliveryTimeEn->setAttribute('tab', 'EN');
            $propertyDeliveryTimeEn->setAttribute('size', '60');
            $propertyDeliveryTimeEn->setAttribute('maxlength', '');
            $propertyDeliveryTimeEn->setAttribute('readonly', 'F');
            $propertyDeliveryTimeEn->setAttribute('pattern', '');
            $propertyDeliveryTimeEn->setAttribute('inherit_value', 'F');
            $propertyDeliveryTimeEn->setAttribute('onchange-js', '');
            $propertyDeliveryTimeEn->setAttribute('onkeyup-js', '');
            $propertyDeliveryTimeEn->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyDeliveryTimeEn);
        } else {
            $this->writeLine('Property with tag delivery_time_en already exists');
            $this->setDataKey('property_delivery_time_en_existed', true);
        }
        if ($this->pageTypeExists('rolller_item_supplier')) {
            $this->addPropertyToPageType('delivery_time_en', 'rolller_item_supplier', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Doba dodania [EN](delivery_time_en)
        $propertyDeliveryTimeEn = $this->propertyManager()->propertyExistsByTag('delivery_time_en');
        if (($propertyDeliveryTimeEn !== false) && ($this->getDataKey('property_delivery_time_en_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyDeliveryTimeEn);
        }

        // remove property: Doba dodania [CZ](delivery_time_cz)
        $propertyDeliveryTimeCz = $this->propertyManager()->propertyExistsByTag('delivery_time_cz');
        if (($propertyDeliveryTimeCz !== false) && ($this->getDataKey('property_delivery_time_cz_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyDeliveryTimeCz);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
