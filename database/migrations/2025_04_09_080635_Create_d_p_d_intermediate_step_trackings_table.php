<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Automatic generation from (rinoparts) at 2025-04-09 08:06:35
 */
class Create_d_p_d_intermediate_step_trackings_table extends AbstractMigration
{
    public function up()
    {
        // create table dpd_intermediate_step_trackings
        Schema::create('dpd_intermediate_step_trackings', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('parcel_no', 96);
            $table->string('customer_reference', 96)->index();
            $table->string('filename_reference')->index();
            $table->unique(['parcel_no', 'customer_reference'], 'parcel_no_customer_reference_unique');
            $table->timestamps();
        });
    }

    public function down()
    {
        // drop table dpd_intermediate_step_trackings
        Schema::dropIfExists('dpd_intermediate_step_trackings');
    }
}
