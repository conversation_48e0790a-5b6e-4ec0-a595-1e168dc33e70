<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (rinoparts) at 2022-05-15 17:51:16
 */
class TableForInvoiceEmailRecords extends AbstractMigration
{
    public function up()
    {
        Schema::create('tblInvoiceEmailRecords', function (\Illuminate\Database\Schema\Blueprint $table) {
            $table->id();
            $table->foreignId('enclosure_record_id')->references('enclosure_record_id')->on('onix_enclosures');
            $table->string('email_type_tag');
            $table->timestamp('created_at');
        });
    }

    public function down()
    {
        Schema::dropIfExists('tblInvoiceEmailRecords');
    }
}
