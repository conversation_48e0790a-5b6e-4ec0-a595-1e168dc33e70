<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2023-12-05 09:30:41
 * Property generator: property=cei_latest_stock_balance_import,cei_stock_balance
 */
class CeiAvailabilityPropertiesMigration extends AbstractMigration
{
    public function up()
    {
        // property: CEI - Posledný import skladovej dostupnosti(cei_latest_stock_balance_import)
        $propertyCeiLatestStockBalanceImport = $this->propertyManager()->propertyExistsByTag('cei_latest_stock_balance_import');
        if ($propertyCeiLatestStockBalanceImport === false) {
            $propertyCeiLatestStockBalanceImport = new Property();
            $propertyCeiLatestStockBalanceImport->setTag('cei_latest_stock_balance_import');
            $propertyCeiLatestStockBalanceImport->setDescription('');
            $propertyCeiLatestStockBalanceImport->setExtendedDescription('');
            $propertyCeiLatestStockBalanceImport->setName('CEI - Posledný import skladovej dostupnosti');
            $propertyCeiLatestStockBalanceImport->setClassId(4);
            $propertyCeiLatestStockBalanceImport->setShowType(null);
            $propertyCeiLatestStockBalanceImport->setShowTypeTag('text');
            $propertyCeiLatestStockBalanceImport->setValueType('oneline_text');
            $propertyCeiLatestStockBalanceImport->setDefaultValue('');
            $propertyCeiLatestStockBalanceImport->setMultiOperations(false);
            $propertyCeiLatestStockBalanceImport->setInputString('');
            $propertyCeiLatestStockBalanceImport->setAttribute('tab', 'CEI');
            $propertyCeiLatestStockBalanceImport->setAttribute('size', '60');
            $propertyCeiLatestStockBalanceImport->setAttribute('maxlength', '');
            $propertyCeiLatestStockBalanceImport->setAttribute('readonly', 'F');
            $propertyCeiLatestStockBalanceImport->setAttribute('pattern', '');
            $propertyCeiLatestStockBalanceImport->setAttribute('inherit_value', 'F');
            $propertyCeiLatestStockBalanceImport->setAttribute('onchange-js', '');
            $propertyCeiLatestStockBalanceImport->setAttribute('onkeyup-js', '');
            $propertyCeiLatestStockBalanceImport->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyCeiLatestStockBalanceImport);
        } else {
            $this->writeLine('Property with tag cei_latest_stock_balance_import already exists');
            $this->setDataKey('property_cei_latest_stock_balance_import_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('cei_latest_stock_balance_import', 'eshop_product', false);
        }

        // property: CEI - Skladová zásoba(cei_stock_balance)
        $propertyCeiStockBalance = $this->propertyManager()->propertyExistsByTag('cei_stock_balance');
        if ($propertyCeiStockBalance === false) {
            $propertyCeiStockBalance = new Property();
            $propertyCeiStockBalance->setTag('cei_stock_balance');
            $propertyCeiStockBalance->setDescription('');
            $propertyCeiStockBalance->setExtendedDescription('');
            $propertyCeiStockBalance->setName('CEI - Skladová zásoba');
            $propertyCeiStockBalance->setClassId(4);
            $propertyCeiStockBalance->setShowType(null);
            $propertyCeiStockBalance->setShowTypeTag('text');
            $propertyCeiStockBalance->setValueType('oneline_text');
            $propertyCeiStockBalance->setDefaultValue('');
            $propertyCeiStockBalance->setMultiOperations(false);
            $propertyCeiStockBalance->setInputString('');
            $propertyCeiStockBalance->setAttribute('tab', 'CEI');
            $propertyCeiStockBalance->setAttribute('size', '60');
            $propertyCeiStockBalance->setAttribute('maxlength', '');
            $propertyCeiStockBalance->setAttribute('readonly', 'F');
            $propertyCeiStockBalance->setAttribute('pattern', '');
            $propertyCeiStockBalance->setAttribute('inherit_value', 'F');
            $propertyCeiStockBalance->setAttribute('onchange-js', '');
            $propertyCeiStockBalance->setAttribute('onkeyup-js', '');
            $propertyCeiStockBalance->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyCeiStockBalance);
        } else {
            $this->writeLine('Property with tag cei_stock_balance already exists');
            $this->setDataKey('property_cei_stock_balance_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('cei_stock_balance', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: CEI - Skladová zásoba(cei_stock_balance)
        $propertyCeiStockBalance = $this->propertyManager()->propertyExistsByTag('cei_stock_balance');
        if (($propertyCeiStockBalance !== false) && ($this->getDataKey('property_cei_stock_balance_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyCeiStockBalance);
        }

        // remove property: CEI - Posledný import skladovej dostupnosti(cei_latest_stock_balance_import)
        $propertyCeiLatestStockBalanceImport = $this->propertyManager()->propertyExistsByTag('cei_latest_stock_balance_import');
        if (($propertyCeiLatestStockBalanceImport !== false) && ($this->getDataKey('property_cei_latest_stock_balance_import_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyCeiLatestStockBalanceImport);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
