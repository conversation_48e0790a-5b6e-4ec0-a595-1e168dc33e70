<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2022-02-11 22:10:59
 * Property generator: property=seo_title_brand_cz,seo_title_brand_en
 */
class SeoLanguageProperties extends AbstractMigration
{
    public function up()
    {
        // property: Seo nadpis - značka [CZ](seo_title_brand_cz)
        $propertySeoTitleBrandCz = $this->propertyManager()->propertyExistsByTag('seo_title_brand_cz');
        if ($propertySeoTitleBrandCz === false) {
            $propertySeoTitleBrandCz = new Property();
            $propertySeoTitleBrandCz->setTag('seo_title_brand_cz');
            $propertySeoTitleBrandCz->setDescription('Toto je preddefinovaná hodnota uvádzaná vždy v <title></title> meta tagu stránky za oddeľovačom
napr. pri hodnote Buxus.sk bude meta title vyzerať nasledovne 
Úvod | Buxus.sk, 
alebo
 Kontakt | Buxus.sk');
            $propertySeoTitleBrandCz->setExtendedDescription('');
            $propertySeoTitleBrandCz->setName('Seo nadpis - značka [CZ]');
            $propertySeoTitleBrandCz->setClassId(4);
            $propertySeoTitleBrandCz->setShowType(null);
            $propertySeoTitleBrandCz->setShowTypeTag('text');
            $propertySeoTitleBrandCz->setValueType('oneline_text');
            $propertySeoTitleBrandCz->setDefaultValue('');
            $propertySeoTitleBrandCz->setMultiOperations(false);
            $propertySeoTitleBrandCz->setInputString('');
            $propertySeoTitleBrandCz->setAttribute('tab', 'CZ');
            $propertySeoTitleBrandCz->setAttribute('size', '60');
            $propertySeoTitleBrandCz->setAttribute('maxlength', '');
            $propertySeoTitleBrandCz->setAttribute('readonly', 'F');
            $propertySeoTitleBrandCz->setAttribute('pattern', '');
            $propertySeoTitleBrandCz->setAttribute('inherit_value', 'F');
            $propertySeoTitleBrandCz->setAttribute('onchange-js', '');
            $propertySeoTitleBrandCz->setAttribute('onkeyup-js', '');
            $propertySeoTitleBrandCz->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertySeoTitleBrandCz);
        } else {
            $this->writeLine('Property with tag seo_title_brand_cz already exists');
            $this->setDataKey('property_seo_title_brand_cz_existed', true);
        }
        if ($this->pageTypeExists('seo_settings')) {
            $this->addPropertyToPageType('seo_title_brand_cz', 'seo_settings', false);
        }

        // property: Seo nadpis - značka [EN](seo_title_brand_en)
        $propertySeoTitleBrandEn = $this->propertyManager()->propertyExistsByTag('seo_title_brand_en');
        if ($propertySeoTitleBrandEn === false) {
            $propertySeoTitleBrandEn = new Property();
            $propertySeoTitleBrandEn->setTag('seo_title_brand_en');
            $propertySeoTitleBrandEn->setDescription('Toto je preddefinovaná hodnota uvádzaná vždy v <title></title> meta tagu stránky za oddeľovačom
napr. pri hodnote Buxus.sk bude meta title vyzerať nasledovne 
Úvod | Buxus.sk, 
alebo
 Kontakt | Buxus.sk');
            $propertySeoTitleBrandEn->setExtendedDescription('');
            $propertySeoTitleBrandEn->setName('Seo nadpis - značka [EN]');
            $propertySeoTitleBrandEn->setClassId(4);
            $propertySeoTitleBrandEn->setShowType(null);
            $propertySeoTitleBrandEn->setShowTypeTag('text');
            $propertySeoTitleBrandEn->setValueType('oneline_text');
            $propertySeoTitleBrandEn->setDefaultValue('');
            $propertySeoTitleBrandEn->setMultiOperations(false);
            $propertySeoTitleBrandEn->setInputString('');
            $propertySeoTitleBrandEn->setAttribute('tab', 'EN');
            $propertySeoTitleBrandEn->setAttribute('size', '60');
            $propertySeoTitleBrandEn->setAttribute('maxlength', '');
            $propertySeoTitleBrandEn->setAttribute('readonly', 'F');
            $propertySeoTitleBrandEn->setAttribute('pattern', '');
            $propertySeoTitleBrandEn->setAttribute('inherit_value', 'F');
            $propertySeoTitleBrandEn->setAttribute('onchange-js', '');
            $propertySeoTitleBrandEn->setAttribute('onkeyup-js', '');
            $propertySeoTitleBrandEn->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertySeoTitleBrandEn);
        } else {
            $this->writeLine('Property with tag seo_title_brand_en already exists');
            $this->setDataKey('property_seo_title_brand_en_existed', true);
        }
        if ($this->pageTypeExists('seo_settings')) {
            $this->addPropertyToPageType('seo_title_brand_en', 'seo_settings', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Seo nadpis - značka [EN](seo_title_brand_en)
        $propertySeoTitleBrandEn = $this->propertyManager()->propertyExistsByTag('seo_title_brand_en');
        if (($propertySeoTitleBrandEn !== false) && ($this->getDataKey('property_seo_title_brand_en_existed') === null)) {
            $this->propertyManager()->removeProperty($propertySeoTitleBrandEn);
        }

        // remove property: Seo nadpis - značka [CZ](seo_title_brand_cz)
        $propertySeoTitleBrandCz = $this->propertyManager()->propertyExistsByTag('seo_title_brand_cz');
        if (($propertySeoTitleBrandCz !== false) && ($this->getDataKey('property_seo_title_brand_cz_existed') === null)) {
            $this->propertyManager()->removeProperty($propertySeoTitleBrandCz);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
