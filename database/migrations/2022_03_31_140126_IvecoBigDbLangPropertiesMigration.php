<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2022-03-31 14:01:26
 * Property generator: property=eshop_eur_iveco_big_db_price_without_vat_en,iveco_big_db_import_code_en,iveco_big_db_latest_import_en,availability_big_db_en
 */
class IvecoBigDbLangPropertiesMigration extends AbstractMigration
{
    public function up()
    {
        // property: Iveco Original (big DB) EUR bez DPH [EN](eshop_eur_iveco_big_db_price_without_vat_en)
        $propertyEshopEurIvecoBigDbPriceWithoutVatEn = $this->propertyManager()->propertyExistsByTag('eshop_eur_iveco_big_db_price_without_vat_en');
        if ($propertyEshopEurIvecoBigDbPriceWithoutVatEn === false) {
            $propertyEshopEurIvecoBigDbPriceWithoutVatEn = new Property();
            $propertyEshopEurIvecoBigDbPriceWithoutVatEn->setTag('eshop_eur_iveco_big_db_price_without_vat_en');
            $propertyEshopEurIvecoBigDbPriceWithoutVatEn->setDescription('');
            $propertyEshopEurIvecoBigDbPriceWithoutVatEn->setExtendedDescription('');
            $propertyEshopEurIvecoBigDbPriceWithoutVatEn->setName('Iveco Original (big DB) EUR bez DPH [EN]');
            $propertyEshopEurIvecoBigDbPriceWithoutVatEn->setClassId(4);
            $propertyEshopEurIvecoBigDbPriceWithoutVatEn->setShowType(null);
            $propertyEshopEurIvecoBigDbPriceWithoutVatEn->setShowTypeTag('text');
            $propertyEshopEurIvecoBigDbPriceWithoutVatEn->setValueType('oneline_text');
            $propertyEshopEurIvecoBigDbPriceWithoutVatEn->setDefaultValue('');
            $propertyEshopEurIvecoBigDbPriceWithoutVatEn->setMultiOperations(false);
            $propertyEshopEurIvecoBigDbPriceWithoutVatEn->setInputString('');
            $propertyEshopEurIvecoBigDbPriceWithoutVatEn->setAttribute('tab', 'IVECO BIG DB [EN]');
            $propertyEshopEurIvecoBigDbPriceWithoutVatEn->setAttribute('size', '60');
            $propertyEshopEurIvecoBigDbPriceWithoutVatEn->setAttribute('maxlength', '');
            $propertyEshopEurIvecoBigDbPriceWithoutVatEn->setAttribute('readonly', 'F');
            $propertyEshopEurIvecoBigDbPriceWithoutVatEn->setAttribute('pattern', '');
            $propertyEshopEurIvecoBigDbPriceWithoutVatEn->setAttribute('inherit_value', 'F');
            $propertyEshopEurIvecoBigDbPriceWithoutVatEn->setAttribute('onchange-js', '');
            $propertyEshopEurIvecoBigDbPriceWithoutVatEn->setAttribute('onkeyup-js', '');
            $propertyEshopEurIvecoBigDbPriceWithoutVatEn->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyEshopEurIvecoBigDbPriceWithoutVatEn);
        } else {
            $this->writeLine('Property with tag eshop_eur_iveco_big_db_price_without_vat_en already exists');
            $this->setDataKey('property_eshop_eur_iveco_big_db_price_without_vat_en_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('eshop_eur_iveco_big_db_price_without_vat_en', 'eshop_product', false);
        }

        // property: IVECO Big DB import kód [EN](iveco_big_db_import_code_en)
        $propertyIvecoBigDbImportCodeEn = $this->propertyManager()->propertyExistsByTag('iveco_big_db_import_code_en');
        if ($propertyIvecoBigDbImportCodeEn === false) {
            $propertyIvecoBigDbImportCodeEn = new Property();
            $propertyIvecoBigDbImportCodeEn->setTag('iveco_big_db_import_code_en');
            $propertyIvecoBigDbImportCodeEn->setDescription('');
            $propertyIvecoBigDbImportCodeEn->setExtendedDescription('');
            $propertyIvecoBigDbImportCodeEn->setName('IVECO Big DB import kód [EN]');
            $propertyIvecoBigDbImportCodeEn->setClassId(4);
            $propertyIvecoBigDbImportCodeEn->setShowType(null);
            $propertyIvecoBigDbImportCodeEn->setShowTypeTag('text');
            $propertyIvecoBigDbImportCodeEn->setValueType('oneline_text');
            $propertyIvecoBigDbImportCodeEn->setDefaultValue('');
            $propertyIvecoBigDbImportCodeEn->setMultiOperations(false);
            $propertyIvecoBigDbImportCodeEn->setInputString('');
            $propertyIvecoBigDbImportCodeEn->setAttribute('tab', 'IVECO BIG DB [EN]');
            $propertyIvecoBigDbImportCodeEn->setAttribute('size', '60');
            $propertyIvecoBigDbImportCodeEn->setAttribute('maxlength', '');
            $propertyIvecoBigDbImportCodeEn->setAttribute('readonly', 'F');
            $propertyIvecoBigDbImportCodeEn->setAttribute('pattern', '');
            $propertyIvecoBigDbImportCodeEn->setAttribute('inherit_value', 'F');
            $propertyIvecoBigDbImportCodeEn->setAttribute('onchange-js', '');
            $propertyIvecoBigDbImportCodeEn->setAttribute('onkeyup-js', '');
            $propertyIvecoBigDbImportCodeEn->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyIvecoBigDbImportCodeEn);
        } else {
            $this->writeLine('Property with tag iveco_big_db_import_code_en already exists');
            $this->setDataKey('property_iveco_big_db_import_code_en_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('iveco_big_db_import_code_en', 'eshop_product', false);
        }

        // property: IVECO Big DB - posledný import [EN](iveco_big_db_latest_import_en)
        $propertyIvecoBigDbLatestImportEn = $this->propertyManager()->propertyExistsByTag('iveco_big_db_latest_import_en');
        if ($propertyIvecoBigDbLatestImportEn === false) {
            $propertyIvecoBigDbLatestImportEn = new Property();
            $propertyIvecoBigDbLatestImportEn->setTag('iveco_big_db_latest_import_en');
            $propertyIvecoBigDbLatestImportEn->setDescription('');
            $propertyIvecoBigDbLatestImportEn->setExtendedDescription('');
            $propertyIvecoBigDbLatestImportEn->setName('IVECO Big DB - posledný import [EN]');
            $propertyIvecoBigDbLatestImportEn->setClassId(4);
            $propertyIvecoBigDbLatestImportEn->setShowType(null);
            $propertyIvecoBigDbLatestImportEn->setShowTypeTag('text');
            $propertyIvecoBigDbLatestImportEn->setValueType('oneline_text');
            $propertyIvecoBigDbLatestImportEn->setDefaultValue('');
            $propertyIvecoBigDbLatestImportEn->setMultiOperations(false);
            $propertyIvecoBigDbLatestImportEn->setInputString('');
            $propertyIvecoBigDbLatestImportEn->setAttribute('tab', 'IVECO BIG DB [EN]');
            $propertyIvecoBigDbLatestImportEn->setAttribute('size', '60');
            $propertyIvecoBigDbLatestImportEn->setAttribute('maxlength', '');
            $propertyIvecoBigDbLatestImportEn->setAttribute('readonly', 'F');
            $propertyIvecoBigDbLatestImportEn->setAttribute('pattern', '');
            $propertyIvecoBigDbLatestImportEn->setAttribute('inherit_value', 'F');
            $propertyIvecoBigDbLatestImportEn->setAttribute('onchange-js', '');
            $propertyIvecoBigDbLatestImportEn->setAttribute('onkeyup-js', '');
            $propertyIvecoBigDbLatestImportEn->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyIvecoBigDbLatestImportEn);
        } else {
            $this->writeLine('Property with tag iveco_big_db_latest_import_en already exists');
            $this->setDataKey('property_iveco_big_db_latest_import_en_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('iveco_big_db_latest_import_en', 'eshop_product', false);
        }

        // property: Dostupnosť - IVECO BIG DB import [EN](availability_big_db_en)
        $propertyAvailabilityBigDbEn = $this->propertyManager()->propertyExistsByTag('availability_big_db_en');
        if ($propertyAvailabilityBigDbEn === false) {
            $propertyAvailabilityBigDbEn = new Property();
            $propertyAvailabilityBigDbEn->setTag('availability_big_db_en');
            $propertyAvailabilityBigDbEn->setDescription('');
            $propertyAvailabilityBigDbEn->setExtendedDescription('');
            $propertyAvailabilityBigDbEn->setName('Dostupnosť - IVECO BIG DB import [EN]');
            $propertyAvailabilityBigDbEn->setClassId(4);
            $propertyAvailabilityBigDbEn->setShowType(null);
            $propertyAvailabilityBigDbEn->setShowTypeTag('select');
            $propertyAvailabilityBigDbEn->setValueType('items_set');
            $propertyAvailabilityBigDbEn->setDefaultValue('');
            $propertyAvailabilityBigDbEn->setMultiOperations(false);
            $propertyAvailabilityBigDbEn->setInputString('');
            $propertyAvailabilityBigDbEn->setAttribute('tab', 'IVECO BIG DB [EN]');
            $propertyAvailabilityBigDbEn->setAttribute('options', [
    1 => 'Na sklade',
    2 => 'U dodávateľa',
    3 => 'Na objednávku',
]);
            $propertyAvailabilityBigDbEn->setAttribute('multiple', 'F');
            $propertyAvailabilityBigDbEn->setAttribute('show_as_list', 'T');
            $propertyAvailabilityBigDbEn->setAttribute('empty_option', 'F');
            $propertyAvailabilityBigDbEn->setAttribute('size', '');
            $propertyAvailabilityBigDbEn->setAttribute('cols', null);
            $propertyAvailabilityBigDbEn->setAttribute('rows', '');
            $propertyAvailabilityBigDbEn->setAttribute('onchange-js', '');
            $propertyAvailabilityBigDbEn->setAttribute('disabled', 'F');
            $propertyAvailabilityBigDbEn->setAttribute('check_read_rights', 'T');
            $propertyAvailabilityBigDbEn->setAttribute('inherit_value', 'F');
            $this->propertyManager()->saveProperty($propertyAvailabilityBigDbEn);
        } else {
            $this->writeLine('Property with tag availability_big_db_en already exists');
            $this->setDataKey('property_availability_big_db_en_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('availability_big_db_en', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Dostupnosť - IVECO BIG DB import [EN](availability_big_db_en)
        $propertyAvailabilityBigDbEn = $this->propertyManager()->propertyExistsByTag('availability_big_db_en');
        if (($propertyAvailabilityBigDbEn !== false) && ($this->getDataKey('property_availability_big_db_en_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyAvailabilityBigDbEn);
        }

        // remove property: IVECO Big DB - posledný import [EN](iveco_big_db_latest_import_en)
        $propertyIvecoBigDbLatestImportEn = $this->propertyManager()->propertyExistsByTag('iveco_big_db_latest_import_en');
        if (($propertyIvecoBigDbLatestImportEn !== false) && ($this->getDataKey('property_iveco_big_db_latest_import_en_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyIvecoBigDbLatestImportEn);
        }

        // remove property: IVECO Big DB import kód [EN](iveco_big_db_import_code_en)
        $propertyIvecoBigDbImportCodeEn = $this->propertyManager()->propertyExistsByTag('iveco_big_db_import_code_en');
        if (($propertyIvecoBigDbImportCodeEn !== false) && ($this->getDataKey('property_iveco_big_db_import_code_en_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyIvecoBigDbImportCodeEn);
        }

        // remove property: Iveco Original (big DB) EUR bez DPH [EN](eshop_eur_iveco_big_db_price_without_vat_en)
        $propertyEshopEurIvecoBigDbPriceWithoutVatEn = $this->propertyManager()->propertyExistsByTag('eshop_eur_iveco_big_db_price_without_vat_en');
        if (($propertyEshopEurIvecoBigDbPriceWithoutVatEn !== false) && ($this->getDataKey('property_eshop_eur_iveco_big_db_price_without_vat_en_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyEshopEurIvecoBigDbPriceWithoutVatEn);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
