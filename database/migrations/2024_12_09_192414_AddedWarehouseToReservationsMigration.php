<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Automatic generation from (rinoparts) at 2024-12-09 19:24:14
 */
class AddedWarehouseToReservationsMigration extends AbstractMigration
{
    public function up()
    {
        // update table onix_reserved_stock_products
        Schema::table('onix_reserved_stock_products', function (Blueprint $table) {
            $table->string('warehouse')->after('reserved_amount')->nullable();
        });

        \DB::table('onix_reserved_stock_products')
            ->where('warehouse', null)
            ->update(['warehouse' => 'S1']);
    }

    public function down()
    {
        // revert changes to table onix_reserved_stock_products
        Schema::table('onix_reserved_stock_products', function (Blueprint $table) {
            $table->dropColumn('warehouse');
        });
    }
}
