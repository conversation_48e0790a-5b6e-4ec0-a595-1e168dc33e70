<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Automatic generation from (rinoparts) at 2022-03-08 17:34:33
 */
class Create_delivery_addresses_table extends AbstractMigration
{
    public function up()
    {
        // create table tblDeliveryAddresses
        Schema::create('tblDeliveryAddresses', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('webuser_id');
            $table->string('fullname');
            $table->string('company_name');
            $table->string('delivery_phone');
            $table->string('street');
            $table->string('city');
            $table->string('zip');
            $table->string('country');
            $table->timestamps();
        });
    }

    public function down()
    {
        // drop table tblDeliveryAddresses
        Schema::dropIfExists('tblDeliveryAddresses');
    }
}
