<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2022-10-24 14:36:40
 * Property generator: property=show_all_products
 */
class ShowAllProductsPropertyMigration extends AbstractMigration
{
    public function up()
    {
        // property: Zobrazovať všetky produkty(show_all_products)
        $propertyShowAllProducts = $this->propertyManager()->propertyExistsByTag('show_all_products');
        if ($propertyShowAllProducts === false) {
            $propertyShowAllProducts = new Property();
            $propertyShowAllProducts->setTag('show_all_products');
            $propertyShowAllProducts->setDescription('');
            $propertyShowAllProducts->setExtendedDescription('');
            $propertyShowAllProducts->setName('Zobrazovať všetky produkty');
            $propertyShowAllProducts->setClassId(4);
            $propertyShowAllProducts->setShowType(null);
            $propertyShowAllProducts->setShowTypeTag('checkbox');
            $propertyShowAllProducts->setValueType('logical_value');
            $propertyShowAllProducts->setDefaultValue('');
            $propertyShowAllProducts->setMultiOperations(false);
            $propertyShowAllProducts->setInputString('');
            $propertyShowAllProducts->setAttribute('tab', '');
            $propertyShowAllProducts->setAttribute('on_value', 'T');
            $propertyShowAllProducts->setAttribute('off_value', 'F');
            $propertyShowAllProducts->setAttribute('onclick-js', '');
            $propertyShowAllProducts->setAttribute('inherit_value', 'F');
            $propertyShowAllProducts->setAttribute('readonly', 'F');
            $this->propertyManager()->saveProperty($propertyShowAllProducts);
        } else {
            $this->writeLine('Property with tag show_all_products already exists');
            $this->setDataKey('property_show_all_products_existed', true);
        }
        if ($this->pageTypeExists('eshop_category')) {
            $this->addPropertyToPageType('show_all_products', 'eshop_category', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Zobrazovať všetky produkty(show_all_products)
        $propertyShowAllProducts = $this->propertyManager()->propertyExistsByTag('show_all_products');
        if (($propertyShowAllProducts !== false) && ($this->getDataKey('property_show_all_products_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyShowAllProducts);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
