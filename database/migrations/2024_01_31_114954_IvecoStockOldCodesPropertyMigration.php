<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2024-01-31 11:49:54
 * Property generator: property=iveco_stock_old_codes
 */
class IvecoStockOldCodesPropertyMigration extends AbstractMigration
{
    public function up()
    {
        // property: Iveco Stock - Staré kódy(iveco_stock_old_codes)
        $propertyIvecoStockOldCodes = $this->propertyManager()->propertyExistsByTag('iveco_stock_old_codes');
        if ($propertyIvecoStockOldCodes === false) {
            $propertyIvecoStockOldCodes = new Property();
            $propertyIvecoStockOldCodes->setTag('iveco_stock_old_codes');
            $propertyIvecoStockOldCodes->setDescription('');
            $propertyIvecoStockOldCodes->setExtendedDescription('');
            $propertyIvecoStockOldCodes->setName('Iveco Stock - Staré kódy');
            $propertyIvecoStockOldCodes->setClassId(4);
            $propertyIvecoStockOldCodes->setShowType(null);
            $propertyIvecoStockOldCodes->setShowTypeTag('text');
            $propertyIvecoStockOldCodes->setValueType('oneline_text');
            $propertyIvecoStockOldCodes->setDefaultValue('');
            $propertyIvecoStockOldCodes->setMultiOperations(false);
            $propertyIvecoStockOldCodes->setInputString('');
            $propertyIvecoStockOldCodes->setAttribute('tab', 'Iveco Stock Prices');
            $propertyIvecoStockOldCodes->setAttribute('size', '60');
            $propertyIvecoStockOldCodes->setAttribute('maxlength', '');
            $propertyIvecoStockOldCodes->setAttribute('readonly', 'F');
            $propertyIvecoStockOldCodes->setAttribute('pattern', '');
            $propertyIvecoStockOldCodes->setAttribute('inherit_value', 'F');
            $propertyIvecoStockOldCodes->setAttribute('onchange-js', '');
            $propertyIvecoStockOldCodes->setAttribute('onkeyup-js', '');
            $propertyIvecoStockOldCodes->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyIvecoStockOldCodes);
        } else {
            $this->writeLine('Property with tag iveco_stock_old_codes already exists');
            $this->setDataKey('property_iveco_stock_old_codes_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('iveco_stock_old_codes', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Iveco Stock - Staré kódy(iveco_stock_old_codes)
        $propertyIvecoStockOldCodes = $this->propertyManager()->propertyExistsByTag('iveco_stock_old_codes');
        if (($propertyIvecoStockOldCodes !== false) && ($this->getDataKey('property_iveco_stock_old_codes_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyIvecoStockOldCodes);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
