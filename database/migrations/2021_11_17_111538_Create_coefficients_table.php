<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Automatic generation from (rinoparts) at 2021-11-17 11:15:38
 */
class Create_coefficients_table extends AbstractMigration
{
    public function up()
    {
        // create table coefficients
        Schema::create('coefficients', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('producer_ciselnik_id');
            $table->decimal('coefficient');
            $table->timestamps();
        });
    }

    public function down()
    {
        // drop table coefficients
        Schema::dropIfExists('coefficients');
    }
}
