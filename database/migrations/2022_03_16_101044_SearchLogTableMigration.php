<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (rinoparts) at 2022-03-16 10:10:44
 */
class SearchLogTableMigration extends AbstractMigration
{
    public function up()
    {
        Schema::create('tblWebUserSearchLog', function ($table) {
            $table->unsignedBigInteger('webuser_id');
            $table->string('search_term');
            $table->datetime('search_time');
        });
    }

    public function down()
    {
        Schema::dropIfExists('tblWebUserSearchLog');
    }
}
