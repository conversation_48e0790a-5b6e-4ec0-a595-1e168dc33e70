<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2024-05-28 09:36:47
 * Property generator: property=special_turbo_stock_code
 */
class SpecialTurboStockCodePropertyMigration extends AbstractMigration
{
    public function up()
    {
        // property: Special Turbo - Kód skladovej zásoby(special_turbo_stock_code)
        $propertySpecialTurboStockCode = $this->propertyManager()->propertyExistsByTag('special_turbo_stock_code');
        if ($propertySpecialTurboStockCode === false) {
            $propertySpecialTurboStockCode = new Property();
            $propertySpecialTurboStockCode->setTag('special_turbo_stock_code');
            $propertySpecialTurboStockCode->setDescription('');
            $propertySpecialTurboStockCode->setExtendedDescription('');
            $propertySpecialTurboStockCode->setName('Special Turbo - Kód skladovej zásoby');
            $propertySpecialTurboStockCode->setClassId(4);
            $propertySpecialTurboStockCode->setShowType(null);
            $propertySpecialTurboStockCode->setShowTypeTag('text');
            $propertySpecialTurboStockCode->setValueType('oneline_text');
            $propertySpecialTurboStockCode->setDefaultValue('');
            $propertySpecialTurboStockCode->setMultiOperations(false);
            $propertySpecialTurboStockCode->setInputString('');
            $propertySpecialTurboStockCode->setAttribute('tab', 'SpecialTurbo');
            $propertySpecialTurboStockCode->setAttribute('size', '60');
            $propertySpecialTurboStockCode->setAttribute('maxlength', '');
            $propertySpecialTurboStockCode->setAttribute('readonly', 'F');
            $propertySpecialTurboStockCode->setAttribute('pattern', '');
            $propertySpecialTurboStockCode->setAttribute('inherit_value', 'F');
            $propertySpecialTurboStockCode->setAttribute('onchange-js', '');
            $propertySpecialTurboStockCode->setAttribute('onkeyup-js', '');
            $propertySpecialTurboStockCode->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertySpecialTurboStockCode);
        } else {
            $this->writeLine('Property with tag special_turbo_stock_code already exists');
            $this->setDataKey('property_special_turbo_stock_code_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('special_turbo_stock_code', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Special Turbo - Kód skladovej zásoby(special_turbo_stock_code)
        $propertySpecialTurboStockCode = $this->propertyManager()->propertyExistsByTag('special_turbo_stock_code');
        if (($propertySpecialTurboStockCode !== false) && ($this->getDataKey('property_special_turbo_stock_code_existed') === null)) {
            $this->propertyManager()->removeProperty($propertySpecialTurboStockCode);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
