<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Automatic generation from (rinoparts) at 2025-03-10 08:03:50
 */
class DropEnclosureRecordIdAsPrimaryKey extends AbstractMigration
{
    public function up()
    {
        DB::statement('ALTER TABLE onix_enclosures MODIFY enclosure_record_id INT;');
        DB::statement('ALTER TABLE onix_enclosures DROP PRIMARY KEY;');
        DB::statement('ALTER TABLE onix_enclosures ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST;');
        DB::statement('ALTER TABLE onix_enclosures MODIFY enclosure_record_id INT NULL;');
    }

    public function down()
    {

    }
}
