<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (rinoparts) at 2024-01-26 13:40:30
 * Page generator: page_id=1721094,1726569
 */
class EminiaPagesMigration extends AbstractMigration
{
    public function up()
    {
        // page: Eminia(ID: 1721094 TAG: eminia_supplier)
        $pageId = $this->getPageIdByTag('eminia_supplier');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('rolller_item_supplier');
            $page1721094 = \PageFactory::create($this->getPageIdByTag('ciselnik-dodavatel'), $pageType->getId());
        } else {
            $page1721094 = \PageFactory::get($pageId);
        }
        $page1721094->setPageName('Eminia');
        $page1721094->setPageTag('eminia_supplier');
        $page1721094->setPageStateId('1');
        $page1721094->setPageClassId(1);
        $page1721094->setValue('title', 'Eminia');
        $page1721094->setValue('title_en', '');
        $page1721094->setValue('title_cz', '');
        $page1721094->setValue('image', '');
        $page1721094->setValue('delivery_time', '');
        $page1721094->setValue('delivery_time_cz', '');
        $page1721094->setValue('delivery_time_en', '');
        $page1721094->save();

        // page: Eminia 1(ID: 1726569 TAG: eminia_settings)
        $pageId = $this->getPageIdByTag('eminia_settings');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('supplier_catalog_settings');
            $page1726569 = \PageFactory::create($this->getPageIdByTag('suppliers_settings'), $pageType->getId());
        } else {
            $page1726569 = \PageFactory::get($pageId);
        }
        $page1726569->setPageName('Eminia 1');
        $page1726569->setPageTag('eminia_settings');
        $page1726569->setPageStateId('2');
        $page1726569->setPageClassId(1);
        $page1726569->setValue('transport_surcharge', '4');
        $page1726569->save();
    }

    public function down()
    {
        // remove page: Eminia 1 (eminia_settings)
        $pageId = $this->getPageIdByTag('eminia_settings');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }

        // remove page: Eminia (eminia_supplier)
        $pageId = $this->getPageIdByTag('eminia_supplier');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }
    }
}
