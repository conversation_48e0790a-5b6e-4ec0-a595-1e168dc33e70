<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (rinoparts) at 2022-11-25 10:35:49
 * Page generator: page_id=746036,746037
 */
class NrfSettingsAndSupplierPages extends AbstractMigration
{
    public function up()
    {
        // page: NRF 1(ID: 746036 TAG: nrf_settings)
        $pageId = $this->getPageIdByTag('nrf_settings');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('supplier_catalog_settings');
            $page746036 = \PageFactory::create($this->getPageIdByTag('suppliers_settings'), $pageType->getId());
        } else {
            $page746036 = \PageFactory::get($pageId);
        }
        $page746036->setPageName('NRF 1');
        $page746036->setPageTag('nrf_settings');
        $page746036->setPageStateId('1');
        $page746036->setPageClassId(1);
        $page746036->setValue('transport_surcharge', '10');
        $page746036->save();

        // page: NRF 2(ID: 746037 TAG: nrf_supplier)
        $pageId = $this->getPageIdByTag('nrf_supplier');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('rolller_item_supplier');
            $page746037 = \PageFactory::create($this->getPageIdByTag('ciselnik-dodavatel'), $pageType->getId());
        } else {
            $page746037 = \PageFactory::get($pageId);
        }
        $page746037->setPageName('NRF 2');
        $page746037->setPageTag('nrf_supplier');
        $page746037->setPageStateId('2');
        $page746037->setPageClassId(1);
        $page746037->setValue('title', 'NRF');
        $page746037->setValue('title_en', '');
        $page746037->setValue('title_cz', '');
        $page746037->setValue('image', '');
        $page746037->setValue('delivery_time', '');
        $page746037->setValue('delivery_time_cz', '');
        $page746037->setValue('delivery_time_en', '');
        $page746037->save();
    }

    public function down()
    {
        // remove page: NRF 2 (nrf_supplier)
        $pageId = $this->getPageIdByTag('nrf_supplier');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }

        // remove page: NRF 1 (nrf_settings)
        $pageId = $this->getPageIdByTag('nrf_settings');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }
    }
}
