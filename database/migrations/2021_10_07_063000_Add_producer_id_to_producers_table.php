<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Automatic generation from (rinoparts) at 2021-10-07 06:30:00
 */
class Add_producer_id_to_producers_table extends AbstractMigration
{
    public function up()
    {
        // update table producers
        Schema::table('producers', function (Blueprint $table) {
            $table->unsignedBigInteger('producer_ciselnik_id')->after('name')->unique();
        });
    }

    public function down()
    {
        // revert changes to table producers
        Schema::table('producers', function (Blueprint $table) {
            $table->dropColumn('producer_ciselnik_id');
        });
    }
}
