<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (rinoparts) at 2025-02-10 07:36:04
 * Page generator: page_id=80897152
 */
class FebiBilsteinCampaignPageMigration extends AbstractMigration
{
    public function up()
    {
        // page: Febi Bilstein 2(ID: 80897152 TAG: febi_campaign)
        $pageId = $this->getPageIdByTag('febi_campaign');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('service_page');
            $page80897152 = \PageFactory::create($this->getPageIdByTag('info_pages'), $pageType->getId());
        } else {
            $page80897152 = \PageFactory::get($pageId);
        }
        $page80897152->setPageName('Febi Bilstein 2');
        $page80897152->setPageTag('febi_campaign');
        $page80897152->setPageStateId('2');
        $page80897152->setPageClassId(1);
        $page80897152->setValue('title', 'Febi Bilstein');
        $page80897152->setValue('title_en', '');
        $page80897152->setValue('title_cz', '');
        $page80897152->setValue('text', '');
        $page80897152->setValue('text_en', '');
        $page80897152->setValue('text_cz', '');
        $page80897152->setValue('seo_url_name', null);
        $page80897152->setValue('meta_title', '');
        $page80897152->setValue('seo_url_name_en', null);
        $page80897152->setValue('meta_title_en', '');
        $page80897152->setValue('seo_url_name_cz', null);
        $page80897152->setValue('meta_title_cz', '');
        // set template App\Http\Controllers\IndexController@febiCampaign
        $page80897152->getPageTemplate()->setLaravelAction('App\\Http\\Controllers\\IndexController@febiCampaign');
        $page80897152->save();
    }

    public function down()
    {
        // remove page: Febi Bilstein 2 (febi_campaign)
        $pageId = $this->getPageIdByTag('febi_campaign');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }
    }
}
