<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2022-04-21 14:23:58
 * Property generator: property=onix_stock_reservation
 */
class OnixReservationsPropertyMigration extends AbstractMigration
{
    public function up()
    {
        // property: Počet rezervácií(onix_stock_reservation)
        $propertyOnixStockReservation = $this->propertyManager()->propertyExistsByTag('onix_stock_reservation');
        if ($propertyOnixStockReservation === false) {
            $propertyOnixStockReservation = new Property();
            $propertyOnixStockReservation->setTag('onix_stock_reservation');
            $propertyOnixStockReservation->setDescription('');
            $propertyOnixStockReservation->setExtendedDescription('');
            $propertyOnixStockReservation->setName('Počet rezervácií');
            $propertyOnixStockReservation->setClassId(4);
            $propertyOnixStockReservation->setShowType(null);
            $propertyOnixStockReservation->setShowTypeTag('text');
            $propertyOnixStockReservation->setValueType('oneline_text');
            $propertyOnixStockReservation->setDefaultValue('');
            $propertyOnixStockReservation->setMultiOperations(false);
            $propertyOnixStockReservation->setInputString('');
            $propertyOnixStockReservation->setAttribute('tab', 'Onix');
            $propertyOnixStockReservation->setAttribute('size', '60');
            $propertyOnixStockReservation->setAttribute('maxlength', '');
            $propertyOnixStockReservation->setAttribute('readonly', 'F');
            $propertyOnixStockReservation->setAttribute('pattern', '');
            $propertyOnixStockReservation->setAttribute('inherit_value', 'F');
            $propertyOnixStockReservation->setAttribute('onchange-js', '');
            $propertyOnixStockReservation->setAttribute('onkeyup-js', '');
            $propertyOnixStockReservation->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyOnixStockReservation);
        } else {
            $this->writeLine('Property with tag onix_stock_reservation already exists');
            $this->setDataKey('property_onix_stock_reservation_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('onix_stock_reservation', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Počet rezervácií(onix_stock_reservation)
        $propertyOnixStockReservation = $this->propertyManager()->propertyExistsByTag('onix_stock_reservation');
        if (($propertyOnixStockReservation !== false) && ($this->getDataKey('property_onix_stock_reservation_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyOnixStockReservation);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
