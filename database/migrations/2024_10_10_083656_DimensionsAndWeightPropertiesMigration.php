<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2024-10-10 08:36:56
 * Property generator: property=weight,width,length,height
 */
class DimensionsAndWeightPropertiesMigration extends AbstractMigration
{
    public function up()
    {
        // property: Váha (g)(weight)
        $propertyWeight = $this->propertyManager()->propertyExistsByTag('weight');
        if ($propertyWeight === false) {
            $propertyWeight = new Property();
            $propertyWeight->setTag('weight');
            $propertyWeight->setDescription('');
            $propertyWeight->setExtendedDescription('');
            $propertyWeight->setName('Váha (g)');
            $propertyWeight->setClassId(4);
            $propertyWeight->setShowType(null);
            $propertyWeight->setShowTypeTag('text');
            $propertyWeight->setValueType('oneline_text');
            $propertyWeight->setDefaultValue('');
            $propertyWeight->setMultiOperations(false);
            $propertyWeight->setInputString('');
            $propertyWeight->setAttribute('tab', 'Rozmery a v&aacute;ha');
            $propertyWeight->setAttribute('size', '10');
            $propertyWeight->setAttribute('maxlength', '');
            $propertyWeight->setAttribute('readonly', 'F');
            $propertyWeight->setAttribute('pattern', '');
            $propertyWeight->setAttribute('inherit_value', 'F');
            $propertyWeight->setAttribute('onchange-js', '');
            $propertyWeight->setAttribute('onkeyup-js', '');
            $propertyWeight->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyWeight);
        } else {
            $this->writeLine('Property with tag weight already exists');
            $this->setDataKey('property_weight_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('weight', 'eshop_product', false);
        }

        // property: Šírka (mm)(width)
        $propertyWidth = $this->propertyManager()->propertyExistsByTag('width');
        if ($propertyWidth === false) {
            $propertyWidth = new Property();
            $propertyWidth->setTag('width');
            $propertyWidth->setDescription('');
            $propertyWidth->setExtendedDescription('');
            $propertyWidth->setName('Šírka (mm)');
            $propertyWidth->setClassId(4);
            $propertyWidth->setShowType(null);
            $propertyWidth->setShowTypeTag('text');
            $propertyWidth->setValueType('oneline_text');
            $propertyWidth->setDefaultValue('');
            $propertyWidth->setMultiOperations(false);
            $propertyWidth->setInputString('');
            $propertyWidth->setAttribute('tab', 'Rozmery a v&aacute;ha');
            $propertyWidth->setAttribute('size', '10');
            $propertyWidth->setAttribute('maxlength', '');
            $propertyWidth->setAttribute('readonly', 'F');
            $propertyWidth->setAttribute('pattern', '');
            $propertyWidth->setAttribute('inherit_value', 'F');
            $propertyWidth->setAttribute('onchange-js', '');
            $propertyWidth->setAttribute('onkeyup-js', '');
            $propertyWidth->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyWidth);
        } else {
            $this->writeLine('Property with tag width already exists');
            $this->setDataKey('property_width_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('width', 'eshop_product', false);
        }

        // property: Dĺžka (mm)(length)
        $propertyLength = $this->propertyManager()->propertyExistsByTag('length');
        if ($propertyLength === false) {
            $propertyLength = new Property();
            $propertyLength->setTag('length');
            $propertyLength->setDescription('');
            $propertyLength->setExtendedDescription('');
            $propertyLength->setName('Dĺžka (mm)');
            $propertyLength->setClassId(4);
            $propertyLength->setShowType(null);
            $propertyLength->setShowTypeTag('text');
            $propertyLength->setValueType('oneline_text');
            $propertyLength->setDefaultValue('');
            $propertyLength->setMultiOperations(false);
            $propertyLength->setInputString('');
            $propertyLength->setAttribute('tab', 'Rozmery a v&aacute;ha');
            $propertyLength->setAttribute('size', '10');
            $propertyLength->setAttribute('maxlength', '');
            $propertyLength->setAttribute('readonly', 'F');
            $propertyLength->setAttribute('pattern', '');
            $propertyLength->setAttribute('inherit_value', 'F');
            $propertyLength->setAttribute('onchange-js', '');
            $propertyLength->setAttribute('onkeyup-js', '');
            $propertyLength->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyLength);
        } else {
            $this->writeLine('Property with tag length already exists');
            $this->setDataKey('property_length_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('length', 'eshop_product', false);
        }

        // property: Výška (mm)(height)
        $propertyHeight = $this->propertyManager()->propertyExistsByTag('height');
        if ($propertyHeight === false) {
            $propertyHeight = new Property();
            $propertyHeight->setTag('height');
            $propertyHeight->setDescription('');
            $propertyHeight->setExtendedDescription('');
            $propertyHeight->setName('Výška (mm)');
            $propertyHeight->setClassId(4);
            $propertyHeight->setShowType(null);
            $propertyHeight->setShowTypeTag('text');
            $propertyHeight->setValueType('oneline_text');
            $propertyHeight->setDefaultValue('');
            $propertyHeight->setMultiOperations(false);
            $propertyHeight->setInputString('');
            $propertyHeight->setAttribute('tab', 'Rozmery a v&aacute;ha');
            $propertyHeight->setAttribute('size', '10');
            $propertyHeight->setAttribute('maxlength', '');
            $propertyHeight->setAttribute('readonly', 'F');
            $propertyHeight->setAttribute('pattern', '');
            $propertyHeight->setAttribute('inherit_value', 'F');
            $propertyHeight->setAttribute('onchange-js', '');
            $propertyHeight->setAttribute('onkeyup-js', '');
            $propertyHeight->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyHeight);
        } else {
            $this->writeLine('Property with tag height already exists');
            $this->setDataKey('property_height_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('height', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Výška (mm)(height)
        $propertyHeight = $this->propertyManager()->propertyExistsByTag('height');
        if (($propertyHeight !== false) && ($this->getDataKey('property_height_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyHeight);
        }

        // remove property: Dĺžka (mm)(length)
        $propertyLength = $this->propertyManager()->propertyExistsByTag('length');
        if (($propertyLength !== false) && ($this->getDataKey('property_length_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyLength);
        }

        // remove property: Šírka (mm)(width)
        $propertyWidth = $this->propertyManager()->propertyExistsByTag('width');
        if (($propertyWidth !== false) && ($this->getDataKey('property_width_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyWidth);
        }

        // remove property: Váha (g)(weight)
        $propertyWeight = $this->propertyManager()->propertyExistsByTag('weight');
        if (($propertyWeight !== false) && ($this->getDataKey('property_weight_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyWeight);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
