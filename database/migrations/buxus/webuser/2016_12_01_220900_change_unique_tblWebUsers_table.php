<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeUniqueTblWebUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tblWebUsers', function ($table) {
            $table->dropUnique('tblwebusers_username_unique');
            $table->unique(['username', 'site'], 'tblwebusers_username_site_unique');
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tblWebUsers', function ($table) {
            $table->dropUnique('tblwebusers_username_site_unique');
            $table->unique('username', 'tblwebusers_username_unique');
        });
    }
}
