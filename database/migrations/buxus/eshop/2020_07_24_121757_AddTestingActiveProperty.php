<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (buxus_demo) at 2020-07-24 12:17:57
 * Property generator: property=testing_active
 */
class AddTestingActiveProperty extends AbstractMigration
{
    public function up()
    {
        // property: Testovaci režim(testing_active)
        $propertyTestingActive = $this->propertyManager()->propertyExistsByTag('testing_active');
        if ($propertyTestingActive === false) {
            $propertyTestingActive = new Property();
            $propertyTestingActive->setTag('testing_active');
            $propertyTestingActive->setDescription('');
            $propertyTestingActive->setExtendedDescription('');
            $propertyTestingActive->setName('Testovaci režim');
            $propertyTestingActive->setClassId(4);
            $propertyTestingActive->setShowType(null);
            $propertyTestingActive->setShowTypeTag('checkbox');
            $propertyTestingActive->setValueType('logical_value');
            $propertyTestingActive->setDefaultValue('');
            $propertyTestingActive->setMultiOperations(false);
            $propertyTestingActive->setInputString('');
            $propertyTestingActive->setAttribute('tab', 'Testovanie');
            $propertyTestingActive->setAttribute('on_value', 'T');
            $propertyTestingActive->setAttribute('off_value', 'F');
            $propertyTestingActive->setAttribute('onclick-js', '');
            $propertyTestingActive->setAttribute('inherit_value', 'F');
            $propertyTestingActive->setAttribute('readonly', 'F');
            $this->propertyManager()->saveProperty($propertyTestingActive);
        } else {
            $this->writeLine('Property with tag testing_active already exists');
            $this->setDataKey('property_testing_active_existed', true);
        }
        if ($this->pageTypeExists('eshop_transport_type')) {
            $this->addPropertyToPageType('testing_active', 'eshop_transport_type', false);
        }
        if ($this->pageTypeExists('eshop_payment_type')) {
            $this->addPropertyToPageType('testing_active', 'eshop_payment_type', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Testovaci režim(testing_active)
        $propertyTestingActive = $this->propertyManager()->propertyExistsByTag('testing_active');
        if ($propertyTestingActive !== false) {
            $supportedPageTypes = $this->propertyManager()->getSupportedPageTypes($propertyTestingActive);
            if (($this->getDataKey('property_testing_active_existed') === null) && (count($supportedPageTypes) === 0)) {
                $this->propertyManager()->removeProperty($propertyTestingActive);
            }
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
