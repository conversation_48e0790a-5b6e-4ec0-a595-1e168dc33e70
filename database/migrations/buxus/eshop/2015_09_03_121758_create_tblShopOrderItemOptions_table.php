<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTblShopOrderItemOptionsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('tblShopOrderItemOptions', function(Blueprint $table)
		{
			$table->integer('order_item_id');
			$table->string('option_tag', 50);
			$table->string('option_value')->nullable();
			$table->primary(['order_item_id','option_tag']);
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblShopOrderItemOptions');
	}

}
