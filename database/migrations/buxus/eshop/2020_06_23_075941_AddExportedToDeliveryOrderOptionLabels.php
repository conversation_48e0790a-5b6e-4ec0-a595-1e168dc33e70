<?php

use Buxus\Migration\AbstractMigration;

class AddExportedToDeliveryOrderOptionLabels extends AbstractMigration
{
    protected $orderOptionLabels = [
        'exported_to_delivery' => 'Čas a dátum exportu balíka pre dopravcu',
        'exported_to_delivery_type' => 'Typ exportu balíka pre dopravcu',
    ];

    public function up()
    {
        foreach ($this->orderOptionLabels as $entityTag => $entityName) {
            \DB::table('tblSimpleEntities')
                ->insertOrIgnore([
                    'entity_tag' => $entityTag,
                    'entity_name' => $entityName,
                    'entity_type_tag' => 'order_option'
                ]);
        }
    }

    public function down()
    {
        foreach ($this->orderOptionLabels as $entityTag => $entityName) {
            \DB::table('tblSimpleEntities')
                ->where('entity_tag', $entityTag)
                ->where('entity_name', $entityName)
                ->where('entity_type_tag', 'order_option')
                ->delete();
        }
    }
}
