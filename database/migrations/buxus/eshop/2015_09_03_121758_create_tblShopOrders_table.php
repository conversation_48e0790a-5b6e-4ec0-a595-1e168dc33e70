<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTblShopOrdersTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('tblShopOrders', function(Blueprint $table)
		{
			$table->integer('order_id', true);
			$table->char('variable_symbol', 10)->nullable()->unique('variable_symbol');
			$table->char('order_type', 10)->nullable();
			$table->dateTime('order_datetime');
			$table->integer('order_web_user_id')->nullable();
			$table->string('payment_type', 100)->nullable();
			$table->dateTime('payment_datetime')->nullable();
			$table->integer('payment_web_user_id')->nullable();
			$table->string('delivery_type', 100)->nullable();
			$table->float('paid_price', 10, 0)->default(0);
			$table->char('title', 10)->nullable();
			$table->char('first_name', 100)->nullable();
			$table->char('surname', 100)->nullable();
			$table->char('company_name')->nullable();
			$table->char('street', 100)->nullable();
			$table->char('city', 100)->nullable();
			$table->char('zip', 10)->nullable();
			$table->char('ico', 15)->nullable();
			$table->char('dic', 15)->nullable();
			$table->char('drc', 15)->nullable();
			$table->char('phone', 25)->nullable();
			$table->char('fax', 25)->nullable();
			$table->char('email', 100)->nullable();
			$table->char('inv_company_name')->nullable();
			$table->char('inv_street', 100)->nullable();
			$table->char('inv_city', 100)->nullable();
			$table->char('inv_zip', 10)->nullable();
			$table->char('account_number', 25)->nullable();
			$table->char('bank_number', 25)->nullable();
			$table->char('currency', 3)->nullable()->default('sk');
			$table->float('post_price', 10, 0)->default(0);
			$table->float('post_price_vat', 10, 0)->default(0);
			$table->char('is_active', 1)->nullable()->default('T');
			$table->integer('order_state')->unsigned()->default(0);
			$table->string('invoice_number')->nullable();
			$table->text('note', 65535)->nullable();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblShopOrders');
	}

}
