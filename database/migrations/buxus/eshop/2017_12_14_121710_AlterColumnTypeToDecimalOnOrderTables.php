<?php

use Buxus\Migration\AbstractMigration;

class AlterColumnTypeToDecimalOnOrderTables extends AbstractMigration
{

    public function up()
    {
        \DB::statement("ALTER TABLE `tblShopOrders` CHANGE `paid_price` `paid_price` DECIMAL(12,4) NOT NULL;");

        \DB::statement("ALTER TABLE `tblShopOrderItems` CHANGE `price_per_item` `price_per_item` DECIMAL(12,4) NOT NULL;");
    }

    public function down()
    {
        \DB::statement("ALTER TABLE `tblShopOrders` CHANGE `paid_price` `paid_price` FLOAT NOT NULL;");

        \DB::statement("ALTER TABLE `tblShopOrderItems` CHANGE `price_per_item` `price_per_item` FLOAT NOT NULL;");
    }

}
