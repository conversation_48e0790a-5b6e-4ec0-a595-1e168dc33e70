<?php

use Buxus\Migration\AbstractMigration;

class AddPaymentAndDeliveryTypesSimpleEntities extends AbstractMigration
{
    protected $orderOptionLabels = [
        'payment_type_name' => 'Názov typu platby',
        'delivery_type_name' => 'Názov typu dopravy',
    ];

    public function up()
    {
        foreach ($this->orderOptionLabels as $entityTag => $entityName) {
            \DB::table('tblSimpleEntities')
                ->insertOrIgnore([
                    'entity_tag' => $entityTag,
                    'entity_name' => $entityName,
                    'entity_type_tag' => 'order_option'
                ]);
        }
    }

    public function down()
    {
        foreach ($this->orderOptionLabels as $entityTag => $entityName) {
            \DB::table('tblSimpleEntities')
                ->where('entity_tag', $entityTag)
                ->where('entity_name', $entityName)
                ->where('entity_type_tag', 'order_option')
                ->delete();
        }
    }
}
