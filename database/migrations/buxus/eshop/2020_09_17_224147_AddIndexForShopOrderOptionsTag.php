<?php

use Buxus\Migration\AbstractMigration;

use Buxus\Util\DBSchema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIndexForShopOrderOptionsTag extends AbstractMigration
{
    public function up()
    {
        Schema::table('tblShopOrderOptions', static function (Blueprint $table) {
            if (!DBSchema::tableKeyExists('tblShopOrderOptions', 'order_tag')) {
                $table->index(['order_tag']);
            }
        });
    }

    public function down()
    {
        Schema::table('tblShopOrderOptions', static function (Blueprint $table) {
            $table->dropIndex(['order_tag']);
        });
    }
}
