<?php

use Buxus\Migration\AbstractMigration;

class AddMissingOrderOptionLabels extends AbstractMigration
{
    protected $orderOptionLabels = [
        'order_security_token' => 'Bezpečnostný kód objednávky',
        'order_raw_price' => 'Suma produktov',
        'delivery_address_identical' => 'Zhodná doručovacia a fakturačná adresa',
        'customer_type' => 'Typ zákazníka',
        'country' => 'Krajina',
        'delivery_name' => 'Doručovacia adresa - meno',
        'delivery_phone' => 'Doručovacia adresa - telefón',
        'inv_country' => 'Fakt. adresa - krajina',
    ];

    public function up()
    {
        foreach ($this->orderOptionLabels as $entityTag => $entityName) {
            \DB::table('tblSimpleEntities')
                ->insertOrIgnore([
                    'entity_tag' => $entityTag,
                    'entity_name' => $entityName,
                    'entity_type_tag' => 'order_option'
                ]);
        }
    }

    public function down()
    {
        foreach ($this->orderOptionLabels as $entityTag => $entityName) {
            \DB::table('tblSimpleEntities')
                ->where('entity_tag', $entityTag)
                ->where('entity_name', $entityName)
                ->where('entity_type_tag', 'order_option')
                ->delete();
        }
    }
}
