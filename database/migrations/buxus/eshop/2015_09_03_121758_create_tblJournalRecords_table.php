<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTblJournalRecordsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('tblJournalRecords', function(Blueprint $table)
		{
			$table->integer('record_id', true);
			$table->integer('journal_id')->default(0);
			$table->string('journal_type', 20)->default('');
			$table->timestamp('timestamp')->default(DB::raw('CURRENT_TIMESTAMP'));
			$table->integer('custom_id')->nullable();
			$table->integer('author_id')->default(0);
			$table->binary('comment', 65535)->nullable();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblJournalRecords');
	}

}
