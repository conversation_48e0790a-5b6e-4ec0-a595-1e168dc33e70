<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (buxus_demo) at 2020-01-23 15:29:50
 * Property generator: property=site
 */
class AddSiteBuxusCustomProperty extends AbstractMigration
{
    public function up()
    {
        // property: Doména(site)
        $propertySite = $this->propertyManager()->propertyExistsByTag('site');
        if ($propertySite === false) {
            $propertySite = new Property();
            $propertySite->setTag('site');
            $propertySite->setDescription('');
            $propertySite->setExtendedDescription('');
            $propertySite->setName('Doména');
            $propertySite->setClassId(4);
            $propertySite->setShowType(null);
            $propertySite->setShowTypeTag('custom_property');
            $propertySite->setValueType('custom_property');
            $propertySite->setDefaultValue('');
            $propertySite->setMultiOperations(false);
            $propertySite->setInputString('');
            $propertySite->setAttribute('tab', '');
            $propertySite->setAttribute('class_name', 'Buxus\\Site\\Property\\SiteBuxusCustomProperty');
            $propertySite->setAttribute('inherit_value', 'F');
            $this->propertyManager()->saveProperty($propertySite);
        } else {
            $this->writeLine('Property with tag site already exists');
            $this->setDataKey('property_site_existed', true);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Doména(site)
        $propertySite = $this->propertyManager()->propertyExistsByTag('site');
        if ($propertySite !== false) {
            $supportedPageTypes = $this->propertyManager()->getSupportedPageTypes($propertySite);
            if (($this->getDataKey('property_site_existed') === null) && (count($supportedPageTypes) === 0)) {
                $this->propertyManager()->removeProperty($propertySite);
            }
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
