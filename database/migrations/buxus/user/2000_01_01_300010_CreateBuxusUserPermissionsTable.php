<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
/**
 * Automatic generation from (luxus2) at 2017-10-02 12:23:14
 */

class CreateBuxusUserPermissionsTable extends AbstractMigration
{
	public function up()
	{
        if (!Schema::hasTable('bx_buxus_user_permissions')) {
            Schema::create('bx_buxus_user_permissions', function (Blueprint $table) {
                $table->integer('user_id');
                $table->string('permission_name', 128);
                $table->primary(['user_id', 'permission_name']);
            });
        }
	}

	public function down()
	{
        if (Schema::hasTable('bx_buxus_user_permissions')) {
            Schema::drop('bx_buxus_user_permissions');
        }
	}
}
