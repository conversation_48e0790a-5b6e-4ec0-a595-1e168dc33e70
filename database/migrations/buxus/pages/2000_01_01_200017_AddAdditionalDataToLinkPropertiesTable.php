<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;

class AddAdditionalDataToLinkPropertiesTable extends AbstractMigration
{
    public function up()
    {
        if (!Schema::hasColumn('tblLinkProperties', 'additionalData')) {
            Schema::table('tblLinkProperties', function($table) {
                $table->text('additionalData')->nullable();
            });
        }

        if (!Schema::hasColumn('tblArchiveLinkProperties', 'additionalData')) {
            Schema::table('tblArchiveLinkProperties', function($table) {
                $table->text('additionalData')->nullable();
            });
        }
    }

    public function down()
    {
        if (Schema::hasColumn('tblArchiveLinkProperties', 'additionalData')) {
            Schema::table('tblArchiveLinkProperties', function($table) {
                $table->dropColumn('additionalData');
            });
        }

        if (Schema::hasColumn('tblLinkProperties', 'additionalData')) {
            Schema::table('tblLinkProperties', function($table) {
                $table->dropColumn('additionalData');
            });
        }
    }
}
