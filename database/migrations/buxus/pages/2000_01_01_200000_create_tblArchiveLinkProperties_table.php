<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTblArchiveLinkPropertiesTable extends AbstractMigration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
        if (!Schema::hasTable('tblArchiveLinkProperties')) {
            Schema::create('tblArchiveLinkProperties', function (Blueprint $table) {
                $table->integer('property_id')->default(0);
                $table->integer('from_page_archive_id', false, true);
                $table->integer('to_page_id', false, true);
                $table->string('url', 255);
                $table->text('text')->nullable();
                $table->integer('order_index', false, true);
                $table->text('additionalData');
            });
            DB::unprepared("ALTER TABLE `tblArchiveLinkProperties` ADD PRIMARY KEY (`property_id`, `from_page_archive_id`, `to_page_id`, `url`(32), `order_index`)");
        }
		
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblArchiveLinkProperties');
	}

}
