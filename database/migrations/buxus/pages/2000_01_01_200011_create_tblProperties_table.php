<?php

use Buxus\Legacy\Services\PageAndPropertiesConstantsGenerator;
use Buxus\Migration\AbstractMigration;
use Buxus\Property\Facade\PropertyManager;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTblPropertiesTable extends AbstractMigration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('tblProperties')) {
            Schema::create('tblProperties', function (Blueprint $table) {
                $table->integer('property_id')->unsigned()->default(0)->primary();
                $table->string('property_name')->nullable();
                $table->string('property_tag', 128)->unique('property_tag');
                $table->integer('show_type')->nullable();
                $table->text('input_string', 16777215)->nullable();
                $table->char('default_value', 30)->nullable();
                $table->integer('property_class_id')->nullable();
                $table->text('property_description', 16777215)->nullable();
                $table->text('property_extended_description', 65535)->nullable();
                $table->string('show_type_tag', 100)->nullable();
                $table->text('property_properties', 65535)->nullable();
                $table->char('multi_operations', 1)->nullable()->default('F');
                $table->string('value_type', 100)->nullable();
            });
        }
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('tblProperties');

        // invalidate caches
        PropertyManager::invalidateCache();
    }
}
