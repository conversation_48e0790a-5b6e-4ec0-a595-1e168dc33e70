<?php
use Buxus\Migration\AbstractMigration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class PageDraftsTable extends AbstractMigration
{
    public function supportedBuxusVersions()
    {
        return [99];
//        return [7];
        // @TODO: BUXUS7 MIGRATIONS
    }
    
    public function up()
    {
        if (config('database.default') === 'mysql') {
            \DB::unprepared('ALTER TABLE `tblPages` CHANGE `page_id` `page_id` INT(11)  UNSIGNED  NOT NULL  AUTO_INCREMENT;');
        }
        Schema::create('bx_page_drafts', function (Blueprint $table) {
            $table->increments('id');
            $table->text('data')->nullable();
            $table->integer('page_id')->unsigned()->nullable();
            $table->integer('author_id')->unsigned();
            $table->nullableTimestamps();
            $table->foreign('page_id')->references('page_id')->on('tblPages')->onDelete('cascade');
            $table->unique(['author_id', 'page_id']);
        });
    }

    public function down()
    {
        Schema::drop('bx_page_drafts');
    }
}
