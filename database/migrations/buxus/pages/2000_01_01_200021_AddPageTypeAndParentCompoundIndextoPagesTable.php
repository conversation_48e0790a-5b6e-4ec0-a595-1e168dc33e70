<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Util\DBSchema;

class AddPageTypeAndParentCompoundIndextoPagesTable extends AbstractMigration
{
    public function up()
    {
        if (!DBSchema::tableKeyExists('tblPages', ['parent_page_id', 'page_type_id'])) {
            \DB::statement('create index parent_and_type on tblPages (parent_page_id, page_type_id)');
        }
    }

    public function down()
    {
        \DB::statement('alter table tblPages drop index parent_and_type');
    }
}
