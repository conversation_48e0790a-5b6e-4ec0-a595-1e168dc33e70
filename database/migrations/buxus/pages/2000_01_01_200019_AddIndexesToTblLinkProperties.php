<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Util\DBSchema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIndexesToTblLinkProperties extends AbstractMigration
{
    public function up()
    {
        Schema::table('tblLinkProperties', static function (Blueprint $table) {
            if (!DBSchema::tableKeyExists('tblLinkProperties', 'from_page_id')) {
                $table->index(['from_page_id']);
            }
            if (!DBSchema::tableKeyExists('tblLinkProperties', 'to_page_id')) {
                $table->index(['to_page_id']);
            }
        });
    }

    public function down()
    {
        Schema::table('tblLinkProperties', static function (Blueprint $table) {
            $table->dropIndex(['from_page_id']);
            $table->dropIndex(['to_page_id']);
        });
    }
}
