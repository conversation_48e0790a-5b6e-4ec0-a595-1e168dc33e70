CREATE TRIGGER `seo_url_before_delete` <PERSON><PERSON><PERSON><PERSON> DELETE ON `tblSeoUrl` FOR EACH ROW BEGIN
  IF (OLD.no_seo_url = 0) THEN
    INSERT INTO
      tblSeoUrlArchive
    SET
      page_id = OLD.page_id,
      protocol = OLD.protocol,
      domain = OLD.domain,
      path = OLD.path,
      parameters = OLD.parameters
    ON DUPLICATE KEY UPDATE
      page_id = OLD.page_id,
      parameters = OLD.parameters,
      archive_type = 'archived';
  END IF;
END
