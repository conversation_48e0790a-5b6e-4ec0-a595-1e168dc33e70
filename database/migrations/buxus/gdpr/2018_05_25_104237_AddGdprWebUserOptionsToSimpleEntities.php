<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (ipredplatne) at 2018-05-25 10:42:37
 */

class AddGdprWebUserOptionsToSimpleEntities extends AbstractMigration
{
	public function up()
	{
        \BuxusDB::get()->insert('tblSimpleEntities', array(
            'entity_tag' => 'age_limit_agreement',
            'entity_name' => 'Používateľ má aspoň 16 rokov',
            'entity_type_tag' => 'web_user_option',
        ));

        \BuxusDB::get()->insert('tblSimpleEntities', array(
            'entity_tag' => 'gdpr_agreement',
            'entity_name' => 'Používateľ súhlasí so zasielaním údajov podľa podmienok GDPR',
            'entity_type_tag' => 'web_user_option',
        ));
	}

	public function down()
	{
        \BuxusDB::get()->delete('tblSimpleEntities', array(
            'entity_tag = ?' => 'age_limit_agreement',
            'entity_type_tag = ?' => 'web_user_option',
        ));

        \BuxusDB::get()->delete('tblSimpleEntities', array(
            'entity_tag = ?' => 'gdpr_agreement',
            'entity_type_tag = ?' => 'web_user_option',
        ));
	}

}
