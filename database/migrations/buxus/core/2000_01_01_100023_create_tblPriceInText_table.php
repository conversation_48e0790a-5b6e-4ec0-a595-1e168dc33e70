<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTblPriceInTextTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('tblPriceInText', function(Blueprint $table)
		{
			$table->increments('entry_id');
			$table->integer('page_id');
			$table->integer('property_id');
			$table->integer('offset');
			$table->integer('lenght');
			$table->string('strvalue');
			$table->float('value', 10, 0);
			$table->boolean('shortcut')->default(0);
			$table->text('context', 65535)->nullable();
			$table->boolean('ignore')->nullable();
			$table->dateTime('updated_date')->nullable();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblPriceInText');
	}

}
