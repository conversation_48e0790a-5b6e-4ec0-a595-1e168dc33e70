<?php
namespace Buxus\Migrations;

use Buxus\Migration\AbstractMigration;
use Buxus\PageType\PageType;
use Buxus\PageType\PageTypePropertyItem;

class BuxusBasicTypes extends AbstractMigration {
    public function dependencies()
    {
        return [
            BuxusMainPage::class,
//            \PropertyConfigTable::class,
//            \CreateTblSeoUrlModuleTable::class,
        ];
    }

    public function up() {
        // property: Titulok(title)
        $property_title = $this->propertyManager()->propertyExistsByTag('title');
        if ($property_title === false) {
            $property_title = new \Buxus\Property\Types\Input();
            $property_title->setTag('title');
            $property_title->setDescription('Štandardný názov stránky. Text, ktorý sa zobrazí v záložke prehliadača. Podľa SEO odporúčaní max. 65 znakov.');
            $property_title->setExtendedDescription('');
            $property_title->setName('Titulok');
            $property_title->setClassId('4');
            $property_title->setShowType(null);
            $property_title->setShowTypeTag('text');
            $property_title->setValueType('oneline_text');
            $property_title->setDefaultValue('');
            $property_title->setMultiOperations(false);
            $property_title->setInputString(NULL);
            $property_title->setAttribute('tab', '');
            $property_title->setAttribute('size', '60');
            $property_title->setAttribute('maxlength', '');
            $property_title->setAttribute('readonly', 'F');
            $property_title->setAttribute('pattern', '');
            $property_title->setAttribute('inherit_value', 'F');
            $property_title->setAttribute('onchange-js', '');
            $property_title->setAttribute('onkeyup-js', '');
            $property_title->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_title);
        } else {
            $this->writeLine('Property with tag title already exists');
            $this->setDataKey('property_title_existed', true);
        }

        // property: Text(text)
        $property_text = $this->propertyManager()->propertyExistsByTag('text');
        if ($property_text === false) {
            $property_text = new \Buxus\Property\Types\Textarea();
            $property_text->setTag('text');
            $property_text->setDescription('Štandardný text stránky.');
            $property_text->setExtendedDescription('');
            $property_text->setName('Text');
            $property_text->setClassId('4');
            $property_text->setShowType(NULL);
            $property_text->setShowTypeTag('textarea');
            $property_text->setValueType('multiline_text');
            $property_text->setDefaultValue('');
            $property_text->setMultiOperations(false);
            $property_text->setInputString(NULL);
            $property_text->setAttribute('tab', '');
            $property_text->setAttribute('cols', '60');
            $property_text->setAttribute('rows', '');
            $property_text->setAttribute('dhtml-edit', '1');
            $property_text->setAttribute('dhtml-configuration', 'full');
            $property_text->setAttribute('import-word', '0');
            $property_text->setAttribute('auto', '1');
            $property_text->setAttribute('inherit_value', 'F');
            $property_text->setAttribute('onchange-js', '');
            $property_text->setAttribute('onkeyup-js', '');
            $property_text->setAttribute('onkeydown-js', '');
            $property_text->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($property_text);
        } else {
            $this->writeLine('Property with tag text already exists');
            $this->setDataKey('property_text_existed', true);
        }

        // property: SEO URL name(seo_url_name)
        $property_seo_url_name = $this->propertyManager()->propertyExistsByTag('seo_url_name');
        if ($property_seo_url_name === false) {
            $property_seo_url_name = new \Buxus\Property\Property();
            $property_seo_url_name->setTag('seo_url_name');
            $property_seo_url_name->setDescription('Podľa SEO odporúčaní max. 35 znakov.');
            $property_seo_url_name->setExtendedDescription('');
            $property_seo_url_name->setName('SEO URL name');
            $property_seo_url_name->setClassId('4');
            $property_seo_url_name->setShowType(NULL);
            $property_seo_url_name->setShowTypeTag('seo_url_name');
            $property_seo_url_name->setValueType('seo_url_name');
            $property_seo_url_name->setDefaultValue('');
            $property_seo_url_name->setMultiOperations(false);
            $property_seo_url_name->setInputString(NULL);
            $property_seo_url_name->setAttribute('tab', 'SEO');
            $property_seo_url_name->setAttribute('size', '80');
            $property_seo_url_name->setAttribute('onchange-js', '');
            $property_seo_url_name->setAttribute('onkeyup-js', '');
            $property_seo_url_name->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_seo_url_name);
        } else {
            $this->writeLine('Property with tag seo_url_name already exists');
            $this->setDataKey('property_seo_url_name_existed', true);
        }

        // page type: Hlavná stránka (homepage)
        $page_type_homepage = $this->pageTypesManager()->pageTypeExistsByTag('homepage');
        if ($page_type_homepage === false) {
            $page_type_homepage = new PageType();
            $page_type_homepage->setTag('homepage');
            $page_type_homepage->setName('Hlavná stránka');
            $page_type_homepage->setPageClassId('1');
            $page_type_homepage->setDefaultTemplateId('2');
            $page_type_homepage->setDeleteTrigger('');
            $page_type_homepage->setIncludeInSync(NULL);
            $page_type_homepage->setPageDetailsLayout('');
            $page_type_homepage->setPageSortTypeTag('sort_date_time');
            $page_type_homepage->setPageTypeOrder('10');
            $page_type_homepage->setPostmoveTrigger('');
            $page_type_homepage->setPostsubmitTrigger('');
            $page_type_homepage->setPresubmitTrigger('');
            $page_type_homepage->setParent(NULL);
        } else {
            $this->writeLine('Page type with tag homepage already exists');
            $this->setDataKey('page_type_homepage_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('title');
        $property_id = $property->getId();
        $tmp = $page_type_homepage->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder('1');
            $tmp->setRequired(false);
            $page_type_homepage->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('text');
        $property_id = $property->getId();
        $tmp = $page_type_homepage->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder('3');
            $tmp->setRequired(false);
            $page_type_homepage->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('seo_url_name');
        $property_id = $property->getId();
        $tmp = $page_type_homepage->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder('5');
            $tmp->setRequired(false);
            $page_type_homepage->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($page_type_homepage);
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('homepage'), 'index', 'homepage');

        // page type: special (service_page)
        $page_type_service_page = $this->pageTypesManager()->pageTypeExistsByTag('service_page');
        if ($page_type_service_page === false) {
            $page_type_service_page = new PageType();
            $page_type_service_page->setTag('service_page');
            $page_type_service_page->setName('special');
            $page_type_service_page->setPageClassId('1');
            $page_type_service_page->setDefaultTemplateId('2');
            $page_type_service_page->setDeleteTrigger('');
            $page_type_service_page->setIncludeInSync('1');
            $page_type_service_page->setPageDetailsLayout('');
            $page_type_service_page->setPageSortTypeTag('sort_date_time');
            $page_type_service_page->setPageTypeOrder('1000');
            $page_type_service_page->setPostmoveTrigger('');
            $page_type_service_page->setPostsubmitTrigger('');
            $page_type_service_page->setPresubmitTrigger('');
            $page_type_service_page->setParent(NULL);
        } else {
            $this->writeLine('Page type with tag service_page already exists');
            $this->setDataKey('page_type_service_page_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('title');
        $property_id = $property->getId();
        $tmp = $page_type_service_page->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder('1');
            $tmp->setRequired(true);
            $page_type_service_page->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('text');
        $property_id = $property->getId();
        $tmp = $page_type_service_page->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder('2');
            $tmp->setRequired(false);
            $page_type_service_page->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('seo_url_name');
        $property_id = $property->getId();
        $tmp = $page_type_service_page->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder('3');
            $tmp->setRequired(false);
            $page_type_service_page->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($page_type_service_page);
        // set template on MAIN PAGE index::index
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('service_page'), 'index', 'index');

        // page type: Nastavenia (settings)
        $page_type_settings = $this->pageTypesManager()->pageTypeExistsByTag('settings');
        if ($page_type_settings === false) {
            $page_type_settings = new PageType();
            $page_type_settings->setTag('settings');
            $page_type_settings->setName('Nastavenia');
            $page_type_settings->setPageClassId('1');
            $page_type_settings->setDefaultTemplateId('2');
            $page_type_settings->setDeleteTrigger('');
            $page_type_settings->setIncludeInSync('1');
            $page_type_settings->setPageDetailsLayout('');
            $page_type_settings->setPageSortTypeTag('sort_date_time');
            $page_type_settings->setPageTypeOrder('400');
            $page_type_settings->setPostmoveTrigger('');
            $page_type_settings->setPostsubmitTrigger('');
            $page_type_settings->setPresubmitTrigger('');
            $page_type_settings->setParent(NULL);
        } else {
            $this->writeLine('Page type with tag settings already exists');
            $this->setDataKey('page_type_settings_existed', true);
        }
        $this->pageTypesManager()->savePageType($page_type_settings);
        // set template on MAIN PAGE index::error404
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('settings'), 'index', 'error404');

        // page type: Priečinok (folder)
        $page_type_folder = $this->pageTypesManager()->pageTypeExistsByTag('folder');
        if ($page_type_folder === false) {
            $page_type_folder = new PageType();
            $page_type_folder->setTag('folder');
            $page_type_folder->setName('Priečinok');
            $page_type_folder->setPageClassId('1');
            $page_type_folder->setDefaultTemplateId('2');
            $page_type_folder->setDeleteTrigger('');
            $page_type_folder->setIncludeInSync(NULL);
            $page_type_folder->setPageDetailsLayout('');
            $page_type_folder->setPageSortTypeTag('sort_date_time');
            $page_type_folder->setPageTypeOrder('30');
            $page_type_folder->setPostmoveTrigger('');
            $page_type_folder->setPostsubmitTrigger('');
            $page_type_folder->setPresubmitTrigger('');
            $page_type_folder->setParent(NULL);
        } else {
            $this->writeLine('Page type with tag folder already exists');
            $this->setDataKey('page_type_folder_existed', true);
        }
        $this->pageTypesManager()->savePageType($page_type_folder);
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('folder'), 'index', 'error404');

        if ($this->pageTypeExists('main_page')) {
            $this->addPageTypeSuperiorPageType('homepage', 'main_page');
        }
        if ($this->pageTypeExists('settings')) {
            $this->addPageTypeSuperiorPageType('service_page', 'settings');
        }
        if ($this->pageTypeExists('folder')) {
            $this->addPageTypeSuperiorPageType('service_page', 'folder');
        }
        if ($this->pageTypeExists('service_page')) {
            $this->addPageTypeSuperiorPageType('service_page', 'service_page');
        }
        if ($this->pageTypeExists('template')) {
            $this->addPageTypeSuperiorPageType('service_page', 'template');
        }
        if ($this->pageTypeExists('main_page')) {
            $this->addPageTypeSuperiorPageType('service_page', 'main_page');
        }
        if ($this->pageTypeExists('element_entity')) {
            $this->addPageTypeSuperiorPageType('settings', 'element_entity');
        }
        if ($this->pageTypeExists('settings')) {
            $this->addPageTypeSuperiorPageType('settings', 'settings');
        }
        if ($this->pageTypeExists('folder')) {
            $this->addPageTypeSuperiorPageType('settings', 'folder');
        }
        if ($this->pageTypeExists('main_page')) {
            $this->addPageTypeSuperiorPageType('settings', 'main_page');
        }
        if ($this->pageTypeExists('settings')) {
            $this->addPageTypeSuperiorPageType('folder', 'settings');
        }
        if ($this->pageTypeExists('folder')) {
            $this->addPageTypeSuperiorPageType('folder', 'folder');
        }
        if ($this->pageTypeExists('main_page')) {
            $this->addPageTypeSuperiorPageType('folder', 'main_page');
        }

        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

    }

    public function down() {
        // remove page type: Priečinok (folder)
        $page_type_folder = $this->pageTypesManager()->pageTypeExistsByTag('folder');
        if (($page_type_folder != false) && (is_null($this->getDataKey('page_type_folder_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_folder);
        }

        // remove page type: Nastavenia (settings)
        $page_type_settings = $this->pageTypesManager()->pageTypeExistsByTag('settings');
        if (($page_type_settings != false) && (is_null($this->getDataKey('page_type_settings_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_settings);
        }

        // remove page type: special (service_page)
        $page_type_service_page = $this->pageTypesManager()->pageTypeExistsByTag('service_page');
        if (($page_type_service_page != false) && (is_null($this->getDataKey('page_type_service_page_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_service_page);
        }

        // remove page type: Hlavná stránka (homepage)
        $page_type_homepage = $this->pageTypesManager()->pageTypeExistsByTag('homepage');
        if (($page_type_homepage != false) && (is_null($this->getDataKey('page_type_homepage_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_homepage);
        }

        // remove property: SEO URL name(seo_url_name)
        $property_seo_url_name = $this->propertyManager()->propertyExistsByTag('seo_url_name');
        if ($property_seo_url_name != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_seo_url_name);
            if ((is_null($this->getDataKey('property_seo_url_name_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_seo_url_name);
            }
        }

        // remove property: Text(text)
        $property_text = $this->propertyManager()->propertyExistsByTag('text');
        if ($property_text != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_text);
            if ((is_null($this->getDataKey('property_text_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_text);
            }
        }

        // remove property: Titulok(title)
        $property_title = $this->propertyManager()->propertyExistsByTag('title');
        if ($property_title != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_title);
            if ((is_null($this->getDataKey('property_title_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_title);
            }
        }

        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

    }

}
