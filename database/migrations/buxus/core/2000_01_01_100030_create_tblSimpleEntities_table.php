<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTblSimpleEntitiesTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('tblSimpleEntities', function(Blueprint $table)
		{
			$table->integer('entity_id', true);
			$table->string('entity_tag', 64)->nullable();
			$table->string('entity_name', 128)->nullable();
			$table->string('entity_type_tag', 64)->nullable();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblSimpleEntities');
	}

}
