<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTblAccessRightsForumUserTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('tblAccessRightsForumUser', function(Blueprint $table)
		{
			$table->integer('user_id');
			$table->integer('message_id');
			$table->char('edit_right', 1)->nullable();
			$table->primary(['user_id','message_id']);
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblAccessRightsForumUser');
	}

}
