<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTblSubmitedFormsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('tblSubmitedForms', function(Blueprint $table)
		{
			$table->integer('form_submit_id', true);
			$table->string('form_type_tag', 64)->default('');
			$table->char('form_state_flag', 1)->default('N');
			$table->timestamp('form_submit_time')->default(DB::raw('CURRENT_TIMESTAMP'));
			$table->text('received_properties', 65535)->nullable();
			$table->index(['form_type_tag','form_state_flag','form_submit_time'], 'form_type_tag');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblSubmitedForms');
	}

}
