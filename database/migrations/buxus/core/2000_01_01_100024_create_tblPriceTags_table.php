<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTblPriceTagsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('tblPriceTags', function(Blueprint $table)
		{
			$table->increments('entry_id');
			$table->integer('page_id')->unsigned();
			$table->integer('property_id')->unsigned();
			$table->integer('offset')->unsigned();
			$table->integer('lenght')->unsigned();
			$table->string('strvalue');
			$table->dateTime('updated_date');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblPriceTags');
	}

}
