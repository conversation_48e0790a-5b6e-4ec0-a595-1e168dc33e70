<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTblAdvancedEntitiesTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('tblAdvancedEntities', function(Blueprint $table)
		{
			$table->integer('entity_id', true);
			$table->integer('master_id')->nullable()->index('master_id');
			$table->string('entity_type_tag', 30)->nullable()->index('entity_type_tag');
			$table->integer('custom_int')->nullable()->index('custom_int');
			$table->string('custom_varchar')->nullable()->index('custom_varchar');
			$table->string('custom_fulltext')->nullable()->index('custom_fulltext');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblAdvancedEntities');
	}

}
