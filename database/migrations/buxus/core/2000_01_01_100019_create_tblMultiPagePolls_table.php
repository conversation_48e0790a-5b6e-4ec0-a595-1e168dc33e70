<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTblMultiPagePollsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('tblMultiPagePolls', function(Blueprint $table)
		{
			$table->integer('poll_id')->default(0);
			$table->integer('page_id')->default(0);
			$table->unique(['poll_id','page_id'], 'poll_id_page_id');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblMultiPagePolls');
	}

}
