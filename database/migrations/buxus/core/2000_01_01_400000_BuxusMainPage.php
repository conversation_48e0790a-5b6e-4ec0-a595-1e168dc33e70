<?php
namespace Buxus\Migrations;

use Buxus\Migration\AbstractMigration;
use Buxus\PageType\PageType;
use Buxus\Util\PageIds;

class BuxusMainPage extends AbstractMigration {
    public function dependencies()
    {
        return [
            \InitBuxusData::class,
        ];
    }

    public function up() {
        // page type: Web (main_page)
        $page_type_main_page = $this->pageTypesManager()->pageTypeExistsByTag('main_page');
        if ($page_type_main_page === false) {
            $page_type_main_page = new PageType();
            $page_type_main_page->setTag('main_page');
            $page_type_main_page->setName('Web');
            $page_type_main_page->setPageClassId('1');
            $page_type_main_page->setDefaultTemplateId('1');
            $page_type_main_page->setDeleteTrigger('');
            $page_type_main_page->setIncludeInSync('0');
            $page_type_main_page->setPageDetailsLayout('');
            $page_type_main_page->setPageSortTypeTag('sort_date_time');
            $page_type_main_page->setPageTypeOrder('0');
            $page_type_main_page->setPostmoveTrigger('');
            $page_type_main_page->setPostsubmitTrigger('');
            $page_type_main_page->setPresubmitTrigger('');
            $page_type_main_page->setParent(NULL);
        } else {
            $this->writeLine('Page type with tag main_page already exists');
            $this->setDataKey('page_type_main_page_existed', true);
        }
        $this->pageTypesManager()->savePageType($page_type_main_page);
        // set template on MAIN PAGE index::homepage

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('main_page'), 'index', 'homepage');

        // page: Web(ID: 1 TAG: root_page)
        $page_id = $this->getPageIdByTag('root_page');
        if ($page_id === NULL) {
            $page_type = $this->pageTypesManager()->getPageTypeByTag('main_page');
            $page_1 = \PageFactory::create(NULL, $page_type->getId());
        } else {
            $page_1 = \PageFactory::get($page_id);
        }
        $page_1->setPageName('Web');
        $page_1->setPageTag('root_page');
        $page_1->setPageStateId('1');
        $page_1->setPageClassId('1');
        $page_1->save(false);

        // regenerate page tags
        PageIds::generatePageTagsList();
    }

    public function down() {
        // remove page: Web (root_page)
        $page_id = $this->getPageIdByTag('root_page');
        if ($page_id !== NULL) {
            \PageFactory::get($page_id)->delete();
        }

        // regenerate page tags
        PageIds::generatePageTagsList();

        // remove page type: Web (main_page)
        $page_type_main_page = $this->pageTypesManager()->pageTypeExistsByTag('main_page');
        if (($page_type_main_page != false) && (is_null($this->getDataKey('page_type_main_page_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_main_page);
        }

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

    }
}
