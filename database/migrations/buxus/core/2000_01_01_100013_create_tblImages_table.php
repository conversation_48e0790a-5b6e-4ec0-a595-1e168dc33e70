<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTblImagesTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('tblImages', function(Blueprint $table)
		{
			$table->integer('image_id', true);
			$table->string('caption')->default('');
			$table->string('filename')->default('');
			$table->integer('folder_id')->default(0);
			$table->integer('user_id')->default(0);
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblImages');
	}

}
