<?php
namespace Buxus\Migrations;

use Buxus\Migration\AbstractMigration;
use Buxus\Util\PageIds;

class BuxusBasicPages extends AbstractMigration {
    public function dependencies()
    {
        return [
            BuxusBasicTypes::class,
//            \CreateTblSeoUrlModuleTable::class,
        ];
    }

    public function up() {

        // page: <PERSON><PERSON><PERSON> stránka(ID: 8 TAG: homepage)
        $page_id = $this->getPageIdByTag('homepage');
        if ($page_id === NULL) {
            $page_type = $this->pageTypesManager()->getPageTypeByTag('homepage');
            $page_8 = \PageFactory::create($this->getPageIdByTag('root_page'), $page_type->getId());
        } else {
            $page_8 = \PageFactory::get($page_id);
        }
        $page_8->setPageName('Hlavná stránka');
        $page_8->setPageTag('homepage');
        $page_8->setPageStateId('1');
        $page_8->setPageClassId('1');
        $page_8->setPropertyValue('title', 'homepage');
        $page_8->setPropertyValue('text', '<p>HOMEPAGE page_id:<buxus-prop>page_id</buxus-prop></p>');
        $page_8->setPropertyValue('seo_url_name', '/homepage');
        $page_8->save();

        // page: Administrátorské nastavenia(ID: 11 TAG: admin_settings)
        $page_id = $this->getPageIdByTag('admin_settings');
        if ($page_id === NULL) {
            $page_type = $this->pageTypesManager()->getPageTypeByTag('settings');
            $page_11 = \PageFactory::create($this->getPageIdByTag('root_page'), $page_type->getId());
        } else {
            $page_11 = \PageFactory::get($page_id);
        }
        $page_11->setPageName('Administrátorské nastavenia');
        $page_11->setPageTag('admin_settings');
        $page_11->setPageStateId('1');
        $page_11->setPageClassId('1');
        // set template on MAIN PAGE index::error404
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('settings'), 'index', 'error404');
        $page_11->save();

        // page: Špeciálne stránky(ID: 4 TAG: special_pages)
        $page_type = $this->pageTypesManager()->getPageTypeByTag('folder');
        $page_4 = \PageFactory::create($page_11->getPageId(), $page_type->getId());
        $page_4->setPageName('Špeciálne stránky');
        $page_4->setPageTag('special_pages');
        $page_4->setPageStateId('2');
        $page_4->setPageClassId('1');
        $page_4->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_4->save();

        // page: 404: Stránka sa nenašla(ID: 6 TAG: page_404)
        $page_type = $this->pageTypesManager()->getPageTypeByTag('service_page');
        $page_6 = \PageFactory::create($page_4->getPageId(), $page_type->getId());
        $page_6->setPageName('404: Stránka sa nenašla');
        $page_6->setPageTag('page_404');
        $page_6->setPageStateId('2');
        $page_6->setPageClassId('1');
        $page_6->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_6->setPropertyValue('title', 'Požadovaná stránka sa nenašla');
        $page_6->setPropertyValue('text', 'Vážený používateľ, požadovaná stránka sa nenašla. Skontrolujte, či ste zadali adresu do internetového prehliadača správne. V prípade, že ste sa dostali na túto stránku priamo kliknutím z nášho webu, kontaktujte nás prosím.');
        // set template index::error404
        $page_6->getPageTemplate()->setController('index');
        $page_6->getPageTemplate()->setAction('error404');
        $page_6->save();

        // page: 403: Zakázaný prístup(ID: 7 TAG: page_403)
        $page_type = $this->pageTypesManager()->getPageTypeByTag('service_page');
        $page_7 = \PageFactory::create($page_4->getPageId(), $page_type->getId());
        $page_7->setPageName('403: Zakázaný prístup');
        $page_7->setPageTag('page_403');
        $page_7->setPageStateId('2');
        $page_7->setPageClassId('1');
        $page_7->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_7->setPropertyValue('title', 'Zakázaný prístup');
        $page_7->setPropertyValue('text', 'Ospravedlňujeme sa, ale na uvedenú stránku nemáte oprávnenie pristupovať.');
        // set template on MAIN PAGE index::index
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('service_page'), 'index', 'index');
        $page_7->save();

        // regenerate page tags
        PageIds::generatePageTagsList();

    }

    public function down() {
        // remove page: Administrátorské nastavenia (admin_settings)
        $page_id = $this->getPageIdByTag('admin_settings');
        if ($page_id !== NULL) {
            \PageFactory::get($page_id)->delete();
        }

        // remove page: Hlavná stránka (homepage)
        $page_id = $this->getPageIdByTag('homepage');
        if ($page_id !== NULL) {
            \PageFactory::get($page_id)->delete();
        }

        // regenerate page tags
        PageIds::generatePageTagsList();

    }

}
