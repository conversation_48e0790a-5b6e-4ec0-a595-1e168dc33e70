<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTblCachedPropertiesTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('tblCachedProperties', function(Blueprint $table)
		{
			$table->integer('page_id')->default(0);
			$table->integer('property_id')->default(0);
			$table->text('property_value', 16777215)->nullable();
			$table->dateTime('expiration_datetime')->nullable();
			$table->primary(['page_id','property_id']);
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblCachedProperties');
	}

}
