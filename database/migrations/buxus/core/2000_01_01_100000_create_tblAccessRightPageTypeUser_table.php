<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTblAccessRightPageTypeUserTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('tblAccessRightPageTypeUser')) {
            Schema::create('tblAccessRightPageTypeUser', function (Blueprint $table) {
                $table->integer('user_id')->default(0)->index('FK_tblUsers');
                $table->integer('page_type_id')->default(0);
                $table->char('insert_page_type_right', 1)->nullable();
                $table->primary(['page_type_id', 'user_id']);
            });
        }
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('tblAccessRightPageTypeUser');
    }

}
