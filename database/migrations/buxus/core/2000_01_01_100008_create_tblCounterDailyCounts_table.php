<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTblCounterDailyCountsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('tblCounterDailyCounts', function(Blueprint $table)
		{
			$table->integer('counter_id');
			$table->date('for_date')->index('for_date');
			$table->integer('counter_value')->nullable();
			$table->primary(['counter_id','for_date']);
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblCounterDailyCounts');
	}

}
