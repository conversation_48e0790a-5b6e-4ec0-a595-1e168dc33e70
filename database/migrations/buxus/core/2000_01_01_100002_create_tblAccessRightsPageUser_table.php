<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTblAccessRightsPageUserTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('tblAccessRightsPageUser', function(Blueprint $table)
		{
			$table->integer('page_id')->default(0);
			$table->integer('user_id')->default(0);
			$table->char('read_right', 1)->nullable();
			$table->char('write_right', 1)->nullable();
			$table->char('generate_right', 1)->nullable();
			$table->char('publish_right', 1)->nullable();
			$table->primary(['page_id','user_id']);
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblAccessRightsPageUser');
	}

}
