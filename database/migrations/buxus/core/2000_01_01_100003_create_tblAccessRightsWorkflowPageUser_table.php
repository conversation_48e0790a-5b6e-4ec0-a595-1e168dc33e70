<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTblAccessRightsWorkflowPageUserTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('tblAccessRightsWorkflowPageUser', function(Blueprint $table)
		{
			$table->integer('page_id')->default(0);
			$table->integer('user_id')->default(0)->index('user_id');
			$table->integer('state_id')->default(0)->index('state_id');
			$table->char('access_right', 1)->nullable();
			$table->primary(['page_id','user_id','state_id']);
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblAccessRightsWorkflowPageUser');
	}

}
