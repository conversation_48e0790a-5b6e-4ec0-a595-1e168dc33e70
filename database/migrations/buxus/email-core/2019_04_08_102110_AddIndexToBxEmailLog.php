<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Util\DBSchema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIndexToBxEmailLog extends AbstractMigration
{
    public function dependencies()
    {
        return [
            \Email\Migrations\EmailLogMigration::class,
        ];
    }

    public function up()
    {
        if (!DBSchema::tableKeyExists('bx_email_log', ['custom_id', 'tag'])) {
            \DB::statement('create index custom_id_tag on bx_email_log (tag, custom_id)');
        }
    }

    public function down()
    {
        Schema::table('bx_email_log', function (Blueprint $table) {
            $table->dropIndex(['custom_id_tag']);
        });
    }
}
