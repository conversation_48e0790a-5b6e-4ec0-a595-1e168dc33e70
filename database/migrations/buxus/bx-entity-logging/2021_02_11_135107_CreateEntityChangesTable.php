<?php // phpcs:disable PSR1.Classes.ClassDeclaration.MissingNamespace

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateEntityChangesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        \Schema::create('bx_entity_changes', function (Blueprint $table) {
            $table->increments('id');
            $table->string('type');
            $table->integer('identifier');
            $table->json('diff');
            $table->json('metadata');
            $table->dateTime('datetime');

            $table->index('type', 'type_index');
            $table->index(['type', 'identifier'], 'type_identifier_index');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        \Schema::drop('bx_entity_changes');
    }
}
