<?php

namespace Buxus\Translate\Migrations;

use Buxus\Migration\AbstractMigration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class TranslationTableMigration extends AbstractMigration
{
    public function up()
    {
        if (!Schema::hasTable('tblTranslations')) {
            Schema::create('tblTranslations', function (Blueprint $table) {
                $table->string('lang', 4)->default('')->index('lang');
                $table->string('collection', 255)->default('')->index('collection');
                $table->string('tag', 255)->default('')->index('tag');
                $table->text('value');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->tinyInteger('translated')->nullable();
            });

            \DB::unprepared("ALTER TABLE `tblTranslations` ADD PRIMARY KEY (`lang`, `collection`(32), `tag`(120))");
        }
    }

    public function down()
    {
        Schema::drop('tblTranslations');
    }

}
