<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Automatic generation from (buxus_rinoparts_dev) at 2025-06-11 16:00:10
 */
class StockStateLogTable extends AbstractMigration
{
    public function up()
    {
        Schema::create('stock_state_log', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('buxus_product_id');
            $table->integer('stock_value');
            $table->timestamps();

            $table->index(['created_at', 'buxus_product_id'], 'created_at_buxus_product_id_index');
        });
    }

    public function down()
    {
        Schema::dropIfExists('stock_state_log');
    }
}
