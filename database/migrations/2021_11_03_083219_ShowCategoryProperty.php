<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2021-11-03 08:32:19
 * Property generator: property=show_category
 */
class ShowCategoryProperty extends AbstractMigration
{
    public function up()
    {
        // property: Zobrazenie kategórie v megamenu(show_category)
        $propertyShowCategory = $this->propertyManager()->propertyExistsByTag('show_category');
        if ($propertyShowCategory === false) {
            $propertyShowCategory = new Property();
            $propertyShowCategory->setTag('show_category');
            $propertyShowCategory->setDescription('');
            $propertyShowCategory->setExtendedDescription('');
            $propertyShowCategory->setName('Zobrazenie kategórie v megamenu');
            $propertyShowCategory->setClassId(4);
            $propertyShowCategory->setShowType(null);
            $propertyShowCategory->setShowTypeTag('checkbox');
            $propertyShowCategory->setValueType('logical_value');
            $propertyShowCategory->setDefaultValue('T');
            $propertyShowCategory->setMultiOperations(false);
            $propertyShowCategory->setInputString('');
            $propertyShowCategory->setAttribute('tab', '');
            $propertyShowCategory->setAttribute('on_value', 'T');
            $propertyShowCategory->setAttribute('off_value', 'F');
            $propertyShowCategory->setAttribute('onclick-js', '');
            $propertyShowCategory->setAttribute('inherit_value', 'F');
            $propertyShowCategory->setAttribute('readonly', 'F');
            $this->propertyManager()->saveProperty($propertyShowCategory);
        } else {
            $this->writeLine('Property with tag show_category already exists');
            $this->setDataKey('property_show_category_existed', true);
        }
        if ($this->pageTypeExists('eshop_category')) {
            $this->addPropertyToPageType('show_category', 'eshop_category', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Zobrazenie kategórie v megamenu(show_category)
        $propertyShowCategory = $this->propertyManager()->propertyExistsByTag('show_category');
        if (($propertyShowCategory !== false) && ($this->getDataKey('property_show_category_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyShowCategory);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
