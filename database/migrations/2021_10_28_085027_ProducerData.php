<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Translate\TranslatorFactory;

/**
 * Automatic generation from (rinoparts) at 2021-10-28 08:50:26
 * Translation generator: sections=sk.cart
 */
class ProducerData extends AbstractMigration
{
    public function up()
    {
        \DB::table('producers')->insert([
            [
                'price_levels_on' => 0,
                'name' => 'IVECO nákupné ceny',
            ], [
                'price_levels_on' => 0,
                'name' => 'IVECO big db'
            ]
        ]);
    }

    public function down()
    {
    }
}
