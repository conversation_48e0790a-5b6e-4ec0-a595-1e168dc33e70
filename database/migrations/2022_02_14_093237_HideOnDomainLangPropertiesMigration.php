<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2022-02-14 09:32:37
 * Property generator: property=hide_on_domain_cz,hide_on_domain_en
 */
class HideOnDomainLangPropertiesMigration extends AbstractMigration
{
    public function up()
    {
        // property: Nezobrazovať na doméne [CZ](hide_on_domain_cz)
        $propertyHideOnDomainCz = $this->propertyManager()->propertyExistsByTag('hide_on_domain_cz');
        if ($propertyHideOnDomainCz === false) {
            $propertyHideOnDomainCz = new Property();
            $propertyHideOnDomainCz->setTag('hide_on_domain_cz');
            $propertyHideOnDomainCz->setDescription('Ak je táto vlastnosť zaškrtnutá, daný produkt, kategória sa nebude zobrazovať na doméne.');
            $propertyHideOnDomainCz->setExtendedDescription('');
            $propertyHideOnDomainCz->setName('Nezobrazovať na doméne [CZ]');
            $propertyHideOnDomainCz->setClassId(4);
            $propertyHideOnDomainCz->setShowType(null);
            $propertyHideOnDomainCz->setShowTypeTag('checkbox');
            $propertyHideOnDomainCz->setValueType('logical_value');
            $propertyHideOnDomainCz->setDefaultValue('F');
            $propertyHideOnDomainCz->setMultiOperations(false);
            $propertyHideOnDomainCz->setInputString('');
            $propertyHideOnDomainCz->setAttribute('tab', 'CZ');
            $propertyHideOnDomainCz->setAttribute('on_value', 'T');
            $propertyHideOnDomainCz->setAttribute('off_value', 'F');
            $propertyHideOnDomainCz->setAttribute('onclick-js', '');
            $propertyHideOnDomainCz->setAttribute('inherit_value', 'F');
            $propertyHideOnDomainCz->setAttribute('readonly', 'F');
            $this->propertyManager()->saveProperty($propertyHideOnDomainCz);
        } else {
            $this->writeLine('Property with tag hide_on_domain_cz already exists');
            $this->setDataKey('property_hide_on_domain_cz_existed', true);
        }
        if ($this->pageTypeExists('article')) {
            $this->addPropertyToPageType('hide_on_domain_cz', 'article', false);
        }
        if ($this->pageTypeExists('eshop_subcategory')) {
            $this->addPropertyToPageType('hide_on_domain_cz', 'eshop_subcategory', false);
        }
        if ($this->pageTypeExists('eshop_subcategory_3')) {
            $this->addPropertyToPageType('hide_on_domain_cz', 'eshop_subcategory_3', false);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('hide_on_domain_cz', 'eshop_product', false);
        }
        if ($this->pageTypeExists('eshop_product_variant')) {
            $this->addPropertyToPageType('hide_on_domain_cz', 'eshop_product_variant', false);
        }

        // property: Nezobrazovať na doméne [EN](hide_on_domain_en)
        $propertyHideOnDomainEn = $this->propertyManager()->propertyExistsByTag('hide_on_domain_en');
        if ($propertyHideOnDomainEn === false) {
            $propertyHideOnDomainEn = new Property();
            $propertyHideOnDomainEn->setTag('hide_on_domain_en');
            $propertyHideOnDomainEn->setDescription('Ak je táto vlastnosť zaškrtnutá, daný produkt, kategória sa nebude zobrazovať na doméne.');
            $propertyHideOnDomainEn->setExtendedDescription('');
            $propertyHideOnDomainEn->setName('Nezobrazovať na doméne [EN]');
            $propertyHideOnDomainEn->setClassId(4);
            $propertyHideOnDomainEn->setShowType(null);
            $propertyHideOnDomainEn->setShowTypeTag('checkbox');
            $propertyHideOnDomainEn->setValueType('logical_value');
            $propertyHideOnDomainEn->setDefaultValue('F');
            $propertyHideOnDomainEn->setMultiOperations(false);
            $propertyHideOnDomainEn->setInputString('');
            $propertyHideOnDomainEn->setAttribute('tab', 'EN');
            $propertyHideOnDomainEn->setAttribute('on_value', 'T');
            $propertyHideOnDomainEn->setAttribute('off_value', 'F');
            $propertyHideOnDomainEn->setAttribute('onclick-js', '');
            $propertyHideOnDomainEn->setAttribute('inherit_value', 'F');
            $propertyHideOnDomainEn->setAttribute('readonly', 'F');
            $this->propertyManager()->saveProperty($propertyHideOnDomainEn);
        } else {
            $this->writeLine('Property with tag hide_on_domain_en already exists');
            $this->setDataKey('property_hide_on_domain_en_existed', true);
        }
        if ($this->pageTypeExists('article')) {
            $this->addPropertyToPageType('hide_on_domain_en', 'article', false);
        }
        if ($this->pageTypeExists('eshop_subcategory')) {
            $this->addPropertyToPageType('hide_on_domain_en', 'eshop_subcategory', false);
        }
        if ($this->pageTypeExists('eshop_subcategory_3')) {
            $this->addPropertyToPageType('hide_on_domain_en', 'eshop_subcategory_3', false);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('hide_on_domain_en', 'eshop_product', false);
        }
        if ($this->pageTypeExists('eshop_product_variant')) {
            $this->addPropertyToPageType('hide_on_domain_en', 'eshop_product_variant', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Nezobrazovať na doméne [EN](hide_on_domain_en)
        $propertyHideOnDomainEn = $this->propertyManager()->propertyExistsByTag('hide_on_domain_en');
        if (($propertyHideOnDomainEn !== false) && ($this->getDataKey('property_hide_on_domain_en_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyHideOnDomainEn);
        }

        // remove property: Nezobrazovať na doméne [CZ](hide_on_domain_cz)
        $propertyHideOnDomainCz = $this->propertyManager()->propertyExistsByTag('hide_on_domain_cz');
        if (($propertyHideOnDomainCz !== false) && ($this->getDataKey('property_hide_on_domain_cz_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyHideOnDomainCz);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
