<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2022-11-09 19:29:24
 * Property generator: property=producer_image
 */
class ProducerImagePropertyMigration extends AbstractMigration
{
    public function up()
    {
        // property: Obrázok pre zobrazenie na produkte(producer_image)
        $propertyProducerImage = $this->propertyManager()->propertyExistsByTag('producer_image');
        if ($propertyProducerImage === false) {
            $propertyProducerImage = new Property();
            $propertyProducerImage->setTag('producer_image');
            $propertyProducerImage->setDescription('');
            $propertyProducerImage->setExtendedDescription('');
            $propertyProducerImage->setName('Obrázok pre zobrazenie na produkte');
            $propertyProducerImage->setClassId(4);
            $propertyProducerImage->setShowType(null);
            $propertyProducerImage->setShowTypeTag('image_name_upload');
            $propertyProducerImage->setValueType('file');
            $propertyProducerImage->setDefaultValue('');
            $propertyProducerImage->setMultiOperations(false);
            $propertyProducerImage->setInputString('');
            $propertyProducerImage->setAttribute('tab', '');
            $propertyProducerImage->setAttribute('inherit_value', 'F');
            $propertyProducerImage->setAttribute('with_upload', 'T');
            $propertyProducerImage->setAttribute('simple_upload', 'F');
            $propertyProducerImage->setAttribute('show_input_element', 'T');
            $propertyProducerImage->setAttribute('filename', '');
            $propertyProducerImage->setAttribute('show_file_name', 'T');
            $propertyProducerImage->setAttribute('file_type', 'image');
            $propertyProducerImage->setAttribute('pattern', '');
            $propertyProducerImage->setAttribute('show_thumbnail', 'T');
            $propertyProducerImage->setAttribute('max_thumbnail_width', '150');
            $propertyProducerImage->setAttribute('max_thumbnail_height', '80');
            $propertyProducerImage->setAttribute('upload_subdir', '');
            $propertyProducerImage->setAttribute('eval-code', '//$this-&gt;parsed_parm[\'upload_subdir\'] = \'my-dir\';');
            $propertyProducerImage->setAttribute('onchange-js', '');
            $this->propertyManager()->saveProperty($propertyProducerImage);
        } else {
            $this->writeLine('Property with tag producer_image already exists');
            $this->setDataKey('property_producer_image_existed', true);
        }
        if ($this->pageTypeExists('roller_item_producer')) {
            $this->addPropertyToPageType('producer_image', 'roller_item_producer', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Obrázok pre zobrazenie na produkte(producer_image)
        $propertyProducerImage = $this->propertyManager()->propertyExistsByTag('producer_image');
        if (($propertyProducerImage !== false) && ($this->getDataKey('property_producer_image_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyProducerImage);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
