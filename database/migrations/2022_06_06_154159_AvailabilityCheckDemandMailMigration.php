<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (rinoparts) at 2022-06-06 15:41:58
 * Page generator: page_id=377282
 */
class AvailabilityCheckDemandMailMigration extends AbstractMigration
{
    public function up()
    {
        // page: <PERSON><PERSON><PERSON>ť o prezistenie dostupnosti - email(ID: 377282 TAG: availability_check_demand)
        $pageId = $this->getPageIdByTag('availability_check_demand');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('basic_email');
            $page377282 = \PageFactory::create($this->getPageIdByTag('information_emails'), $pageType->getId());
        } else {
            $page377282 = \PageFactory::get($pageId);
        }
        $page377282->setPageName('Žiadosť o prezistenie dostupnosti - email');
        $page377282->setPageTag('availability_check_demand');
        $page377282->setPageStateId('2');
        $page377282->setPageClassId(1);
        $page377282->setValue('email_sender', '<EMAIL>');
        $page377282->setValue('email_recipients', '<EMAIL>');
        $page377282->setValue('email_subject', 'Žiadosť o prezistenie dostupnosti {{TERM}}');
        $page377282->setValue('mail_embed_images', 'F');
        $page377282->setValue('email_body_html', base64_decode('PHA+xb1pYWRvc8WlIG8gcHJlemlzdGVuaWUgZG9zdHVwbm9zdGkgcHJvZHVrdHUgPHN0cm9uZz57e1RFUk19fSZuYnNwOzwvc3Ryb25nPjwvcD4NCjxwPjwvcD4NCjxwPkZpcm1hOiZuYnNwOzxzdHJvbmc+e3tDT01QQU5ZX05BTUV9fTwvc3Ryb25nPjwvcD4NCjxwPkVtYWlsOiZuYnNwOzxzdHJvbmc+e3tFTUFJTH19PC9zdHJvbmc+PC9wPg0KPHA+U3Byw6F2YTogPHN0cm9uZz57e01FU1NBR0V9fTwvc3Ryb25nPjwvcD4='));
        $page377282->setValue('rendered_email_bottom_text', '');
        $page377282->setValue('email_body_html_cz', '');
        $page377282->setValue('email_body_html_en', '');
        $page377282->setValue('email_subject_cz', '');
        $page377282->setValue('email_subject_en', '');
        $page377282->setValue('rendered_email_bottom_text_cz', '');
        $page377282->setValue('rendered_email_bottom_text_en', '');
        $page377282->setValue('attachment_list', []);
        // set template on MAIN PAGE index::error404
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('basic_email'), 'index', 'error404');
        $page377282->save();
    }

    public function down()
    {
        // remove page: Žiadosť o prezistenie dostupnosti - email (availability_check_demand)
        $pageId = $this->getPageIdByTag('availability_check_demand');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }
    }
}
