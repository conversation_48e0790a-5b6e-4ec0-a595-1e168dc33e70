<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Automatic generation from (rinoparts) at 2024-01-17 11:02:57
 */
class Create_index_levels_table extends AbstractMigration
{
    public function up()
    {
        // create table index_levels
        Schema::create('index_levels', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->decimal('index_level_from', 10, 2);
            $table->decimal('additional_price_increase', 10, 2);
            $table->decimal('additional_price_increase_eu', 10, 2);
            $table->string('type_tag');
            $table->boolean('index_level_enabled');
            $table->timestamps();
        });
    }

    public function down()
    {
        // drop table index_levels
        Schema::dropIfExists('index_levels');
    }
}
