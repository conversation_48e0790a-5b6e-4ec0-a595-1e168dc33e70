<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2024-08-21 13:06:26
 * Property generator: property=oe_germany_supplier_code,oe_germany_price_without_vat,oe_germany_stock_balance,oe_germany_oe_number,oe_germany_oe_numbers,oe_germany_latest_import
 */
class OeGermanyPropertiesMigration extends AbstractMigration
{
    public function up()
    {
        // property: OeGermany - Dodávateľský kód(oe_germany_supplier_code)
        $propertyOeGermanySupplierCode = $this->propertyManager()->propertyExistsByTag('oe_germany_supplier_code');
        if ($propertyOeGermanySupplierCode === false) {
            $propertyOeGermanySupplierCode = new Property();
            $propertyOeGermanySupplierCode->setTag('oe_germany_supplier_code');
            $propertyOeGermanySupplierCode->setDescription('');
            $propertyOeGermanySupplierCode->setExtendedDescription('');
            $propertyOeGermanySupplierCode->setName('OeGermany - Dodávateľský kód');
            $propertyOeGermanySupplierCode->setClassId(4);
            $propertyOeGermanySupplierCode->setShowType(null);
            $propertyOeGermanySupplierCode->setShowTypeTag('text');
            $propertyOeGermanySupplierCode->setValueType('oneline_text');
            $propertyOeGermanySupplierCode->setDefaultValue('');
            $propertyOeGermanySupplierCode->setMultiOperations(false);
            $propertyOeGermanySupplierCode->setInputString('');
            $propertyOeGermanySupplierCode->setAttribute('tab', 'OeGermany');
            $propertyOeGermanySupplierCode->setAttribute('size', '60');
            $propertyOeGermanySupplierCode->setAttribute('maxlength', '');
            $propertyOeGermanySupplierCode->setAttribute('readonly', 'F');
            $propertyOeGermanySupplierCode->setAttribute('pattern', '');
            $propertyOeGermanySupplierCode->setAttribute('inherit_value', 'F');
            $propertyOeGermanySupplierCode->setAttribute('onchange-js', '');
            $propertyOeGermanySupplierCode->setAttribute('onkeyup-js', '');
            $propertyOeGermanySupplierCode->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyOeGermanySupplierCode);
        } else {
            $this->writeLine('Property with tag oe_germany_supplier_code already exists');
            $this->setDataKey('property_oe_germany_supplier_code_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('oe_germany_supplier_code', 'eshop_product', false);
        }

        // property: OeGermany - Cena bez DPH(oe_germany_price_without_vat)
        $propertyOeGermanyPriceWithoutVat = $this->propertyManager()->propertyExistsByTag('oe_germany_price_without_vat');
        if ($propertyOeGermanyPriceWithoutVat === false) {
            $propertyOeGermanyPriceWithoutVat = new Property();
            $propertyOeGermanyPriceWithoutVat->setTag('oe_germany_price_without_vat');
            $propertyOeGermanyPriceWithoutVat->setDescription('');
            $propertyOeGermanyPriceWithoutVat->setExtendedDescription('');
            $propertyOeGermanyPriceWithoutVat->setName('OeGermany - Cena bez DPH');
            $propertyOeGermanyPriceWithoutVat->setClassId(4);
            $propertyOeGermanyPriceWithoutVat->setShowType(null);
            $propertyOeGermanyPriceWithoutVat->setShowTypeTag('text');
            $propertyOeGermanyPriceWithoutVat->setValueType('oneline_text');
            $propertyOeGermanyPriceWithoutVat->setDefaultValue('');
            $propertyOeGermanyPriceWithoutVat->setMultiOperations(false);
            $propertyOeGermanyPriceWithoutVat->setInputString('');
            $propertyOeGermanyPriceWithoutVat->setAttribute('tab', 'OeGermany');
            $propertyOeGermanyPriceWithoutVat->setAttribute('size', '60');
            $propertyOeGermanyPriceWithoutVat->setAttribute('maxlength', '');
            $propertyOeGermanyPriceWithoutVat->setAttribute('readonly', 'F');
            $propertyOeGermanyPriceWithoutVat->setAttribute('pattern', '');
            $propertyOeGermanyPriceWithoutVat->setAttribute('inherit_value', 'F');
            $propertyOeGermanyPriceWithoutVat->setAttribute('onchange-js', '');
            $propertyOeGermanyPriceWithoutVat->setAttribute('onkeyup-js', '');
            $propertyOeGermanyPriceWithoutVat->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyOeGermanyPriceWithoutVat);
        } else {
            $this->writeLine('Property with tag oe_germany_price_without_vat already exists');
            $this->setDataKey('property_oe_germany_price_without_vat_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('oe_germany_price_without_vat', 'eshop_product', false);
        }

        // property: OeGermany - Skladová zásoba(oe_germany_stock_balance)
        $propertyOeGermanyStockBalance = $this->propertyManager()->propertyExistsByTag('oe_germany_stock_balance');
        if ($propertyOeGermanyStockBalance === false) {
            $propertyOeGermanyStockBalance = new Property();
            $propertyOeGermanyStockBalance->setTag('oe_germany_stock_balance');
            $propertyOeGermanyStockBalance->setDescription('');
            $propertyOeGermanyStockBalance->setExtendedDescription('');
            $propertyOeGermanyStockBalance->setName('OeGermany - Skladová zásoba');
            $propertyOeGermanyStockBalance->setClassId(4);
            $propertyOeGermanyStockBalance->setShowType(null);
            $propertyOeGermanyStockBalance->setShowTypeTag('text');
            $propertyOeGermanyStockBalance->setValueType('oneline_text');
            $propertyOeGermanyStockBalance->setDefaultValue('');
            $propertyOeGermanyStockBalance->setMultiOperations(false);
            $propertyOeGermanyStockBalance->setInputString('');
            $propertyOeGermanyStockBalance->setAttribute('tab', 'OeGermany');
            $propertyOeGermanyStockBalance->setAttribute('size', '60');
            $propertyOeGermanyStockBalance->setAttribute('maxlength', '');
            $propertyOeGermanyStockBalance->setAttribute('readonly', 'F');
            $propertyOeGermanyStockBalance->setAttribute('pattern', '');
            $propertyOeGermanyStockBalance->setAttribute('inherit_value', 'F');
            $propertyOeGermanyStockBalance->setAttribute('onchange-js', '');
            $propertyOeGermanyStockBalance->setAttribute('onkeyup-js', '');
            $propertyOeGermanyStockBalance->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyOeGermanyStockBalance);
        } else {
            $this->writeLine('Property with tag oe_germany_stock_balance already exists');
            $this->setDataKey('property_oe_germany_stock_balance_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('oe_germany_stock_balance', 'eshop_product', false);
        }

        // property: OeGermany - OE number(oe_germany_oe_number)
        $propertyOeGermanyOeNumber = $this->propertyManager()->propertyExistsByTag('oe_germany_oe_number');
        if ($propertyOeGermanyOeNumber === false) {
            $propertyOeGermanyOeNumber = new Property();
            $propertyOeGermanyOeNumber->setTag('oe_germany_oe_number');
            $propertyOeGermanyOeNumber->setDescription('');
            $propertyOeGermanyOeNumber->setExtendedDescription('');
            $propertyOeGermanyOeNumber->setName('OeGermany - OE number');
            $propertyOeGermanyOeNumber->setClassId(4);
            $propertyOeGermanyOeNumber->setShowType(null);
            $propertyOeGermanyOeNumber->setShowTypeTag('text');
            $propertyOeGermanyOeNumber->setValueType('oneline_text');
            $propertyOeGermanyOeNumber->setDefaultValue('');
            $propertyOeGermanyOeNumber->setMultiOperations(false);
            $propertyOeGermanyOeNumber->setInputString('');
            $propertyOeGermanyOeNumber->setAttribute('tab', 'OeGermany');
            $propertyOeGermanyOeNumber->setAttribute('size', '60');
            $propertyOeGermanyOeNumber->setAttribute('maxlength', '');
            $propertyOeGermanyOeNumber->setAttribute('readonly', 'F');
            $propertyOeGermanyOeNumber->setAttribute('pattern', '');
            $propertyOeGermanyOeNumber->setAttribute('inherit_value', 'F');
            $propertyOeGermanyOeNumber->setAttribute('onchange-js', '');
            $propertyOeGermanyOeNumber->setAttribute('onkeyup-js', '');
            $propertyOeGermanyOeNumber->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyOeGermanyOeNumber);
        } else {
            $this->writeLine('Property with tag oe_germany_oe_number already exists');
            $this->setDataKey('property_oe_germany_oe_number_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('oe_germany_oe_number', 'eshop_product', false);
        }

        // property: OeGermany - OE numbers(oe_germany_oe_numbers)
        $propertyOeGermanyOeNumbers = $this->propertyManager()->propertyExistsByTag('oe_germany_oe_numbers');
        if ($propertyOeGermanyOeNumbers === false) {
            $propertyOeGermanyOeNumbers = new Property();
            $propertyOeGermanyOeNumbers->setTag('oe_germany_oe_numbers');
            $propertyOeGermanyOeNumbers->setDescription('');
            $propertyOeGermanyOeNumbers->setExtendedDescription('');
            $propertyOeGermanyOeNumbers->setName('OeGermany - OE numbers');
            $propertyOeGermanyOeNumbers->setClassId(4);
            $propertyOeGermanyOeNumbers->setShowType(null);
            $propertyOeGermanyOeNumbers->setShowTypeTag('text');
            $propertyOeGermanyOeNumbers->setValueType('oneline_text');
            $propertyOeGermanyOeNumbers->setDefaultValue('');
            $propertyOeGermanyOeNumbers->setMultiOperations(false);
            $propertyOeGermanyOeNumbers->setInputString('');
            $propertyOeGermanyOeNumbers->setAttribute('tab', 'OeGermany');
            $propertyOeGermanyOeNumbers->setAttribute('size', '60');
            $propertyOeGermanyOeNumbers->setAttribute('maxlength', '');
            $propertyOeGermanyOeNumbers->setAttribute('readonly', 'F');
            $propertyOeGermanyOeNumbers->setAttribute('pattern', '');
            $propertyOeGermanyOeNumbers->setAttribute('inherit_value', 'F');
            $propertyOeGermanyOeNumbers->setAttribute('onchange-js', '');
            $propertyOeGermanyOeNumbers->setAttribute('onkeyup-js', '');
            $propertyOeGermanyOeNumbers->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyOeGermanyOeNumbers);
        } else {
            $this->writeLine('Property with tag oe_germany_oe_numbers already exists');
            $this->setDataKey('property_oe_germany_oe_numbers_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('oe_germany_oe_numbers', 'eshop_product', false);
        }

        // property: OeGermany - Posledný import(oe_germany_latest_import)
        $propertyOeGermanyLatestImport = $this->propertyManager()->propertyExistsByTag('oe_germany_latest_import');
        if ($propertyOeGermanyLatestImport === false) {
            $propertyOeGermanyLatestImport = new Property();
            $propertyOeGermanyLatestImport->setTag('oe_germany_latest_import');
            $propertyOeGermanyLatestImport->setDescription('');
            $propertyOeGermanyLatestImport->setExtendedDescription('');
            $propertyOeGermanyLatestImport->setName('OeGermany - Posledný import');
            $propertyOeGermanyLatestImport->setClassId(4);
            $propertyOeGermanyLatestImport->setShowType(null);
            $propertyOeGermanyLatestImport->setShowTypeTag('text');
            $propertyOeGermanyLatestImport->setValueType('oneline_text');
            $propertyOeGermanyLatestImport->setDefaultValue('');
            $propertyOeGermanyLatestImport->setMultiOperations(false);
            $propertyOeGermanyLatestImport->setInputString('');
            $propertyOeGermanyLatestImport->setAttribute('tab', 'OeGermany');
            $propertyOeGermanyLatestImport->setAttribute('size', '60');
            $propertyOeGermanyLatestImport->setAttribute('maxlength', '');
            $propertyOeGermanyLatestImport->setAttribute('readonly', 'F');
            $propertyOeGermanyLatestImport->setAttribute('pattern', '');
            $propertyOeGermanyLatestImport->setAttribute('inherit_value', 'F');
            $propertyOeGermanyLatestImport->setAttribute('onchange-js', '');
            $propertyOeGermanyLatestImport->setAttribute('onkeyup-js', '');
            $propertyOeGermanyLatestImport->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyOeGermanyLatestImport);
        } else {
            $this->writeLine('Property with tag oe_germany_latest_import already exists');
            $this->setDataKey('property_oe_germany_latest_import_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('oe_germany_latest_import', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: OeGermany - Posledný import(oe_germany_latest_import)
        $propertyOeGermanyLatestImport = $this->propertyManager()->propertyExistsByTag('oe_germany_latest_import');
        if (($propertyOeGermanyLatestImport !== false) && ($this->getDataKey('property_oe_germany_latest_import_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyOeGermanyLatestImport);
        }

        // remove property: OeGermany - OE numbers(oe_germany_oe_numbers)
        $propertyOeGermanyOeNumbers = $this->propertyManager()->propertyExistsByTag('oe_germany_oe_numbers');
        if (($propertyOeGermanyOeNumbers !== false) && ($this->getDataKey('property_oe_germany_oe_numbers_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyOeGermanyOeNumbers);
        }

        // remove property: OeGermany - OE number(oe_germany_oe_number)
        $propertyOeGermanyOeNumber = $this->propertyManager()->propertyExistsByTag('oe_germany_oe_number');
        if (($propertyOeGermanyOeNumber !== false) && ($this->getDataKey('property_oe_germany_oe_number_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyOeGermanyOeNumber);
        }

        // remove property: OeGermany - Skladová zásoba(oe_germany_stock_balance)
        $propertyOeGermanyStockBalance = $this->propertyManager()->propertyExistsByTag('oe_germany_stock_balance');
        if (($propertyOeGermanyStockBalance !== false) && ($this->getDataKey('property_oe_germany_stock_balance_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyOeGermanyStockBalance);
        }

        // remove property: OeGermany - Cena bez DPH(oe_germany_price_without_vat)
        $propertyOeGermanyPriceWithoutVat = $this->propertyManager()->propertyExistsByTag('oe_germany_price_without_vat');
        if (($propertyOeGermanyPriceWithoutVat !== false) && ($this->getDataKey('property_oe_germany_price_without_vat_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyOeGermanyPriceWithoutVat);
        }

        // remove property: OeGermany - Dodávateľský kód(oe_germany_supplier_code)
        $propertyOeGermanySupplierCode = $this->propertyManager()->propertyExistsByTag('oe_germany_supplier_code');
        if (($propertyOeGermanySupplierCode !== false) && ($this->getDataKey('property_oe_germany_supplier_code_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyOeGermanySupplierCode);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
