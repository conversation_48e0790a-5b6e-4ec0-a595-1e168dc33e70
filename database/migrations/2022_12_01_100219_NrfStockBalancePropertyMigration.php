<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2022-12-01 10:02:19
 * Property generator: property=nrf_stock_balance
 */
class NrfStockBalancePropertyMigration extends AbstractMigration
{
    public function up()
    {
        // property: NRF - Skladová zásoba(nrf_stock_balance)
        $propertyNrfStockBalance = $this->propertyManager()->propertyExistsByTag('nrf_stock_balance');
        if ($propertyNrfStockBalance === false) {
            $propertyNrfStockBalance = new Property();
            $propertyNrfStockBalance->setTag('nrf_stock_balance');
            $propertyNrfStockBalance->setDescription('');
            $propertyNrfStockBalance->setExtendedDescription('');
            $propertyNrfStockBalance->setName('NRF - Skladová zásoba');
            $propertyNrfStockBalance->setClassId(4);
            $propertyNrfStockBalance->setShowType(null);
            $propertyNrfStockBalance->setShowTypeTag('text');
            $propertyNrfStockBalance->setValueType('oneline_text');
            $propertyNrfStockBalance->setDefaultValue('');
            $propertyNrfStockBalance->setMultiOperations(false);
            $propertyNrfStockBalance->setInputString('');
            $propertyNrfStockBalance->setAttribute('tab', 'NRF');
            $propertyNrfStockBalance->setAttribute('size', '60');
            $propertyNrfStockBalance->setAttribute('maxlength', '');
            $propertyNrfStockBalance->setAttribute('readonly', 'F');
            $propertyNrfStockBalance->setAttribute('pattern', '');
            $propertyNrfStockBalance->setAttribute('inherit_value', 'F');
            $propertyNrfStockBalance->setAttribute('onchange-js', '');
            $propertyNrfStockBalance->setAttribute('onkeyup-js', '');
            $propertyNrfStockBalance->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyNrfStockBalance);
        } else {
            $this->writeLine('Property with tag nrf_stock_balance already exists');
            $this->setDataKey('property_nrf_stock_balance_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('nrf_stock_balance', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: NRF - Skladová zásoba(nrf_stock_balance)
        $propertyNrfStockBalance = $this->propertyManager()->propertyExistsByTag('nrf_stock_balance');
        if (($propertyNrfStockBalance !== false) && ($this->getDataKey('property_nrf_stock_balance_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyNrfStockBalance);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
