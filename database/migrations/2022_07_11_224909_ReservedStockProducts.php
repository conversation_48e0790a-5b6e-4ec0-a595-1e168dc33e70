<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Automatic generation from (buxus_rinoparts_dev) at 2022-07-11 22:49:09
 */
class ReservedStockProducts extends AbstractMigration
{
    public function up()
    {
        Schema::create('onix_reserved_stock_products', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->datetime('inserted');
            $table->bigInteger('buxus_order_id');
            $table->bigInteger('buxus_product_id');
            $table->float('reserved_amount');
        });
    }

    public function down()
    {
        Schema::dropIfExists('onix_reserved_stock_products');
    }
}
