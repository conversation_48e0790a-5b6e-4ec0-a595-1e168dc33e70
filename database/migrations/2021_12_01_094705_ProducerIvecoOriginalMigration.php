<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (rinoparts) at 2021-12-01 09:47:04
 * Page generator: page_id=1309
 */
class ProducerIvecoOriginalMigration extends AbstractMigration
{
    public function up()
    {
        // page: IVECO original(ID: 1309 TAG: producer_iveco_original)
        $pageId = $this->getPageIdByTag('producer_iveco_original');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('roller_item_producer');
            $page1309 = \PageFactory::create($this->getPageIdByTag('ciselnik-vyrobca'), $pageType->getId());
        } else {
            $page1309 = \PageFactory::get($pageId);
        }
        $page1309->setPageName('IVECO original');
        $page1309->setPageTag('producer_iveco_original');
        $page1309->setPageStateId(1);
        $page1309->setPageClassId(1);
        $page1309->setValue('title_en', '');
        $page1309->setValue('title_cz', '');
        $page1309->setValue('roller_value_name', 'IVECO original');
        $page1309->setValue('title', 'IVECO original');
        $page1309->setValue('image', '');
        $page1309->save();
    }

    public function down()
    {
        // remove page: IVECO original (producer_iveco_original)
        $pageId = $this->getPageIdByTag('producer_iveco_original');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }
    }
}
