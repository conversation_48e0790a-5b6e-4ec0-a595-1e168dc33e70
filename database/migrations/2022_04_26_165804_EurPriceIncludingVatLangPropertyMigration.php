<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2022-04-26 16:58:04
 * Property generator: property=eshop_eur_price_including_vat_en
 */
class EurPriceIncludingVatLangPropertyMigration extends AbstractMigration
{
    public function up()
    {
        // property: Cena EUR s DPH [EN](eshop_eur_price_including_vat_en)
        $propertyEshopEurPriceIncludingVatEn = $this->propertyManager()->propertyExistsByTag('eshop_eur_price_including_vat_en');
        if ($propertyEshopEurPriceIncludingVatEn === false) {
            $propertyEshopEurPriceIncludingVatEn = new Property();
            $propertyEshopEurPriceIncludingVatEn->setTag('eshop_eur_price_including_vat_en');
            $propertyEshopEurPriceIncludingVatEn->setDescription('Cena v EUR s DPH.');
            $propertyEshopEurPriceIncludingVatEn->setExtendedDescription('');
            $propertyEshopEurPriceIncludingVatEn->setName('Cena EUR s DPH [EN]');
            $propertyEshopEurPriceIncludingVatEn->setClassId(4);
            $propertyEshopEurPriceIncludingVatEn->setShowType(null);
            $propertyEshopEurPriceIncludingVatEn->setShowTypeTag('text');
            $propertyEshopEurPriceIncludingVatEn->setValueType('oneline_text');
            $propertyEshopEurPriceIncludingVatEn->setDefaultValue('');
            $propertyEshopEurPriceIncludingVatEn->setMultiOperations(false);
            $propertyEshopEurPriceIncludingVatEn->setInputString('');
            $propertyEshopEurPriceIncludingVatEn->setAttribute('tab', 'EN');
            $propertyEshopEurPriceIncludingVatEn->setAttribute('size', '60');
            $propertyEshopEurPriceIncludingVatEn->setAttribute('maxlength', '');
            $propertyEshopEurPriceIncludingVatEn->setAttribute('readonly', 'F');
            $propertyEshopEurPriceIncludingVatEn->setAttribute('pattern', '');
            $propertyEshopEurPriceIncludingVatEn->setAttribute('inherit_value', 'F');
            $propertyEshopEurPriceIncludingVatEn->setAttribute('onchange-js', '');
            $propertyEshopEurPriceIncludingVatEn->setAttribute('onkeyup-js', '');
            $propertyEshopEurPriceIncludingVatEn->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyEshopEurPriceIncludingVatEn);
        } else {
            $this->writeLine('Property with tag eshop_eur_price_including_vat_en already exists');
            $this->setDataKey('property_eshop_eur_price_including_vat_en_existed', true);
        }
        if ($this->pageTypeExists('eshop_transport_type')) {
            $this->addPropertyToPageType('eshop_eur_price_including_vat_en', 'eshop_transport_type', false);
        }
        if ($this->pageTypeExists('eshop_payment_type')) {
            $this->addPropertyToPageType('eshop_eur_price_including_vat_en', 'eshop_payment_type', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Cena EUR s DPH [EN](eshop_eur_price_including_vat_en)
        $propertyEshopEurPriceIncludingVatEn = $this->propertyManager()->propertyExistsByTag('eshop_eur_price_including_vat_en');
        if (($propertyEshopEurPriceIncludingVatEn !== false) && ($this->getDataKey('property_eshop_eur_price_including_vat_en_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyEshopEurPriceIncludingVatEn);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
