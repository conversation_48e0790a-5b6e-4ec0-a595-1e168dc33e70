<?php

use Buxus\Migration\AbstractMigration;
use Buxus\PageType\PageType;
use Buxus\Property\Types\PageList;
use Layout\Migrations\LayoutBasic;

/**
 * Automatic generation from make layout command
 */
class AddBrandPageType extends AbstractMigration
{
    public function dependencies()
    {
        return [LayoutBasic::class];
    }

	public function up()
	{
        // page type: Brands (layout_element_brand_list)
        $page_type_layout_element_brand_list = $this->pageTypesManager()->pageTypeExistsByTag('layout_element_brand_list');
        if ($page_type_layout_element_brand_list === false) {
            $page_type_layout_element_brand_list = new \Buxus\PageType\PageType();
            $page_type_layout_element_brand_list->setTag('layout_element_brand_list');
            $page_type_layout_element_brand_list->setName('Značky');
            $page_type_layout_element_brand_list->setPageClassId('1');
            $page_type_layout_element_brand_list->setDefaultTemplateId('1');
            $page_type_layout_element_brand_list->setDeleteTrigger('');
            $page_type_layout_element_brand_list->setIncludeInSync(NULL);
            $page_type_layout_element_brand_list->setPageDetailsLayout('');
            $page_type_layout_element_brand_list->setPageSortTypeTag('sort_date_time');
            $page_type_layout_element_brand_list->setPageTypeOrder('0');
            $page_type_layout_element_brand_list->setPostmoveTrigger('');
            $page_type_layout_element_brand_list->setPostsubmitTrigger('');
            $page_type_layout_element_brand_list->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('layout_element_container');
            $page_type_layout_element_brand_list->setParent($parent);
        } else {
            $this->writeLine('Page type with tag layout_element_brand_list already exists');
            $this->setDataKey('page_type_layout_element_brand_list_existed', true);
        }
        $this->pageTypesManager()->savePageType($page_type_layout_element_brand_list);

        // property: Zoznam znaciek (brand_page)
        $property_brand_page = $this->propertyManager()->propertyExistsByTag('brand_page');
        if ($property_brand_page === false) {
            $property_brand_page = new PageList();
            $property_brand_page->setTag('brand_page');
            $property_brand_page->setDescription('Stránka z číselníku značky značky.');
            $property_brand_page->setExtendedDescription('');
            $property_brand_page->setName('Zoznam značiek');
            $property_brand_page->setClassId('4');
            $property_brand_page->setShowType(NULL);
            $property_brand_page->setShowTypeTag('page_list');
            $property_brand_page->setValueType('page_list');
            $property_brand_page->setDefaultValue('');
            $property_brand_page->setMultiOperations(false);
            $property_brand_page->setInputString(NULL);
            $property_brand_page->setAttribute('tab', '');
            $property_brand_page->setAttribute('root_page_id', \Buxus\Util\PageIds::getCiselnikBrand());
            $property_brand_page->setAttribute('page_type_id', '');
            $property_brand_page->setAttribute('default_sort', 'tblPages.sort_date_time');
            $property_brand_page->setAttribute('advanced_mode', 'T');
            $property_brand_page->setAttribute('external_url', 'T');
            $property_brand_page->setAttribute('max_items', '');
            $property_brand_page->setAttribute('middle_col_width', '');
            $property_brand_page->setAttribute('apply_user_rights', 'T');
            $property_brand_page->setAttribute('property_for_link_name', 'title');
            $property_brand_page->setAttribute('properties_for_search', 'tblPages.page_name,title');
            $this->propertyManager()->saveProperty($property_brand_page);
        } else {
            $this->writeLine('Property with tag brand_page already exists');
            $this->setDataKey('property_brand_page_existed', true);
        }


        $property = $this->propertyManager()->getPropertyByTag('brand_page');
        $property_id = $property->getId();
        $tmp = $page_type_layout_element_brand_list->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('3');
            $tmp->setRequired(false);
            $page_type_layout_element_brand_list->addPropertyItem($tmp);
        }

		$this->pageTypesManager()->savePageType($page_type_layout_element_brand_list);

        if ($this->pageTypeExists('homepage')) {
            $this->addPageTypeSuperiorPageType('layout_element_brand_list', 'homepage');
        }

        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();
	}

	public function down()
	{
        // remove property: Cieľová stránka(brand_page)
        $property_brand_page = $this->propertyManager()->propertyExistsByTag('brand_page');
        if ($property_brand_page != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_brand_page);
            if ((is_null($this->getDataKey('property_brand_page_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_brand_page);
            }
        }

        // remove page type: Brandy (layout_element_brand_list)
        $page_type_layout_element_brand_list = $this->pageTypesManager()->pageTypeExistsByTag('layout_element_brand_list');
        if (($page_type_layout_element_brand_list != false) && (is_null($this->getDataKey('page_type_layout_element_brand_list_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_layout_element_brand_list);
        }

        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();
	}
}
