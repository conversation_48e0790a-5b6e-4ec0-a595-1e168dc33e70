<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (buxus_rinoparts_test) at 2023-05-01 16:47:22
 * Property generator: property=motorservice_latest_import
 */
class MotorserviceLatestImportPropertyMigration extends AbstractMigration
{
    public function up()
    {
        // property: MS Motorservice - Posledný import(motorservice_latest_import)
        $propertyMotorserviceLatestImport = $this->propertyManager()->propertyExistsByTag('motorservice_latest_import');
        if ($propertyMotorserviceLatestImport === false) {
            $propertyMotorserviceLatestImport = new Property();
            $propertyMotorserviceLatestImport->setTag('motorservice_latest_import');
            $propertyMotorserviceLatestImport->setDescription('');
            $propertyMotorserviceLatestImport->setExtendedDescription('');
            $propertyMotorserviceLatestImport->setName('MS Motorservice - Posledný import');
            $propertyMotorserviceLatestImport->setClassId(4);
            $propertyMotorserviceLatestImport->setShowType(null);
            $propertyMotorserviceLatestImport->setShowTypeTag('text');
            $propertyMotorserviceLatestImport->setValueType('oneline_text');
            $propertyMotorserviceLatestImport->setDefaultValue('');
            $propertyMotorserviceLatestImport->setMultiOperations(false);
            $propertyMotorserviceLatestImport->setInputString('');
            $propertyMotorserviceLatestImport->setAttribute('tab', 'MS Motorservice');
            $propertyMotorserviceLatestImport->setAttribute('size', '60');
            $propertyMotorserviceLatestImport->setAttribute('maxlength', '');
            $propertyMotorserviceLatestImport->setAttribute('readonly', 'F');
            $propertyMotorserviceLatestImport->setAttribute('pattern', '');
            $propertyMotorserviceLatestImport->setAttribute('inherit_value', 'F');
            $propertyMotorserviceLatestImport->setAttribute('onchange-js', '');
            $propertyMotorserviceLatestImport->setAttribute('onkeyup-js', '');
            $propertyMotorserviceLatestImport->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyMotorserviceLatestImport);
        } else {
            $this->writeLine('Property with tag motorservice_latest_import already exists');
            $this->setDataKey('property_motorservice_latest_import_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('motorservice_latest_import', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: MS Motorservice - Posledný import(motorservice_latest_import)
        $propertyMotorserviceLatestImport = $this->propertyManager()->propertyExistsByTag('motorservice_latest_import');
        if (($propertyMotorserviceLatestImport !== false) && ($this->getDataKey('property_motorservice_latest_import_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyMotorserviceLatestImport);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
