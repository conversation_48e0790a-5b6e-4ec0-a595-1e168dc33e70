<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Translate\TranslatorFactory;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2021-10-17 12:03:09
 * Translation generator: sections=sk.cart
 * Property generator: property=iveco_big_db_import_code
 */
class AddIvecoBigDbProperty extends AbstractMigration
{
    public function dependencies()
    {
        return [
            'Buxus\\Translate\\Migrations\\TranslationTableMigration',
        ];
}

    public function up()
    {
        $translator = TranslatorFactory::get();
        $editor = $translator->getEditor();

        if (!(in_array('sk', $translator->getAvailableLanguages()))) {
            $translator->setAvailableLanguages(array_merge($translator->getAvailableLanguages(), ['sk',]));
        }

        // section: cart, language: sk
        $translations = [
            'alebo_si_nechajte' => 'Alebo si nechajte',
            'login_popup_activated_user_text' => 'Ak sa prihlásite, nemusíte vyplňovať Vaše údaje. Ak si nepamätáte heslo, môžete si vyžiadať reset hesla.',
            'login_popup_activated_user_title' => 'Konto pre tento e-mail už existuje',
            'login_popup_automatic_registration_text' => 'Prihláste sa heslom, ktoré ste dostali e-mailom.',
            'login_popup_automatic_registration_title' => 'Z tejto e-mailovej adresy ste už uskutočnili nákup',
            'zaslat_docasne_heslo_na_email' => 'zaslať dočasné heslo na e-mail',
            ];
        foreach ($translations as $key => $value) {
            $editor->setTranslation($key, $value, 'cart', 'sk', true);
        }
        $editor->save();

        // property: IVECO Big DB import kód(iveco_big_db_import_code)
        $propertyIvecoBigDbImportCode = $this->propertyManager()->propertyExistsByTag('iveco_big_db_import_code');
        if ($propertyIvecoBigDbImportCode === false) {
            $propertyIvecoBigDbImportCode = new Property();
            $propertyIvecoBigDbImportCode->setTag('iveco_big_db_import_code');
            $propertyIvecoBigDbImportCode->setDescription('');
            $propertyIvecoBigDbImportCode->setExtendedDescription('');
            $propertyIvecoBigDbImportCode->setName('IVECO Big DB import kód');
            $propertyIvecoBigDbImportCode->setClassId(4);
            $propertyIvecoBigDbImportCode->setShowType(null);
            $propertyIvecoBigDbImportCode->setShowTypeTag('text');
            $propertyIvecoBigDbImportCode->setValueType('oneline_text');
            $propertyIvecoBigDbImportCode->setDefaultValue('');
            $propertyIvecoBigDbImportCode->setMultiOperations(false);
            $propertyIvecoBigDbImportCode->setInputString('');
            $propertyIvecoBigDbImportCode->setAttribute('tab', '');
            $propertyIvecoBigDbImportCode->setAttribute('size', '60');
            $propertyIvecoBigDbImportCode->setAttribute('maxlength', '');
            $propertyIvecoBigDbImportCode->setAttribute('readonly', 'F');
            $propertyIvecoBigDbImportCode->setAttribute('pattern', '');
            $propertyIvecoBigDbImportCode->setAttribute('inherit_value', 'F');
            $propertyIvecoBigDbImportCode->setAttribute('onchange-js', '');
            $propertyIvecoBigDbImportCode->setAttribute('onkeyup-js', '');
            $propertyIvecoBigDbImportCode->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyIvecoBigDbImportCode);
        } else {
            $this->writeLine('Property with tag iveco_big_db_import_code already exists');
            $this->setDataKey('property_iveco_big_db_import_code_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('iveco_big_db_import_code', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: IVECO Big DB import kód(iveco_big_db_import_code)
        $propertyIvecoBigDbImportCode = $this->propertyManager()->propertyExistsByTag('iveco_big_db_import_code');
        if (($propertyIvecoBigDbImportCode !== false) && ($this->getDataKey('property_iveco_big_db_import_code_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyIvecoBigDbImportCode);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
