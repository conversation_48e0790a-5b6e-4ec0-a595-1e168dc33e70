<?php

namespace Mailinglist\Migrations;

use Buxus\Migration\AbstractMigration;

class MakeBodyHtmlColumnMediumtext extends AbstractMigration
{
    public function dependencies()
    {
        return [
            '\Mailinglist\Migrations\BaseMailinglistTables',
            '\Mailinglist\Migrations\MakeMailingTableWithBothFormats',
        ];
    }

    public function up()
    {
        \DB::statement("ALTER TABLE `tblMailingListBatchEmails` MODIFY `body_html` MEDIUMTEXT NULL");
    }

    public function down()
    {
        \DB::statement("ALTER TABLE `tblMailingListBatchEmails` MODIFY `body_html` TEXT NULL");
    }
}
