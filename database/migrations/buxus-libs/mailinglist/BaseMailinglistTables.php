<?php

namespace Mailinglist\Migrations;

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class BaseMailinglistTables extends AbstractMigration
{
    public function up()
    {
        if (!Schema::hasTable('tblMailingListBatchEmails')) {
            DB::statement("
            CREATE TABLE `tblMailingListBatchEmails` (
                  `email_id` int(11) NOT NULL AUTO_INCREMENT,
                  `format` enum('T','F','D') COLLATE utf8_unicode_ci NOT NULL DEFAULT 'T',
                  `from` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `reply_to` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `subject` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `body` mediumtext COLLATE utf8_unicode_ci,
                  `attachment` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `hash` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
                  `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  PRIMARY KEY (`email_id`),
                  UNIQUE KEY `hash` (`hash`),
                  KEY `created` (`created`)
                ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci
          ");
        }

        if (!Schema::hasColumn('tblMailingListBatchEmails', 'return_path')) {
            DB::statement("ALTER TABLE `tblMailingListBatchEmails` ADD COLUMN `return_path` VARCHAR(255) NULL AFTER `reply_to`");
        }

        if (!Schema::hasTable('tblMailingListBatchEmailsBackup')) {
            DB::statement("
                CREATE TABLE `tblMailingListBatchEmailsBackup` (
                  `email_id` int(11) NOT NULL,
                  `format` enum('T','F','D') COLLATE utf8_unicode_ci NOT NULL DEFAULT 'T',
                  `from` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `reply_to` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `subject` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `body` mediumtext COLLATE utf8_unicode_ci,
                  `attachment` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `hash` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
                  `created` datetime DEFAULT NULL,
                  `moved` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  PRIMARY KEY (`email_id`),
                  UNIQUE KEY `hash` (`hash`),
                  KEY `created` (`created`),
                  KEY `moved` (`moved`)
                ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci
                ");
        }

        if (!Schema::hasTable('tblMailingListBatchRecipients')) {
            DB::statement("
                CREATE TABLE `tblMailingListBatchRecipients` (
                  `to` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
                  `message_id` int(11) NOT NULL DEFAULT '0',
                  `date` datetime DEFAULT NULL,
                  `email_id` int(11) NOT NULL DEFAULT '0',
                  `status` enum('READY','SENT','FAILED','ATTACHMENT_ERROR') COLLATE utf8_unicode_ci NOT NULL DEFAULT 'READY',
                  `mail_duration` double DEFAULT NULL,
                  `send_duration` double DEFAULT NULL,
                  `last_attempt` datetime DEFAULT NULL,
                  KEY `date` (`date`),
                  KEY `message_id_date` (`message_id`,`date`)
                ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci
                ");
        }

        if (!Schema::hasTable('tblMailingListMessages')) {
            DB::statement("
                CREATE TABLE `tblMailingListMessages` (
                  `message_id` int(11) NOT NULL AUTO_INCREMENT,
                  `default_list_tag` varchar(64) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `subject` varchar(128) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
                  `body_text` text COLLATE utf8_unicode_ci,
                  `body_html` text COLLATE utf8_unicode_ci,
                  `created_time` datetime DEFAULT NULL,
                  `changed_time` datetime DEFAULT NULL,
                  `sended_time` datetime DEFAULT NULL,
                  `nr_of_recipients` int(11) DEFAULT NULL,
                  `run_condition` text COLLATE utf8_unicode_ci,
                  `send_user_condition` text COLLATE utf8_unicode_ci,
                  `default_flag` varchar(64) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `text_prefix` text COLLATE utf8_unicode_ci,
                  `html_prefix` text COLLATE utf8_unicode_ci,
                  `text_postfix` text COLLATE utf8_unicode_ci,
                  `html_postfix` text COLLATE utf8_unicode_ci,
                  `mailinglist_page_id` int(11) DEFAULT NULL,
                  `attachment` varchar(250) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `state` enum('new','deleted') COLLATE utf8_unicode_ci NOT NULL DEFAULT 'new',
                  PRIMARY KEY (`message_id`)
                ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci
                ");
        }
    }

    public function down()
    {
        if (Schema::hasTable('tblMailingListMessages')) {
            Schema::drop('tblMailingListMessages');
        }
        if (Schema::hasTable('tblMailingListBatchRecipients')) {
            Schema::drop('tblMailingListBatchRecipients');
        }
        if (Schema::hasTable('tblMailingListBatchEmailsBackup')) {
            Schema::drop('tblMailingListBatchEmailsBackup');
        }
        if (Schema::hasTable('tblMailingListBatchEmails')) {
            Schema::drop('tblMailingListBatchEmails');
        }
    }
}
