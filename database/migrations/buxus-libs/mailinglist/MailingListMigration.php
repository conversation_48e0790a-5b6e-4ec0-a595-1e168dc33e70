<?php
namespace Mailinglist\Migrations;

use Buxus\Migration\AbstractMigration;
use Buxus\Util\PageIds;
use Email\Migrations\EmailMigration;
use Mailinglist\MailinglistPages;

class MailingListMigration extends AbstractMigration {
    public function dependencies()
    {
        return array(
            EmailMigration::class
        );
    }

    public function up() {
        // property: Titulok(title)
        $property_title = $this->propertyManager()->propertyExistsByTag('title');
        if ($property_title === false) {
            $property_title = new \Buxus\Property\Types\Input();
            $property_title->setTag('title');
            $property_title->setDescription('Štandardný názov stránky. Text, ktorý sa zobrazí v záložke prehliadača. Podľa SEO odporúčaní max. 65 znakov.');
            $property_title->setExtendedDescription('');
            $property_title->setName('Titulok');
            $property_title->setClassId('4');
            $property_title->setShowType(NULL);
            $property_title->setShowTypeTag('text');
            $property_title->setValueType('oneline_text');
            $property_title->setDefaultValue('');
            $property_title->setMultiOperations(false);
            $property_title->setInputString(NULL);
            $property_title->setAttribute('tab', '');
            $property_title->setAttribute('size', '60');
            $property_title->setAttribute('maxlength', '');
            $property_title->setAttribute('readonly', 'F');
            $property_title->setAttribute('pattern', '');
            $property_title->setAttribute('inherit_value', 'F');
            $property_title->setAttribute('onchange-js', '');
            $property_title->setAttribute('onkeyup-js', '');
            $property_title->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_title);

        } else {
            $this->writeLine('Property with tag title already exists');
            $this->setDataKey('property_title_existed', true);
        }

        // property: Kampaň(mailinglist_campaign_tag)
        $property_mailinglist_campaign_tag = $this->propertyManager()->propertyExistsByTag('mailinglist_campaign_tag');
        if ($property_mailinglist_campaign_tag === false) {
            $property_mailinglist_campaign_tag = new \Buxus\Property\Types\Input();
            $property_mailinglist_campaign_tag->setTag('mailinglist_campaign_tag');
            $property_mailinglist_campaign_tag->setDescription('Tag kampane, ktoré sa bude pridávať do liniek smerujúcich z mailinglist.');
            $property_mailinglist_campaign_tag->setExtendedDescription(NULL);
            $property_mailinglist_campaign_tag->setName('Kampaň');
            $property_mailinglist_campaign_tag->setClassId('4');
            $property_mailinglist_campaign_tag->setShowType(NULL);
            $property_mailinglist_campaign_tag->setShowTypeTag('text');
            $property_mailinglist_campaign_tag->setValueType('oneline_text');
            $property_mailinglist_campaign_tag->setDefaultValue('');
            $property_mailinglist_campaign_tag->setMultiOperations(false);
            $property_mailinglist_campaign_tag->setInputString(NULL);
            $property_mailinglist_campaign_tag->setAttribute('tab', '');
            $property_mailinglist_campaign_tag->setAttribute('size', '25');
            $property_mailinglist_campaign_tag->setAttribute('maxlength', '');
            $property_mailinglist_campaign_tag->setAttribute('readonly', '0');
            $property_mailinglist_campaign_tag->setAttribute('pattern', '');
            $property_mailinglist_campaign_tag->setAttribute('inherit_value', '0');
            $property_mailinglist_campaign_tag->setAttribute('onchange-js', '');
            $property_mailinglist_campaign_tag->setAttribute('onkeyup-js', '');
            $property_mailinglist_campaign_tag->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_mailinglist_campaign_tag);

        } else {
            $this->writeLine('Property with tag mailinglist_campaign_tag already exists');
            $this->setDataKey('property_mailinglist_campaign_tag_existed', true);
        }

        // property: Text(text)
        $property_text = $this->propertyManager()->propertyExistsByTag('text');
        if ($property_text === false) {
            $property_text = new \Buxus\Property\Types\Textarea();
            $property_text->setTag('text');
            $property_text->setDescription('Štandardný text stránky.');
            $property_text->setExtendedDescription('');
            $property_text->setName('Text');
            $property_text->setClassId('4');
            $property_text->setShowType(NULL);
            $property_text->setShowTypeTag('textarea');
            $property_text->setValueType('multiline_text');
            $property_text->setDefaultValue('');
            $property_text->setMultiOperations(false);
            $property_text->setInputString(NULL);
            $property_text->setAttribute('tab', '');
            $property_text->setAttribute('cols', '60');
            $property_text->setAttribute('rows', '');
            $property_text->setAttribute('dhtml-edit', '1');
            $property_text->setAttribute('dhtml-configuration', 'full');
            $property_text->setAttribute('import-word', '0');
            $property_text->setAttribute('auto', '1');
            $property_text->setAttribute('inherit_value', 'F');
            $property_text->setAttribute('onchange-js', '');
            $property_text->setAttribute('onkeyup-js', '');
            $property_text->setAttribute('onkeydown-js', '');
            $property_text->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($property_text);

        } else {
            $this->writeLine('Property with tag text already exists');
            $this->setDataKey('property_text_existed', true);
        }

        // page type: Mailinglist (mailinglist)
        $page_type_mailinglist = $this->pageTypesManager()->pageTypeExistsByTag('mailinglist');
        if ($page_type_mailinglist === false) {
            $page_type_mailinglist = new \Buxus\PageType\PageType();
            $page_type_mailinglist->setTag('mailinglist');
            $page_type_mailinglist->setName('Mailinglist');
            $page_type_mailinglist->setPageClassId('1');
            $page_type_mailinglist->setDefaultTemplateId('2');
            $page_type_mailinglist->setDeleteTrigger(NULL);
            $page_type_mailinglist->setIncludeInSync(NULL);
            $page_type_mailinglist->setPageDetailsLayout(NULL);
            $page_type_mailinglist->setPageSortTypeTag('sort_date_time');
            $page_type_mailinglist->setPageTypeOrder(NULL);
            $page_type_mailinglist->setPostmoveTrigger(NULL);
            $page_type_mailinglist->setPostsubmitTrigger(NULL);
            $page_type_mailinglist->setPresubmitTrigger(NULL);
            $page_type_mailinglist->setParent(NULL);

        } else {
            $this->writeLine('Page type with tag mailinglist already exists');
            $this->setDataKey('page_type_mailinglist_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('title');
        $property_id = $property->getId();
        $tmp = $page_type_mailinglist->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('10');
            $tmp->setRequired(true);
            $page_type_mailinglist->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('mailinglist_campaign_tag');
        $property_id = $property->getId();
        $tmp = $page_type_mailinglist->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('20');
            $tmp->setRequired(true);
            $page_type_mailinglist->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('text');
        $property_id = $property->getId();
        $tmp = $page_type_mailinglist->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('30');
            $tmp->setRequired(true);
            $page_type_mailinglist->addPropertyItem($tmp);
        }
        if ($this->pageTypeExists('service_page')) {
            $page_type_mailinglist->addSuperiorPageType($this->getPageTypeByTag('service_page'));
        }
        $this->pageTypesManager()->savePageType($page_type_mailinglist);
        // set template on MAIN PAGE mailinglist::index
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('mailinglist'), 'mailinglist', 'index');

        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

        // page: Mailinglist(ID: 187 TAG: mailinglist_service_pages)
        $page_id = $this->getPageIdByTag(MailinglistPages::SERVICE_PAGES);
        if ($page_id === NULL) {
            $page_type = $this->pageTypesManager()->getPageTypeByTag('folder');
            $page_187 = \PageFactory::create($this->getPageIdByTag('admin_settings'), $page_type->getId());

        } else {
            $page_187 = \PageFactory::get($page_id);
        }
        $page_187->setPageName('Mailinglist');
        $page_187->setPageTag(MailinglistPages::SERVICE_PAGES);
        $page_187->setPageStateId('2');
        $page_187->setPageClassId('1');
        $page_187->save();

        // page: Odhlásenie z mailinglistu(ID: 478 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('folder');
        $page_478 = \PageFactory::create($page_187->getPageId(), $page_type->getId());
        $page_478->setPageName('Odhlásenie z mailinglistu');
        $page_478->setPageTag(false);
        $page_478->setPageStateId('2');
        $page_478->setPageClassId('1');
        $page_478->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_478->save();

        // page: Odhlásenie z mailinglistu - stránka na webe(ID: 190 TAG: Odhlásenie z mailinglistu)
        $page_type = $this->pageTypesManager()->getPageTypeByTag('service_page');
        $page_190 = \PageFactory::create($page_478->getPageId(), $page_type->getId());
        $page_190->setPageName('Odhlásenie z mailinglistu - stránka na webe');
        $page_190->setPageTag(MailinglistPages::SIGN_OUT);
        $page_190->setPageStateId('2');
        $page_190->setPageClassId('1');
        $page_190->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_190->setPropertyValue('title', 'Odhlásenie z mailinglistu');
        $page_190->setPropertyValue('text', '<p>Pre odhlásenie z prijímania mailinglistu "{MAILINGLIST_NAME}" uveďte vašu emailovú adresu, na ktorú je Vám zasielaný mailinglist. Po odoslaní formulára Vám príde na zadanú emailovú adresu email pre potvrdenie odhlásenia.</p>');
        // set template mailinglist::sign-out
        $page_190->getPageTemplate()->setController('mailinglist');
        $page_190->getPageTemplate()->setAction('sign-out');
        $page_190->save();

        // page: Neznáma chyba pri odhlásení z mailinglistu(ID: 191 TAG: Neznáma chyba pri odhlásení z mailinglistu)
        $page_type = $this->pageTypesManager()->getPageTypeByTag('service_page');
        $page_191 = \PageFactory::create($page_478->getPageId(), $page_type->getId());
        $page_191->setPageName('Neznáma chyba pri odhlásení z mailinglistu');
        $page_191->setPageTag(MailinglistPages::SIGN_OUT_ERROR);
        $page_191->setPageStateId('2');
        $page_191->setPageClassId('1');
        $page_191->setSortDateTime(date('Y-m-d H:i:s', time() + 2));
        $page_191->setPropertyValue('title', 'Neznáma chyba');
        $page_191->setPropertyValue('text', '<p>Pri odhlásení z mailinglistu nastala neznáma chyba. Na túto chybu bol práve upozornený správca webu.</p>');
        // set template mailinglist::show-message
        $page_191->getPageTemplate()->setController('mailinglist');
        $page_191->getPageTemplate()->setAction('show-message');
        $page_191->save();

        // page: Prijatie požiadavky na odhlásenie z mailinglistu(ID: 192 TAG: Prijatie požiadavky na odhlásenie z mailinglistu)
        $page_type = $this->pageTypesManager()->getPageTypeByTag('service_page');
        $page_192 = \PageFactory::create($page_478->getPageId(), $page_type->getId());
        $page_192->setPageName('Prijatie požiadavky na odhlásenie z mailinglistu');
        $page_192->setPageTag(MailinglistPages::SIGN_OUT_RECEIVED);
        $page_192->setPageStateId('2');
        $page_192->setPageClassId('1');
        $page_192->setSortDateTime(date('Y-m-d H:i:s', time() + 3));
        $page_192->setPropertyValue('title', 'Prijatie požiadavky na odhlásenie');
        $page_192->setPropertyValue('text', '<p>Vaša požiadavka na odhlásenie z mailinglistu bola prijatá. Na Vami definovanú emailovú adresu Vám bol zaslaný email s potvrdzujúcim reťazcom.</p>');
        // set template mailinglist::show-message
        $page_192->getPageTemplate()->setController('mailinglist');
        $page_192->getPageTemplate()->setAction('show-message');
        $page_192->save();

        // page: Potvrdenie odhlásenia z mailinglistu(ID: 193 TAG: Potvrdenie odhlásenia z mailinglistu)
        $page_type = $this->pageTypesManager()->getPageTypeByTag('service_page');
        $page_193 = \PageFactory::create($page_478->getPageId(), $page_type->getId());
        $page_193->setPageName('Potvrdenie odhlásenia z mailinglistu');
        $page_193->setPageTag(MailinglistPages::SIGN_OUT_CONFIRM);
        $page_193->setPageStateId('2');
        $page_193->setPageClassId('1');
        $page_193->setSortDateTime(date('Y-m-d H:i:s', time() + 4));
        $page_193->setPropertyValue('title', 'Potvrdenie odhlásenia');
        $page_193->setPropertyValue('text', '<p>Boli ste úspešne odhlásený z mailinglistu.</p>');
        // set template mailinglist::sign-out-confirm
        $page_193->getPageTemplate()->setController('mailinglist');
        $page_193->getPageTemplate()->setAction('sign-out-confirm');
        $page_193->save();

        // page: Neúspešné potvrdenie odhlásenia z mailinglistu(ID: 194 TAG: Neúspešné potvrdenie odhlásenia z mailinglistu)
        $page_type = $this->pageTypesManager()->getPageTypeByTag('service_page');
        $page_194 = \PageFactory::create($page_478->getPageId(), $page_type->getId());
        $page_194->setPageName('Neúspešné potvrdenie odhlásenia z mailinglistu');
        $page_194->setPageTag(MailinglistPages::SIGN_OUT_UNSUCCESFULL);
        $page_194->setPageStateId('2');
        $page_194->setPageClassId('1');
        $page_194->setSortDateTime(date('Y-m-d H:i:s', time() + 5));
        $page_194->setPropertyValue('title', 'Neúspešné potvrdenie odhlásenia');
        $page_194->setPropertyValue('text', '<p>Vašu emailovú adresu sa nepodarilo úspešne odhlásiť z odoberania mailinglistu. Pokúste sa skopírovať linku z potvrdzujúceho emailu a tú vložiť do prehliadača. Ak ani potom potvrdenie nebude úspešné, kontaktuje správcu webu.</p>');
        // set template mailinglist::show-message
        $page_194->getPageTemplate()->setController('mailinglist');
        $page_194->getPageTemplate()->setAction('show-message');
        $page_194->save();

        // page: Prihlásenie do mailinglistu(ID: 479 TAG: )
        $page_type = $this->pageTypesManager()->getPageTypeByTag('folder');
        $page_479 = \PageFactory::create($page_187->getPageId(), $page_type->getId());
        $page_479->setPageName('Prihlásenie do mailinglistu');
        $page_479->setPageTag(false);
        $page_479->setPageStateId('2');
        $page_479->setPageClassId('1');
        $page_479->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_479->save();

        // page: Prihlásenie do mailinglistu - stránka na webe(ID: 195 TAG: Prihlásenie do mailinglistu)
        $page_type = $this->pageTypesManager()->getPageTypeByTag('service_page');
        $page_195 = \PageFactory::create($page_479->getPageId(), $page_type->getId());
        $page_195->setPageName('Prihlásenie do mailinglistu - stránka na webe');
        $page_195->setPageTag(MailinglistPages::SIGN_IN);
        $page_195->setPageStateId('2');
        $page_195->setPageClassId('1');
        $page_195->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_195->setPropertyValue('title', 'Prihlásenie do mailinglistu');
        $page_195->setPropertyValue('text', '<p>Pre prihlásenie do mailinglistu "{MAILINGLIST_NAME}" uveďte vašu emailovú adresu, na ktorú Vám bude zasielaný mailinglist. Po odoslaní formulára Vám príde na zadanú emailovú adresu email pre potvrdenie prihlásenia.</p>');
        // set template mailinglist::sign-in
        $page_195->getPageTemplate()->setController('mailinglist');
        $page_195->getPageTemplate()->setAction('sign-in');
        $page_195->save();

        // page: Neznáma chyba pri prihlásení do mailinglistu(ID: 196 TAG: Neznáma chyba pri prihlásení do mailinglistu)
        $page_type = $this->pageTypesManager()->getPageTypeByTag('service_page');
        $page_196 = \PageFactory::create($page_479->getPageId(), $page_type->getId());
        $page_196->setPageName('Neznáma chyba pri prihlásení do mailinglistu');
        $page_196->setPageTag(MailinglistPages::SIGN_IN_ERROR);
        $page_196->setPageStateId('2');
        $page_196->setPageClassId('1');
        $page_196->setSortDateTime(date('Y-m-d H:i:s', time() + 2));
        $page_196->setPropertyValue('title', 'Neznáma chyba');
        $page_196->setPropertyValue('text', '<p>Pri prihlásení do mailinglistu nastala neznáma chyba. Na túto chybu bol práve upozornený správca webu.</p>');
        // set template mailinglist::show-message
        $page_196->getPageTemplate()->setController('mailinglist');
        $page_196->getPageTemplate()->setAction('show-message');
        $page_196->save();

        // page: Prijatie požiadavky na prihlásenie do mailinglistu(ID: 197 TAG: Prijatie požiadavky na prihlásenie do mailinglistu)
        $page_type = $this->pageTypesManager()->getPageTypeByTag('service_page');
        $page_197 = \PageFactory::create($page_479->getPageId(), $page_type->getId());
        $page_197->setPageName('Prijatie požiadavky na prihlásenie do mailinglistu');
        $page_197->setPageTag(MailinglistPages::SIGN_IN_RECEIVED);
        $page_197->setPageStateId('2');
        $page_197->setPageClassId('1');
        $page_197->setSortDateTime(date('Y-m-d H:i:s', time() + 3));
        $page_197->setPropertyValue('title', 'Prijatie požiadavky na prihlásenie');
        $page_197->setPropertyValue('text', '<p>Vaša požiadavka na prihlásenie do mailinglistu "{MAILINGLIST_NAME}" bola prijatá. Na Vami definovanú emailovú adresu Vám bol zaslaný email s potvrdzujúcim reťazcom.</p>');
        // set template mailinglist::show-message
        $page_197->getPageTemplate()->setController('mailinglist');
        $page_197->getPageTemplate()->setAction('show-message');
        $page_197->save();

        // page: Potvrdenie prihlásenia do mailinglistu(ID: 198 TAG: Potvrdenie prihlásenia do mailinglistu)
        $page_type = $this->pageTypesManager()->getPageTypeByTag('service_page');
        $page_198 = \PageFactory::create($page_479->getPageId(), $page_type->getId());
        $page_198->setPageName('Potvrdenie prihlásenia do mailinglistu');
        $page_198->setPageTag(MailinglistPages::SIGN_IN_CONFIRM);
        $page_198->setPageStateId('2');
        $page_198->setPageClassId('1');
        $page_198->setSortDateTime(date('Y-m-d H:i:s', time() + 4));
        $page_198->setPropertyValue('title', 'Potvrdenie prihlásenia');
        $page_198->setPropertyValue('text', '<p>Boli ste úspešne prihlásený do mailinglistu "{MAILINGLIST_NAME}".</p>');
        // set template mailinglist::sign-in-confirm
        $page_198->getPageTemplate()->setController('mailinglist');
        $page_198->getPageTemplate()->setAction('sign-in-confirm');
        $page_198->save();

        // page: Neúspešné potvrdenie prihlásenia do mailinglistu(ID: 199 TAG: Neúspešné potvrdenie prihlásenia do mailinglistu)
        $page_type = $this->pageTypesManager()->getPageTypeByTag('service_page');
        $page_199 = \PageFactory::create($page_479->getPageId(), $page_type->getId());
        $page_199->setPageName('Neúspešné potvrdenie prihlásenia do mailinglistu');
        $page_199->setPageTag(MailinglistPages::SIGN_IN_UNSUCCESFULL);
        $page_199->setPageStateId('2');
        $page_199->setPageClassId('1');
        $page_199->setSortDateTime(date('Y-m-d H:i:s', time() + 5));
        $page_199->setPropertyValue('title', 'Neúspešné potvrdenie prihlásenia');
        $page_199->setPropertyValue('text', '<p>Vašu emailovú adresu sa nepodarilo úspešne prihlásiť do mailinglistu. Pokúste sa skopírovať linku z potvrdzujúceho emailu a tú vložiť do prehliadača. Ak ani potom potvrdenie nebude úspešné, kontaktuje správcu webu.</p>');
        // set template mailinglist::show-message
        $page_199->getPageTemplate()->setController('mailinglist');
        $page_199->getPageTemplate()->setAction('show-message');
        $page_199->save();

        // page: Podmienky malinglistu(ID: 504 TAG: Podmienky malinglistu)
        $page_type = $this->pageTypesManager()->getPageTypeByTag('service_page');
        $page_504 = \PageFactory::create($page_479->getPageId(), $page_type->getId());
        $page_504->setPageName('Podmienky malinglistu');
        $page_504->setPageTag('Podmienky malinglistu');
        $page_504->setPageStateId('1');
        $page_504->setPageClassId('1');
        $page_504->setSortDateTime(date('Y-m-d H:i:s', time() + 6));
        $page_504->setPropertyValue('title', 'Novinky emailom');
        $page_504->setPropertyValue('text', base64_decode('PHA+UmF6IHTDvcW+ZGVubmUgVsOhbSB6YcWhbGVtZSBzw7pocm4gYWtjacOtIGEgbm92aW5pZWsuPC9wPg0KPHA+WiBtYWlsaW5nbGlzdHUgc2EgbcO0xb50ZSBrZWR5a2/EvnZlayBvZGhsw6FzacWlLjwvcD4='));
        $page_504->setPropertyValue('seo_url_name', '/novinky-emailom');
        // set template on MAIN PAGE index::index
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('service_page'), 'index', 'index');
        $page_504->save();

        // regenerate page tags
        PageIds::generatePageTagsList();

        // add simple entities

        \BuxusDB::get()->insert('tblSimpleEntities', array(
            'entity_tag' => 'newsletter',
            'entity_name' => 'Newsletter',
            'entity_type_tag' => 'mailing_list',
        ));
        \BuxusDB::get()->insert('tblSimpleEntities', array(
            'entity_tag' => 'ml_newsletter',
            'entity_name' => 'Buxus Newsletter <<EMAIL>>',
            'entity_type_tag' => 'ml_from',
        ));
    }

    public function down() {
        \BuxusDB::get()->delete('tblSimpleEntities', array(
            'entity_tag = ?' => 'ml_newsletter',
            'entity_type_tag = ?' => 'ml_from',
        ));
        \BuxusDB::get()->delete('tblSimpleEntities', array(
            'entity_tag = ?' => 'newsletter',
            'entity_type_tag = ?' => 'mailing_list',
        ));

        // remove page: Mailinglist (mailinglist_service_pages)
        $page_id = $this->getPageIdByTag(MailinglistPages::SERVICE_PAGES);
        if ($page_id !== NULL) {
            \PageFactory::get($page_id)->delete();
        }

        // regenerate page tags
        PageIds::generatePageTagsList();

        // remove page type: Mailinglist (mailinglist)
        $page_type_mailinglist = $this->pageTypesManager()->pageTypeExistsByTag('mailinglist');
        if (($page_type_mailinglist != false) && (is_null($this->getDataKey('page_type_mailinglist_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_mailinglist);
        }

        // remove property: Text(text)
        $property_text = $this->propertyManager()->propertyExistsByTag('text');
        if ($property_text != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_text);
            if ((is_null($this->getDataKey('property_text_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_text);
            }
        }

        // remove property: Kampaň(mailinglist_campaign_tag)
        $property_mailinglist_campaign_tag = $this->propertyManager()->propertyExistsByTag('mailinglist_campaign_tag');
        if ($property_mailinglist_campaign_tag != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_mailinglist_campaign_tag);
            if ((is_null($this->getDataKey('property_mailinglist_campaign_tag_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_mailinglist_campaign_tag);
            }
        }

        // remove property: Titulok(title)
        $property_title = $this->propertyManager()->propertyExistsByTag('title');
        if ($property_title != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_title);
            if ((is_null($this->getDataKey('property_title_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_title);
            }
        }

        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

    }

}
