<?php

use Buxus\Migration\AbstractMigration;
use Mailinglist\MailinglistPages;
use Mailinglist\Migrations\FixNewMailinglistPageTags;

class AddMailinglistEmailPages extends AbstractMigration
{

    public function dependencies()
    {
        return [
            FixNewMailinglistPageTags::class,
        ];
    }

    public function up()
    {
        // page: Mailinglist - odhlásenie Email(ID: 231 TAG: mailinglist_sign_out_email)
        $page_id = $this->getPageIdByTag(MailinglistPages::SIGN_OUT_EMAIL);
        if ($page_id === null) {
            $page_type = $this->pageTypesManager()->getPageTypeByTag('basic_email');
            $page_231 = \PageFactory::create($this->getPageIdByTag(MailinglistPages::SERVICE_PAGES), $page_type->getId());
            $page_231->setPageName('Mailinglist - odhlásenie Email');
            $page_231->setPageTag(MailinglistPages::SIGN_OUT_EMAIL);
            $page_231->setPageStateId(2);
            $page_231->setPageClassId(1);
            $page_231->setValue('email_sender', 'Buxus Mailinglist <<EMAIL>>');
            $page_231->setValue('email_recipients', '');
            $page_231->setValue('email_subject', 'Odhlásenie z mailinglistu "{{MAILINGLIST_NAME}}"');
            $page_231->setValue('eshop_email_bottom_text', '');
            $page_231->setValue('mail_embed_images', 'F');
            $page_231->setValue('attachment_list', array());
            // set template on MAIN PAGE index::error404
            $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('basic_email'), 'index', 'error404');
        } else {
            $page_231 = \PageFactory::get($page_id);
        }
        $page_231->setValue(
            'email_body_html',
            file_get_contents(__DIR__ . '/email_templates/newsletter_logout_html.mustache')
        );
        $page_231->setPageType($this->pageTypesManager()->getPageTypeByTag('basic_email'));
        $page_231->save();

        // page: Mailinglist - prihlásenie Email(ID: 238 TAG: mailinglist_sign_in_email)
        $page_id = $this->getPageIdByTag(MailinglistPages::SIGN_IN_EMAIL);
        if ($page_id === null) {
            $page_type = $this->pageTypesManager()->getPageTypeByTag('basic_email');
            $page_238 = \PageFactory::create($this->getPageIdByTag(MailinglistPages::SERVICE_PAGES), $page_type->getId());
            $page_238->setPageName('Mailinglist - prihlásenie Email');
            $page_238->setPageTag(MailinglistPages::SIGN_IN_EMAIL);
            $page_238->setPageStateId(2);
            $page_238->setPageClassId(1);
            $page_238->setValue('email_sender', 'Buxus Mailinglist <<EMAIL>>');
            $page_238->setValue('email_recipients', '');
            $page_238->setValue('email_subject', 'Prihlásenie do mailinglistu "{{MAILINGLIST_NAME}}"');
            $page_238->setValue('eshop_email_bottom_text', '');
            $page_238->setValue('mail_embed_images', 'F');
            $page_238->setValue('attachment_list', array());
            // set template on MAIN PAGE index::error404
            $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('basic_email'), 'index', 'error404');
        } else {
            $page_238 = \PageFactory::get($page_id);
        }
        $page_238->setValue(
            'email_body_html',
            file_get_contents(__DIR__ . '/email_templates/newsletter_login_html.mustache')
        );
        $page_238->setPageType($this->pageTypesManager()->getPageTypeByTag('basic_email'));
        $page_238->save();

        // page: Mailinglist - prihlásenie Email bez potvrdzovacieho odkazu(ID: 256 TAG: mailinglist_sign_in_email_without_confirmation)
        $page_id = $this->getPageIdByTag(MailinglistPages::SIGN_IN_EMAIL_WITHOUT_CONFIRMATION);
        if ($page_id === null) {
            $page_type = $this->pageTypesManager()->getPageTypeByTag('basic_email');
            $page_256 = \PageFactory::create($this->getPageIdByTag(MailinglistPages::SERVICE_PAGES), $page_type->getId());
            $page_256->setPageName('Mailinglist - prihlásenie Email bez potvrdzovacieho odkazu');
            $page_256->setPageTag(MailinglistPages::SIGN_IN_EMAIL_WITHOUT_CONFIRMATION);
            $page_256->setPageStateId(2);
            $page_256->setPageClassId(1);
            $page_256->setValue('email_sender', 'Buxus Mailinglist <<EMAIL>>');
            $page_256->setValue('email_recipients', '');
            $page_256->setValue('email_subject', 'Prihlásenie do mailinglistu "{{MAILINGLIST_NAME}}"');
            $page_256->setValue('eshop_email_bottom_text', '');
            $page_256->setValue('mail_embed_images', 'F');
            $page_256->setValue('attachment_list', array());
            // set template on MAIN PAGE index::error404
            $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('basic_email'), 'index', 'error404');
        } else {
            $page_256 = \PageFactory::get($page_id);
        }
        $page_256->setValue(
            'email_body_html',
            file_get_contents(__DIR__ . '/email_templates/newsletter_login_without_confirmation_html.mustache')
        );
        $page_256->setPageType($this->pageTypesManager()->getPageTypeByTag('basic_email'));
        $page_256->save();
    }

    public function down()
    {
        // remove page: Mailinglist - prihlásenie Email bez potvrdzovacieho odkazu (mailinglist_sign_in_email_without_confirmation)
        $page_id = $this->getPageIdByTag(MailinglistPages::SIGN_IN_EMAIL_WITHOUT_CONFIRMATION);
        if ($page_id !== null) {
            \PageFactory::get($page_id)->delete();
        }

        // remove page: Mailinglist - prihlásenie Email (mailinglist_sign_in_email)
        $page_id = $this->getPageIdByTag(MailinglistPages::SIGN_IN_EMAIL);
        if ($page_id !== null) {
            \PageFactory::get($page_id)->delete();
        }

        // remove page: Mailinglist - odhlásenie Email (mailinglist_sign_out_email)
        $page_id = $this->getPageIdByTag(MailinglistPages::SIGN_OUT_EMAIL);
        if ($page_id !== null) {
            \PageFactory::get($page_id)->delete();
        }
    }
}
