<?php
namespace Mailinglist\Migrations;

use Buxus\Migration\AbstractMigration;
use Layout\Migrations\LayoutBasic;

class NewsletterSignUpLayoutMigration extends AbstractMigration {
    public function dependencies()
    {
        return array(
            LayoutBasic::class,
        );
    }

    public function up() {
        // property: CSS trieda(css_class)
        $property_css_class = $this->propertyManager()->propertyExistsByTag('css_class');
        if ($property_css_class === false) {
            $property_css_class = new \Buxus\Property\Types\Input();
            $property_css_class->setTag('css_class');
            $property_css_class->setDescription('CSS trieda alebo zoznam tried oddelený medzerou');
            $property_css_class->setExtendedDescription('');
            $property_css_class->setName('CSS trieda');
            $property_css_class->setClassId('4');
            $property_css_class->setShowType(NULL);
            $property_css_class->setShowTypeTag('text');
            $property_css_class->setValueType('oneline_text');
            $property_css_class->setDefaultValue('');
            $property_css_class->setMultiOperations(false);
            $property_css_class->setInputString('');
            $property_css_class->setAttribute('tab', '');
            $property_css_class->setAttribute('size', '25');
            $property_css_class->setAttribute('maxlength', '');
            $property_css_class->setAttribute('readonly', 'F');
            $property_css_class->setAttribute('pattern', '');
            $property_css_class->setAttribute('inherit_value', 'F');
            $property_css_class->setAttribute('onchange-js', '');
            $property_css_class->setAttribute('onkeyup-js', '');
            $property_css_class->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_css_class);
        } else {
            $this->writeLine('Property with tag css_class already exists');
            $this->setDataKey('property_css_class_existed', true);
        }

        // page type: Abstract layout element (abstract_layout_element)
        $page_type_abstract_layout_element = $this->pageTypesManager()->pageTypeExistsByTag('abstract_layout_element');
        if ($page_type_abstract_layout_element === false) {
            $page_type_abstract_layout_element = new \Buxus\PageType\PageType();
            $page_type_abstract_layout_element->setTag('abstract_layout_element');
            $page_type_abstract_layout_element->setName('Abstract layout element');
            $page_type_abstract_layout_element->setPageClassId('1');
            $page_type_abstract_layout_element->setDefaultTemplateId('2');
            $page_type_abstract_layout_element->setDeleteTrigger('');
            $page_type_abstract_layout_element->setIncludeInSync(NULL);
            $page_type_abstract_layout_element->setPageDetailsLayout('');
            $page_type_abstract_layout_element->setPageSortTypeTag('sort_date_time');
            $page_type_abstract_layout_element->setPageTypeOrder('0');
            $page_type_abstract_layout_element->setPostmoveTrigger('');
            $page_type_abstract_layout_element->setPostsubmitTrigger('');
            $page_type_abstract_layout_element->setPresubmitTrigger('');
            $page_type_abstract_layout_element->setParent(NULL);
        } else {
            $this->writeLine('Page type with tag abstract_layout_element already exists');
            $this->setDataKey('page_type_abstract_layout_element_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('css_class');
        $property_id = $property->getId();
        $tmp = $page_type_abstract_layout_element->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('1');
            $tmp->setRequired(false);
            $page_type_abstract_layout_element->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($page_type_abstract_layout_element);

        // page type: Layout - newsletter sign up (layout_element_newsletter)
        $page_type_layout_element_newsletter = $this->pageTypesManager()->pageTypeExistsByTag('layout_element_newsletter');
        if ($page_type_layout_element_newsletter === false) {
            $page_type_layout_element_newsletter = new \Buxus\PageType\PageType();
            $page_type_layout_element_newsletter->setTag('layout_element_newsletter');
            $page_type_layout_element_newsletter->setName('Prihlásenie do mailinglistu');
            $page_type_layout_element_newsletter->setPageClassId('1');
            $page_type_layout_element_newsletter->setDefaultTemplateId('2');
            $page_type_layout_element_newsletter->setDeleteTrigger('');
            $page_type_layout_element_newsletter->setIncludeInSync(NULL);
            $page_type_layout_element_newsletter->setPageDetailsLayout('');
            $page_type_layout_element_newsletter->setPageSortTypeTag('sort_date_time');
            $page_type_layout_element_newsletter->setPageTypeOrder('0');
            $page_type_layout_element_newsletter->setPostmoveTrigger('');
            $page_type_layout_element_newsletter->setPostsubmitTrigger('');
            $page_type_layout_element_newsletter->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('abstract_layout_element');
            $page_type_layout_element_newsletter->setParent($parent);
        } else {
            $this->writeLine('Page type with tag layout_element_newsletter already exists');
            $this->setDataKey('page_type_layout_element_newsletter_existed', true);
        }
        $this->pageTypesManager()->savePageType($page_type_layout_element_newsletter);

        if ($this->pageTypeExists('layout_element_container')) {
            $this->addPageTypeSuperiorPageType('layout_element_newsletter', 'layout_element_container');
        }

        if ($this->pageTypeExists('layout_element_row')) {
            $this->addPageTypeSuperiorPageType('layout_element_newsletter', 'layout_element_row');
        }

        if ($this->pageTypeExists('layout_element_column')) {
            $this->addPageTypeSuperiorPageType('layout_element_newsletter', 'layout_element_column');
        }

        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

    }

    public function down() {
        // remove page type: Layout - newsletter sign up (layout_element_newsletter)
        $page_type_layout_element_newsletter = $this->pageTypesManager()->pageTypeExistsByTag('layout_element_newsletter');
        if (($page_type_layout_element_newsletter != false) && (is_null($this->getDataKey('page_type_layout_element_newsletter_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_layout_element_newsletter);
        }

        // remove page type: Abstract layout element (abstract_layout_element)
        $page_type_abstract_layout_element = $this->pageTypesManager()->pageTypeExistsByTag('abstract_layout_element');
        if (($page_type_abstract_layout_element != false) && (is_null($this->getDataKey('page_type_abstract_layout_element_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_abstract_layout_element);
        }

        // remove property: CSS trieda(css_class)
        $property_css_class = $this->propertyManager()->propertyExistsByTag('css_class');
        if ($property_css_class != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_css_class);
            if ((is_null($this->getDataKey('property_css_class_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_css_class);
            }
        }

        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

    }

}