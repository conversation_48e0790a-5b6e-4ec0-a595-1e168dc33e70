<?php

namespace Mailinglist\Migrations;

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;

class AddMailinglistProcessorAndEmbedImagesColumns extends AbstractMigration
{
    public function dependencies()
    {
        return [
            BaseMailinglistTables::class,
        ];
    }

    public function up()
    {
        if (!Schema::hasColumn('tblMailingListMessages', 'processor')) {
            Schema::table('tblMailingListMessages', function($table) {
                $table->string('processor', 255)->nullable();
            });
        }

        if (!Schema::hasColumn('tblMailingListMessages', 'embed_images')) {
            Schema::table('tblMailingListMessages', function($table) {
                $table->boolean('embed_images')->default(false);
            });
        }
    }

    public function down()
    {
        if (Schema::hasColumn('tblMailingListMessages', 'embed_images')) {
            Schema::table('tblMailingListMessages', function($table) {
                $table->dropColumn('embed_images');
            });
        }
        if (Schema::hasColumn('tblMailingListMessages', 'processor')) {
            Schema::table('tblMailingListMessages', function($table) {
                $table->dropColumn('processor');
            });
        }
    }

}