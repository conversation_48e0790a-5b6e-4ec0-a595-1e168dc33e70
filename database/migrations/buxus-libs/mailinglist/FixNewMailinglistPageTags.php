<?php

namespace Mailinglist\Migrations;

use Buxus\Migration\AbstractMigration;
use Mailinglist\MailinglistPages;

class FixNewMailinglistPageTags extends AbstractMigration
{
    public function dependencies()
    {
        return [
            MailingListMigration::class,
        ];
    }

    protected function fixPageTag($old, $new)
    {
        $page_id = $this->getPageIdByTag($old);
        if ($page_id) {
            $page = \PageFactory::get($page_id);
            $page->setPageTag($new);
            $page->save();
            return true;
        }

        return false;
    }

    public function up()
    {
        $list = [
            'Mailinglist' => MailinglistPages::SERVICE_PAGES,
            'Mailinglist - odhlásenie' => MailinglistPages::SIGN_OUT_EMAIL,
            'Odhlásenie z mailinglistu' => MailinglistPages::SIGN_OUT,
            'Neznáma chyba pri odhlásení z mailinglistu' => MailinglistPages::SIGN_OUT_ERROR,
            'Prija<PERSON> požiadavky na odhlásenie z mailinglistu' => MailinglistPages::SIGN_OUT_RECEIVED,
            'Potvrdenie odhlásenia z mailinglistu' => MailinglistPages::SIGN_OUT_CONFIRM,
            'Neúspešné potvrdenie odhlásenia z mailinglistu' => MailinglistPages::SIGN_OUT_UNSUCCESFULL,
            'Mailinglist - prihlásenie' => MailinglistPages::SIGN_IN_EMAIL,
            'Prihlásenie do mailinglistu' => MailinglistPages::SIGN_IN,
            'Neznáma chyba pri prihlásení do mailinglistu' => MailinglistPages::SIGN_IN_ERROR,
            'Prijatie požiadavky na prihlásenie do mailinglistu' => MailinglistPages::SIGN_IN_RECEIVED,
            'Potvrdenie prihlásenia do mailinglistu' => MailinglistPages::SIGN_IN_CONFIRM,
            'Neúspešné potvrdenie prihlásenia do mailinglistu' => MailinglistPages::SIGN_IN_UNSUCCESFULL,
        ];

        $changed_tags = [];

        foreach ($list as $old => $new) {
            if ($this->fixPageTag($old, $new)) {
                $changed_tags[$old] = $new;
            }
        }

        $this->setDataKey('chnaged_tags', $changed_tags);
    }

    public function down()
    {
        $changed_tags = $this->getDataKey('chnaged_tags');
        if (is_array($changed_tags)) {
            foreach ($changed_tags as $old => $new) {
                $this->fixPageTag($new, $old);
            }
        }
    }
}