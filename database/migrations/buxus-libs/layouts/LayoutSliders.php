<?php
namespace Layout\Migrations;

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Types\PageList;

class LayoutSliders extends AbstractMigration {
    public function dependencies() {
        return array(
            '\Layout\Migrations\LayoutBasic',
        );
    }

    public function up() {
        // property: CSS trieda(css_class)
        $property_css_class = $this->propertyManager()->propertyExistsByTag('css_class');
        if ($property_css_class === false) {
            $property_css_class = new \Buxus\Property\Types\Input();
            $property_css_class->setTag('css_class');
            $property_css_class->setDescription('CSS trieda alebo zoznam tried oddelený medzerou');
            $property_css_class->setExtendedDescription('');
            $property_css_class->setName('CSS trieda');
            $property_css_class->setClassId('4');
            $property_css_class->setShowType(NULL);
            $property_css_class->setShowTypeTag('text');
            $property_css_class->setValueType('oneline_text');
            $property_css_class->setDefaultValue('');
            $property_css_class->setMultiOperations(false);
            $property_css_class->setInputString('');
            $property_css_class->setAttribute('tab', '');
            $property_css_class->setAttribute('size', '25');
            $property_css_class->setAttribute('maxlength', '');
            $property_css_class->setAttribute('readonly', 'F');
            $property_css_class->setAttribute('pattern', '');
            $property_css_class->setAttribute('inherit_value', 'F');
            $property_css_class->setAttribute('onchange-js', '');
            $property_css_class->setAttribute('onkeyup-js', '');
            $property_css_class->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_css_class);
        } else {
            $this->writeLine('Property with tag css_class already exists');
            $this->setDataKey('property_css_class_existed', true);
        }

        // page type: Abstract layout element (abstract_layout_element)
        $page_type_abstract_layout_element = $this->pageTypesManager()->pageTypeExistsByTag('abstract_layout_element');
        if ($page_type_abstract_layout_element === false) {
            $page_type_abstract_layout_element = new \Buxus\PageType\PageType();
            $page_type_abstract_layout_element->setTag('abstract_layout_element');
            $page_type_abstract_layout_element->setName('Abstract layout element');
            $page_type_abstract_layout_element->setPageClassId('1');
            $page_type_abstract_layout_element->setDefaultTemplateId('2');
            $page_type_abstract_layout_element->setDeleteTrigger('');
            $page_type_abstract_layout_element->setIncludeInSync(NULL);
            $page_type_abstract_layout_element->setPageDetailsLayout('');
            $page_type_abstract_layout_element->setPageSortTypeTag('sort_date_time');
            $page_type_abstract_layout_element->setPageTypeOrder('0');
            $page_type_abstract_layout_element->setPostmoveTrigger('');
            $page_type_abstract_layout_element->setPostsubmitTrigger('');
            $page_type_abstract_layout_element->setPresubmitTrigger('');
            $page_type_abstract_layout_element->setParent(NULL);
        } else {
            $this->writeLine('Page type with tag abstract_layout_element already exists');
            $this->setDataKey('page_type_abstract_layout_element_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('css_class');
        $property_id = $property->getId();
        $tmp = $page_type_abstract_layout_element->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('1');
            $tmp->setRequired(false);
            $page_type_abstract_layout_element->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($page_type_abstract_layout_element);

        // property: Content before(content_before)
        $property_content_before = $this->propertyManager()->propertyExistsByTag('content_before');
        if ($property_content_before === false) {
            $property_content_before = new \Buxus\Property\Types\Textarea();
            $property_content_before->setTag('content_before');
            $property_content_before->setDescription('Obsah na začiatok');
            $property_content_before->setExtendedDescription('');
            $property_content_before->setName('Content before');
            $property_content_before->setClassId('4');
            $property_content_before->setShowType(NULL);
            $property_content_before->setShowTypeTag('textarea');
            $property_content_before->setValueType('multiline_text');
            $property_content_before->setDefaultValue('');
            $property_content_before->setMultiOperations(false);
            $property_content_before->setInputString('');
            $property_content_before->setAttribute('tab', '');
            $property_content_before->setAttribute('cols', '60');
            $property_content_before->setAttribute('rows', '3');
            $property_content_before->setAttribute('dhtml-edit', '1');
            $property_content_before->setAttribute('dhtml-configuration', 'full_no_p');
            $property_content_before->setAttribute('import-word', '0');
            $property_content_before->setAttribute('auto', '');
            $property_content_before->setAttribute('inherit_value', 'F');
            $property_content_before->setAttribute('onchange-js', '');
            $property_content_before->setAttribute('onkeyup-js', '');
            $property_content_before->setAttribute('onkeydown-js', '');
            $property_content_before->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($property_content_before);
        } else {
            $this->writeLine('Property with tag content_before already exists');
            $this->setDataKey('property_content_before_existed', true);
        }

        // property: Content after(content_after)
        $property_content_after = $this->propertyManager()->propertyExistsByTag('content_after');
        if ($property_content_after === false) {
            $property_content_after = new \Buxus\Property\Types\Textarea();
            $property_content_after->setTag('content_after');
            $property_content_after->setDescription('Obsah na koniec');
            $property_content_after->setExtendedDescription('');
            $property_content_after->setName('Content after');
            $property_content_after->setClassId('4');
            $property_content_after->setShowType(NULL);
            $property_content_after->setShowTypeTag('textarea');
            $property_content_after->setValueType('multiline_text');
            $property_content_after->setDefaultValue('');
            $property_content_after->setMultiOperations(false);
            $property_content_after->setInputString('');
            $property_content_after->setAttribute('tab', '');
            $property_content_after->setAttribute('cols', '60');
            $property_content_after->setAttribute('rows', '3');
            $property_content_after->setAttribute('dhtml-edit', '1');
            $property_content_after->setAttribute('dhtml-configuration', 'full_no_p');
            $property_content_after->setAttribute('import-word', '0');
            $property_content_after->setAttribute('auto', '');
            $property_content_after->setAttribute('inherit_value', 'F');
            $property_content_after->setAttribute('onchange-js', '');
            $property_content_after->setAttribute('onkeyup-js', '');
            $property_content_after->setAttribute('onkeydown-js', '');
            $property_content_after->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($property_content_after);
        } else {
            $this->writeLine('Property with tag content_after already exists');
            $this->setDataKey('property_content_after_existed', true);
        }

        // page type: Container (layout_element_container)
        $page_type_layout_element_container = $this->pageTypesManager()->pageTypeExistsByTag('layout_element_container');
        if ($page_type_layout_element_container === false) {
            $page_type_layout_element_container = new \Buxus\PageType\PageType();
            $page_type_layout_element_container->setTag('layout_element_container');
            $page_type_layout_element_container->setName('Container');
            $page_type_layout_element_container->setPageClassId('1');
            $page_type_layout_element_container->setDefaultTemplateId('2');
            $page_type_layout_element_container->setDeleteTrigger('');
            $page_type_layout_element_container->setIncludeInSync(NULL);
            $page_type_layout_element_container->setPageDetailsLayout('');
            $page_type_layout_element_container->setPageSortTypeTag('sort_date_time');
            $page_type_layout_element_container->setPageTypeOrder('0');
            $page_type_layout_element_container->setPostmoveTrigger('');
            $page_type_layout_element_container->setPostsubmitTrigger('');
            $page_type_layout_element_container->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('abstract_layout_element');
            $page_type_layout_element_container->setParent($parent);
        } else {
            $this->writeLine('Page type with tag layout_element_container already exists');
            $this->setDataKey('page_type_layout_element_container_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('content_before');
        $property_id = $property->getId();
        $tmp = $page_type_layout_element_container->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('1');
            $tmp->setRequired(false);
            $page_type_layout_element_container->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('content_after');
        $property_id = $property->getId();
        $tmp = $page_type_layout_element_container->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('2');
            $tmp->setRequired(false);
            $page_type_layout_element_container->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($page_type_layout_element_container);

        // page type: Zoznam bannerov (layout_element_banner_list)
        $page_type_layout_element_banner_list = $this->pageTypesManager()->pageTypeExistsByTag('layout_element_banner_list');
        if ($page_type_layout_element_banner_list === false) {
            $page_type_layout_element_banner_list = new \Buxus\PageType\PageType();
            $page_type_layout_element_banner_list->setTag('layout_element_banner_list');
            $page_type_layout_element_banner_list->setName('Zoznam bannerov');
            $page_type_layout_element_banner_list->setPageClassId('1');
            $page_type_layout_element_banner_list->setDefaultTemplateId('1');
            $page_type_layout_element_banner_list->setDeleteTrigger('');
            $page_type_layout_element_banner_list->setIncludeInSync(NULL);
            $page_type_layout_element_banner_list->setPageDetailsLayout('');
            $page_type_layout_element_banner_list->setPageSortTypeTag('sort_date_time');
            $page_type_layout_element_banner_list->setPageTypeOrder('0');
            $page_type_layout_element_banner_list->setPostmoveTrigger('');
            $page_type_layout_element_banner_list->setPostsubmitTrigger('');
            $page_type_layout_element_banner_list->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('layout_element_container');
            $page_type_layout_element_banner_list->setParent($parent);
        } else {
            $this->writeLine('Page type with tag layout_element_banner_list already exists');
            $this->setDataKey('page_type_layout_element_banner_list_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('css_class');
        $property_id = $property->getId();
        $tmp = $page_type_layout_element_banner_list->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('1');
            $tmp->setRequired(false);
            $page_type_layout_element_banner_list->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('content_before');
        $property_id = $property->getId();
        $tmp = $page_type_layout_element_banner_list->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('2');
            $tmp->setRequired(false);
            $page_type_layout_element_banner_list->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('content_after');
        $property_id = $property->getId();
        $tmp = $page_type_layout_element_banner_list->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('3');
            $tmp->setRequired(false);
            $page_type_layout_element_banner_list->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($page_type_layout_element_banner_list);

        // property: Nadpis(heading_text)
        $property_heading_text = $this->propertyManager()->propertyExistsByTag('heading_text');
        if ($property_heading_text === false) {
            $property_heading_text = new \Buxus\Property\Types\Textarea();
            $property_heading_text->setTag('heading_text');
            $property_heading_text->setDescription('Text, ktorý sa zobrazí v elemente nadpis.');
            $property_heading_text->setExtendedDescription('');
            $property_heading_text->setName('Nadpis');
            $property_heading_text->setClassId('4');
            $property_heading_text->setShowType(NULL);
            $property_heading_text->setShowTypeTag('text');
            $property_heading_text->setValueType('oneline_text');
            $property_heading_text->setDefaultValue('');
            $property_heading_text->setMultiOperations(false);
            $property_heading_text->setInputString(NULL);
            $property_heading_text->setAttribute('tab', '');
            $property_heading_text->setAttribute('size', '60');
            $property_heading_text->setAttribute('maxlength', '');
            $property_heading_text->setAttribute('readonly', 'F');
            $property_heading_text->setAttribute('pattern', '');
            $property_heading_text->setAttribute('inherit_value', 'F');
            $property_heading_text->setAttribute('onchange-js', '');
            $property_heading_text->setAttribute('onkeyup-js', '');
            $property_heading_text->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_heading_text);
        } else {
            $this->writeLine('Property with tag heading_text already exists');
            $this->setDataKey('property_heading_text_existed', true);
        }

        // property: Text banneru(info_text)
        $property_info_text = $this->propertyManager()->propertyExistsByTag('info_text');
        if ($property_info_text === false) {
            $property_info_text = new \Buxus\Property\Types\Textarea();
            $property_info_text->setTag('info_text');
            $property_info_text->setDescription('Vstupný text do elementu napr na zobrazenie info. ');
            $property_info_text->setExtendedDescription('');
            $property_info_text->setName('Text banneru');
            $property_info_text->setClassId('4');
            $property_info_text->setShowType(NULL);
            $property_info_text->setShowTypeTag('textarea');
            $property_info_text->setValueType('multiline_text');
            $property_info_text->setDefaultValue('');
            $property_info_text->setMultiOperations(false);
            $property_info_text->setInputString(NULL);
            $property_info_text->setAttribute('tab', '');
            $property_info_text->setAttribute('cols', '60');
            $property_info_text->setAttribute('rows', '3');
            $property_info_text->setAttribute('dhtml-edit', '0');
            $property_info_text->setAttribute('dhtml-configuration', 'full');
            $property_info_text->setAttribute('import-word', '0');
            $property_info_text->setAttribute('auto', '');
            $property_info_text->setAttribute('inherit_value', 'F');
            $property_info_text->setAttribute('onchange-js', '');
            $property_info_text->setAttribute('onkeyup-js', '');
            $property_info_text->setAttribute('onkeydown-js', '');
            $property_info_text->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($property_info_text);
        } else {
            $this->writeLine('Property with tag info_text already exists');
            $this->setDataKey('property_info_text_existed', true);
        }

        // property: Obrázok banneru(slider_text_sticker)
        $property_slider_text_sticker = $this->propertyManager()->propertyExistsByTag('slider_text_sticker');
        if ($property_slider_text_sticker === false) {
            $property_slider_text_sticker = new \Buxus\Property\Types\Image();
            $property_slider_text_sticker->setTag('slider_text_sticker');
            $property_slider_text_sticker->setDescription('Obrázok by mal mať rozlíšenie 1170 x 300px. Pri väčšom rozlíšení sa obrázok oreže na túto šírku, výšku. Pri menšom rozlíšení sa obrázok roztiahne do potrebnej veľkosti. V prípade, že na vašom obrázku zaniká text, môžete zapnúť "Pozadie za textom" v samostatnej záložke.');
            $property_slider_text_sticker->setExtendedDescription('');
            $property_slider_text_sticker->setName('Obrázok banneru');
            $property_slider_text_sticker->setClassId('4');
            $property_slider_text_sticker->setShowType(NULL);
            $property_slider_text_sticker->setShowTypeTag('image_name_upload');
            $property_slider_text_sticker->setValueType('file');
            $property_slider_text_sticker->setDefaultValue('');
            $property_slider_text_sticker->setMultiOperations(false);
            $property_slider_text_sticker->setInputString(NULL);
            $property_slider_text_sticker->setAttribute('tab', '');
            $property_slider_text_sticker->setAttribute('with_upload', 'T');
            $property_slider_text_sticker->setAttribute('simple_upload', 'F');
            $property_slider_text_sticker->setAttribute('show_input_element', 'T');
            $property_slider_text_sticker->setAttribute('filename', '');
            $property_slider_text_sticker->setAttribute('show_file_name', 'T');
            $property_slider_text_sticker->setAttribute('file_type', 'image');
            $property_slider_text_sticker->setAttribute('pattern', '');
            $property_slider_text_sticker->setAttribute('show_thumbnail', 'T');
            $property_slider_text_sticker->setAttribute('max_thumbnail_width', '150');
            $property_slider_text_sticker->setAttribute('max_thumbnail_height', '80');
            $property_slider_text_sticker->setAttribute('upload_subdir', '');
            $property_slider_text_sticker->setAttribute('eval-code', '//$this-&gt;parsed_parm[\'upload_subdir\'] = \'my-dir\';');
            $property_slider_text_sticker->setAttribute('onchange-js', '');
            $this->propertyManager()->saveProperty($property_slider_text_sticker);
        } else {
            $this->writeLine('Property with tag slider_text_sticker already exists');
            $this->setDataKey('property_slider_text_sticker_existed', true);
        }

        // property: Text tlačidla(slider_text_button_label)
        $property_slider_text_button_label = $this->propertyManager()->propertyExistsByTag('slider_text_button_label');
        if ($property_slider_text_button_label === false) {
            $property_slider_text_button_label = new \Buxus\Property\Types\Input();
            $property_slider_text_button_label->setTag('slider_text_button_label');
            $property_slider_text_button_label->setDescription('Text tlačidla. Mal by zákazníka nalákať ku akcii (tzv. Call to Action) alebo naznačiť, čo sa pod ním skrýva. Ak nechcete mať žiadne tlačidlo, stačí nechať text prázdny.');
            $property_slider_text_button_label->setExtendedDescription('');
            $property_slider_text_button_label->setName('Text tlačidla');
            $property_slider_text_button_label->setClassId('4');
            $property_slider_text_button_label->setShowType(NULL);
            $property_slider_text_button_label->setShowTypeTag('text');
            $property_slider_text_button_label->setValueType('oneline_text');
            $property_slider_text_button_label->setDefaultValue('');
            $property_slider_text_button_label->setMultiOperations(false);
            $property_slider_text_button_label->setInputString(NULL);
            $property_slider_text_button_label->setAttribute('tab', '');
            $property_slider_text_button_label->setAttribute('size', '60');
            $property_slider_text_button_label->setAttribute('maxlength', '');
            $property_slider_text_button_label->setAttribute('readonly', 'F');
            $property_slider_text_button_label->setAttribute('pattern', '');
            $property_slider_text_button_label->setAttribute('inherit_value', 'F');
            $property_slider_text_button_label->setAttribute('onchange-js', '');
            $property_slider_text_button_label->setAttribute('onkeyup-js', '');
            $property_slider_text_button_label->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_slider_text_button_label);
        } else {
            $this->writeLine('Property with tag slider_text_button_label already exists');
            $this->setDataKey('property_slider_text_button_label_existed', true);
        }

        // property: Cieľová stránka tlačidla/banneru(slider_text_button_link)
        $property_slider_text_button_link = $this->propertyManager()->propertyExistsByTag('slider_text_button_link');
        if ($property_slider_text_button_link === false) {
            $property_slider_text_button_link = new PageList();
            $property_slider_text_button_link->setTag('slider_text_button_link');
            $property_slider_text_button_link->setDescription('Cieľová stránka, na ktorú sa zákazník dostane po kliku na tlačidlo. Ak tlačidlo nie je zobrazené (pretože nemá žiadny text), odkaz sa nastaví pre celý banner.');
            $property_slider_text_button_link->setExtendedDescription('');
            $property_slider_text_button_link->setName('Cieľová stránka tlačidla/banneru');
            $property_slider_text_button_link->setClassId('4');
            $property_slider_text_button_link->setShowType(NULL);
            $property_slider_text_button_link->setShowTypeTag('page_list');
            $property_slider_text_button_link->setValueType('page_list');
            $property_slider_text_button_link->setDefaultValue('');
            $property_slider_text_button_link->setMultiOperations(false);
            $property_slider_text_button_link->setInputString(NULL);
            $property_slider_text_button_link->setAttribute('tab', '');
            $property_slider_text_button_link->setAttribute('root_page_id', '');
            $property_slider_text_button_link->setAttribute('page_type_id', '');
            $property_slider_text_button_link->setAttribute('default_sort', 'tblPages.sort_date_time');
            $property_slider_text_button_link->setAttribute('advanced_mode', 'T');
            $property_slider_text_button_link->setAttribute('external_url', 'T');
            $property_slider_text_button_link->setAttribute('max_items', '1');
            $property_slider_text_button_link->setAttribute('middle_col_width', '');
            $property_slider_text_button_link->setAttribute('apply_user_rights', 'T');
            $property_slider_text_button_link->setAttribute('property_for_link_name', 'tblPages.page_name');
            $property_slider_text_button_link->setAttribute('properties_for_search', 'tblPages.page_name');
            $this->propertyManager()->saveProperty($property_slider_text_button_link);
        } else {
            $this->writeLine('Property with tag slider_text_button_link already exists');
            $this->setDataKey('property_slider_text_button_link_existed', true);
        }

        // property: Zapnúť pozadie za textom(slider_text_background)
        $property_slider_text_background = $this->propertyManager()->propertyExistsByTag('slider_text_background');
        if ($property_slider_text_background === false) {
            $property_slider_text_background = new \Buxus\Property\Types\Input();
            $property_slider_text_background->setTag('slider_text_background');
            $property_slider_text_background->setDescription('Zapne/vypne pozadie za textom. Ak na pozadí banneru máte výrazný obrázok, text ktorý je nad ním môže zanikať.');
            $property_slider_text_background->setExtendedDescription('<p>Takto vyzerá text bez pozadia nad "strakatým" obrázkom:</p>
<p><img src="/buxus/images/manual/slider_text_background/bez_pozadia.png" /></p>
<p>Keď nastavíme biele pozadie s 50 percentnou priehľadnosťou, text je hneď čitateľnejší:</p>
<p><img src="/buxus/images/manual/slider_text_background/biele_pozadie.png" /></p>
<p>Farbu i mieru priehľadnosti môžeme nastaviť podľa ľubovôle. V tomto príklade je šedá farba (969696) a priehľadnosť 75:</p>
<p><img src="/buxus/images/manual/slider_text_background/sede_pozadie.png" /></p>');
            $property_slider_text_background->setName('Zapnúť pozadie za textom');
            $property_slider_text_background->setClassId('4');
            $property_slider_text_background->setShowType(NULL);
            $property_slider_text_background->setShowTypeTag('checkbox');
            $property_slider_text_background->setValueType('logical_value');
            $property_slider_text_background->setDefaultValue('');
            $property_slider_text_background->setMultiOperations(false);
            $property_slider_text_background->setInputString(NULL);
            $property_slider_text_background->setAttribute('tab', 'Textov&yacute; blok');
            $property_slider_text_background->setAttribute('on_value', 'T');
            $property_slider_text_background->setAttribute('off_value', 'F');
            $property_slider_text_background->setAttribute('onclick-js', '');
            $property_slider_text_background->setAttribute('inherit_value', 'F');
            $this->propertyManager()->saveProperty($property_slider_text_background);
        } else {
            $this->writeLine('Property with tag slider_text_background already exists');
            $this->setDataKey('property_slider_text_background_existed', true);
        }

        // property: Priehľadnosť pozadia(background_opacity)
        $property_background_opacity = $this->propertyManager()->propertyExistsByTag('background_opacity');
        if ($property_background_opacity === false) {
            $property_background_opacity = new \Buxus\Property\Types\Input();
            $property_background_opacity->setTag('background_opacity');
            $property_background_opacity->setDescription('Čím nižšie číslo, tým viac bude presvitať obrázok na pozadí. Pozrite si príklady.');
            $property_background_opacity->setExtendedDescription('<p>Takto vyzerá text bez pozadia nad "strakatým" obrázkom:</p>
<p><img src="/buxus/images/manual/slider_text_background/bez_pozadia.png" /></p>
<p>Keď nastavíme biele pozadie s 50 percentnou priehľadnosťou, text je hneď čitateľnejší:</p>
<p><img src="/buxus/images/manual/slider_text_background/biele_pozadie.png" /></p>
<p>Farbu i mieru priehľadnosti môžeme nastaviť podľa ľubovôle. V tomto príklade je šedá farba (969696) a priehľadnosť 75.</p>
<p><img src="/buxus/images/manual/slider_text_background/sede_pozadie.png" /></p>');
            $property_background_opacity->setName('Priehľadnosť pozadia');
            $property_background_opacity->setClassId('4');
            $property_background_opacity->setShowType(NULL);
            $property_background_opacity->setShowTypeTag('number');
            $property_background_opacity->setValueType('number');
            $property_background_opacity->setDefaultValue('');
            $property_background_opacity->setMultiOperations(false);
            $property_background_opacity->setInputString(NULL);
            $property_background_opacity->setAttribute('tab', 'Textov&yacute; blok');
            $property_background_opacity->setAttribute('size', '10');
            $property_background_opacity->setAttribute('min', '-1');
            $property_background_opacity->setAttribute('max', '101');
            $property_background_opacity->setAttribute('step', '1');
            $property_background_opacity->setAttribute('inherit_value', 'F');
            $property_background_opacity->setAttribute('onchange-js', '');
            $property_background_opacity->setAttribute('onkeyup-js', '');
            $property_background_opacity->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_background_opacity);
        } else {
            $this->writeLine('Property with tag background_opacity already exists');
            $this->setDataKey('property_background_opacity_existed', true);
        }

        // property: Farba pozadia(promobox_background)
        $property_promobox_background = $this->propertyManager()->propertyExistsByTag('promobox_background');
        if ($property_promobox_background === false) {
            $property_promobox_background = new \Buxus\Property\Types\Input();
            $property_promobox_background->setTag('promobox_background');
            $property_promobox_background->setDescription('Vyberte farbu a kliknutím na farebný kruh vpravo dole sa uloží jej hodnota do vlastnosti.');
            $property_promobox_background->setExtendedDescription('');
            $property_promobox_background->setName('Farba pozadia');
            $property_promobox_background->setClassId('4');
            $property_promobox_background->setShowType(NULL);
            $property_promobox_background->setShowTypeTag('text');
            $property_promobox_background->setValueType('oneline_text');
            $property_promobox_background->setDefaultValue('');
            $property_promobox_background->setMultiOperations(false);
            $property_promobox_background->setInputString('');
            $property_promobox_background->setAttribute('tab', 'Textov&yacute; blok');
            $property_promobox_background->setAttribute('size', '25');
            $property_promobox_background->setAttribute('maxlength', '');
            $property_promobox_background->setAttribute('readonly', 'F');
            $property_promobox_background->setAttribute('pattern', '');
            $property_promobox_background->setAttribute('inherit_value', 'F');
            $property_promobox_background->setAttribute('onchange-js', '');
            $property_promobox_background->setAttribute('onkeyup-js', '');
            $property_promobox_background->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_promobox_background);
        } else {
            $this->writeLine('Property with tag promobox_background already exists');
            $this->setDataKey('property_promobox_background_existed', true);
        }

        // page type: Banner (layout_element_slider_banner)
        $page_type_layout_element_slider_banner = $this->pageTypesManager()->pageTypeExistsByTag('layout_element_slider_banner');
        if ($page_type_layout_element_slider_banner === false) {
            $page_type_layout_element_slider_banner = new \Buxus\PageType\PageType();
            $page_type_layout_element_slider_banner->setTag('layout_element_slider_banner');
            $page_type_layout_element_slider_banner->setName('Banner');
            $page_type_layout_element_slider_banner->setPageClassId('1');
            $page_type_layout_element_slider_banner->setDefaultTemplateId('1');
            $page_type_layout_element_slider_banner->setDeleteTrigger('');
            $page_type_layout_element_slider_banner->setIncludeInSync('0');
            $page_type_layout_element_slider_banner->setPageDetailsLayout('');
            $page_type_layout_element_slider_banner->setPageSortTypeTag('sort_date_time');
            $page_type_layout_element_slider_banner->setPageTypeOrder('0');
            $page_type_layout_element_slider_banner->setPostmoveTrigger('');
            $page_type_layout_element_slider_banner->setPostsubmitTrigger('');
            $page_type_layout_element_slider_banner->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('abstract_layout_element');
            $page_type_layout_element_slider_banner->setParent($parent);
        } else {
            $this->writeLine('Page type with tag layout_element_slider_banner already exists');
            $this->setDataKey('page_type_layout_element_slider_banner_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('heading_text');
        $property_id = $property->getId();
        $tmp = $page_type_layout_element_slider_banner->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('1');
            $tmp->setRequired(false);
            $page_type_layout_element_slider_banner->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('info_text');
        $property_id = $property->getId();
        $tmp = $page_type_layout_element_slider_banner->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('2');
            $tmp->setRequired(false);
            $page_type_layout_element_slider_banner->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('slider_text_sticker');
        $property_id = $property->getId();
        $tmp = $page_type_layout_element_slider_banner->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('3');
            $tmp->setRequired(false);
            $page_type_layout_element_slider_banner->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('slider_text_button_label');
        $property_id = $property->getId();
        $tmp = $page_type_layout_element_slider_banner->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('4');
            $tmp->setRequired(false);
            $page_type_layout_element_slider_banner->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('slider_text_button_link');
        $property_id = $property->getId();
        $tmp = $page_type_layout_element_slider_banner->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('5');
            $tmp->setRequired(false);
            $page_type_layout_element_slider_banner->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('slider_text_background');
        $property_id = $property->getId();
        $tmp = $page_type_layout_element_slider_banner->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('6');
            $tmp->setRequired(false);
            $page_type_layout_element_slider_banner->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('background_opacity');
        $property_id = $property->getId();
        $tmp = $page_type_layout_element_slider_banner->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('7');
            $tmp->setRequired(false);
            $page_type_layout_element_slider_banner->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('promobox_background');
        $property_id = $property->getId();
        $tmp = $page_type_layout_element_slider_banner->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('8');
            $tmp->setRequired(false);
            $page_type_layout_element_slider_banner->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($page_type_layout_element_slider_banner);

        if ($this->pageTypeExists('layout_element_container')) {
            $this->addPageTypeSuperiorPageType('layout_element_container', 'layout_element_container');
        }
        if ($this->pageTypeExists('layout')) {
            $this->addPageTypeSuperiorPageType('layout_element_container', 'layout');
        }
        if ($this->pageTypeExists('layout_element_menu_link')) {
            $this->addPageTypeSuperiorPageType('layout_element_container', 'layout_element_menu_link');
        }
        if ($this->pageTypeExists('homepage')) {
            $this->addPageTypeSuperiorPageType('layout_element_banner_list', 'homepage');
        }

        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();
    }

    public function down() {
        // remove page type: Banner (layout_element_slider_banner)
        $page_type_layout_element_slider_banner = $this->pageTypesManager()->pageTypeExistsByTag('layout_element_slider_banner');
        if (($page_type_layout_element_slider_banner != false) && (is_null($this->getDataKey('page_type_layout_element_slider_banner_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_layout_element_slider_banner);
        }

        // remove property: Farba pozadia(promobox_background)
        $property_promobox_background = $this->propertyManager()->propertyExistsByTag('promobox_background');
        if ($property_promobox_background != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_promobox_background);
            if ((is_null($this->getDataKey('property_promobox_background_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_promobox_background);
            }
        }

        // remove property: Priehľadnosť pozadia(background_opacity)
        $property_background_opacity = $this->propertyManager()->propertyExistsByTag('background_opacity');
        if ($property_background_opacity != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_background_opacity);
            if ((is_null($this->getDataKey('property_background_opacity_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_background_opacity);
            }
        }

        // remove property: Zapnúť pozadie za textom(slider_text_background)
        $property_slider_text_background = $this->propertyManager()->propertyExistsByTag('slider_text_background');
        if ($property_slider_text_background != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_slider_text_background);
            if ((is_null($this->getDataKey('property_slider_text_background_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_slider_text_background);
            }
        }

        // remove property: Cieľová stránka tlačidla/banneru(slider_text_button_link)
        $property_slider_text_button_link = $this->propertyManager()->propertyExistsByTag('slider_text_button_link');
        if ($property_slider_text_button_link != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_slider_text_button_link);
            if ((is_null($this->getDataKey('property_slider_text_button_link_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_slider_text_button_link);
            }
        }

        // remove property: Text tlačidla(slider_text_button_label)
        $property_slider_text_button_label = $this->propertyManager()->propertyExistsByTag('slider_text_button_label');
        if ($property_slider_text_button_label != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_slider_text_button_label);
            if ((is_null($this->getDataKey('property_slider_text_button_label_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_slider_text_button_label);
            }
        }

        // remove property: Obrázok banneru(slider_text_sticker)
        $property_slider_text_sticker = $this->propertyManager()->propertyExistsByTag('slider_text_sticker');
        if ($property_slider_text_sticker != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_slider_text_sticker);
            if ((is_null($this->getDataKey('property_slider_text_sticker_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_slider_text_sticker);
            }
        }

        // remove property: Text banneru(info_text)
        $property_info_text = $this->propertyManager()->propertyExistsByTag('info_text');
        if ($property_info_text != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_info_text);
            if ((is_null($this->getDataKey('property_info_text_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_info_text);
            }
        }

        // remove property: Nadpis(heading_text)
        $property_heading_text = $this->propertyManager()->propertyExistsByTag('heading_text');
        if ($property_heading_text != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_heading_text);
            if ((is_null($this->getDataKey('property_heading_text_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_heading_text);
            }
        }

        // remove page type: Zoznam bannerov (layout_element_banner_list)
        $page_type_layout_element_banner_list = $this->pageTypesManager()->pageTypeExistsByTag('layout_element_banner_list');
        if (($page_type_layout_element_banner_list != false) && (is_null($this->getDataKey('page_type_layout_element_banner_list_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_layout_element_banner_list);
        }

        // remove page type: Container (layout_element_container)
        $page_type_layout_element_container = $this->pageTypesManager()->pageTypeExistsByTag('layout_element_container');
        if (($page_type_layout_element_container != false) && (is_null($this->getDataKey('page_type_layout_element_container_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_layout_element_container);
        }

        // remove property: Content after(content_after)
        $property_content_after = $this->propertyManager()->propertyExistsByTag('content_after');
        if ($property_content_after != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_content_after);
            if ((is_null($this->getDataKey('property_content_after_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_content_after);
            }
        }

        // remove property: Content before(content_before)
        $property_content_before = $this->propertyManager()->propertyExistsByTag('content_before');
        if ($property_content_before != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_content_before);
            if ((is_null($this->getDataKey('property_content_before_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_content_before);
            }
        }

        // remove page type: Abstract layout element (abstract_layout_element)
        $page_type_abstract_layout_element = $this->pageTypesManager()->pageTypeExistsByTag('abstract_layout_element');
        if (($page_type_abstract_layout_element != false) && (is_null($this->getDataKey('page_type_abstract_layout_element_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_abstract_layout_element);
        }

        // remove property: CSS trieda(css_class)
        $property_css_class = $this->propertyManager()->propertyExistsByTag('css_class');
        if ($property_css_class != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_css_class);
            if ((is_null($this->getDataKey('property_css_class_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_css_class);
            }
        }

        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();
    }
}
