<?php

namespace Layout\Migrations;

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (wide-krabica) at 2016-06-22 15:40:03
 * Property generator: property=banner_aspect_ratio,text_align_horizontal,text_align_vertical
 */

class GridCarouselBannerPropertiesMigration extends AbstractMigration
{
	public function dependencies()
	{
		return array(
			'\Layout\Migrations\GridCarouselMigration',
		);
	}

	public function up()
	{
		// property: Pomer stran banneru(banner_aspect_ratio)
		$property_banner_aspect_ratio = $this->propertyManager()->propertyExistsByTag('banner_aspect_ratio');
		if ($property_banner_aspect_ratio === false) {
			$property_banner_aspect_ratio = new Property();
			$property_banner_aspect_ratio->setTag('banner_aspect_ratio');
			$property_banner_aspect_ratio->setDescription('Pomer stran banneru. Pomer slidu je 2:3.');
			$property_banner_aspect_ratio->setExtendedDescription('');
			$property_banner_aspect_ratio->setName('Pomer stran banneru');
			$property_banner_aspect_ratio->setClassId('4');
			$property_banner_aspect_ratio->setShowType(NULL);
			$property_banner_aspect_ratio->setShowTypeTag('select');
			$property_banner_aspect_ratio->setValueType('items_set');
			$property_banner_aspect_ratio->setDefaultValue('');
			$property_banner_aspect_ratio->setMultiOperations(false);
			$property_banner_aspect_ratio->setInputString('');
			$property_banner_aspect_ratio->setAttribute('tab', '');
			$property_banner_aspect_ratio->setAttribute('options', array (
			  11 => '1:1',
			  12 => '1:2',
			  21 => '2:1',
			  22 => '2:2',
			));
			$property_banner_aspect_ratio->setAttribute('multiple', 'F');
			$property_banner_aspect_ratio->setAttribute('show_as_list', 'T');
			$property_banner_aspect_ratio->setAttribute('empty_option', 'F');
			$property_banner_aspect_ratio->setAttribute('size', '');
			$property_banner_aspect_ratio->setAttribute('cols', NULL);
			$property_banner_aspect_ratio->setAttribute('rows', '');
			$property_banner_aspect_ratio->setAttribute('onchange-js', '');
			$property_banner_aspect_ratio->setAttribute('disabled', 'F');
			$property_banner_aspect_ratio->setAttribute('check_read_rights', 'T');
			$property_banner_aspect_ratio->setAttribute('inherit_value', 'F');
			$this->propertyManager()->saveProperty($property_banner_aspect_ratio);
		} else {
			$this->writeLine('Property with tag banner_aspect_ratio already exists');
			$this->setDataKey('property_banner_aspect_ratio_existed', true);
		}
		if ($this->pageTypeExists('layout_element_slider_banner')) {
			$this->addPropertyToPageType('banner_aspect_ratio', 'layout_element_slider_banner', false);
		}

		// property: Zarovnanie textu - horizontalne(text_align_horizontal)
		$property_text_align_horizontal = $this->propertyManager()->propertyExistsByTag('text_align_horizontal');
		if ($property_text_align_horizontal === false) {
			$property_text_align_horizontal = new Property();
			$property_text_align_horizontal->setTag('text_align_horizontal');
			$property_text_align_horizontal->setDescription('');
			$property_text_align_horizontal->setExtendedDescription('');
			$property_text_align_horizontal->setName('Zarovnanie textu - horizontalne');
			$property_text_align_horizontal->setClassId('4');
			$property_text_align_horizontal->setShowType(NULL);
			$property_text_align_horizontal->setShowTypeTag('select');
			$property_text_align_horizontal->setValueType('items_set');
			$property_text_align_horizontal->setDefaultValue('');
			$property_text_align_horizontal->setMultiOperations(false);
			$property_text_align_horizontal->setInputString('');
			$property_text_align_horizontal->setAttribute('tab', '');
			$property_text_align_horizontal->setAttribute('options', array (
			  'left' => 'Vľavo',
			  'center' => 'Stred',
			  'right' => 'Vpravo',
			));
			$property_text_align_horizontal->setAttribute('multiple', 'F');
			$property_text_align_horizontal->setAttribute('show_as_list', 'T');
			$property_text_align_horizontal->setAttribute('empty_option', 'F');
			$property_text_align_horizontal->setAttribute('size', '');
			$property_text_align_horizontal->setAttribute('cols', NULL);
			$property_text_align_horizontal->setAttribute('rows', '');
			$property_text_align_horizontal->setAttribute('onchange-js', '');
			$property_text_align_horizontal->setAttribute('disabled', 'F');
			$property_text_align_horizontal->setAttribute('check_read_rights', 'T');
			$property_text_align_horizontal->setAttribute('inherit_value', 'F');
			$this->propertyManager()->saveProperty($property_text_align_horizontal);
		} else {
			$this->writeLine('Property with tag text_align_horizontal already exists');
			$this->setDataKey('property_text_align_horizontal_existed', true);
		}
		if ($this->pageTypeExists('layout_element_slider_banner')) {
			$this->addPropertyToPageType('text_align_horizontal', 'layout_element_slider_banner', false);
		}

		// property: Zarovnanie textu - vertikálne(text_align_vertical)
		$property_text_align_vertical = $this->propertyManager()->propertyExistsByTag('text_align_vertical');
		if ($property_text_align_vertical === false) {
			$property_text_align_vertical = new Property();
			$property_text_align_vertical->setTag('text_align_vertical');
			$property_text_align_vertical->setDescription('');
			$property_text_align_vertical->setExtendedDescription('');
			$property_text_align_vertical->setName('Zarovnanie textu - vertikálne');
			$property_text_align_vertical->setClassId('4');
			$property_text_align_vertical->setShowType(NULL);
			$property_text_align_vertical->setShowTypeTag('select');
			$property_text_align_vertical->setValueType('items_set');
			$property_text_align_vertical->setDefaultValue('');
			$property_text_align_vertical->setMultiOperations(false);
			$property_text_align_vertical->setInputString('');
			$property_text_align_vertical->setAttribute('tab', '');
			$property_text_align_vertical->setAttribute('options', array (
			  'top' => 'Hore',
			  'middle' => 'Stred',
			  'bottom' => 'Dole',
			));
			$property_text_align_vertical->setAttribute('multiple', 'F');
			$property_text_align_vertical->setAttribute('show_as_list', 'T');
			$property_text_align_vertical->setAttribute('empty_option', 'F');
			$property_text_align_vertical->setAttribute('size', '');
			$property_text_align_vertical->setAttribute('cols', NULL);
			$property_text_align_vertical->setAttribute('rows', '');
			$property_text_align_vertical->setAttribute('onchange-js', '');
			$property_text_align_vertical->setAttribute('disabled', 'F');
			$property_text_align_vertical->setAttribute('check_read_rights', 'T');
			$property_text_align_vertical->setAttribute('inherit_value', 'F');
			$this->propertyManager()->saveProperty($property_text_align_vertical);
		} else {
			$this->writeLine('Property with tag text_align_vertical already exists');
			$this->setDataKey('property_text_align_vertical_existed', true);
		}
		if ($this->pageTypeExists('layout_element_slider_banner')) {
			$this->addPropertyToPageType('text_align_vertical', 'layout_element_slider_banner', false);
		}

		// regenerate property constants
		$this->propertyManager()->generateConstants();

	}

	public function down()
	{
		// remove property: Zarovnanie textu - vertikálne(text_align_vertical)
		$property_text_align_vertical = $this->propertyManager()->propertyExistsByTag('text_align_vertical');
		if ($property_text_align_vertical != false) {
			$supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_text_align_vertical);
			if ((is_null($this->getDataKey('property_text_align_vertical_existed'))) && (count($supported_page_types) == 0)) {
				$this->propertyManager()->removeProperty($property_text_align_vertical);
			}
		}

		// remove property: Zarovnanie textu - horizontalne(text_align_horizontal)
		$property_text_align_horizontal = $this->propertyManager()->propertyExistsByTag('text_align_horizontal');
		if ($property_text_align_horizontal != false) {
			$supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_text_align_horizontal);
			if ((is_null($this->getDataKey('property_text_align_horizontal_existed'))) && (count($supported_page_types) == 0)) {
				$this->propertyManager()->removeProperty($property_text_align_horizontal);
			}
		}

		// remove property: Pomer stran banneru(banner_aspect_ratio)
		$property_banner_aspect_ratio = $this->propertyManager()->propertyExistsByTag('banner_aspect_ratio');
		if ($property_banner_aspect_ratio != false) {
			$supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_banner_aspect_ratio);
			if ((is_null($this->getDataKey('property_banner_aspect_ratio_existed'))) && (count($supported_page_types) == 0)) {
				$this->propertyManager()->removeProperty($property_banner_aspect_ratio);
			}
		}

		// regenerate property constants
		$this->propertyManager()->generateConstants();

	}

}
