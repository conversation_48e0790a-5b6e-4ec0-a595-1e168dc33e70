<?php
namespace Layout\Migrations;

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Types\PageList;

class LayoutPromoboxes extends AbstractMigration {
    public function dependencies() {
        return array(
            '\Layout\Migrations\LayoutBasic',
        );
    }

    public function up() {
        // property: CSS trieda(css_class)
        $property_css_class = $this->propertyManager()->propertyExistsByTag('css_class');
        if ($property_css_class === false) {
            $property_css_class = new \Buxus\Property\Types\Input();
            $property_css_class->setTag('css_class');
            $property_css_class->setDescription('CSS trieda alebo zoznam tried oddelený medzerou');
            $property_css_class->setExtendedDescription('');
            $property_css_class->setName('CSS trieda');
            $property_css_class->setClassId('4');
            $property_css_class->setShowType(NULL);
            $property_css_class->setShowTypeTag('text');
            $property_css_class->setValueType('oneline_text');
            $property_css_class->setDefaultValue('');
            $property_css_class->setMultiOperations(false);
            $property_css_class->setInputString('');
            $property_css_class->setAttribute('tab', '');
            $property_css_class->setAttribute('size', '25');
            $property_css_class->setAttribute('maxlength', '');
            $property_css_class->setAttribute('readonly', 'F');
            $property_css_class->setAttribute('pattern', '');
            $property_css_class->setAttribute('inherit_value', 'F');
            $property_css_class->setAttribute('onchange-js', '');
            $property_css_class->setAttribute('onkeyup-js', '');
            $property_css_class->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_css_class);
        } else {
            $this->writeLine('Property with tag css_class already exists');
            $this->setDataKey('property_css_class_existed', true);
        }

        // page type: Abstract layout element (abstract_layout_element)
        $page_type_abstract_layout_element = $this->pageTypesManager()->pageTypeExistsByTag('abstract_layout_element');
        if ($page_type_abstract_layout_element === false) {
            $page_type_abstract_layout_element = new \Buxus\PageType\PageType();
            $page_type_abstract_layout_element->setTag('abstract_layout_element');
            $page_type_abstract_layout_element->setName('Abstract layout element');
            $page_type_abstract_layout_element->setPageClassId('1');
            $page_type_abstract_layout_element->setDefaultTemplateId('2');
            $page_type_abstract_layout_element->setDeleteTrigger('');
            $page_type_abstract_layout_element->setIncludeInSync(NULL);
            $page_type_abstract_layout_element->setPageDetailsLayout('');
            $page_type_abstract_layout_element->setPageSortTypeTag('sort_date_time');
            $page_type_abstract_layout_element->setPageTypeOrder('0');
            $page_type_abstract_layout_element->setPostmoveTrigger('');
            $page_type_abstract_layout_element->setPostsubmitTrigger('');
            $page_type_abstract_layout_element->setPresubmitTrigger('');
            $page_type_abstract_layout_element->setParent(NULL);
        } else {
            $this->writeLine('Page type with tag abstract_layout_element already exists');
            $this->setDataKey('page_type_abstract_layout_element_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('css_class');
        $property_id = $property->getId();
        $tmp = $page_type_abstract_layout_element->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('1');
            $tmp->setRequired(false);
            $page_type_abstract_layout_element->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($page_type_abstract_layout_element);

        // property: Content before(content_before)
        $property_content_before = $this->propertyManager()->propertyExistsByTag('content_before');
        if ($property_content_before === false) {
            $property_content_before = new \Buxus\Property\Types\Textarea();
            $property_content_before->setTag('content_before');
            $property_content_before->setDescription('Obsah na začiatok');
            $property_content_before->setExtendedDescription('');
            $property_content_before->setName('Content before');
            $property_content_before->setClassId('4');
            $property_content_before->setShowType(NULL);
            $property_content_before->setShowTypeTag('textarea');
            $property_content_before->setValueType('multiline_text');
            $property_content_before->setDefaultValue('');
            $property_content_before->setMultiOperations(false);
            $property_content_before->setInputString('');
            $property_content_before->setAttribute('tab', '');
            $property_content_before->setAttribute('cols', '60');
            $property_content_before->setAttribute('rows', '3');
            $property_content_before->setAttribute('dhtml-edit', '1');
            $property_content_before->setAttribute('dhtml-configuration', 'full_no_p');
            $property_content_before->setAttribute('import-word', '0');
            $property_content_before->setAttribute('auto', '');
            $property_content_before->setAttribute('inherit_value', 'F');
            $property_content_before->setAttribute('onchange-js', '');
            $property_content_before->setAttribute('onkeyup-js', '');
            $property_content_before->setAttribute('onkeydown-js', '');
            $property_content_before->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($property_content_before);
        } else {
            $this->writeLine('Property with tag content_before already exists');
            $this->setDataKey('property_content_before_existed', true);
        }

        // property: Content after(content_after)
        $property_content_after = $this->propertyManager()->propertyExistsByTag('content_after');
        if ($property_content_after === false) {
            $property_content_after = new \Buxus\Property\Types\Textarea();
            $property_content_after->setTag('content_after');
            $property_content_after->setDescription('Obsah na koniec');
            $property_content_after->setExtendedDescription('');
            $property_content_after->setName('Content after');
            $property_content_after->setClassId('4');
            $property_content_after->setShowType(NULL);
            $property_content_after->setShowTypeTag('textarea');
            $property_content_after->setValueType('multiline_text');
            $property_content_after->setDefaultValue('');
            $property_content_after->setMultiOperations(false);
            $property_content_after->setInputString('');
            $property_content_after->setAttribute('tab', '');
            $property_content_after->setAttribute('cols', '60');
            $property_content_after->setAttribute('rows', '3');
            $property_content_after->setAttribute('dhtml-edit', '1');
            $property_content_after->setAttribute('dhtml-configuration', 'full_no_p');
            $property_content_after->setAttribute('import-word', '0');
            $property_content_after->setAttribute('auto', '');
            $property_content_after->setAttribute('inherit_value', 'F');
            $property_content_after->setAttribute('onchange-js', '');
            $property_content_after->setAttribute('onkeyup-js', '');
            $property_content_after->setAttribute('onkeydown-js', '');
            $property_content_after->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($property_content_after);
        } else {
            $this->writeLine('Property with tag content_after already exists');
            $this->setDataKey('property_content_after_existed', true);
        }

        // page type: Container (layout_element_container)
        $page_type_layout_element_container = $this->pageTypesManager()->pageTypeExistsByTag('layout_element_container');
        if ($page_type_layout_element_container === false) {
            $page_type_layout_element_container = new \Buxus\PageType\PageType();
            $page_type_layout_element_container->setTag('layout_element_container');
            $page_type_layout_element_container->setName('Container');
            $page_type_layout_element_container->setPageClassId('1');
            $page_type_layout_element_container->setDefaultTemplateId('2');
            $page_type_layout_element_container->setDeleteTrigger('');
            $page_type_layout_element_container->setIncludeInSync(NULL);
            $page_type_layout_element_container->setPageDetailsLayout('');
            $page_type_layout_element_container->setPageSortTypeTag('sort_date_time');
            $page_type_layout_element_container->setPageTypeOrder('0');
            $page_type_layout_element_container->setPostmoveTrigger('');
            $page_type_layout_element_container->setPostsubmitTrigger('');
            $page_type_layout_element_container->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('abstract_layout_element');
            $page_type_layout_element_container->setParent($parent);
        } else {
            $this->writeLine('Page type with tag layout_element_container already exists');
            $this->setDataKey('page_type_layout_element_container_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('content_before');
        $property_id = $property->getId();
        $tmp = $page_type_layout_element_container->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('1');
            $tmp->setRequired(false);
            $page_type_layout_element_container->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('content_after');
        $property_id = $property->getId();
        $tmp = $page_type_layout_element_container->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('2');
            $tmp->setRequired(false);
            $page_type_layout_element_container->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($page_type_layout_element_container);

        // page type: Promoboxy (layout_element_promobox_list)
        $page_type_layout_element_promobox_list = $this->pageTypesManager()->pageTypeExistsByTag('layout_element_promobox_list');
        if ($page_type_layout_element_promobox_list === false) {
            $page_type_layout_element_promobox_list = new \Buxus\PageType\PageType();
            $page_type_layout_element_promobox_list->setTag('layout_element_promobox_list');
            $page_type_layout_element_promobox_list->setName('Promoboxy');
            $page_type_layout_element_promobox_list->setPageClassId('1');
            $page_type_layout_element_promobox_list->setDefaultTemplateId('1');
            $page_type_layout_element_promobox_list->setDeleteTrigger('');
            $page_type_layout_element_promobox_list->setIncludeInSync(NULL);
            $page_type_layout_element_promobox_list->setPageDetailsLayout('');
            $page_type_layout_element_promobox_list->setPageSortTypeTag('sort_date_time');
            $page_type_layout_element_promobox_list->setPageTypeOrder('0');
            $page_type_layout_element_promobox_list->setPostmoveTrigger('');
            $page_type_layout_element_promobox_list->setPostsubmitTrigger('');
            $page_type_layout_element_promobox_list->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('layout_element_container');
            $page_type_layout_element_promobox_list->setParent($parent);
        } else {
            $this->writeLine('Page type with tag layout_element_promobox_list already exists');
            $this->setDataKey('page_type_layout_element_promobox_list_existed', true);
        }
        $this->pageTypesManager()->savePageType($page_type_layout_element_promobox_list);

        // property: Obrázok(promo_bg_image)
        $property_promo_bg_image = $this->propertyManager()->propertyExistsByTag('promo_bg_image');
        if ($property_promo_bg_image === false) {
            $property_promo_bg_image = new \Buxus\Property\Types\Image();
            $property_promo_bg_image->setTag('promo_bg_image');
            $property_promo_bg_image->setDescription('Obrazok, ktorý sa nastaví ako pozadie pre promobox. Veĺkosť obrázka na výšku by nemala presiahnuť 130px. Obrázok na šírku by mal mať max. 150px.');
            $property_promo_bg_image->setExtendedDescription('');
            $property_promo_bg_image->setName('Pozadie promoboxu');
            $property_promo_bg_image->setClassId('4');
            $property_promo_bg_image->setShowType(NULL);
            $property_promo_bg_image->setShowTypeTag('image_name_upload');
            $property_promo_bg_image->setValueType('file');
            $property_promo_bg_image->setDefaultValue('');
            $property_promo_bg_image->setMultiOperations(false);
            $property_promo_bg_image->setInputString(NULL);
            $property_promo_bg_image->setAttribute('tab', '');
            $property_promo_bg_image->setAttribute('with_upload', 'T');
            $property_promo_bg_image->setAttribute('simple_upload', 'F');
            $property_promo_bg_image->setAttribute('show_input_element', 'T');
            $property_promo_bg_image->setAttribute('filename', '');
            $property_promo_bg_image->setAttribute('show_file_name', 'T');
            $property_promo_bg_image->setAttribute('file_type', 'image');
            $property_promo_bg_image->setAttribute('pattern', '');
            $property_promo_bg_image->setAttribute('show_thumbnail', 'T');
            $property_promo_bg_image->setAttribute('max_thumbnail_width', '150');
            $property_promo_bg_image->setAttribute('max_thumbnail_height', '80');
            $property_promo_bg_image->setAttribute('upload_subdir', '');
            $property_promo_bg_image->setAttribute('eval-code', '//$this-&gt;parsed_parm[\'upload_subdir\'] = \'my-dir\';');
            $property_promo_bg_image->setAttribute('onchange-js', '');
            $this->propertyManager()->saveProperty($property_promo_bg_image);
        } else {
            $this->writeLine('Property with tag promo_bg_image already exists');
            $this->setDataKey('property_promo_bg_image_existed', true);
        }

        // property: Text tlačidla promobox(promo_btn_text)
        $property_promo_btn_text = $this->propertyManager()->propertyExistsByTag('promo_btn_text');
        if ($property_promo_btn_text === false) {
            $property_promo_btn_text = new \Buxus\Property\Types\Input();
            $property_promo_btn_text->setTag('promo_btn_text');
            $property_promo_btn_text->setDescription('Text, ktorý sa zobrazí v tlačidle v promoboxe. Doporučuje sa používať max. 15 znakov pre správna zobrazenie na rôznych zariadeniach.');
            $property_promo_btn_text->setExtendedDescription('');
            $property_promo_btn_text->setName('Text tlačidla promobox');
            $property_promo_btn_text->setClassId('4');
            $property_promo_btn_text->setShowType(NULL);
            $property_promo_btn_text->setShowTypeTag('text');
            $property_promo_btn_text->setValueType('oneline_text');
            $property_promo_btn_text->setDefaultValue('');
            $property_promo_btn_text->setMultiOperations(false);
            $property_promo_btn_text->setInputString(NULL);
            $property_promo_btn_text->setAttribute('tab', '');
            $property_promo_btn_text->setAttribute('size', '60');
            $property_promo_btn_text->setAttribute('maxlength', '30');
            $property_promo_btn_text->setAttribute('readonly', 'F');
            $property_promo_btn_text->setAttribute('pattern', '');
            $property_promo_btn_text->setAttribute('inherit_value', 'F');
            $property_promo_btn_text->setAttribute('onchange-js', '');
            $property_promo_btn_text->setAttribute('onkeyup-js', '');
            $property_promo_btn_text->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_promo_btn_text);
        } else {
            $this->writeLine('Property with tag promo_btn_text already exists');
            $this->setDataKey('property_promo_btn_text_existed', true);
        }

        // property: Cieľová stránka(target_page)
        $property_target_page = $this->propertyManager()->propertyExistsByTag('target_page');
        if ($property_target_page === false) {
            $property_target_page = new PageList();
            $property_target_page->setTag('target_page');
            $property_target_page->setDescription('Stránka, ktorá sa načíta po kliknutí na tlačidlo v promoboxe.');
            $property_target_page->setExtendedDescription('');
            $property_target_page->setName('Cieľová stránka');
            $property_target_page->setClassId('4');
            $property_target_page->setShowType(NULL);
            $property_target_page->setShowTypeTag('page_list');
            $property_target_page->setValueType('page_list');
            $property_target_page->setDefaultValue('');
            $property_target_page->setMultiOperations(false);
            $property_target_page->setInputString(NULL);
            $property_target_page->setAttribute('tab', '');
            $property_target_page->setAttribute('root_page_id', '');
            $property_target_page->setAttribute('page_type_id', '');
            $property_target_page->setAttribute('default_sort', 'tblPages.sort_date_time');
            $property_target_page->setAttribute('advanced_mode', 'T');
            $property_target_page->setAttribute('external_url', 'T');
            $property_target_page->setAttribute('max_items', '1');
            $property_target_page->setAttribute('middle_col_width', '');
            $property_target_page->setAttribute('apply_user_rights', 'T');
            $property_target_page->setAttribute('property_for_link_name', 'title');
            $property_target_page->setAttribute('properties_for_search', 'tblPages.page_name,title');
            $this->propertyManager()->saveProperty($property_target_page);
        } else {
            $this->writeLine('Property with tag target_page already exists');
            $this->setDataKey('property_target_page_existed', true);
        }

        // property: Promobox text(promo_text)
        $property_promo_text = $this->propertyManager()->propertyExistsByTag('promo_text');
        if ($property_promo_text === false) {
            $property_promo_text = new \Buxus\Property\Types\Textarea();
            $property_promo_text->setTag('promo_text');
            $property_promo_text->setDescription('Text, ktorý sa zobrazí v promoboxe.');
            $property_promo_text->setExtendedDescription('');
            $property_promo_text->setName('Promobox text');
            $property_promo_text->setClassId('4');
            $property_promo_text->setShowType(NULL);
            $property_promo_text->setShowTypeTag('text');
            $property_promo_text->setValueType('oneline_text');
            $property_promo_text->setDefaultValue('');
            $property_promo_text->setMultiOperations(false);
            $property_promo_text->setInputString(NULL);
            $property_promo_text->setAttribute('tab', '');
            $property_promo_text->setAttribute('size', '60');
            $property_promo_text->setAttribute('maxlength', '');
            $property_promo_text->setAttribute('readonly', 'F');
            $property_promo_text->setAttribute('pattern', '');
            $property_promo_text->setAttribute('inherit_value', 'F');
            $property_promo_text->setAttribute('onchange-js', '');
            $property_promo_text->setAttribute('onkeyup-js', '');
            $property_promo_text->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_promo_text);
        } else {
            $this->writeLine('Property with tag promo_text already exists');
            $this->setDataKey('property_promo_text_existed', true);
        }

        // property: Farba pozadia(promobox_background)
        $property_promobox_background = $this->propertyManager()->propertyExistsByTag('promobox_background');
        if ($property_promobox_background === false) {
            $property_promobox_background = new \Buxus\Property\Types\Input();
            $property_promobox_background->setTag('promobox_background');
            $property_promobox_background->setDescription('Vyberte farbu a kliknutím na farebný kruh vpravo dole sa uloží jej hodnota do vlastnosti.');
            $property_promobox_background->setExtendedDescription('');
            $property_promobox_background->setName('Farba pozadia');
            $property_promobox_background->setClassId('4');
            $property_promobox_background->setShowType(NULL);
            $property_promobox_background->setShowTypeTag('text');
            $property_promobox_background->setValueType('oneline_text');
            $property_promobox_background->setDefaultValue('');
            $property_promobox_background->setMultiOperations(false);
            $property_promobox_background->setInputString('');
            $property_promobox_background->setAttribute('tab', 'Textov&yacute; blok');
            $property_promobox_background->setAttribute('size', '25');
            $property_promobox_background->setAttribute('maxlength', '');
            $property_promobox_background->setAttribute('readonly', 'F');
            $property_promobox_background->setAttribute('pattern', '');
            $property_promobox_background->setAttribute('inherit_value', 'F');
            $property_promobox_background->setAttribute('onchange-js', '');
            $property_promobox_background->setAttribute('onkeyup-js', '');
            $property_promobox_background->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_promobox_background);
        } else {
            $this->writeLine('Property with tag promobox_background already exists');
            $this->setDataKey('property_promobox_background_existed', true);
        }

        // page type: Promobox (layout_element_promobox)
        $page_type_layout_element_promobox = $this->pageTypesManager()->pageTypeExistsByTag('layout_element_promobox');
        if ($page_type_layout_element_promobox === false) {
            $page_type_layout_element_promobox = new \Buxus\PageType\PageType();
            $page_type_layout_element_promobox->setTag('layout_element_promobox');
            $page_type_layout_element_promobox->setName('Promobox');
            $page_type_layout_element_promobox->setPageClassId('1');
            $page_type_layout_element_promobox->setDefaultTemplateId('1');
            $page_type_layout_element_promobox->setDeleteTrigger('');
            $page_type_layout_element_promobox->setIncludeInSync('0');
            $page_type_layout_element_promobox->setPageDetailsLayout('');
            $page_type_layout_element_promobox->setPageSortTypeTag('sort_date_time');
            $page_type_layout_element_promobox->setPageTypeOrder('0');
            $page_type_layout_element_promobox->setPostmoveTrigger('');
            $page_type_layout_element_promobox->setPostsubmitTrigger('');
            $page_type_layout_element_promobox->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('abstract_layout_element');
            $page_type_layout_element_promobox->setParent($parent);
        } else {
            $this->writeLine('Page type with tag layout_element_promobox already exists');
            $this->setDataKey('page_type_layout_element_promobox_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('promo_bg_image');
        $property_id = $property->getId();
        $tmp = $page_type_layout_element_promobox->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('1');
            $tmp->setRequired(false);
            $page_type_layout_element_promobox->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('promo_btn_text');
        $property_id = $property->getId();
        $tmp = $page_type_layout_element_promobox->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('2');
            $tmp->setRequired(false);
            $page_type_layout_element_promobox->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('target_page');
        $property_id = $property->getId();
        $tmp = $page_type_layout_element_promobox->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('3');
            $tmp->setRequired(false);
            $page_type_layout_element_promobox->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('title');
        $property_id = $property->getId();
        $tmp = $page_type_layout_element_promobox->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('4');
            $tmp->setRequired(false);
            $page_type_layout_element_promobox->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('promo_text');
        $property_id = $property->getId();
        $tmp = $page_type_layout_element_promobox->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('4');
            $tmp->setRequired(false);
            $page_type_layout_element_promobox->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('promobox_background');
        $property_id = $property->getId();
        $tmp = $page_type_layout_element_promobox->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('5');
            $tmp->setRequired(false);
            $page_type_layout_element_promobox->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($page_type_layout_element_promobox);

        if ($this->pageTypeExists('layout_element_container')) {
            $this->addPageTypeSuperiorPageType('layout_element_container', 'layout_element_container');
        }
        if ($this->pageTypeExists('layout')) {
            $this->addPageTypeSuperiorPageType('layout_element_container', 'layout');
        }
        if ($this->pageTypeExists('layout_element_menu_link')) {
            $this->addPageTypeSuperiorPageType('layout_element_container', 'layout_element_menu_link');
        }
        if ($this->pageTypeExists('homepage')) {
            $this->addPageTypeSuperiorPageType('layout_element_promobox_list', 'homepage');
        }
        if ($this->pageTypeExists('layout_element_promobox_list')) {
            $this->addPageTypeSuperiorPageType('layout_element_promobox', 'layout_element_promobox_list');
        }

        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

    }

    public function down() {
        // remove page type: Promobox (layout_element_promobox)
        $page_type_layout_element_promobox = $this->pageTypesManager()->pageTypeExistsByTag('layout_element_promobox');
        if (($page_type_layout_element_promobox != false) && (is_null($this->getDataKey('page_type_layout_element_promobox_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_layout_element_promobox);
        }

        // remove property: Farba pozadia(promobox_background)
        $property_promobox_background = $this->propertyManager()->propertyExistsByTag('promobox_background');
        if ($property_promobox_background != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_promobox_background);
            if ((is_null($this->getDataKey('property_promobox_background_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_promobox_background);
            }
        }

        // remove property: Promobox text(promo_text)
        $property_promo_text = $this->propertyManager()->propertyExistsByTag('promo_text');
        if ($property_promo_text != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_promo_text);
            if ((is_null($this->getDataKey('property_promo_text_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_promo_text);
            }
        }

        // remove property: Cieľová stránka(target_page)
        $property_target_page = $this->propertyManager()->propertyExistsByTag('target_page');
        if ($property_target_page != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_target_page);
            if ((is_null($this->getDataKey('property_target_page_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_target_page);
            }
        }

        // remove property: Text tlačidla promobox(promo_btn_text)
        $property_promo_btn_text = $this->propertyManager()->propertyExistsByTag('promo_btn_text');
        if ($property_promo_btn_text != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_promo_btn_text);
            if ((is_null($this->getDataKey('property_promo_btn_text_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_promo_btn_text);
            }
        }

        // remove property: Obrázok(promo_bg_image)
        $property_promo_bg_image = $this->propertyManager()->propertyExistsByTag('promo_bg_image');
        if ($property_promo_bg_image != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_promo_bg_image);
            if ((is_null($this->getDataKey('property_promo_bg_image_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_promo_bg_image);
            }
        }

        // remove page type: Promoboxy (layout_element_promobox_list)
        $page_type_layout_element_promobox_list = $this->pageTypesManager()->pageTypeExistsByTag('layout_element_promobox_list');
        if (($page_type_layout_element_promobox_list != false) && (is_null($this->getDataKey('page_type_layout_element_promobox_list_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_layout_element_promobox_list);
        }

        // remove page type: Container (layout_element_container)
        $page_type_layout_element_container = $this->pageTypesManager()->pageTypeExistsByTag('layout_element_container');
        if (($page_type_layout_element_container != false) && (is_null($this->getDataKey('page_type_layout_element_container_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_layout_element_container);
        }

        // remove property: Content after(content_after)
        $property_content_after = $this->propertyManager()->propertyExistsByTag('content_after');
        if ($property_content_after != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_content_after);
            if ((is_null($this->getDataKey('property_content_after_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_content_after);
            }
        }

        // remove property: Content before(content_before)
        $property_content_before = $this->propertyManager()->propertyExistsByTag('content_before');
        if ($property_content_before != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_content_before);
            if ((is_null($this->getDataKey('property_content_before_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_content_before);
            }
        }

        // remove page type: Abstract layout element (abstract_layout_element)
        $page_type_abstract_layout_element = $this->pageTypesManager()->pageTypeExistsByTag('abstract_layout_element');
        if (($page_type_abstract_layout_element != false) && (is_null($this->getDataKey('page_type_abstract_layout_element_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_abstract_layout_element);
        }

        // remove property: CSS trieda(css_class)
        $property_css_class = $this->propertyManager()->propertyExistsByTag('css_class');
        if ($property_css_class != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_css_class);
            if ((is_null($this->getDataKey('property_css_class_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_css_class);
            }
        }

        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

    }
}
