<?php

namespace FullTextSearch\Migrations;

use Buxus\Migration\AbstractMigration;
use Buxus\Util\PageIds;

class FulltextSearch extends AbstractMigration
{
    public function up()
    {
        // page type: <PERSON><PERSON><PERSON>ino<PERSON> (folder)
        $pageTypeFolder = $this->pageTypesManager()->pageTypeExistsByTag('folder');
        if ($pageTypeFolder === false) {
            $pageTypeFolder = new \Buxus\PageType\PageType();
            $pageTypeFolder->setTag('folder');
            $pageTypeFolder->setName('Priečinok');
            $pageTypeFolder->setPageClassId('1');
            $pageTypeFolder->setDefaultTemplateId('2');
            $pageTypeFolder->setDeleteTrigger('');
            $pageTypeFolder->setIncludeInSync(null);
            $pageTypeFolder->setPageDetailsLayout('');
            $pageTypeFolder->setPageSortTypeTag('sort_date_time');
            $pageTypeFolder->setPageTypeOrder('30');
            $pageTypeFolder->setPostmoveTrigger('');
            $pageTypeFolder->setPostsubmitTrigger('');
            $pageTypeFolder->setPresubmitTrigger('');
            $pageTypeFolder->setParent(null);
        } else {
            $this->writeLine('Page type with tag folder already exists');
            $this->setDataKey('page_type_folder_existed', true);
        }
        $this->pageTypesManager()->savePageType($pageTypeFolder);

        if ($this->pageTypeExists('settings')) {
            $this->addPageTypeSuperiorPageType('folder', 'settings');
        }
        if ($this->pageTypeExists('folder')) {
            $this->addPageTypeSuperiorPageType('folder', 'folder');
        }
        if ($this->pageTypeExists('main_page')) {
            $this->addPageTypeSuperiorPageType('folder', 'main_page');
        }

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

        // page: Vyhľadávanie(ID: 508 TAG: search_service_pages)
        $page_id = $this->getPageIdByTag('search_service_pages');
        if ($page_id === null) {
            $page_type = $this->pageTypesManager()->getPageTypeByTag('folder');
            $page_508 = \PageFactory::create($this->getPageIdByTag('admin_settings'), $page_type->getId());
        } else {
            $page_508 = \PageFactory::get($page_id);
        }
        $page_508->setPageName('Vyhľadávanie');
        $page_508->setPageTag('search_service_pages');
        $page_508->setPageStateId('2');
        $page_508->setPageClassId('1');
        $page_508->save();

        // page: Výsledky vyhľadávania 1(ID: 444 TAG: Výsledky vyhľadávania)
        $page_type = $this->pageTypesManager()->getPageTypeByTag('section');
        $page_444 = \PageFactory::create($page_508->getPageId(), $page_type->getId());
        $page_444->setPageName('Výsledky vyhľadávania');
        $page_444->setPageTag('search_results');
        $page_444->setPageStateId('2');
        $page_444->setPageClassId('1');
        $page_444->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
        $page_444->setPropertyValue('title', 'Výsledky vyhľadávania');
        // set template fulltext-search::search
        $page_444->getPageTemplate()->setController('fulltext-search');
        $page_444->getPageTemplate()->setAction('search');
        $page_444->save();

        // page: Výsledky vyhľadávania - suggest(ID: 445 TAG: search_suggest)
        $page_type = $this->pageTypesManager()->getPageTypeByTag('section');
        $page_445 = \PageFactory::create($page_508->getPageId(), $page_type->getId());
        $page_445->setPageName('Výsledky vyhľadávania - suggest');
        $page_445->setPageTag('search_suggest');
        $page_445->setPageStateId('2');
        $page_445->setPageClassId('1');
        $page_445->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
        $page_445->setPropertyValue('title', 'Výsledky vyhľadávania - quicksearch');
        // set template fulltext-search::suggest
        $page_445->getPageTemplate()->setController('fulltext-search');
        $page_445->getPageTemplate()->setAction('suggest');
        $page_445->save();

        // regenerate page tags
        PageIds::generatePageTagsList();
    }

    public function down()
    {
        // remove page: Vyhľadávanie (search_service_pages)
        $page_id = $this->getPageIdByTag('search_service_pages');
        if ($page_id !== null) {
            \PageFactory::get($page_id)->delete();
        }

        // regenerate page tags
        PageIds::generatePageTagsList();

        // remove page type: Priečinok (folder)
        $page_type_folder = $this->pageTypesManager()->pageTypeExistsByTag('folder');
        if (($page_type_folder != false) && ($this->getDataKey('page_type_folder_existed') === null)) {
            $this->pageTypesManager()->removePageType($page_type_folder);
        }

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();
    }
}
