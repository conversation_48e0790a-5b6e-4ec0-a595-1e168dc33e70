<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Util\PageIds;

class SetOgTitleTreePropertyValueForProductCatalogPages extends AbstractMigration
{
    protected $pageTypeTags = [
        'eshop_category',
        'eshop_product',
    ];

    public function up()
    {
        if (!class_exists(\Buxus\OpenGraph\OpenGraphDataProviderModule::class)) {
            return;
        }

        if (!$this->propertyManager()->propertyExistsByTag('og_title')) {
            return;
        }

        $property = $this->propertyManager()->getPropertyByTag('og_title');

        foreach ($this->pageTypeTags as $pageTypeTag) {
            if (! $this->pageTypeExists($pageTypeTag)) {
                continue;
            }

            $this->addPropertyToPageType('og_title', $pageTypeTag);

            $pageType = $this->pageTypesManager()->getPageTypeByTag($pageTypeTag);


            \DB::table('tblTreeProperties')->insertOrIgnore([
                'page_id' => PageIds::getPageId('root_page'),
                'property_id' => $property->getId(),
                'page_type_id' => $pageType->getId(),
                'property_value' => '<buxus-prop>title</buxus-prop>',
            ]);
            \TreeProperty\Resolver::getInstance()->invalidateDefinitions($property->getId());
        }
    }

    public function down()
    {
        if (!class_exists(\Buxus\OpenGraph\OpenGraphDataProviderModule::class)) {
            return;
        }

        if (!$this->propertyManager()->propertyExistsByTag('og_title')) {
            return;
        }

        $property = $this->propertyManager()->getPropertyByTag('og_title');

        foreach ($this->pageTypeTags as $pageTypeTag) {
            if (! $this->pageTypeExists($pageTypeTag)) {
                continue;
            }

            $this->deletePropertyFromPageType('og_title', $pageTypeTag);

            $pageType = $this->pageTypesManager()->getPageTypeByTag($pageTypeTag);
            \DB::table('tblTreeProperties')
                ->where('page_id', PageIds::getPageId('root_page'))
                ->where('property_id', $property->getId())
                ->where('page_type_id', $pageType->getId())
                ->delete();
            \TreeProperty\Resolver::getInstance()->invalidateDefinitions($property->getId());
        }
    }
}
