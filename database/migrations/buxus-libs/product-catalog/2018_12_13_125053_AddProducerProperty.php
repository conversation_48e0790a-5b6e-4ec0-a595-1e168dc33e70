<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (buxus7_clean_install) at 2018-12-13 12:50:53
 */
class AddProducerProperty extends AbstractMigration
{
    public function dependencies()
    {
        return [
            \Eshop\Catalog\Migrations\EshopCatalogue::class,
        ];
    }

    public function up()
    {
        // property: Výrobca(eshop_roller_producer)
        $property_producer = $this->propertyManager()->propertyExistsByTag('eshop_roller_producer');
        if ($property_producer === false) {
            $property_producer = new Property();
            $property_producer->setTag('eshop_roller_producer');
            $property_producer->setDescription('Výrobca produktu');
            $property_producer->setExtendedDescription('');
            $property_producer->setName('Výrobca');
            $property_producer->setClassId(4);
            $property_producer->setShowType(NULL);
            $property_producer->setShowTypeTag('custom_property');
            $property_producer->setValueType('custom_property');
            $property_producer->setDefaultValue('');
            $property_producer->setMultiOperations(false);
            $property_producer->setInputString('');
            $property_producer->setAttribute('tab', '');
            $property_producer->setAttribute('class_name', 'Eshop\\Catalog\\Property\\RollerProducerProperty');
            $property_producer->setAttribute('inherit_value', 'F');
            $this->propertyManager()->saveProperty($property_producer);
        } else {
            $this->writeLine('Property with tag eshop_roller_producer already exists');
            $this->setDataKey('property_eshop_roller_producer_existed', true);
        }

        $this->addPropertyToPageType('eshop_roller_producer', 'eshop_product');

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Výrobca(eshop_roller_producer)
        $property_producer = $this->propertyManager()->propertyExistsByTag('eshop_roller_producer');
        if ($property_producer != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_producer);
            if ((is_null($this->getDataKey('property_eshop_roller_producer_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_producer);
            }
        }

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
