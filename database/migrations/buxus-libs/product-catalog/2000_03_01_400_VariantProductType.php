<?php

namespace Eshop\Catalog\Migrations;

use Buxus\Migration\AbstractMigration;
use Buxus\PageType\PageType;
use Buxus\PageType\PageTypePropertyItem;
use Buxus\Property\Types\Input;

class VariantProductType extends AbstractMigration
{
    public function up()
    {
        // page type: Eshop (eshop)
        $page_type_eshop = $this->pageTypesManager()->pageTypeExistsByTag('eshop');
        if ($page_type_eshop === false) {
            $page_type_eshop = new PageType();
            $page_type_eshop->setTag('eshop');
            $page_type_eshop->setName('Eshop');
            $page_type_eshop->setPageClassId(1);
            $page_type_eshop->setDefaultTemplateId(2);
            $page_type_eshop->setDeleteTrigger('');
            $page_type_eshop->setIncludeInSync(NULL);
            $page_type_eshop->setPageDetailsLayout('');
            $page_type_eshop->setPageSortTypeTag('sort_date_time');
            $page_type_eshop->setPageTypeOrder(0);
            $page_type_eshop->setPostmoveTrigger('');
            $page_type_eshop->setPostsubmitTrigger('');
            $page_type_eshop->setPresubmitTrigger('');
            $page_type_eshop->setParent(NULL);
        } else {
            $this->writeLine('Page type with tag eshop already exists');
            $this->setDataKey('page_type_eshop_existed', true);
        }
        $this->pageTypesManager()->savePageType($page_type_eshop);

        // property: Titulok(title)
        $property_title = $this->propertyManager()->propertyExistsByTag('title');
        if ($property_title === false) {
            $property_title = new Input();
            $property_title->setTag('title');
            $property_title->setDescription('Štandardný názov stránky. Text, ktorý sa zobrazí v záložke prehliadača. Podľa SEO odporúčaní max. 65 znakov.');
            $property_title->setExtendedDescription('');
            $property_title->setName('Titulok');
            $property_title->setClassId(4);
            $property_title->setShowType(NULL);
            $property_title->setShowTypeTag('text');
            $property_title->setValueType('oneline_text');
            $property_title->setDefaultValue('');
            $property_title->setMultiOperations(false);
            $property_title->setInputString(NULL);
            $property_title->setAttribute('tab', '');
            $property_title->setAttribute('size', '60');
            $property_title->setAttribute('maxlength', '');
            $property_title->setAttribute('readonly', 'F');
            $property_title->setAttribute('pattern', '');
            $property_title->setAttribute('inherit_value', 'F');
            $property_title->setAttribute('onchange-js', '');
            $property_title->setAttribute('onkeyup-js', '');
            $property_title->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_title);
        } else {
            $this->writeLine('Property with tag title already exists');
            $this->setDataKey('property_title_existed', true);
        }

        // property: Cena EUR bez DPH(eshop_eur_price_without_vat)
        $property_eshop_eur_price_without_vat = $this->propertyManager()->propertyExistsByTag('eshop_eur_price_without_vat');
        if ($property_eshop_eur_price_without_vat === false) {
            $property_eshop_eur_price_without_vat = new Input();
            $property_eshop_eur_price_without_vat->setTag('eshop_eur_price_without_vat');
            $property_eshop_eur_price_without_vat->setDescription('Cena v EUR bez DPH.');
            $property_eshop_eur_price_without_vat->setExtendedDescription('');
            $property_eshop_eur_price_without_vat->setName('Cena EUR bez DPH');
            $property_eshop_eur_price_without_vat->setClassId(4);
            $property_eshop_eur_price_without_vat->setShowType(NULL);
            $property_eshop_eur_price_without_vat->setShowTypeTag('text');
            $property_eshop_eur_price_without_vat->setValueType('oneline_text');
            $property_eshop_eur_price_without_vat->setDefaultValue('');
            $property_eshop_eur_price_without_vat->setMultiOperations(false);
            $property_eshop_eur_price_without_vat->setInputString(NULL);
            $property_eshop_eur_price_without_vat->setAttribute('tab', '');
            $property_eshop_eur_price_without_vat->setAttribute('size', '10');
            $property_eshop_eur_price_without_vat->setAttribute('maxlength', '');
            $property_eshop_eur_price_without_vat->setAttribute('readonly', 'F');
            $property_eshop_eur_price_without_vat->setAttribute('pattern', '');
            $property_eshop_eur_price_without_vat->setAttribute('inherit_value', 'F');
            $property_eshop_eur_price_without_vat->setAttribute('onchange-js', '');
            $property_eshop_eur_price_without_vat->setAttribute('onkeyup-js', '');
            $property_eshop_eur_price_without_vat->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_eshop_eur_price_without_vat);
        } else {
            $this->writeLine('Property with tag eshop_eur_price_without_vat already exists');
            $this->setDataKey('property_eshop_eur_price_without_vat_existed', true);
        }

        // property: Veľkosť(size)
        $property_size = $this->propertyManager()->propertyExistsByTag('size');
        if ($property_size === false) {
            $property_size = new Input();
            $property_size->setTag('size');
            $property_size->setDescription('Veľkosť: XS,S,M,L,XL ...');
            $property_size->setExtendedDescription('');
            $property_size->setName('Veľkosť');
            $property_size->setClassId(4);
            $property_size->setShowType(NULL);
            $property_size->setShowTypeTag('text');
            $property_size->setValueType('oneline_text');
            $property_size->setDefaultValue('');
            $property_size->setMultiOperations(false);
            $property_size->setInputString('');
            $property_size->setAttribute('tab', '');
            $property_size->setAttribute('size', '10');
            $property_size->setAttribute('maxlength', '');
            $property_size->setAttribute('readonly', 'F');
            $property_size->setAttribute('pattern', '');
            $property_size->setAttribute('inherit_value', 'F');
            $property_size->setAttribute('onchange-js', '');
            $property_size->setAttribute('onkeyup-js', '');
            $property_size->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_size);
        } else {
            $this->writeLine('Property with tag size already exists');
            $this->setDataKey('property_size_existed', true);
        }

        // page type: Variant produktu (eshop_product_variant)
        $page_type_eshop_product_variant = $this->pageTypesManager()->pageTypeExistsByTag('eshop_product_variant');
        if ($page_type_eshop_product_variant === false) {
            $page_type_eshop_product_variant = new PageType();
            $page_type_eshop_product_variant->setTag('eshop_product_variant');
            $page_type_eshop_product_variant->setName('Variant produktu');
            $page_type_eshop_product_variant->setPageClassId(1);
            $page_type_eshop_product_variant->setDefaultTemplateId(1);
            $page_type_eshop_product_variant->setDeleteTrigger('');
            $page_type_eshop_product_variant->setIncludeInSync(NULL);
            $page_type_eshop_product_variant->setPageDetailsLayout('');
            $page_type_eshop_product_variant->setPageSortTypeTag('sort_date_time');
            $page_type_eshop_product_variant->setPageTypeOrder(0);
            $page_type_eshop_product_variant->setPostmoveTrigger('');
            $page_type_eshop_product_variant->setPostsubmitTrigger('');
            $page_type_eshop_product_variant->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('eshop');
            $page_type_eshop_product_variant->setParent($parent);
        } else {
            $this->writeLine('Page type with tag eshop_product_variant already exists');
            $this->setDataKey('page_type_eshop_product_variant_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('title');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_product_variant->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(1);
            $tmp->setRequired(false);
            $page_type_eshop_product_variant->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('eshop_eur_price_without_vat');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_product_variant->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(2);
            $tmp->setRequired(false);
            $page_type_eshop_product_variant->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('size');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_product_variant->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(3);
            $tmp->setRequired(false);
            $page_type_eshop_product_variant->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($page_type_eshop_product_variant);

        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('eshop_product_variant'), 'product-catalog', 'product');

        if ($this->pageTypeExists('eshop_product')) {
            $this->addPageTypeSuperiorPageType('eshop_product_variant', 'eshop_product');
        }

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

        // regenerate property constants
        $this->propertyManager()->generateConstants();

    }

    public function down()
    {
        // remove page type: Variant produktu (eshop_product_variant)
        $page_type_eshop_product_variant = $this->pageTypesManager()->pageTypeExistsByTag('eshop_product_variant');
        if (($page_type_eshop_product_variant != false) && (is_null($this->getDataKey('page_type_eshop_product_variant_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_eshop_product_variant);
        }

        // remove property: Veľkosť(size)
        $property_size = $this->propertyManager()->propertyExistsByTag('size');
        if ($property_size != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_size);
            if ((is_null($this->getDataKey('property_size_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_size);
            }
        }

        // remove property: Cena EUR bez DPH(eshop_eur_price_without_vat)
        $property_eshop_eur_price_without_vat = $this->propertyManager()->propertyExistsByTag('eshop_eur_price_without_vat');
        if ($property_eshop_eur_price_without_vat != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_eshop_eur_price_without_vat);
            if ((is_null($this->getDataKey('property_eshop_eur_price_without_vat_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_eshop_eur_price_without_vat);
            }
        }

        // remove property: Titulok(title)
        $property_title = $this->propertyManager()->propertyExistsByTag('title');
        if ($property_title != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_title);
            if ((is_null($this->getDataKey('property_title_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_title);
            }
        }

        // remove page type: Eshop (eshop)
        $page_type_eshop = $this->pageTypesManager()->pageTypeExistsByTag('eshop');
        if (($page_type_eshop != false) && (is_null($this->getDataKey('page_type_eshop_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_eshop);
        }

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
