<?php

namespace Eshop\Catalog\Migrations;

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;
use Buxus\Util\PageIds;
use TreeProperty\Property\TreeProperty;

class EshopCatalogue extends AbstractMigration
{
    public function up()
    {
        // page type: Eshop (eshop)
        $page_type_eshop = $this->pageTypesManager()->pageTypeExistsByTag('eshop');
        if ($page_type_eshop === false) {
            $page_type_eshop = new \Buxus\PageType\PageType();
            $page_type_eshop->setTag('eshop');
            $page_type_eshop->setName('Eshop');
            $page_type_eshop->setPageClassId('1');
            $page_type_eshop->setDefaultTemplateId('2');
            $page_type_eshop->setDeleteTrigger('');
            $page_type_eshop->setIncludeInSync(NULL);
            $page_type_eshop->setPageDetailsLayout('');
            $page_type_eshop->setPageSortTypeTag('sort_date_time');
            $page_type_eshop->setPageTypeOrder('0');
            $page_type_eshop->setPostmoveTrigger('');
            $page_type_eshop->setPostsubmitTrigger('');
            $page_type_eshop->setPresubmitTrigger('');
            $page_type_eshop->setParent(NULL);

        } else {
            $this->writeLine('Page type with tag eshop already exists');
            $this->setDataKey('page_type_eshop_existed', true);
        }
        $this->pageTypesManager()->savePageType($page_type_eshop);

        // property: Titulok(title)
        $property_title = $this->propertyManager()->propertyExistsByTag('title');
        if ($property_title === false) {
            $property_title = new \Buxus\Property\Types\Input();
            $property_title->setTag('title');
            $property_title->setDescription('Štandardný názov stránky. Text, ktorý sa zobrazí v záložke prehliadača. Podľa SEO odporúčaní max. 65 znakov.');
            $property_title->setExtendedDescription('<p><img src="/buxus/images//tt01_1.JPG" alt="Text stránky v záložke prehliadača" title="Text stránky v záložke prehliadača" /></p>');
            $property_title->setName('Titulok');
            $property_title->setClassId('4');
            $property_title->setShowType(NULL);
            $property_title->setShowTypeTag('text');
            $property_title->setValueType('oneline_text');
            $property_title->setDefaultValue('');
            $property_title->setMultiOperations(false);
            $property_title->setInputString(NULL);
            $property_title->setAttribute('tab', '');
            $property_title->setAttribute('size', '60');
            $property_title->setAttribute('maxlength', '');
            $property_title->setAttribute('readonly', 'F');
            $property_title->setAttribute('pattern', '');
            $property_title->setAttribute('inherit_value', 'F');
            $property_title->setAttribute('onchange-js', '');
            $property_title->setAttribute('onkeyup-js', '');
            $property_title->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_title);

        } else {
            $this->writeLine('Property with tag title already exists');
            $this->setDataKey('property_title_existed', true);
        }

        // property: URL adresa spoločnosti(eshop_company_url)
        $property_eshop_company_url = $this->propertyManager()->propertyExistsByTag('eshop_company_url');
        if ($property_eshop_company_url === false) {
            $property_eshop_company_url = new \Buxus\Property\Types\Input();
            $property_eshop_company_url->setTag('eshop_company_url');
            $property_eshop_company_url->setDescription('URL adresa spoločnosti.');
            $property_eshop_company_url->setExtendedDescription(NULL);
            $property_eshop_company_url->setName('URL adresa spoločnosti');
            $property_eshop_company_url->setClassId('4');
            $property_eshop_company_url->setShowType(NULL);
            $property_eshop_company_url->setShowTypeTag('text');
            $property_eshop_company_url->setValueType('oneline_text');
            $property_eshop_company_url->setDefaultValue('');
            $property_eshop_company_url->setMultiOperations(false);
            $property_eshop_company_url->setInputString(NULL);
            $property_eshop_company_url->setAttribute('tab', '');
            $property_eshop_company_url->setAttribute('size', '60');
            $property_eshop_company_url->setAttribute('maxlength', '');
            $property_eshop_company_url->setAttribute('readonly', '0');
            $property_eshop_company_url->setAttribute('pattern', '');
            $property_eshop_company_url->setAttribute('inherit_value', '0');
            $property_eshop_company_url->setAttribute('onchange-js', '');
            $property_eshop_company_url->setAttribute('onkeyup-js', '');
            $property_eshop_company_url->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_eshop_company_url);

        } else {
            $this->writeLine('Property with tag eshop_company_url already exists');
            $this->setDataKey('property_eshop_company_url_existed', true);
        }

        // property: Anotácia(annotation)
        $property_annotation = $this->propertyManager()->propertyExistsByTag('annotation');
        if ($property_annotation === false) {
            $property_annotation = new \Buxus\Property\Types\Textarea();
            $property_annotation->setTag('annotation');
            $property_annotation->setDescription('Štandardná anotácia stránky.');
            $property_annotation->setExtendedDescription('');
            $property_annotation->setName('Anotácia');
            $property_annotation->setClassId('4');
            $property_annotation->setShowType(NULL);
            $property_annotation->setShowTypeTag('textarea');
            $property_annotation->setValueType('multiline_text');
            $property_annotation->setDefaultValue('');
            $property_annotation->setMultiOperations(false);
            $property_annotation->setInputString(NULL);
            $property_annotation->setAttribute('tab', '');
            $property_annotation->setAttribute('cols', '60');
            $property_annotation->setAttribute('rows', '');
            $property_annotation->setAttribute('dhtml-edit', '1');
            $property_annotation->setAttribute('dhtml-configuration', 'full');
            $property_annotation->setAttribute('import-word', '0');
            $property_annotation->setAttribute('auto', '1');
            $property_annotation->setAttribute('inherit_value', 'F');
            $property_annotation->setAttribute('onchange-js', '');
            $property_annotation->setAttribute('onkeyup-js', '');
            $property_annotation->setAttribute('onkeydown-js', '');
            $property_annotation->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($property_annotation);

        } else {
            $this->writeLine('Property with tag annotation already exists');
            $this->setDataKey('property_annotation_existed', true);
        }

        // property: Text(text)
        $property_text = $this->propertyManager()->propertyExistsByTag('text');
        if ($property_text === false) {
            $property_text = new \Buxus\Property\Types\Textarea();
            $property_text->setTag('text');
            $property_text->setDescription('Štandardný text stránky.');
            $property_text->setExtendedDescription('');
            $property_text->setName('Text');
            $property_text->setClassId('4');
            $property_text->setShowType(NULL);
            $property_text->setShowTypeTag('textarea');
            $property_text->setValueType('multiline_text');
            $property_text->setDefaultValue('');
            $property_text->setMultiOperations(false);
            $property_text->setInputString(NULL);
            $property_text->setAttribute('tab', '');
            $property_text->setAttribute('cols', '60');
            $property_text->setAttribute('rows', '');
            $property_text->setAttribute('dhtml-edit', '1');
            $property_text->setAttribute('dhtml-configuration', 'full');
            $property_text->setAttribute('import-word', '0');
            $property_text->setAttribute('auto', '1');
            $property_text->setAttribute('inherit_value', 'F');
            $property_text->setAttribute('onchange-js', '');
            $property_text->setAttribute('onkeyup-js', '');
            $property_text->setAttribute('onkeydown-js', '');
            $property_text->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($property_text);

        } else {
            $this->writeLine('Property with tag text already exists');
            $this->setDataKey('property_text_existed', true);
        }

        // property: Logo spoločnosti(eshop_company_logo)
        $property_eshop_company_logo = $this->propertyManager()->propertyExistsByTag('eshop_company_logo');
        if ($property_eshop_company_logo === false) {
            $property_eshop_company_logo = new \Buxus\Property\Types\Image();
            $property_eshop_company_logo->setTag('eshop_company_logo');
            $property_eshop_company_logo->setDescription('Logo spoločnosti.');
            $property_eshop_company_logo->setExtendedDescription(NULL);
            $property_eshop_company_logo->setName('Logo spoločnosti');
            $property_eshop_company_logo->setClassId('4');
            $property_eshop_company_logo->setShowType(NULL);
            $property_eshop_company_logo->setShowTypeTag('image_name_upload');
            $property_eshop_company_logo->setValueType('file');
            $property_eshop_company_logo->setDefaultValue('');
            $property_eshop_company_logo->setMultiOperations(false);
            $property_eshop_company_logo->setInputString(NULL);
            $property_eshop_company_logo->setAttribute('tab', '');
            $property_eshop_company_logo->setAttribute('with_upload', '0');
            $property_eshop_company_logo->setAttribute('simple_upload', '0');
            $property_eshop_company_logo->setAttribute('show_input_element', 'T');
            $property_eshop_company_logo->setAttribute('filename', '');
            $property_eshop_company_logo->setAttribute('show_file_name', 'T');
            $property_eshop_company_logo->setAttribute('file_type', 'image');
            $property_eshop_company_logo->setAttribute('pattern', '');
            $property_eshop_company_logo->setAttribute('show_thumbnail', 'T');
            $property_eshop_company_logo->setAttribute('max_thumbnail_width', 150);
            $property_eshop_company_logo->setAttribute('max_thumbnail_height', 80);
            $property_eshop_company_logo->setAttribute('upload_subdir', '');
            $property_eshop_company_logo->setAttribute('eval-code', '//$this-&gt;parsed_parm[\'upload_subdir\'] = \'my-dir\';');
            $property_eshop_company_logo->setAttribute('onchange-js', '');
            $this->propertyManager()->saveProperty($property_eshop_company_logo);

        } else {
            $this->writeLine('Property with tag eshop_company_logo already exists');
            $this->setDataKey('property_eshop_company_logo_existed', true);
        }

        // property: SEO URL name(seo_url_name)
        $property_seo_url_name = $this->propertyManager()->propertyExistsByTag('seo_url_name');
        if ($property_seo_url_name === false) {
            $property_seo_url_name = new Property();
            $property_seo_url_name->setTag('seo_url_name');
            $property_seo_url_name->setDescription('Podľa SEO odporúčaní max. 35 znakov.');
            $property_seo_url_name->setExtendedDescription('');
            $property_seo_url_name->setName('SEO URL name');
            $property_seo_url_name->setClassId('4');
            $property_seo_url_name->setShowType(NULL);
            $property_seo_url_name->setShowTypeTag('seo_url_name');
            $property_seo_url_name->setValueType('seo_url_name');
            $property_seo_url_name->setDefaultValue('');
            $property_seo_url_name->setMultiOperations(false);
            $property_seo_url_name->setInputString(NULL);
            $property_seo_url_name->setAttribute('tab', 'SEO');
            $property_seo_url_name->setAttribute('size', '80');
            $property_seo_url_name->setAttribute('onchange-js', '');
            $property_seo_url_name->setAttribute('onkeyup-js', '');
            $property_seo_url_name->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_seo_url_name);

        } else {
            $this->writeLine('Property with tag seo_url_name already exists');
            $this->setDataKey('property_seo_url_name_existed', true);
        }

        // property: Obrázok(promo_bg_image)
        $property_promo_bg_image = $this->propertyManager()->propertyExistsByTag('promo_bg_image');
        if ($property_promo_bg_image === false) {
            $property_promo_bg_image = new \Buxus\Property\Types\Image();
            $property_promo_bg_image->setTag('promo_bg_image');
            $property_promo_bg_image->setDescription('Obrazok, ktorý sa nastaví ako pozadie pre promobox. Veĺkosť obrázka na výšku by nemala presiahnuť 130px. Obrázok na šírku by mal mať max. 150px.  ');
            $property_promo_bg_image->setExtendedDescription('');
            $property_promo_bg_image->setName('Obrázok');
            $property_promo_bg_image->setClassId('4');
            $property_promo_bg_image->setShowType(NULL);
            $property_promo_bg_image->setShowTypeTag('image_name_upload');
            $property_promo_bg_image->setValueType('file');
            $property_promo_bg_image->setDefaultValue('');
            $property_promo_bg_image->setMultiOperations(false);
            $property_promo_bg_image->setInputString(NULL);
            $property_promo_bg_image->setAttribute('tab', '');
            $property_promo_bg_image->setAttribute('with_upload', 'T');
            $property_promo_bg_image->setAttribute('simple_upload', 'F');
            $property_promo_bg_image->setAttribute('show_input_element', 'T');
            $property_promo_bg_image->setAttribute('filename', '');
            $property_promo_bg_image->setAttribute('show_file_name', 'T');
            $property_promo_bg_image->setAttribute('file_type', 'image');
            $property_promo_bg_image->setAttribute('pattern', '');
            $property_promo_bg_image->setAttribute('show_thumbnail', 'T');
            $property_promo_bg_image->setAttribute('max_thumbnail_width', '150');
            $property_promo_bg_image->setAttribute('max_thumbnail_height', '80');
            $property_promo_bg_image->setAttribute('upload_subdir', '');
            $property_promo_bg_image->setAttribute('eval-code', '//$this-&gt;parsed_parm[\'upload_subdir\'] = \'my-dir\';');
            $property_promo_bg_image->setAttribute('onchange-js', '');
            $this->propertyManager()->saveProperty($property_promo_bg_image);
        } else {
            $this->writeLine('Property with tag promo_bg_image already exists');
            $this->setDataKey('property_promo_bg_image_existed', true);
        }

        // page type: Výrobca (eshop_producer)
        $page_type_eshop_producer = $this->pageTypesManager()->pageTypeExistsByTag('eshop_producer');
        if ($page_type_eshop_producer === false) {
            $page_type_eshop_producer = new \Buxus\PageType\PageType();
            $page_type_eshop_producer->setTag('eshop_producer');
            $page_type_eshop_producer->setName('Výrobca');
            $page_type_eshop_producer->setPageClassId('1');
            $page_type_eshop_producer->setDefaultTemplateId('2');
            $page_type_eshop_producer->setDeleteTrigger('');
            $page_type_eshop_producer->setIncludeInSync('0');
            $page_type_eshop_producer->setPageDetailsLayout('');
            $page_type_eshop_producer->setPageSortTypeTag('sort_date_time');
            $page_type_eshop_producer->setPageTypeOrder('40');
            $page_type_eshop_producer->setPostmoveTrigger('');
            $page_type_eshop_producer->setPostsubmitTrigger('');
            $page_type_eshop_producer->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('eshop');
            $page_type_eshop_producer->setParent($parent);

        } else {
            $this->writeLine('Page type with tag eshop_producer already exists');
            $this->setDataKey('page_type_eshop_producer_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('title');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_producer->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('1');
            $tmp->setRequired(false);
            $page_type_eshop_producer->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('eshop_company_url');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_producer->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('2');
            $tmp->setRequired(false);
            $page_type_eshop_producer->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('annotation');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_producer->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('3');
            $tmp->setRequired(false);
            $page_type_eshop_producer->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('text');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_producer->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('4');
            $tmp->setRequired(false);
            $page_type_eshop_producer->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('eshop_company_logo');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_producer->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('5');
            $tmp->setRequired(false);
            $page_type_eshop_producer->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('seo_url_name');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_producer->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('6');
            $tmp->setRequired(false);
            $page_type_eshop_producer->addPropertyItem($tmp);
        }
        if ($this->pageTypeExists('eshop_catalog')) {
            $page_type_eshop_producer->addSuperiorPageType($this->getPageTypeByTag('eshop_catalog'));
        }
        $this->pageTypesManager()->savePageType($page_type_eshop_producer);
        // set template on MAIN PAGE index::index
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('eshop_producer'), 'index', 'index');

        // property: Sadzba DPH %(eshop_vat_rate)
        $property_eshop_vat_rate = $this->propertyManager()->propertyExistsByTag('eshop_vat_rate');
        if ($property_eshop_vat_rate === false) {
            $property_eshop_vat_rate = new \Buxus\Property\Types\Input();
            $property_eshop_vat_rate->setTag('eshop_vat_rate');
            $property_eshop_vat_rate->setDescription('DPH sadzba v %');
            $property_eshop_vat_rate->setExtendedDescription('');
            $property_eshop_vat_rate->setName('Sadzba DPH %');
            $property_eshop_vat_rate->setClassId('4');
            $property_eshop_vat_rate->setShowType(NULL);
            $property_eshop_vat_rate->setShowTypeTag('text');
            $property_eshop_vat_rate->setValueType('oneline_text');
            $property_eshop_vat_rate->setDefaultValue('');
            $property_eshop_vat_rate->setMultiOperations(false);
            $property_eshop_vat_rate->setInputString(NULL);
            $property_eshop_vat_rate->setAttribute('tab', '_Administrator');
            $property_eshop_vat_rate->setAttribute('size', '10');
            $property_eshop_vat_rate->setAttribute('maxlength', '');
            $property_eshop_vat_rate->setAttribute('readonly', 'F');
            $property_eshop_vat_rate->setAttribute('pattern', '');
            $property_eshop_vat_rate->setAttribute('inherit_value', 'T');
            $property_eshop_vat_rate->setAttribute('onchange-js', '');
            $property_eshop_vat_rate->setAttribute('onkeyup-js', '');
            $property_eshop_vat_rate->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_eshop_vat_rate);

        } else {
            $this->writeLine('Property with tag eshop_vat_rate already exists');
            $this->setDataKey('property_eshop_vat_rate_existed', true);
        }

        // page type: Katalóg produktov (eshop_catalog)
        $page_type_eshop_catalog = $this->pageTypesManager()->pageTypeExistsByTag('eshop_catalog');
        if ($page_type_eshop_catalog === false) {
            $page_type_eshop_catalog = new \Buxus\PageType\PageType();
            $page_type_eshop_catalog->setTag('eshop_catalog');
            $page_type_eshop_catalog->setName('Katalóg produktov');
            $page_type_eshop_catalog->setPageClassId('1');
            $page_type_eshop_catalog->setDefaultTemplateId('2');
            $page_type_eshop_catalog->setDeleteTrigger('');
            $page_type_eshop_catalog->setIncludeInSync('0');
            $page_type_eshop_catalog->setPageDetailsLayout('');
            $page_type_eshop_catalog->setPageSortTypeTag('sort_date_time');
            $page_type_eshop_catalog->setPageTypeOrder('1');
            $page_type_eshop_catalog->setPostmoveTrigger('');
            $page_type_eshop_catalog->setPostsubmitTrigger('');
            $page_type_eshop_catalog->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('eshop');
            $page_type_eshop_catalog->setParent($parent);

        } else {
            $this->writeLine('Page type with tag eshop_catalog already exists');
            $this->setDataKey('page_type_eshop_catalog_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('title');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_catalog->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('1');
            $tmp->setRequired(false);
            $page_type_eshop_catalog->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('eshop_vat_rate');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_catalog->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('2');
            $tmp->setRequired(false);
            $page_type_eshop_catalog->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('seo_url_name');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_catalog->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('3');
            $tmp->setRequired(false);
            $page_type_eshop_catalog->addPropertyItem($tmp);
        }
        if ($this->pageTypeExists('main_page')) {
            $page_type_eshop_catalog->addSuperiorPageType($this->getPageTypeByTag('main_page'));
        }
        $this->pageTypesManager()->savePageType($page_type_eshop_catalog);
        // set template on MAIN PAGE eshop_catalog::list
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('eshop_catalog'), 'product-catalog', 'list');

        // page type: Eshop kategórie (eshop_categories)
        $page_type_eshop_categories = $this->pageTypesManager()->pageTypeExistsByTag('eshop_categories');
        if ($page_type_eshop_categories === false) {
            $page_type_eshop_categories = new \Buxus\PageType\PageType();
            $page_type_eshop_categories->setTag('eshop_categories');
            $page_type_eshop_categories->setName('Eshop kategórie');
            $page_type_eshop_categories->setPageClassId('1');
            $page_type_eshop_categories->setDefaultTemplateId('2');
            $page_type_eshop_categories->setDeleteTrigger('');
            $page_type_eshop_categories->setIncludeInSync(NULL);
            $page_type_eshop_categories->setPageDetailsLayout('');
            $page_type_eshop_categories->setPageSortTypeTag('sort_date_time');
            $page_type_eshop_categories->setPageTypeOrder('0');
            $page_type_eshop_categories->setPostmoveTrigger('');
            $page_type_eshop_categories->setPostsubmitTrigger('');
            $page_type_eshop_categories->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('eshop');
            $page_type_eshop_categories->setParent($parent);

        } else {
            $this->writeLine('Page type with tag eshop_categories already exists');
            $this->setDataKey('page_type_eshop_categories_existed', true);
        }
        $this->pageTypesManager()->savePageType($page_type_eshop_categories);

        // page type: Podkategórie produktov 2 (eshop_subcategory_3)
        $page_type_eshop_subcategory_3 = $this->pageTypesManager()->pageTypeExistsByTag('eshop_subcategory_3');
        if ($page_type_eshop_subcategory_3 === false) {
            $page_type_eshop_subcategory_3 = new \Buxus\PageType\PageType();
            $page_type_eshop_subcategory_3->setTag('eshop_subcategory_3');
            $page_type_eshop_subcategory_3->setName('Podkategórie produktov 2');
            $page_type_eshop_subcategory_3->setPageClassId('1');
            $page_type_eshop_subcategory_3->setDefaultTemplateId('2');
            $page_type_eshop_subcategory_3->setDeleteTrigger('');
            $page_type_eshop_subcategory_3->setIncludeInSync('0');
            $page_type_eshop_subcategory_3->setPageDetailsLayout('');
            $page_type_eshop_subcategory_3->setPageSortTypeTag('sort_date_time');
            $page_type_eshop_subcategory_3->setPageTypeOrder('0');
            $page_type_eshop_subcategory_3->setPostmoveTrigger('');
            $page_type_eshop_subcategory_3->setPostsubmitTrigger('');
            $page_type_eshop_subcategory_3->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('eshop_categories');
            $page_type_eshop_subcategory_3->setParent($parent);

        } else {
            $this->writeLine('Page type with tag eshop_subcategory_3 already exists');
            $this->setDataKey('page_type_eshop_subcategory_3_existed', true);
        }
        if ($this->pageTypeExists('eshop_subcategory')) {
            $page_type_eshop_subcategory_3->addSuperiorPageType($this->getPageTypeByTag('eshop_subcategory'));
        }
        $this->pageTypesManager()->savePageType($page_type_eshop_subcategory_3);

        // property: META title(meta_title)
        $property_meta_title = $this->propertyManager()->propertyExistsByTag('meta_title');
        if ($property_meta_title === false) {
            $property_meta_title = new TreeProperty();
            $property_meta_title->setTag('meta_title');
            $property_meta_title->setDescription('Titulok stránky');
            $property_meta_title->setExtendedDescription('');
            $property_meta_title->setName('META title');
            $property_meta_title->setClassId('4');
            $property_meta_title->setShowType(NULL);
            $property_meta_title->setShowTypeTag('custom_property');
            $property_meta_title->setValueType('custom_property');
            $property_meta_title->setDefaultValue('');
            $property_meta_title->setMultiOperations(false);
            $property_meta_title->setInputString('');
            $property_meta_title->setAttribute('tab', 'SEO');
            $property_meta_title->setAttribute('class_name', \TreeProperty\BuxusProperty::class);
            $property_meta_title->setAttribute('inherit_value', 'F');
            $this->propertyManager()->saveProperty($property_meta_title);

        } else {
            $this->writeLine('Property with tag meta_title already exists');
            $this->setDataKey('property_meta_title_existed', true);
        }

        // property: META description(meta_description)
        $property_meta_description = $this->propertyManager()->propertyExistsByTag('meta_description');
        if ($property_meta_description === false) {
            $property_meta_description = new TreeProperty();
            $property_meta_description->setTag('meta_description');
            $property_meta_description->setDescription('Podľa SEO odporúčaní max. 170 znakov.');
            $property_meta_description->setExtendedDescription('');
            $property_meta_description->setName('META description');
            $property_meta_description->setClassId('4');
            $property_meta_description->setShowType(NULL);
            $property_meta_description->setShowTypeTag('custom_property');
            $property_meta_description->setValueType('custom_property');
            $property_meta_description->setDefaultValue('');
            $property_meta_description->setMultiOperations(false);
            $property_meta_description->setInputString(NULL);
            $property_meta_description->setAttribute('tab', 'SEO');
            $property_meta_description->setAttribute('class_name', \TreeProperty\BuxusProperty::class);
            $property_meta_description->setAttribute('inherit_value', 'F');
            $this->propertyManager()->saveProperty($property_meta_description);

        } else {
            $this->writeLine('Property with tag meta_description already exists');
            $this->setDataKey('property_meta_description_existed', true);
        }

        // property: Cena EUR bez DPH(eshop_eur_price_without_vat)
        $property_eshop_eur_price_without_vat = $this->propertyManager()->propertyExistsByTag('eshop_eur_price_without_vat');
        if ($property_eshop_eur_price_without_vat === false) {
            $property_eshop_eur_price_without_vat = new \Buxus\Property\Types\Input();
            $property_eshop_eur_price_without_vat->setTag('eshop_eur_price_without_vat');
            $property_eshop_eur_price_without_vat->setDescription('Cena v EUR bez DPH.');
            $property_eshop_eur_price_without_vat->setExtendedDescription('');
            $property_eshop_eur_price_without_vat->setName('Cena EUR bez DPH');
            $property_eshop_eur_price_without_vat->setClassId('4');
            $property_eshop_eur_price_without_vat->setShowType(NULL);
            $property_eshop_eur_price_without_vat->setShowTypeTag('text');
            $property_eshop_eur_price_without_vat->setValueType('oneline_text');
            $property_eshop_eur_price_without_vat->setDefaultValue('');
            $property_eshop_eur_price_without_vat->setMultiOperations(false);
            $property_eshop_eur_price_without_vat->setInputString(NULL);
            $property_eshop_eur_price_without_vat->setAttribute('tab', '');
            $property_eshop_eur_price_without_vat->setAttribute('size', '10');
            $property_eshop_eur_price_without_vat->setAttribute('maxlength', '');
            $property_eshop_eur_price_without_vat->setAttribute('readonly', 'F');
            $property_eshop_eur_price_without_vat->setAttribute('pattern', '');
            $property_eshop_eur_price_without_vat->setAttribute('inherit_value', 'F');
            $property_eshop_eur_price_without_vat->setAttribute('onchange-js', '');
            $property_eshop_eur_price_without_vat->setAttribute('onkeyup-js', '');
            $property_eshop_eur_price_without_vat->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_eshop_eur_price_without_vat);

        } else {
            $this->writeLine('Property with tag eshop_eur_price_without_vat already exists');
            $this->setDataKey('property_eshop_eur_price_without_vat_existed', true);
        }

        // property: Akciový produkt(eshop_product_akcia)
        $property_eshop_product_akcia = $this->propertyManager()->propertyExistsByTag('eshop_product_akcia');
        if ($property_eshop_product_akcia === false) {
            $property_eshop_product_akcia = new \Buxus\Property\Types\CheckBox();
            $property_eshop_product_akcia->setTag('eshop_product_akcia');
            $property_eshop_product_akcia->setDescription('Je tento produkt v akcii?');
            $property_eshop_product_akcia->setExtendedDescription('');
            $property_eshop_product_akcia->setName('Akciový produkt');
            $property_eshop_product_akcia->setClassId('4');
            $property_eshop_product_akcia->setShowType(NULL);
            $property_eshop_product_akcia->setShowTypeTag('checkbox');
            $property_eshop_product_akcia->setValueType('logical_value');
            $property_eshop_product_akcia->setDefaultValue('');
            $property_eshop_product_akcia->setMultiOperations(false);
            $property_eshop_product_akcia->setInputString(NULL);
            $property_eshop_product_akcia->setAttribute('tab', '');
            $property_eshop_product_akcia->setAttribute('on_value', 'T');
            $property_eshop_product_akcia->setAttribute('off_value', 'F');
            $property_eshop_product_akcia->setAttribute('onclick-js', '');
            $property_eshop_product_akcia->setAttribute('inherit_value', 'F');
            $this->propertyManager()->saveProperty($property_eshop_product_akcia);

        } else {
            $this->writeLine('Property with tag eshop_product_akcia already exists');
            $this->setDataKey('property_eshop_product_akcia_existed', true);
        }

        // property: Akciová cena bez DPH(eshop_eur_action_price_without_vat)
        $property_eshop_eur_action_price_without_vat = $this->propertyManager()->propertyExistsByTag('eshop_eur_action_price_without_vat');
        if ($property_eshop_eur_action_price_without_vat === false) {
            $property_eshop_eur_action_price_without_vat = new \Buxus\Property\Types\Input();
            $property_eshop_eur_action_price_without_vat->setTag('eshop_eur_action_price_without_vat');
            $property_eshop_eur_action_price_without_vat->setDescription('Akciová cena bez DPH');
            $property_eshop_eur_action_price_without_vat->setExtendedDescription('');
            $property_eshop_eur_action_price_without_vat->setName('Akciová cena bez DPH');
            $property_eshop_eur_action_price_without_vat->setClassId('4');
            $property_eshop_eur_action_price_without_vat->setShowType(NULL);
            $property_eshop_eur_action_price_without_vat->setShowTypeTag('text');
            $property_eshop_eur_action_price_without_vat->setValueType('oneline_text');
            $property_eshop_eur_action_price_without_vat->setDefaultValue('');
            $property_eshop_eur_action_price_without_vat->setMultiOperations(false);
            $property_eshop_eur_action_price_without_vat->setInputString(NULL);
            $property_eshop_eur_action_price_without_vat->setAttribute('tab', '');
            $property_eshop_eur_action_price_without_vat->setAttribute('size', '10');
            $property_eshop_eur_action_price_without_vat->setAttribute('maxlength', '');
            $property_eshop_eur_action_price_without_vat->setAttribute('readonly', 'F');
            $property_eshop_eur_action_price_without_vat->setAttribute('pattern', '');
            $property_eshop_eur_action_price_without_vat->setAttribute('inherit_value', 'F');
            $property_eshop_eur_action_price_without_vat->setAttribute('onchange-js', '');
            $property_eshop_eur_action_price_without_vat->setAttribute('onkeyup-js', '');
            $property_eshop_eur_action_price_without_vat->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_eshop_eur_action_price_without_vat);

        } else {
            $this->writeLine('Property with tag eshop_eur_action_price_without_vat already exists');
            $this->setDataKey('property_eshop_eur_action_price_without_vat_existed', true);
        }


        // property: Akciová cena s DPH(eshop_eur_action_price_with_vat)
        $property_eshop_eur_action_price_with_vat = $this->propertyManager()->propertyExistsByTag('eshop_eur_action_price_with_vat');
        if ($property_eshop_eur_action_price_with_vat === false) {
            $property_eshop_eur_action_price_with_vat = new \Buxus\Property\Types\Input();
            $property_eshop_eur_action_price_with_vat->setTag('eshop_eur_action_price_with_vat');
            $property_eshop_eur_action_price_with_vat->setDescription('Akciová cena s DPH');
            $property_eshop_eur_action_price_with_vat->setExtendedDescription('');
            $property_eshop_eur_action_price_with_vat->setName('Akciová cena s DPH');
            $property_eshop_eur_action_price_with_vat->setClassId('4');
            $property_eshop_eur_action_price_with_vat->setShowType(NULL);
            $property_eshop_eur_action_price_with_vat->setShowTypeTag('text');
            $property_eshop_eur_action_price_with_vat->setValueType('oneline_text');
            $property_eshop_eur_action_price_with_vat->setDefaultValue('');
            $property_eshop_eur_action_price_with_vat->setMultiOperations(false);
            $property_eshop_eur_action_price_with_vat->setInputString(NULL);
            $property_eshop_eur_action_price_with_vat->setAttribute('tab', '');
            $property_eshop_eur_action_price_with_vat->setAttribute('size', '10');
            $property_eshop_eur_action_price_with_vat->setAttribute('maxlength', '');
            $property_eshop_eur_action_price_with_vat->setAttribute('readonly', 'F');
            $property_eshop_eur_action_price_with_vat->setAttribute('pattern', '');
            $property_eshop_eur_action_price_with_vat->setAttribute('inherit_value', 'F');
            $property_eshop_eur_action_price_with_vat->setAttribute('onchange-js', '');
            $property_eshop_eur_action_price_with_vat->setAttribute('onkeyup-js', '');
            $property_eshop_eur_action_price_with_vat->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_eshop_eur_action_price_with_vat);

        } else {
            $this->writeLine('Property with tag eshop_eur_action_price_with_vat already exists');
            $this->setDataKey('property_eshop_eur_action_price_with_vat_existed', true);
        }

        // property: Dostupnosť(availability)
        $property_availability = $this->propertyManager()->propertyExistsByTag('availability');
        if ($property_availability === false) {
            $property_availability = new \Buxus\Property\Types\SelectBox();
            $property_availability->setTag('availability');
            $property_availability->setDescription('');
            $property_availability->setExtendedDescription('');
            $property_availability->setName('Dostupnosť');
            $property_availability->setClassId('4');
            $property_availability->setShowType(NULL);
            $property_availability->setShowTypeTag('select');
            $property_availability->setValueType('items_set');
            $property_availability->setDefaultValue('');
            $property_availability->setMultiOperations(false);
            $property_availability->setInputString(NULL);
            $property_availability->setAttribute('tab', '');
            $property_availability->setAttribute('options', array (
                1 => 'Na sklade',
                2 => 'U dodávateľa',
                3 => 'Na objednávku',
            ));
            $property_availability->setAttribute('multiple', 'F');
            $property_availability->setAttribute('show_as_list', 'T');
            $property_availability->setAttribute('empty_option', 'F');
            $property_availability->setAttribute('size', '');
            $property_availability->setAttribute('cols', NULL);
            $property_availability->setAttribute('rows', '');
            $property_availability->setAttribute('onchange-js', '');
            $property_availability->setAttribute('disabled', 'F');
            $property_availability->setAttribute('check_read_rights', 'T');
            $property_availability->setAttribute('inherit_value', 'F');
            $this->propertyManager()->saveProperty($property_availability);

        } else {
            $this->writeLine('Property with tag availability already exists');
            $this->setDataKey('property_availability_existed', true);
        }

        // property: Podrobný popis produktu(eshop_detail_text)
        $property_eshop_detail_text = $this->propertyManager()->propertyExistsByTag('eshop_detail_text');
        if ($property_eshop_detail_text === false) {
            $property_eshop_detail_text = new \Buxus\Property\Types\Textarea();
            $property_eshop_detail_text->setTag('eshop_detail_text');
            $property_eshop_detail_text->setDescription('Používa sa pri zobrazení detailov produktu.');
            $property_eshop_detail_text->setExtendedDescription(NULL);
            $property_eshop_detail_text->setName('Podrobný popis produktu');
            $property_eshop_detail_text->setClassId('4');
            $property_eshop_detail_text->setShowType(NULL);
            $property_eshop_detail_text->setShowTypeTag('textarea');
            $property_eshop_detail_text->setValueType('multiline_text');
            $property_eshop_detail_text->setDefaultValue('');
            $property_eshop_detail_text->setMultiOperations(false);
            $property_eshop_detail_text->setInputString(NULL);
            $property_eshop_detail_text->setAttribute('tab', '');
            $property_eshop_detail_text->setAttribute('cols', '60');
            $property_eshop_detail_text->setAttribute('rows', '');
            $property_eshop_detail_text->setAttribute('dhtml-edit', '1');
            $property_eshop_detail_text->setAttribute('dhtml-configuration', 'full');
            $property_eshop_detail_text->setAttribute('import-word', '0');
            $property_eshop_detail_text->setAttribute('auto', '1');
            $property_eshop_detail_text->setAttribute('inherit_value', '0');
            $property_eshop_detail_text->setAttribute('onchange-js', '');
            $property_eshop_detail_text->setAttribute('onkeyup-js', '');
            $property_eshop_detail_text->setAttribute('onkeydown-js', '');
            $property_eshop_detail_text->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($property_eshop_detail_text);

        } else {
            $this->writeLine('Property with tag eshop_detail_text already exists');
            $this->setDataKey('property_eshop_detail_text_existed', true);
        }

        // property: Náhľad(photo_album_thumbnail)
        $property_photo_album_thumbnail = $this->propertyManager()->propertyExistsByTag('photo_album_thumbnail');
        if ($property_photo_album_thumbnail === false) {
            $property_photo_album_thumbnail = new \Buxus\Property\Types\Image();
            $property_photo_album_thumbnail->setTag('photo_album_thumbnail');
            $property_photo_album_thumbnail->setDescription('Fotografia sa bude zobrazovať ako náhľad albumu.');
            $property_photo_album_thumbnail->setExtendedDescription('');
            $property_photo_album_thumbnail->setName('Náhľad');
            $property_photo_album_thumbnail->setClassId('4');
            $property_photo_album_thumbnail->setShowType(NULL);
            $property_photo_album_thumbnail->setShowTypeTag('image_name_upload');
            $property_photo_album_thumbnail->setValueType('file');
            $property_photo_album_thumbnail->setDefaultValue('');
            $property_photo_album_thumbnail->setMultiOperations(false);
            $property_photo_album_thumbnail->setInputString(NULL);
            $property_photo_album_thumbnail->setAttribute('tab', '');
            $property_photo_album_thumbnail->setAttribute('with_upload', 'T');
            $property_photo_album_thumbnail->setAttribute('simple_upload', 'T');
            $property_photo_album_thumbnail->setAttribute('show_input_element', 'F');
            $property_photo_album_thumbnail->setAttribute('filename', '');
            $property_photo_album_thumbnail->setAttribute('show_file_name', 'F');
            $property_photo_album_thumbnail->setAttribute('file_type', 'image');
            $property_photo_album_thumbnail->setAttribute('pattern', '');
            $property_photo_album_thumbnail->setAttribute('show_thumbnail', 'T');
            $property_photo_album_thumbnail->setAttribute('max_thumbnail_width', '150');
            $property_photo_album_thumbnail->setAttribute('max_thumbnail_height', '80');
            $property_photo_album_thumbnail->setAttribute('upload_subdir', 'fotogaleria/');
            $property_photo_album_thumbnail->setAttribute('eval-code', '//$this-&gt;parsed_parm[\'upload_subdir\'] = \'my-dir\';');
            $property_photo_album_thumbnail->setAttribute('onchange-js', '');
            $this->propertyManager()->saveProperty($property_photo_album_thumbnail);

        } else {
            $this->writeLine('Property with tag photo_album_thumbnail already exists');
            $this->setDataKey('property_photo_album_thumbnail_existed', true);
        }

        // property: Fotografie v albume(photo_list)
        $property_photo_list = $this->propertyManager()->propertyExistsByTag('photo_list');
        if ($property_photo_list === false) {
            $property_photo_list = new \Buxus\Property\Types\PhotoGallery();
            $property_photo_list->setTag('photo_list');
            $property_photo_list->setDescription('');
            $property_photo_list->setExtendedDescription(NULL);
            $property_photo_list->setName('Fotografie v albume');
            $property_photo_list->setClassId('4');
            $property_photo_list->setShowType(NULL);
            $property_photo_list->setShowTypeTag('page_edit_list');
            $property_photo_list->setValueType('image_list');
            $property_photo_list->setDefaultValue('');
            $property_photo_list->setMultiOperations(false);
            $property_photo_list->setInputString(NULL);
            $property_photo_list->setAttribute('tab', '');
            $property_photo_list->setAttribute('class_name', \BuxusPhotoGalleryEditList::class);
            $this->propertyManager()->saveProperty($property_photo_list);

        } else {
            $this->writeLine('Property with tag photo_list already exists');
            $this->setDataKey('property_photo_list_existed', true);
        }

        // property: Stručný popis produktu(eshop_short_text)
        $property_eshop_short_text = $this->propertyManager()->propertyExistsByTag('eshop_short_text');
        if ($property_eshop_short_text === false) {
            $property_eshop_short_text = new \Buxus\Property\Types\Textarea();
            $property_eshop_short_text->setTag('eshop_short_text');
            $property_eshop_short_text->setDescription('Používa sa v zoznamoch produktov.');
            $property_eshop_short_text->setExtendedDescription('');
            $property_eshop_short_text->setName('Stručný popis produktu');
            $property_eshop_short_text->setClassId('4');
            $property_eshop_short_text->setShowType(NULL);
            $property_eshop_short_text->setShowTypeTag('textarea');
            $property_eshop_short_text->setValueType('multiline_text');
            $property_eshop_short_text->setDefaultValue('');
            $property_eshop_short_text->setMultiOperations(false);
            $property_eshop_short_text->setInputString(NULL);
            $property_eshop_short_text->setAttribute('tab', '');
            $property_eshop_short_text->setAttribute('cols', '60');
            $property_eshop_short_text->setAttribute('rows', '');
            $property_eshop_short_text->setAttribute('dhtml-edit', '0');
            $property_eshop_short_text->setAttribute('dhtml-configuration', 'full');
            $property_eshop_short_text->setAttribute('import-word', '0');
            $property_eshop_short_text->setAttribute('auto', '1');
            $property_eshop_short_text->setAttribute('inherit_value', 'F');
            $property_eshop_short_text->setAttribute('onchange-js', '');
            $property_eshop_short_text->setAttribute('onkeyup-js', '');
            $property_eshop_short_text->setAttribute('onkeydown-js', '');
            $property_eshop_short_text->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($property_eshop_short_text);

        } else {
            $this->writeLine('Property with tag eshop_short_text already exists');
            $this->setDataKey('property_eshop_short_text_existed', true);
        }

        // page type: Produkt (eshop_product)
        $page_type_eshop_product = $this->pageTypesManager()->pageTypeExistsByTag('eshop_product');
        if ($page_type_eshop_product === false) {
            $page_type_eshop_product = new \Buxus\PageType\PageType();
            $page_type_eshop_product->setTag('eshop_product');
            $page_type_eshop_product->setName('Produkt');
            $page_type_eshop_product->setPageClassId('1');
            $page_type_eshop_product->setDefaultTemplateId('2');
            $page_type_eshop_product->setDeleteTrigger('');
            $page_type_eshop_product->setIncludeInSync('0');
            $page_type_eshop_product->setPageDetailsLayout('');
            $page_type_eshop_product->setPageSortTypeTag('sort_date_time');
            $page_type_eshop_product->setPageTypeOrder('0');
            $page_type_eshop_product->setPostmoveTrigger('');
            $page_type_eshop_product->setPostsubmitTrigger('');
            $page_type_eshop_product->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('eshop');
            $page_type_eshop_product->setParent($parent);

        } else {
            $this->writeLine('Page type with tag eshop_product already exists');
            $this->setDataKey('page_type_eshop_product_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('title');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_product->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('1');
            $tmp->setRequired(false);
            $page_type_eshop_product->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('meta_title');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_product->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('2');
            $tmp->setRequired(false);
            $page_type_eshop_product->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('meta_description');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_product->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('3');
            $tmp->setRequired(false);
            $page_type_eshop_product->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('eshop_eur_price_without_vat');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_product->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('4');
            $tmp->setRequired(false);
            $page_type_eshop_product->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('eshop_product_akcia');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_product->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('5');
            $tmp->setRequired(false);
            $page_type_eshop_product->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('eshop_eur_action_price_without_vat');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_product->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('6');
            $tmp->setRequired(false);
            $page_type_eshop_product->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('availability');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_product->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('7');
            $tmp->setRequired(false);
            $page_type_eshop_product->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('eshop_detail_text');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_product->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('9');
            $tmp->setRequired(false);
            $page_type_eshop_product->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('photo_album_thumbnail');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_product->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('10');
            $tmp->setRequired(false);
            $page_type_eshop_product->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('eshop_vat_rate');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_product->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('11');
            $tmp->setRequired(false);
            $page_type_eshop_product->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('seo_url_name');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_product->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('12');
            $tmp->setRequired(false);
            $page_type_eshop_product->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('photo_list');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_product->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('13');
            $tmp->setRequired(false);
            $page_type_eshop_product->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('eshop_short_text');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_product->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('14');
            $tmp->setRequired(false);
            $page_type_eshop_product->addPropertyItem($tmp);
        }
        if ($this->pageTypeExists('eshop_category')) {
            $page_type_eshop_product->addSuperiorPageType($this->getPageTypeByTag('eshop_category'));
        }
        if ($this->pageTypeExists('eshop_subcategory')) {
            $page_type_eshop_product->addSuperiorPageType($this->getPageTypeByTag('eshop_subcategory'));
        }
        if ($this->pageTypeExists('sliders_list')) {
            $page_type_eshop_product->addSuperiorPageType($this->getPageTypeByTag('sliders_list'));
        }
        $this->pageTypesManager()->savePageType($page_type_eshop_product);
        // set template on MAIN PAGE eshop_catalog::product
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('eshop_product'), 'product-catalog', 'product');

        // page type: Podkategórie produktov (eshop_subcategory)
        $page_type_eshop_subcategory = $this->pageTypesManager()->pageTypeExistsByTag('eshop_subcategory');
        if ($page_type_eshop_subcategory === false) {
            $page_type_eshop_subcategory = new \Buxus\PageType\PageType();
            $page_type_eshop_subcategory->setTag('eshop_subcategory');
            $page_type_eshop_subcategory->setName('Podkategórie produktov');
            $page_type_eshop_subcategory->setPageClassId('1');
            $page_type_eshop_subcategory->setDefaultTemplateId('2');
            $page_type_eshop_subcategory->setDeleteTrigger('');
            $page_type_eshop_subcategory->setIncludeInSync('0');
            $page_type_eshop_subcategory->setPageDetailsLayout('');
            $page_type_eshop_subcategory->setPageSortTypeTag('sort_date_time');
            $page_type_eshop_subcategory->setPageTypeOrder('20');
            $page_type_eshop_subcategory->setPostmoveTrigger('');
            $page_type_eshop_subcategory->setPostsubmitTrigger('');
            $page_type_eshop_subcategory->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('eshop_categories');
            $page_type_eshop_subcategory->setParent($parent);

        } else {
            $this->writeLine('Page type with tag eshop_subcategory already exists');
            $this->setDataKey('page_type_eshop_subcategory_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('title');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_subcategory->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('1');
            $tmp->setRequired(false);
            $page_type_eshop_subcategory->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('annotation');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_subcategory->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('2');
            $tmp->setRequired(false);
            $page_type_eshop_subcategory->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('text');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_subcategory->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('3');
            $tmp->setRequired(false);
            $page_type_eshop_subcategory->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('meta_description');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_subcategory->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('4');
            $tmp->setRequired(false);
            $page_type_eshop_subcategory->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('eshop_vat_rate');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_subcategory->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('5');
            $tmp->setRequired(false);
            $page_type_eshop_subcategory->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('seo_url_name');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_subcategory->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('6');
            $tmp->setRequired(false);
            $page_type_eshop_subcategory->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('promo_bg_image');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_subcategory->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('7');
            $tmp->setRequired(false);
            $page_type_eshop_subcategory->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($page_type_eshop_subcategory);
        // set template on MAIN PAGE eshop_catalog::product-list
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('eshop_subcategory'), 'product-catalog', 'product-list');

        // page type: Kategórie produktov (eshop_category)
        $page_type_eshop_category = $this->pageTypesManager()->pageTypeExistsByTag('eshop_category');
        if ($page_type_eshop_category === false) {
            $page_type_eshop_category = new \Buxus\PageType\PageType();
            $page_type_eshop_category->setTag('eshop_category');
            $page_type_eshop_category->setName('Kategórie produktov');
            $page_type_eshop_category->setPageClassId('1');
            $page_type_eshop_category->setDefaultTemplateId('2');
            $page_type_eshop_category->setDeleteTrigger('');
            $page_type_eshop_category->setIncludeInSync('0');
            $page_type_eshop_category->setPageDetailsLayout('');
            $page_type_eshop_category->setPageSortTypeTag('sort_date_time');
            $page_type_eshop_category->setPageTypeOrder('20');
            $page_type_eshop_category->setPostmoveTrigger('');
            $page_type_eshop_category->setPostsubmitTrigger('');
            $page_type_eshop_category->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('eshop_categories');
            $page_type_eshop_category->setParent($parent);

        } else {
            $this->writeLine('Page type with tag eshop_category already exists');
            $this->setDataKey('page_type_eshop_category_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('title');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_category->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('1');
            $tmp->setRequired(true);
            $page_type_eshop_category->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('annotation');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_category->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('2');
            $tmp->setRequired(false);
            $page_type_eshop_category->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('text');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_category->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('3');
            $tmp->setRequired(false);
            $page_type_eshop_category->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('meta_title');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_category->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('4');
            $tmp->setRequired(false);
            $page_type_eshop_category->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('meta_description');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_category->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('5');
            $tmp->setRequired(false);
            $page_type_eshop_category->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('eshop_vat_rate');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_category->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('6');
            $tmp->setRequired(false);
            $page_type_eshop_category->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('seo_url_name');
        $property_id = $property->getId();
        $tmp = $page_type_eshop_category->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('7');
            $tmp->setRequired(false);
            $page_type_eshop_category->addPropertyItem($tmp);
        }
        if ($this->pageTypeExists('eshop_catalog')) {
            $page_type_eshop_category->addSuperiorPageType($this->getPageTypeByTag('eshop_catalog'));
        }
        $this->pageTypesManager()->savePageType($page_type_eshop_category);
        // set template on MAIN PAGE eshop_catalog::product-list
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('eshop_category'), 'product-catalog', 'product-list');

        // property: Cena EUR s DPH(eshop_eur_price_including_vat)
        $property_eshop_eur_price_including_vat = $this->propertyManager()->propertyExistsByTag('eshop_eur_price_including_vat');
        if ($property_eshop_eur_price_including_vat === false) {
            $property_eshop_eur_price_including_vat = new \Buxus\Property\Types\Input();
            $property_eshop_eur_price_including_vat->setTag('eshop_eur_price_including_vat');
            $property_eshop_eur_price_including_vat->setDescription('Cena v EUR s DPH.');
            $property_eshop_eur_price_including_vat->setExtendedDescription(NULL);
            $property_eshop_eur_price_including_vat->setName('Cena EUR s DPH');
            $property_eshop_eur_price_including_vat->setClassId('4');
            $property_eshop_eur_price_including_vat->setShowType(NULL);
            $property_eshop_eur_price_including_vat->setShowTypeTag('text');
            $property_eshop_eur_price_including_vat->setValueType('oneline_text');
            $property_eshop_eur_price_including_vat->setDefaultValue('');
            $property_eshop_eur_price_including_vat->setMultiOperations(false);
            $property_eshop_eur_price_including_vat->setInputString(NULL);
            $property_eshop_eur_price_including_vat->setAttribute('tab', '');
            $property_eshop_eur_price_including_vat->setAttribute('size', '60');
            $property_eshop_eur_price_including_vat->setAttribute('maxlength', '');
            $property_eshop_eur_price_including_vat->setAttribute('readonly', '0');
            $property_eshop_eur_price_including_vat->setAttribute('pattern', '');
            $property_eshop_eur_price_including_vat->setAttribute('inherit_value', '0');
            $property_eshop_eur_price_including_vat->setAttribute('onchange-js', '');
            $property_eshop_eur_price_including_vat->setAttribute('onkeyup-js', '');
            $property_eshop_eur_price_including_vat->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_eshop_eur_price_including_vat);

        } else {
            $this->writeLine('Property with tag eshop_eur_price_including_vat already exists');
            $this->setDataKey('property_eshop_eur_price_including_vat_existed', true);
        }

        // property: Akciový sticker(sale_sticker_image)
        $property_sale_sticker_image = $this->propertyManager()->propertyExistsByTag('sale_sticker_image');
        if ($property_sale_sticker_image === false) {
            $property_sale_sticker_image = new \Buxus\Property\Types\Image();
            $property_sale_sticker_image->setTag('sale_sticker_image');
            $property_sale_sticker_image->setDescription('Akciový sticker, ktorý sa zobrazí na produktoch, ktoré sú označené ako akciové');
            $property_sale_sticker_image->setExtendedDescription('');
            $property_sale_sticker_image->setName('Akciový sticker');
            $property_sale_sticker_image->setClassId('4');
            $property_sale_sticker_image->setShowType(NULL);
            $property_sale_sticker_image->setShowTypeTag('image_name_upload');
            $property_sale_sticker_image->setValueType('file');
            $property_sale_sticker_image->setDefaultValue('');
            $property_sale_sticker_image->setMultiOperations(false);
            $property_sale_sticker_image->setInputString('');
            $property_sale_sticker_image->setAttribute('tab', 'Eshop');
            $property_sale_sticker_image->setAttribute('with_upload', 'T');
            $property_sale_sticker_image->setAttribute('simple_upload', 'F');
            $property_sale_sticker_image->setAttribute('show_input_element', 'T');
            $property_sale_sticker_image->setAttribute('filename', '');
            $property_sale_sticker_image->setAttribute('show_file_name', 'T');
            $property_sale_sticker_image->setAttribute('file_type', 'image');
            $property_sale_sticker_image->setAttribute('pattern', '');
            $property_sale_sticker_image->setAttribute('show_thumbnail', 'T');
            $property_sale_sticker_image->setAttribute('max_thumbnail_width', '150');
            $property_sale_sticker_image->setAttribute('max_thumbnail_height', '80');
            $property_sale_sticker_image->setAttribute('upload_subdir', '');
            $property_sale_sticker_image->setAttribute('eval-code', '//$this-&gt;parsed_parm[\'upload_subdir\'] = \'my-dir\';');
            $property_sale_sticker_image->setAttribute('onchange-js', '');
            $this->propertyManager()->saveProperty($property_sale_sticker_image);

        } else {
            $this->writeLine('Property with tag sale_sticker_image already exists');
            $this->setDataKey('property_sale_sticker_image_existed', true);
        }

        $property = $this->propertyManager()->getPropertyByTag('sale_sticker_image');
        $property_id = $property->getId();
        $dizajn_type = $this->getPageTypeByTag('design');
        if (!empty($dizajn_type)) {
            $tmp = $dizajn_type->getPropertyItemForPropertyId($property_id);
            if ($tmp === null) {
                $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
                $tmp->setRequired(false);
                $dizajn_type->addPropertyItem($tmp);
                $this->pageTypesManager()->savePageType($dizajn_type);
            }
        }

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // page: Katalóg produktov(ID: 38 TAG: Katalóg produktov)
        $page_id = $this->getPageIdByTag('eshop_catalog');
        if ($page_id === NULL) {
            $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_catalog');
            $page_38 = \PageFactory::create($this->getPageIdByTag('root_page'), $page_type->getId());

        } else {
            $page_38 = \PageFactory::get($page_id);
        }
        $page_38->setPageName('Katalóg produktov');
        $page_38->setPageTag('eshop_catalog');
        $page_38->setPageStateId('1');
        $page_38->setPageClassId('1');
        $page_38->setPropertyValue('title', 'Katalóg produktov');
        $page_38->setPropertyValue('annotation', '<p>Popis katalógu produktov</p>');
        $page_38->setPropertyValue('eshop_vat_rate', '20');
        $page_38->setPropertyValue('seo_url_name', '-NO-SEO-URL-');
        // set template on MAIN PAGE eshop_catalog::list
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('eshop_catalog'), 'product-catalog', 'list');
        $page_38->save();
    }

    public function down()
    {
        // remove page: Katalóg produktov (Katalóg produktov)
        $page_id = $this->getPageIdByTag('eshop_catalog');
        if ($page_id !== NULL) {
            \PageFactory::get($page_id)->delete();
        }

        // regenerate page tags
        PageIds::generatePageTagsList();

        // remove property: Akciový sticker(sale_sticker_image)
        $property_sale_sticker_image = $this->propertyManager()->propertyExistsByTag('sale_sticker_image');
        if ($property_sale_sticker_image != false) {
            if ((is_null($this->getDataKey('property_sale_sticker_image_existed')))) {
                $this->propertyManager()->removeProperty($property_sale_sticker_image);
            }
        }

        // remove property: Cena EUR s DPH(eshop_eur_price_including_vat)
        $property_eshop_eur_price_including_vat = $this->propertyManager()->propertyExistsByTag('eshop_eur_price_including_vat');
        if ($property_eshop_eur_price_including_vat != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_eshop_eur_price_including_vat);
            if ((is_null($this->getDataKey('property_eshop_eur_price_including_vat_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_eshop_eur_price_including_vat);
            }
        }

        // remove page type: Kategórie produktov (eshop_category)
        $page_type_eshop_category = $this->pageTypesManager()->pageTypeExistsByTag('eshop_category');
        if (($page_type_eshop_category != false) && (is_null($this->getDataKey('page_type_eshop_category_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_eshop_category);
        }

        // remove page type: Podkategórie produktov (eshop_subcategory)
        $page_type_eshop_subcategory = $this->pageTypesManager()->pageTypeExistsByTag('eshop_subcategory');
        if (($page_type_eshop_subcategory != false) && (is_null($this->getDataKey('page_type_eshop_subcategory_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_eshop_subcategory);
        }

        // remove page type: Produkt (eshop_product)
        $page_type_eshop_product = $this->pageTypesManager()->pageTypeExistsByTag('eshop_product');
        if (($page_type_eshop_product != false) && (is_null($this->getDataKey('page_type_eshop_product_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_eshop_product);
        }

        // remove property: Stručný popis produktu(eshop_short_text)
        $property_eshop_short_text = $this->propertyManager()->propertyExistsByTag('eshop_short_text');
        if ($property_eshop_short_text != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_eshop_short_text);
            if ((is_null($this->getDataKey('property_eshop_short_text_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_eshop_short_text);
            }
        }

        // remove property: Fotografie v albume(photo_list)
        $property_photo_list = $this->propertyManager()->propertyExistsByTag('photo_list');
        if ($property_photo_list != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_photo_list);
            if ((is_null($this->getDataKey('property_photo_list_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_photo_list);
            }
        }

        // remove property: Náhľad(photo_album_thumbnail)
        $property_photo_album_thumbnail = $this->propertyManager()->propertyExistsByTag('photo_album_thumbnail');
        if ($property_photo_album_thumbnail != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_photo_album_thumbnail);
            if ((is_null($this->getDataKey('property_photo_album_thumbnail_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_photo_album_thumbnail);
            }
        }

        // remove property: Podrobný popis produktu(eshop_detail_text)
        $property_eshop_detail_text = $this->propertyManager()->propertyExistsByTag('eshop_detail_text');
        if ($property_eshop_detail_text != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_eshop_detail_text);
            if ((is_null($this->getDataKey('property_eshop_detail_text_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_eshop_detail_text);
            }
        }

        // remove property: Dostupnosť(availability)
        $property_availability = $this->propertyManager()->propertyExistsByTag('availability');
        if ($property_availability != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_availability);
            if ((is_null($this->getDataKey('property_availability_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_availability);
            }
        }

        // remove property: Akciová cena bez DPH(eshop_eur_action_price_without_vat)
        $property_eshop_eur_action_price_without_vat = $this->propertyManager()->propertyExistsByTag('eshop_eur_action_price_without_vat');
        if ($property_eshop_eur_action_price_without_vat != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_eshop_eur_action_price_without_vat);
            if ((is_null($this->getDataKey('property_eshop_eur_action_price_without_vat_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_eshop_eur_action_price_without_vat);
            }
        }

        // remove property: Akciová cena s DPH(eshop_eur_action_price_with_vat)
        $property_eshop_eur_action_price_with_vat = $this->propertyManager()->propertyExistsByTag('eshop_eur_action_price_with_vat');
        if ($property_eshop_eur_action_price_with_vat != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_eshop_eur_action_price_with_vat);
            if ((is_null($this->getDataKey('property_eshop_eur_action_price_with_vat_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_eshop_eur_action_price_with_vat);
            }
        }

        // remove property: Akciový produkt(eshop_product_akcia)
        $property_eshop_product_akcia = $this->propertyManager()->propertyExistsByTag('eshop_product_akcia');
        if ($property_eshop_product_akcia != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_eshop_product_akcia);
            if ((is_null($this->getDataKey('property_eshop_product_akcia_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_eshop_product_akcia);
            }
        }

        // remove property: Cena EUR bez DPH(eshop_eur_price_without_vat)
        $property_eshop_eur_price_without_vat = $this->propertyManager()->propertyExistsByTag('eshop_eur_price_without_vat');
        if ($property_eshop_eur_price_without_vat != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_eshop_eur_price_without_vat);
            if ((is_null($this->getDataKey('property_eshop_eur_price_without_vat_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_eshop_eur_price_without_vat);
            }
        }

        // remove property: META description(meta_description)
        $property_meta_description = $this->propertyManager()->propertyExistsByTag('meta_description');
        if ($property_meta_description != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_meta_description);
            if ((is_null($this->getDataKey('property_meta_description_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_meta_description);
            }
        }

        // remove property: META title(meta_title)
        $property_meta_title = $this->propertyManager()->propertyExistsByTag('meta_title');
        if ($property_meta_title != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_meta_title);
            if ((is_null($this->getDataKey('property_meta_title_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_meta_title);
            }
        }

        // remove page type: Podkategórie produktov 2 (eshop_subcategory_3)
        $page_type_eshop_subcategory_3 = $this->pageTypesManager()->pageTypeExistsByTag('eshop_subcategory_3');
        if (($page_type_eshop_subcategory_3 != false) && (is_null($this->getDataKey('page_type_eshop_subcategory_3_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_eshop_subcategory_3);
        }

        // remove page type: Eshop kategórie (eshop_categories)
        $page_type_eshop_categories = $this->pageTypesManager()->pageTypeExistsByTag('eshop_categories');
        if (($page_type_eshop_categories != false) && (is_null($this->getDataKey('page_type_eshop_categories_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_eshop_categories);
        }

        // remove page type: Katalóg produktov (eshop_catalog)
        $page_type_eshop_catalog = $this->pageTypesManager()->pageTypeExistsByTag('eshop_catalog');
        if (($page_type_eshop_catalog != false) && (is_null($this->getDataKey('page_type_eshop_catalog_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_eshop_catalog);
        }

        // remove property: Sadzba DPH %(eshop_vat_rate)
        $property_eshop_vat_rate = $this->propertyManager()->propertyExistsByTag('eshop_vat_rate');
        if ($property_eshop_vat_rate != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_eshop_vat_rate);
            if ((is_null($this->getDataKey('property_eshop_vat_rate_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_eshop_vat_rate);
            }
        }

        // remove page type: Výrobca (eshop_producer)
        $page_type_eshop_producer = $this->pageTypesManager()->pageTypeExistsByTag('eshop_producer');
        if (($page_type_eshop_producer != false) && (is_null($this->getDataKey('page_type_eshop_producer_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_eshop_producer);
        }

        // remove property: SEO URL name(seo_url_name)
        $property_seo_url_name = $this->propertyManager()->propertyExistsByTag('seo_url_name');
        if ($property_seo_url_name != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_seo_url_name);
            if ((is_null($this->getDataKey('property_seo_url_name_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_seo_url_name);
            }
        }

        // remove property: Logo spoločnosti(eshop_company_logo)
        $property_eshop_company_logo = $this->propertyManager()->propertyExistsByTag('eshop_company_logo');
        if ($property_eshop_company_logo != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_eshop_company_logo);
            if ((is_null($this->getDataKey('property_eshop_company_logo_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_eshop_company_logo);
            }
        }

        // remove property: Text(text)
        $property_text = $this->propertyManager()->propertyExistsByTag('text');
        if ($property_text != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_text);
            if ((is_null($this->getDataKey('property_text_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_text);
            }
        }

        // remove property: Anotácia(annotation)
        $property_annotation = $this->propertyManager()->propertyExistsByTag('annotation');
        if ($property_annotation != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_annotation);
            if ((is_null($this->getDataKey('property_annotation_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_annotation);
            }
        }

        // remove property: URL adresa spoločnosti(eshop_company_url)
        $property_eshop_company_url = $this->propertyManager()->propertyExistsByTag('eshop_company_url');
        if ($property_eshop_company_url != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_eshop_company_url);
            if ((is_null($this->getDataKey('property_eshop_company_url_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_eshop_company_url);
            }
        }

        // remove property: Titulok(title)
        $property_title = $this->propertyManager()->propertyExistsByTag('title');
        if ($property_title != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_title);
            if ((is_null($this->getDataKey('property_title_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_title);
            }
        }

        // remove page type: Eshop (eshop)
        $page_type_eshop = $this->pageTypesManager()->pageTypeExistsByTag('eshop');
        if (($page_type_eshop != false) && (is_null($this->getDataKey('page_type_eshop_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_eshop);
        }

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
