<?php

namespace Eshop\Catalog\Migrations;

use Buxus\Migration\AbstractMigration;

class SetTitleOnCategoryPageRequired extends AbstractMigration
{
    public function up()
    {
        $page_type_eshop_category = $this->pageTypesManager()->pageTypeExistsByTag('eshop_category');

        $oldState = null;

        if ($page_type_eshop_category) {
            $property = $this->propertyManager()->getPropertyByTag('title');
            $property_id = $property->getId();
            $tmp = $page_type_eshop_category->getPropertyItemForPropertyId($property_id);
            if ($tmp) {
                $oldState = $tmp->getRequired();
                $tmp->setRequired(true);
            }
            $this->pageTypesManager()->savePageType($page_type_eshop_category);
        }

        $this->setDataKey('required_state', $oldState);
    }

    public function down()
    {
        $oldState = $this->getDataKey('required_state');
        if ($oldState !== null) {
            $page_type_eshop_category = $this->pageTypesManager()->pageTypeExistsByTag('eshop_category');
            if ($page_type_eshop_category) {
                $property = $this->propertyManager()->getPropertyByTag('title');
                $property_id = $property->getId();
                $tmp = $page_type_eshop_category->getPropertyItemForPropertyId($property_id);
                if ($tmp) {
                    $tmp->setRequired($oldState);
                }
                $this->pageTypesManager()->savePageType($page_type_eshop_category);
            }
        }
    }
}
