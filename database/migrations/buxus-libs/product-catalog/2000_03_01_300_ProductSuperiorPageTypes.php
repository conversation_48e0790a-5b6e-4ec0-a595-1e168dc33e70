<?php

namespace Eshop\Catalog\Migrations;

use Buxus\Migration\AbstractMigration;

class ProductSuperiorPageTypes extends AbstractMigration {
    public function up() {

        $page_type_eshop_product = $this->pageTypesManager()->pageTypeExistsByTag('eshop_product');
        if ($page_type_eshop_product === false) {
            $page_type_eshop_product = new \Buxus\PageType\PageType();

        } else {
            $this->writeLine('Page type with tag eshop_product already exists');
            $this->setDataKey('page_type_eshop_product_existed', true);
        }

        if ($this->pageTypeExists('eshop_category')) {
            $page_type_eshop_product->addSuperiorPageType($this->getPageTypeByTag('eshop_category'));
        }
        if ($this->pageTypeExists('eshop_subcategory')) {
            $page_type_eshop_product->addSuperiorPageType($this->getPageTypeByTag('eshop_subcategory'));
        }

        $this->pageTypesManager()->savePageType($page_type_eshop_product);

    }

    public function down() {

        $page_type_eshop_product = $this->pageTypesManager()->pageTypeExistsByTag('eshop_product');

        $page_type_eshop_category = $this->pageTypesManager()->pageTypeExistsByTag('eshop_category');
        $page_type_eshop_subcategory = $this->pageTypesManager()->pageTypeExistsByTag('eshop_subcategory');
        $page_type_eshop_product->removeSuperiorPageType($page_type_eshop_category);
        $page_type_eshop_product->removeSuperiorPageType($page_type_eshop_subcategory);

        $this->pageTypesManager()->savePageType($page_type_eshop_product);

    }

}
