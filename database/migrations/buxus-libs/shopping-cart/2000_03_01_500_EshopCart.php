<?php
namespace Eshop\ShoppingCart\Migrations;

use Buxus\Eshop\EshopPages;
use Buxus\Eshop\Properties\PaymentTypesProperty;
use Buxus\Migration\AbstractMigration;
use Buxus\Property\LinkProperty;
use Buxus\Property\Types\Input;
use Buxus\Property\Types\Textarea;
use Buxus\Util\PageIds;
use Email\Migrations\EmailMigration;

class EshopCart extends AbstractMigration
{
    public function dependencies()
    {
        return array(
            EmailMigration::class
        );
    }

    public function up()
    {
//        \DB::transaction(function () {
            // page type: Nastavenia (settings)
            $page_type_settings = $this->pageTypesManager()->pageTypeExistsByTag('settings');
            if ($page_type_settings === false) {
                $page_type_settings = new \Buxus\PageType\PageType();
                $page_type_settings->setTag('settings');
                $page_type_settings->setName('Nastavenia');
                $page_type_settings->setPageClassId('1');
                $page_type_settings->setDefaultTemplateId('2');
                $page_type_settings->setDeleteTrigger('');
                $page_type_settings->setIncludeInSync('1');
                $page_type_settings->setPageDetailsLayout('');
                $page_type_settings->setPageSortTypeTag('sort_date_time');
                $page_type_settings->setPageTypeOrder('999');
                $page_type_settings->setPostmoveTrigger('');
                $page_type_settings->setPostsubmitTrigger('');
                $page_type_settings->setPresubmitTrigger('');
                $page_type_settings->setParent(null);

            } else {
                $this->writeLine('Page type with tag settings already exists');
                $this->setDataKey('page_type_settings_existed', true);
            }
            if ($this->pageTypeExists('main_page')) {
                $page_type_settings->addSuperiorPageType($this->getPageTypeByTag('main_page'));
            }
            if ($this->pageTypeExists('eshop_catalog')) {
                $page_type_settings->addSuperiorPageType($this->getPageTypeByTag('eshop_catalog'));
            }
            if ($this->pageTypeExists('settings')) {
                $page_type_settings->addSuperiorPageType($this->getPageTypeByTag('settings'));
            }
            if ($this->pageTypeExists('folder')) {
                $page_type_settings->addSuperiorPageType($this->getPageTypeByTag('folder'));
            }
            $this->pageTypesManager()->savePageType($page_type_settings);
            // set template on MAIN PAGE index::error404
            $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('settings'), 'index', 'error404');

            // page type: Nastavenia - eshop (settings_eshop)
            $page_type_settings_eshop = $this->pageTypesManager()->pageTypeExistsByTag('settings_eshop');
            if ($page_type_settings_eshop === false) {
                $page_type_settings_eshop = new \Buxus\PageType\PageType();
                $page_type_settings_eshop->setTag('settings_eshop');
                $page_type_settings_eshop->setName('Nastavenia - eshop');
                $page_type_settings_eshop->setPageClassId('1');
                $page_type_settings_eshop->setDefaultTemplateId('2');
                $page_type_settings_eshop->setDeleteTrigger('');
                $page_type_settings_eshop->setIncludeInSync(null);
                $page_type_settings_eshop->setPageDetailsLayout('');
                $page_type_settings_eshop->setPageSortTypeTag('sort_date_time');
                $page_type_settings_eshop->setPageTypeOrder('0');
                $page_type_settings_eshop->setPostmoveTrigger('');
                $page_type_settings_eshop->setPostsubmitTrigger('');
                $page_type_settings_eshop->setPresubmitTrigger('');
                $parent = $this->pageTypesManager()->getPageTypeByTag('settings');
                $page_type_settings_eshop->setParent($parent);

            } else {
                $this->writeLine('Page type with tag settings_eshop already exists');
                $this->setDataKey('page_type_settings_eshop_existed', true);
            }
            $this->pageTypesManager()->savePageType($page_type_settings_eshop);

            // property: Titulok(title)
            $property_title = $this->propertyManager()->propertyExistsByTag('title');
            if ($property_title === false) {
                $property_title = new Input();
                $property_title->setTag('title');
                $property_title->setDescription('Štandardný názov stránky. Text, ktorý sa zobrazí v záložke prehliadača. Podľa SEO odporúčaní max. 65 znakov.');
                $property_title->setExtendedDescription('<p><img src="/buxus/images//tt01_1.JPG" alt="Text stránky v záložke prehliadača" title="Text stránky v záložke prehliadača" /></p>');
                $property_title->setName('Titulok');
                $property_title->setClassId('4');
                $property_title->setShowType(null);
                $property_title->setShowTypeTag('text');
                $property_title->setValueType('oneline_text');
                $property_title->setDefaultValue('');
                $property_title->setMultiOperations(false);
                $property_title->setInputString(null);
                $property_title->setAttribute('tab', '');
                $property_title->setAttribute('size', '60');
                $property_title->setAttribute('maxlength', '');
                $property_title->setAttribute('readonly', 'F');
                $property_title->setAttribute('pattern', '');
                $property_title->setAttribute('inherit_value', 'F');
                $property_title->setAttribute('onchange-js', '');
                $property_title->setAttribute('onkeyup-js', '');
                $property_title->setAttribute('onkeydown-js', '');
                $this->propertyManager()->saveProperty($property_title);

            } else {
                $this->writeLine('Property with tag title already exists');
                $this->setDataKey('property_title_existed', true);
            }

            // property: Tag(eshop_tag)
            $property_eshop_tag = $this->propertyManager()->propertyExistsByTag('eshop_tag');
            if ($property_eshop_tag === false) {
                $property_eshop_tag = new Input();
                $property_eshop_tag->setTag('eshop_tag');
                $property_eshop_tag->setDescription('Tag stránky, slúži na identifikovanie účelu stránky.');
                $property_eshop_tag->setExtendedDescription(null);
                $property_eshop_tag->setName('Tag');
                $property_eshop_tag->setClassId('4');
                $property_eshop_tag->setShowType(null);
                $property_eshop_tag->setShowTypeTag('text');
                $property_eshop_tag->setValueType('oneline_text');
                $property_eshop_tag->setDefaultValue('');
                $property_eshop_tag->setMultiOperations(false);
                $property_eshop_tag->setInputString(null);
                $property_eshop_tag->setAttribute('tab', '');
                $property_eshop_tag->setAttribute('size', '25');
                $property_eshop_tag->setAttribute('maxlength', '');
                $property_eshop_tag->setAttribute('readonly', '0');
                $property_eshop_tag->setAttribute('pattern', '');
                $property_eshop_tag->setAttribute('inherit_value', '0');
                $property_eshop_tag->setAttribute('onchange-js', '');
                $property_eshop_tag->setAttribute('onkeyup-js', '');
                $property_eshop_tag->setAttribute('onkeydown-js', '');
                $this->propertyManager()->saveProperty($property_eshop_tag);

            } else {
                $this->writeLine('Property with tag eshop_tag already exists');
                $this->setDataKey('property_eshop_tag_existed', true);
            }

            // property: Popis(eshop_description)
            $property_eshop_description = $this->propertyManager()->propertyExistsByTag('eshop_description');
            if ($property_eshop_description === false) {
                $property_eshop_description = new Textarea();
                $property_eshop_description->setTag('eshop_description');
                $property_eshop_description->setDescription('Popis, ktorý sa zobrazuje používateľovi.');
                $property_eshop_description->setExtendedDescription(null);
                $property_eshop_description->setName('Popis');
                $property_eshop_description->setClassId('4');
                $property_eshop_description->setShowType(null);
                $property_eshop_description->setShowTypeTag('textarea');
                $property_eshop_description->setValueType('multiline_text');
                $property_eshop_description->setDefaultValue('');
                $property_eshop_description->setMultiOperations(false);
                $property_eshop_description->setInputString(null);
                $property_eshop_description->setAttribute('tab', '');
                $property_eshop_description->setAttribute('cols', '60');
                $property_eshop_description->setAttribute('rows', '');
                $property_eshop_description->setAttribute('dhtml-edit', '0');
                $property_eshop_description->setAttribute('dhtml-configuration', 'full');
                $property_eshop_description->setAttribute('import-word', '0');
                $property_eshop_description->setAttribute('auto', '1');
                $property_eshop_description->setAttribute('inherit_value', '0');
                $property_eshop_description->setAttribute('onchange-js', '');
                $property_eshop_description->setAttribute('onkeyup-js', '');
                $property_eshop_description->setAttribute('onkeydown-js', '');
                $property_eshop_description->setAttribute('pattern', '');
                $this->propertyManager()->saveProperty($property_eshop_description);

            } else {
                $this->writeLine('Property with tag eshop_description already exists');
                $this->setDataKey('property_eshop_description_existed', true);
            }

            // property: Cena EUR s DPH(eshop_eur_price_including_vat)
            $property_eshop_eur_price_including_vat = $this->propertyManager()->propertyExistsByTag('eshop_eur_price_including_vat');
            if ($property_eshop_eur_price_including_vat === false) {
                $property_eshop_eur_price_including_vat = new Input();
                $property_eshop_eur_price_including_vat->setTag('eshop_eur_price_including_vat');
                $property_eshop_eur_price_including_vat->setDescription('Cena v EUR s DPH.');
                $property_eshop_eur_price_including_vat->setExtendedDescription(null);
                $property_eshop_eur_price_including_vat->setName('Cena EUR s DPH');
                $property_eshop_eur_price_including_vat->setClassId('4');
                $property_eshop_eur_price_including_vat->setShowType(null);
                $property_eshop_eur_price_including_vat->setShowTypeTag('text');
                $property_eshop_eur_price_including_vat->setValueType('oneline_text');
                $property_eshop_eur_price_including_vat->setDefaultValue('');
                $property_eshop_eur_price_including_vat->setMultiOperations(false);
                $property_eshop_eur_price_including_vat->setInputString(null);
                $property_eshop_eur_price_including_vat->setAttribute('tab', '');
                $property_eshop_eur_price_including_vat->setAttribute('size', '60');
                $property_eshop_eur_price_including_vat->setAttribute('maxlength', '');
                $property_eshop_eur_price_including_vat->setAttribute('readonly', '0');
                $property_eshop_eur_price_including_vat->setAttribute('pattern', '');
                $property_eshop_eur_price_including_vat->setAttribute('inherit_value', '0');
                $property_eshop_eur_price_including_vat->setAttribute('onchange-js', '');
                $property_eshop_eur_price_including_vat->setAttribute('onkeyup-js', '');
                $property_eshop_eur_price_including_vat->setAttribute('onkeydown-js', '');
                $this->propertyManager()->saveProperty($property_eshop_eur_price_including_vat);

            } else {
                $this->writeLine('Property with tag eshop_eur_price_including_vat already exists');
                $this->setDataKey('property_eshop_eur_price_including_vat_existed', true);
            }

            // property: Sadzba DPH %(eshop_vat_rate)
            $property_eshop_vat_rate = $this->propertyManager()->propertyExistsByTag('eshop_vat_rate');
            if ($property_eshop_vat_rate === false) {
                $property_eshop_vat_rate = new Input();
                $property_eshop_vat_rate->setTag('eshop_vat_rate');
                $property_eshop_vat_rate->setDescription('DPH sadzba v %');
                $property_eshop_vat_rate->setExtendedDescription('');
                $property_eshop_vat_rate->setName('Sadzba DPH %');
                $property_eshop_vat_rate->setClassId('4');
                $property_eshop_vat_rate->setShowType(null);
                $property_eshop_vat_rate->setShowTypeTag('text');
                $property_eshop_vat_rate->setValueType('oneline_text');
                $property_eshop_vat_rate->setDefaultValue('');
                $property_eshop_vat_rate->setMultiOperations(false);
                $property_eshop_vat_rate->setInputString(null);
                $property_eshop_vat_rate->setAttribute('tab', '_Administrator');
                $property_eshop_vat_rate->setAttribute('size', '10');
                $property_eshop_vat_rate->setAttribute('maxlength', '');
                $property_eshop_vat_rate->setAttribute('readonly', 'F');
                $property_eshop_vat_rate->setAttribute('pattern', '');
                $property_eshop_vat_rate->setAttribute('inherit_value', 'T');
                $property_eshop_vat_rate->setAttribute('onchange-js', '');
                $property_eshop_vat_rate->setAttribute('onkeyup-js', '');
                $property_eshop_vat_rate->setAttribute('onkeydown-js', '');
                $this->propertyManager()->saveProperty($property_eshop_vat_rate);

            } else {
                $this->writeLine('Property with tag eshop_vat_rate already exists');
                $this->setDataKey('property_eshop_vat_rate_existed', true);
            }

            // property: Druh platby(eshop_payment_type)
            $property_eshop_payment_type = $this->propertyManager()->propertyExistsByTag('eshop_payment_type');
            if ($property_eshop_payment_type === false) {
//                $property_eshop_payment_type = new LinkProperty();
                $property_eshop_payment_type = new PaymentTypesProperty();
                $property_eshop_payment_type->setTag('eshop_payment_type');
                $property_eshop_payment_type->setDescription('Slúži na zlinkovanie druhov platby s druhom dopravy. Pre výber viac druhov platieb použite kláves CTRL.');
                $property_eshop_payment_type->setExtendedDescription('');
                $property_eshop_payment_type->setName('Druh platby');
                $property_eshop_payment_type->setClassId('5');
                $property_eshop_payment_type->setShowType(null);
                $property_eshop_payment_type->setShowTypeTag('custom_property');
                $property_eshop_payment_type->setValueType('custom_property');
                $property_eshop_payment_type->setDefaultValue('');
                $property_eshop_payment_type->setMultiOperations(false);
                $property_eshop_payment_type->setInputString(null);
                $property_eshop_payment_type->setAttribute('tab', '');
                $property_eshop_payment_type->setAttribute('class_name', \Eshop\ShoppingCart\Property\PaymentType::class);
                $property_eshop_payment_type->setAttribute('inherit_value', 'F');
                $this->propertyManager()->saveProperty($property_eshop_payment_type);

            } else {
                $this->writeLine('Property with tag eshop_payment_type already exists');
                $this->setDataKey('property_eshop_payment_type_existed', true);
            }

            // page type: Typ dopravy (eshop_transport_type)
            $page_type_eshop_transport_type = $this->pageTypesManager()->pageTypeExistsByTag('eshop_transport_type');
            if ($page_type_eshop_transport_type === false) {
                $page_type_eshop_transport_type = new \Buxus\PageType\PageType();
                $page_type_eshop_transport_type->setTag('eshop_transport_type');
                $page_type_eshop_transport_type->setName('Typ dopravy');
                $page_type_eshop_transport_type->setPageClassId('1');
                $page_type_eshop_transport_type->setDefaultTemplateId('2');
                $page_type_eshop_transport_type->setDeleteTrigger('');
                $page_type_eshop_transport_type->setIncludeInSync('0');
                $page_type_eshop_transport_type->setPageDetailsLayout('');
                $page_type_eshop_transport_type->setPageSortTypeTag('sort_date_time');
                $page_type_eshop_transport_type->setPageTypeOrder('0');
                $page_type_eshop_transport_type->setPostmoveTrigger('');
                $page_type_eshop_transport_type->setPostsubmitTrigger('');
                $page_type_eshop_transport_type->setPresubmitTrigger('');
                $parent = $this->pageTypesManager()->getPageTypeByTag('settings_eshop');
                $page_type_eshop_transport_type->setParent($parent);

            } else {
                $this->writeLine('Page type with tag eshop_transport_type already exists');
                $this->setDataKey('page_type_eshop_transport_type_existed', true);
            }
            $property = $this->propertyManager()->getPropertyByTag('title');
            $property_id = $property->getId();
            $tmp = $page_type_eshop_transport_type->getPropertyItemForPropertyId($property_id);
            if ($tmp === null) {
                $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
                $tmp->setOrder('1');
                $tmp->setRequired(false);
                $page_type_eshop_transport_type->addPropertyItem($tmp);
            }
            $property = $this->propertyManager()->getPropertyByTag('eshop_tag');
            $property_id = $property->getId();
            $tmp = $page_type_eshop_transport_type->getPropertyItemForPropertyId($property_id);
            if ($tmp === null) {
                $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
                $tmp->setOrder('2');
                $tmp->setRequired(false);
                $page_type_eshop_transport_type->addPropertyItem($tmp);
            }
            $property = $this->propertyManager()->getPropertyByTag('eshop_description');
            $property_id = $property->getId();
            $tmp = $page_type_eshop_transport_type->getPropertyItemForPropertyId($property_id);
            if ($tmp === null) {
                $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
                $tmp->setOrder('3');
                $tmp->setRequired(false);
                $page_type_eshop_transport_type->addPropertyItem($tmp);
            }
            $property = $this->propertyManager()->getPropertyByTag('eshop_eur_price_including_vat');
            $property_id = $property->getId();
            $tmp = $page_type_eshop_transport_type->getPropertyItemForPropertyId($property_id);
            if ($tmp === null) {
                $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
                $tmp->setOrder('4');
                $tmp->setRequired(false);
                $page_type_eshop_transport_type->addPropertyItem($tmp);
            }
            $property = $this->propertyManager()->getPropertyByTag('eshop_vat_rate');
            $property_id = $property->getId();
            $tmp = $page_type_eshop_transport_type->getPropertyItemForPropertyId($property_id);
            if ($tmp === null) {
                $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
                $tmp->setOrder('5');
                $tmp->setRequired(false);
                $page_type_eshop_transport_type->addPropertyItem($tmp);
            }
            $property = $this->propertyManager()->getPropertyByTag('eshop_payment_type');
            $property_id = $property->getId();
            $tmp = $page_type_eshop_transport_type->getPropertyItemForPropertyId($property_id);
            if ($tmp === null) {
                $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
                $tmp->setOrder('6');
                $tmp->setRequired(false);
                $page_type_eshop_transport_type->addPropertyItem($tmp);
            }
            if ($this->pageTypeExists('settings')) {
                $page_type_eshop_transport_type->addSuperiorPageType($this->getPageTypeByTag('settings'));
            }
            $this->pageTypesManager()->savePageType($page_type_eshop_transport_type);

            // property: Popis IB transakcie(eshop_transaction_description)
            $property_eshop_transaction_description = $this->propertyManager()->propertyExistsByTag('eshop_transaction_description');
            if ($property_eshop_transaction_description === false) {
                $property_eshop_transaction_description = new Textarea();
                $property_eshop_transaction_description->setTag('eshop_transaction_description');
                $property_eshop_transaction_description->setDescription('Popis transakcie, ktorý sa používa pri platbe cez Internet Banking.');
                $property_eshop_transaction_description->setExtendedDescription(null);
                $property_eshop_transaction_description->setName('Popis IB transakcie');
                $property_eshop_transaction_description->setClassId('4');
                $property_eshop_transaction_description->setShowType(null);
                $property_eshop_transaction_description->setShowTypeTag('text');
                $property_eshop_transaction_description->setValueType('oneline_text');
                $property_eshop_transaction_description->setDefaultValue('');
                $property_eshop_transaction_description->setMultiOperations(false);
                $property_eshop_transaction_description->setInputString(null);
                $property_eshop_transaction_description->setAttribute('tab', '');
                $property_eshop_transaction_description->setAttribute('size', '80');
                $property_eshop_transaction_description->setAttribute('maxlength', '');
                $property_eshop_transaction_description->setAttribute('readonly', '0');
                $property_eshop_transaction_description->setAttribute('pattern', '');
                $property_eshop_transaction_description->setAttribute('inherit_value', '0');
                $property_eshop_transaction_description->setAttribute('onchange-js', '');
                $property_eshop_transaction_description->setAttribute('onkeyup-js', '');
                $property_eshop_transaction_description->setAttribute('onkeydown-js', '');
                $this->propertyManager()->saveProperty($property_eshop_transaction_description);

            } else {
                $this->writeLine('Property with tag eshop_transaction_description already exists');
                $this->setDataKey('property_eshop_transaction_description_existed', true);
            }

            // page type: Typ Platby (eshop_payment_type)
            $page_type_eshop_payment_type = $this->pageTypesManager()->pageTypeExistsByTag('eshop_payment_type');
            if ($page_type_eshop_payment_type === false) {
                $page_type_eshop_payment_type = new \Buxus\PageType\PageType();
                $page_type_eshop_payment_type->setTag('eshop_payment_type');
                $page_type_eshop_payment_type->setName('Typ Platby');
                $page_type_eshop_payment_type->setPageClassId('1');
                $page_type_eshop_payment_type->setDefaultTemplateId('2');
                $page_type_eshop_payment_type->setDeleteTrigger('');
                $page_type_eshop_payment_type->setIncludeInSync('0');
                $page_type_eshop_payment_type->setPageDetailsLayout('');
                $page_type_eshop_payment_type->setPageSortTypeTag('sort_date_time');
                $page_type_eshop_payment_type->setPageTypeOrder('0');
                $page_type_eshop_payment_type->setPostmoveTrigger('');
                $page_type_eshop_payment_type->setPostsubmitTrigger('');
                $page_type_eshop_payment_type->setPresubmitTrigger('');
                $parent = $this->pageTypesManager()->getPageTypeByTag('settings_eshop');
                $page_type_eshop_payment_type->setParent($parent);

            } else {
                $this->writeLine('Page type with tag eshop_payment_type already exists');
                $this->setDataKey('page_type_eshop_payment_type_existed', true);
            }
            $property = $this->propertyManager()->getPropertyByTag('title');
            $property_id = $property->getId();
            $tmp = $page_type_eshop_payment_type->getPropertyItemForPropertyId($property_id);
            if ($tmp === null) {
                $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
                $tmp->setOrder('1');
                $tmp->setRequired(false);
                $page_type_eshop_payment_type->addPropertyItem($tmp);
            }
            $property = $this->propertyManager()->getPropertyByTag('eshop_tag');
            $property_id = $property->getId();
            $tmp = $page_type_eshop_payment_type->getPropertyItemForPropertyId($property_id);
            if ($tmp === null) {
                $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
                $tmp->setOrder('2');
                $tmp->setRequired(false);
                $page_type_eshop_payment_type->addPropertyItem($tmp);
            }
            $property = $this->propertyManager()->getPropertyByTag('eshop_transaction_description');
            $property_id = $property->getId();
            $tmp = $page_type_eshop_payment_type->getPropertyItemForPropertyId($property_id);
            if ($tmp === null) {
                $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
                $tmp->setOrder('3');
                $tmp->setRequired(false);
                $page_type_eshop_payment_type->addPropertyItem($tmp);
            }
            $property = $this->propertyManager()->getPropertyByTag('eshop_description');
            $property_id = $property->getId();
            $tmp = $page_type_eshop_payment_type->getPropertyItemForPropertyId($property_id);
            if ($tmp === null) {
                $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
                $tmp->setOrder('4');
                $tmp->setRequired(false);
                $page_type_eshop_payment_type->addPropertyItem($tmp);
            }
            $property = $this->propertyManager()->getPropertyByTag('eshop_eur_price_including_vat');
            $property_id = $property->getId();
            $tmp = $page_type_eshop_payment_type->getPropertyItemForPropertyId($property_id);
            if ($tmp === null) {
                $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
                $tmp->setOrder('5');
                $tmp->setRequired(false);
                $page_type_eshop_payment_type->addPropertyItem($tmp);
            }
            $property = $this->propertyManager()->getPropertyByTag('eshop_vat_rate');
            $property_id = $property->getId();
            $tmp = $page_type_eshop_payment_type->getPropertyItemForPropertyId($property_id);
            if ($tmp === null) {
                $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
                $tmp->setOrder('6');
                $tmp->setRequired(false);
                $page_type_eshop_payment_type->addPropertyItem($tmp);
            }
            if ($this->pageTypeExists('settings')) {
                $page_type_eshop_payment_type->addSuperiorPageType($this->getPageTypeByTag('settings'));
            }
            $this->pageTypesManager()->savePageType($page_type_eshop_payment_type);

            // page: E-shop(ID: 105 TAG: e_shop)
            $page_id = $this->getPageIdByTag('e_shop');
            if ($page_id === null) {
                $page_type = $this->pageTypesManager()->getPageTypeByTag('folder');
                $page_105 = \PageFactory::create($this->getPageIdByTag('admin_settings'), $page_type->getId());

            } else {
                $page_105 = \PageFactory::get($page_id);
            }
            $page_105->setPageName('E-shop');
            $page_105->setPageTag('e_shop');
            $page_105->setPageStateId('2');
            $page_105->setPageClassId('1');
            $page_105->save();

            // page: Nastavenia e-shopu(ID: 106 TAG: Nastavenia e-shopu)
            $page_type = $this->pageTypesManager()->getPageTypeByTag('settings');
            $page_106 = \PageFactory::create($page_105->getPageId(), $page_type->getId());
            $page_106->setPageName('Nastavenia e-shopu');
            $page_106->setPageTag('Nastavenia e-shopu');
            $page_106->setPageStateId('2');
            $page_106->setPageClassId('1');
            $page_106->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
            // set template on MAIN PAGE index::error404
            $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('settings'), 'index', 'error404');
            $page_106->save();

            // page: Eshop - druhy dopravy(ID: 107 TAG: Eshop - druhy dopravy)
            $page_type = $this->pageTypesManager()->getPageTypeByTag('settings');
            $page_107 = \PageFactory::create($page_106->getPageId(), $page_type->getId());
            $page_107->setPageName('Eshop - druhy dopravy');
            $page_107->setPageTag(EshopPages::DRUHY_DOPRAVY);
            $page_107->setPageStateId('2');
            $page_107->setPageClassId('1');
            $page_107->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
            // set template on MAIN PAGE index::error404
            $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('settings'), 'index', 'error404');
            $page_107->save();

            // page: Doručenie na dobierku(ID: 108 TAG: )
            $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_transport_type');
            $page_108 = \PageFactory::create($page_107->getPageId(), $page_type->getId());
            $page_108->setPageName('Doručenie na dobierku');
            $page_108->setPageTag(false);
            $page_108->setPageStateId('1');
            $page_108->setPageClassId('1');
            $page_108->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
            $page_108->setValue('title', 'Doručenie na dobierku');
            $page_108->setValue('eshop_vat_rate', '20');
            $page_108->setValue('eshop_tag', 'cash_on_delivery');
            $page_108->setValue('eshop_description',
                'Objednávku Vám doručíme poštou na dobierkuu do 2 prac. dní pričom objednávku poprosíme uhradiť pri preberaní tovaru. Pri dobierke poštou je potrebné prevziať tovar najneskôr do 14 dní.');
            $page_108->save();

            // page: Doručenie kuriérskou službou po úhrade objednávky zálohovou faktúrou(ID: 109 TAG: )
            $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_transport_type');
            $page_109 = \PageFactory::create($page_107->getPageId(), $page_type->getId());
            $page_109->setPageName('Doručenie kuriérskou službou po úhrade objednávky zálohovou faktúrou');
            $page_109->setPageTag(false);
            $page_109->setPageStateId('1');
            $page_109->setPageClassId('1');
            $page_109->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
            $page_109->setValue('title', 'Doručenie kuriérskou službou po úhrade objednávky zálohovou faktúrou');
            $page_109->setValue('eshop_vat_rate', '20');
            $page_109->setValue('eshop_eur_price_including_vat', '8.73');
            $page_109->setValue('eshop_tag', 'ten_expres');
            $page_109->setValue('eshop_description',
                'Objednávku Vám doručí doručovacia spoločnosť do 2 prac. dní pričom objednávku poprosíme uhradiť zálohovou faktúrou, ktorú Vám pošleme e-mailom.');
            $page_109->save();

            // page: Vyzdvihnutie priamo u predajcu(ID: 110 TAG: )
            $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_transport_type');
            $page_110 = \PageFactory::create($page_107->getPageId(), $page_type->getId());
            $page_110->setPageName('Vyzdvihnutie priamo u predajcu');
            $page_110->setPageTag(false);
            $page_110->setPageStateId('1');
            $page_110->setPageClassId('1');
            $page_110->setSortDateTime(date('Y-m-d H:i:s', time() + 2));
            $page_110->setValue('title', 'Vyzdvihnutie priamo u predajcu');
            $page_110->setValue('eshop_vat_rate', '20');
            $page_110->setValue('eshop_eur_price_including_vat', '0');
            $page_110->setValue('eshop_tag', 'personal');
            $page_110->setValue('eshop_description', 'Tovar si môžete vyzdvihnúť osobne na adrese vydavateľstva na Borodáčovej 42 v Košiciach.');
            $page_110->save();

            // page: Eshop - druhy platby(ID: 111 TAG: Eshop - druhy platby)
            $page_type = $this->pageTypesManager()->getPageTypeByTag('settings');
            $page_111 = \PageFactory::create($page_106->getPageId(), $page_type->getId());
            $page_111->setPageName('Eshop - druhy platby');
            $page_111->setPageTag(EshopPages::DRUHY_PLATBY);
            $page_111->setPageStateId('2');
            $page_111->setPageClassId('1');
            $page_111->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
            // set template on MAIN PAGE index::error404
            $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('settings'), 'index', 'error404');
            $page_111->save();

            // page: Na dobierku(ID: 118 TAG: )
            $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_payment_type');
            $page_118 = \PageFactory::create($page_111->getPageId(), $page_type->getId());
            $page_118->setPageName('Na dobierku');
            $page_118->setPageTag('');
            $page_118->setPageStateId('1');
            $page_118->setPageClassId('1');
            $page_118->setSortDateTime(date('Y-m-d H:i:s', time() + 6));
            $page_118->setValue('title', 'Na dobierku');
            $page_118->setValue('eshop_vat_rate', '20');
            $page_118->setValue('eshop_eur_price_including_vat', '2.5');
            $page_118->setValue('eshop_tag', 'post');
            $page_118->save();

            // page: Zaplatiť u predajcu(ID: 119 TAG: )
            $page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_payment_type');
            $page_119 = \PageFactory::create($page_111->getPageId(), $page_type->getId());
            $page_119->setPageName('Zaplatiť u predajcu');
            $page_119->setPageTag('');
            $page_119->setPageStateId('1');
            $page_119->setPageClassId('1');
            $page_119->setSortDateTime(date('Y-m-d H:i:s', time() + 7));
            $page_119->setValue('title', 'Zaplatiť u predajcu');
            $page_119->setValue('eshop_vat_rate', '20');
            $page_119->setValue('eshop_eur_price_including_vat', '0');
            $page_119->setValue('eshop_tag', 'seller');
            $page_119->save();

            // page: Eshop - oznamy v nákupnom procese(ID: 120 TAG: Eshop - oznamy v nákupnom procese)
            $page_type = $this->pageTypesManager()->getPageTypeByTag('settings');
            $page_120 = \PageFactory::create($page_106->getPageId(), $page_type->getId());
            $page_120->setPageName('Eshop - oznamy v nákupnom procese');
            $page_120->setPageTag('Eshop - oznamy v nákupnom procese');
            $page_120->setPageStateId('2');
            $page_120->setPageClassId('1');
            $page_120->setSortDateTime(date('Y-m-d H:i:s', time() + 2));
            $page_120->setValue('test', array(
                0 => '47',
                1 => '107',
            ));
            // set template on MAIN PAGE index::error404
            $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('settings'), 'index', 'error404');
            $page_120->save();

            // page: Doručenie zásielky(ID: 121 TAG: )
            $page_type = $this->pageTypesManager()->getPageTypeByTag('service_page');
            $page_121 = \PageFactory::create($page_120->getPageId(), $page_type->getId());
            $page_121->setPageName('Doručenie zásielky');
            $page_121->setPageTag('');
            $page_121->setPageStateId('1');
            $page_121->setPageClassId('1');
            $page_121->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
            $page_121->setValue('title', 'Doručenie zásielky');
            $page_121->setValue('text', 'Dopravu objednaného tovaru vieme zabezpečiť do dvoch pracovných dní od telefonického potvrdenia objednávky.');
            // set template on MAIN PAGE index::index
            $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('service_page'), 'index', 'index');
            $page_121->save();

            // page: Potrebujete poradiť?(ID: 122 TAG: )
            $page_type = $this->pageTypesManager()->getPageTypeByTag('service_page');
            $page_122 = \PageFactory::create($page_120->getPageId(), $page_type->getId());
            $page_122->setPageName('Potrebujete poradiť?');
            $page_122->setPageTag('');
            $page_122->setPageStateId('1');
            $page_122->setPageClassId('1');
            $page_122->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
            $page_122->setValue('title', 'Potrebujete poradiť?');
            $page_122->setValue('text',
                '<p>Zatelefonujte nám na 0908 772 604, alebo napíšte e-mail na <a href="mailto:<EMAIL>"><EMAIL></a>. Radi odpovieme na Vaše otázky.</p>');
            // set template on MAIN PAGE index::index
            $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('service_page'), 'index', 'index');
            $page_122->save();

            // page: Garancia spokojnosti(ID: 123 TAG: )
            $page_type = $this->pageTypesManager()->getPageTypeByTag('service_page');
            $page_123 = \PageFactory::create($page_120->getPageId(), $page_type->getId());
            $page_123->setPageName('Garancia spokojnosti');
            $page_123->setPageTag('');
            $page_123->setPageStateId('1');
            $page_123->setPageClassId('1');
            $page_123->setSortDateTime(date('Y-m-d H:i:s', time() + 2));
            $page_123->setValue('title', 'Garancia spokojnosti');
            $page_123->setValue('text',
                'Tovar môžete vrátiť do 7 dní bez udania dôvodu. Po objednaní tovaru cez web sa Vám do jedného pracovného dňa telefonicky ozveme.');
            // set template on MAIN PAGE index::index
            $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('service_page'), 'index', 'index');
            $page_123->save();

            // page: Obslužné stránky e-shopu(ID: 124 TAG: Obslužné stránky e-shopu)
            $page_type = $this->pageTypesManager()->getPageTypeByTag('settings');
            $page_124 = \PageFactory::create($page_105->getPageId(), $page_type->getId());
            $page_124->setPageName('Obslužné stránky e-shopu');
            $page_124->setPageTag('Obslužné stránky e-shopu');
            $page_124->setPageStateId('2');
            $page_124->setPageClassId('1');
            $page_124->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
            // set template on MAIN PAGE index::error404
            $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('settings'), 'index', 'error404');
            $page_124->save();

            // page: krok 5. - Úspešná objednávka(ID: 125 TAG: krok 5. - Úspešná objednávka)
            $page_type = $this->pageTypesManager()->getPageTypeByTag('service_page');
            $page_125 = \PageFactory::create($page_124->getPageId(), $page_type->getId());
            $page_125->setPageName('krok 5. - Úspešná objednávka');
            $page_125->setPageTag(EshopPages::KOSIK5_USPESNA_OBJEDNAVKA);
            $page_125->setPageStateId('2');
            $page_125->setPageClassId('1');
            $page_125->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
            $page_125->setValue('title', 'Ďakujeme, Vaša objednávka bola prijatá');
            $page_125->setValue('text',
                '<p><strong>Číslo objednávky: {VARIABLE_SYMBOL}</strong><br />Do jedného pracovného dňa sa Vám telefonicky ozveme, aby sme mohli potvrdiť dostupnosť tovaru na sklade a dohodnuť detaily doručenia objednaných položiek na Vami požadované miesto.</p><p><strong>Ak chcete vytlačiť sumár vašej objednávky kliknite na tlačítko "Vytlačiť objenávku".</strong></p>');
            // set template cart::successful-order
            $page_125->getPageTemplate()->setController('cart');
            $page_125->getPageTemplate()->setAction('successful-order');
            $page_125->save();

            // page: krok 4. - Súhrn objednávky(ID: 126 TAG: krok 4. - Súhrn objednávky)
            $page_type = $this->pageTypesManager()->getPageTypeByTag('service_page');
            $page_126 = \PageFactory::create($page_124->getPageId(), $page_type->getId());
            $page_126->setPageName('krok 4. - Súhrn objednávky');
            $page_126->setPageTag(EshopPages::KOSIK4_SUHRN_OBJEDNAVKY);
            $page_126->setPageStateId('2');
            $page_126->setPageClassId('1');
            $page_126->setSortDateTime(date('Y-m-d H:i:s', time() + 1));
            $page_126->setValue('title', 'Súhrn objednávky');
            // set template cart::summary-info
            $page_126->getPageTemplate()->setController('cart');
            $page_126->getPageTemplate()->setAction('summary-info');
            $page_126->save();

            // page: krok 4. - Neúspešné potvrdenie platby(ID: 132 TAG: krok 4. - Neúspešné potvrdenie platby)
            $page_type = $this->pageTypesManager()->getPageTypeByTag('service_page');
            $page_132 = \PageFactory::create($page_126->getPageId(), $page_type->getId());
            $page_132->setPageName('krok 4. - Neúspešné potvrdenie platby');
            $page_132->setPageTag(EshopPages::KOSIK4_NEUSPESNE_POTVRDENIE_PLATBY);
            $page_132->setPageStateId('2');
            $page_132->setPageClassId('1');
            $page_132->setSortDateTime(date('Y-m-d H:i:s', time() + 5));
            $page_132->setValue('title', 'Neúspešné potvrdenie platby');
            $page_132->setValue('text', '<p><strong>Vaša platba nebola úspešná!</strong> Pokúste sa ju zrealizovať znovu.</p>');
            // set template cart::unsuccessful-payment
            $page_132->getPageTemplate()->setController('cart');
            $page_132->getPageTemplate()->setAction('unsuccessful-payment');
            $page_132->save();

            // page: krok 3. - Dodacie údaje(ID: 133 TAG: krok 3. - Dodacie údaje)
            $page_type = $this->pageTypesManager()->getPageTypeByTag('service_page');
            $page_133 = \PageFactory::create($page_124->getPageId(), $page_type->getId());
            $page_133->setPageName('krok 3. - Dodacie údaje');
            $page_133->setPageTag(EshopPages::KOSIK3_DODACIE_UDAJE);
            $page_133->setPageStateId('2');
            $page_133->setPageClassId('1');
            $page_133->setSortDateTime(date('Y-m-d H:i:s', time() + 2));
            $page_133->setValue('title', 'Dodacie údaje');
            // set template cart::delivery-data
            $page_133->getPageTemplate()->setController('cart');
            $page_133->getPageTemplate()->setAction('delivery-data');
            $page_133->save();

            // page: krok 2. - Dodávka a platba(ID: 134 TAG: krok 2. - Dodávka a platba)
            $page_type = $this->pageTypesManager()->getPageTypeByTag('service_page');
            $page_134 = \PageFactory::create($page_124->getPageId(), $page_type->getId());
            $page_134->setPageName('krok 2. - Dodávka a platba');
            $page_134->setPageTag(EshopPages::KOSIK2_DODAVKA_A_PLATBA);
            $page_134->setPageStateId('2');
            $page_134->setPageClassId('1');
            $page_134->setSortDateTime(date('Y-m-d H:i:s', time() + 3));
            $page_134->setValue('title', 'Dodávka a platba');
            // set template cart::transport-payment
            $page_134->getPageTemplate()->setController('cart');
            $page_134->getPageTemplate()->setAction('transport-payment');
            $page_134->save();

            // page: krok 1. - Obsah košíka(ID: 135 TAG: krok 1. - Obsah košíka)
            $page_type = $this->pageTypesManager()->getPageTypeByTag('service_page');
            $page_135 = \PageFactory::create($page_124->getPageId(), $page_type->getId());
            $page_135->setPageName('krok 1. - Obsah košíka');
            $page_135->setPageTag(EshopPages::KOSIK1_OBSAH_KOSIKA);
            $page_135->setPageStateId('2');
            $page_135->setPageClassId('1');
            $page_135->setSortDateTime(date('Y-m-d H:i:s', time() + 4));
            $page_135->setValue('title', 'Obsah košíka');
            // set template cart::shopping-cart
            $page_135->getPageTemplate()->setController('cart');
            $page_135->getPageTemplate()->setAction('shopping-cart');
            $page_135->save();

            // page: krok 1. - Uprav košík(ID: 136 TAG: krok 1. - Uprav košík)
            $page_type = $this->pageTypesManager()->getPageTypeByTag('service_page');
            $page_136 = \PageFactory::create($page_135->getPageId(), $page_type->getId());
            $page_136->setPageName('Cart command');
            $page_136->setPageTag('cart-command');
            $page_136->setPageStateId('2');
            $page_136->setPageClassId('1');
            $page_136->setSortDateTime(date('Y-m-d H:i:s', time() + 0));
            $page_136->setValue('title', 'Shopping cart commands');
            // set template cart::change-cart
            $page_136->getPageTemplate()->setController('cart');
            $page_136->getPageTemplate()->setAction('command');
            $page_136->save();

            // page: Tlač objednávky(ID: 138 TAG: Tlač objednávky)
            $page_type = $this->pageTypesManager()->getPageTypeByTag('service_page');
            $page_138 = \PageFactory::create($page_124->getPageId(), $page_type->getId());
            $page_138->setPageName('Tlač objednávky');
            $page_138->setPageTag('Tlač objednávky');
            $page_138->setPageStateId('2');
            $page_138->setPageClassId('1');
            $page_138->setSortDateTime(date('Y-m-d H:i:s', time() + 5));
            $page_138->setValue('title', 'Tlač objednávky');
            // set template cart::print-order
            $page_138->getPageTemplate()->setController('cart');
            $page_138->getPageTemplate()->setAction('print-order');
            $page_138->save();

            // page: E-maily - e-shop(ID: 141 TAG: E-maily shop modulu)
            $page_type = $this->pageTypesManager()->getPageTypeByTag('settings');
            $page_141 = \PageFactory::create($page_105->getPageId(), $page_type->getId());
            $page_141->setPageName('E-maily - e-shop');
            $page_141->setPageTag('E-maily shop modulu');
            $page_141->setPageStateId('2');
            $page_141->setPageClassId('1');
            $page_141->setSortDateTime(date('Y-m-d H:i:s', time() + 2));
            // set template on MAIN PAGE index::error404
            $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('settings'), 'index', 'error404');
            $page_141->save();

            $page_108->setValue('eshop_payment_type', array(
                0 => $page_118->getPageId(),
            ));
            $page_108->save();

            $page_109->setValue('eshop_payment_type', array(
                0 => $page_118->getPageId(),
            ));
            $page_109->save();

            $page_110->setValue('eshop_payment_type', array(
                0 => $page_119->getPageId(),
            ));
            $page_110->save();

            // regenerate page types constants
            $this->pageTypesManager()->generateConstants();

            // regenerate property constants
            $this->propertyManager()->generateConstants();

            // regenerate page tags
            PageIds::generatePageTagsList();
//        });

    }

    public function down()
    {
        // remove page: E-shop (e_shop)
        $page_id = $this->getPageIdByTag('e_shop');
        if ($page_id !== null) {
            \PageFactory::get($page_id)->delete();
        }

        // remove page type: Typ Platby (eshop_payment_type)
        $page_type_eshop_payment_type = $this->pageTypesManager()->pageTypeExistsByTag('eshop_payment_type');
        if (($page_type_eshop_payment_type != false) && (is_null($this->getDataKey('page_type_eshop_payment_type_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_eshop_payment_type);
        }

        // remove property: Popis IB transakcie(eshop_transaction_description)
        $property_eshop_transaction_description = $this->propertyManager()->propertyExistsByTag('eshop_transaction_description');
        if ($property_eshop_transaction_description != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_eshop_transaction_description);
            if ((is_null($this->getDataKey('property_eshop_transaction_description_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_eshop_transaction_description);
            }
        }

        // remove page type: Typ dopravy (eshop_transport_type)
        $page_type_eshop_transport_type = $this->pageTypesManager()->pageTypeExistsByTag('eshop_transport_type');
        if (($page_type_eshop_transport_type != false) && (is_null($this->getDataKey('page_type_eshop_transport_type_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_eshop_transport_type);
        }

        // remove property: Druh platby(eshop_payment_type)
        $property_eshop_payment_type = $this->propertyManager()->propertyExistsByTag('eshop_payment_type');
        if ($property_eshop_payment_type != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_eshop_payment_type);
            if ((is_null($this->getDataKey('property_eshop_payment_type_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_eshop_payment_type);
            }
        }

        // remove property: Sadzba DPH %(eshop_vat_rate)
        $property_eshop_vat_rate = $this->propertyManager()->propertyExistsByTag('eshop_vat_rate');
        if ($property_eshop_vat_rate != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_eshop_vat_rate);
            if ((is_null($this->getDataKey('property_eshop_vat_rate_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_eshop_vat_rate);
            }
        }

        // remove property: Cena EUR s DPH(eshop_eur_price_including_vat)
        $property_eshop_eur_price_including_vat = $this->propertyManager()->propertyExistsByTag('eshop_eur_price_including_vat');
        if ($property_eshop_eur_price_including_vat != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_eshop_eur_price_including_vat);
            if ((is_null($this->getDataKey('property_eshop_eur_price_including_vat_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_eshop_eur_price_including_vat);
            }
        }

        // remove property: Popis(eshop_description)
        $property_eshop_description = $this->propertyManager()->propertyExistsByTag('eshop_description');
        if ($property_eshop_description != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_eshop_description);
            if ((is_null($this->getDataKey('property_eshop_description_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_eshop_description);
            }
        }

        // remove property: Tag(eshop_tag)
        $property_eshop_tag = $this->propertyManager()->propertyExistsByTag('eshop_tag');
        if ($property_eshop_tag != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_eshop_tag);
            if ((is_null($this->getDataKey('property_eshop_tag_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_eshop_tag);
            }
        }

        // remove property: Titulok(title)
        $property_title = $this->propertyManager()->propertyExistsByTag('title');
        if ($property_title != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_title);
            if ((is_null($this->getDataKey('property_title_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_title);
            }
        }

        // remove page type: Nastavenia - eshop (settings_eshop)
        $page_type_settings_eshop = $this->pageTypesManager()->pageTypeExistsByTag('settings_eshop');
        if (($page_type_settings_eshop != false) && (is_null($this->getDataKey('page_type_settings_eshop_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_settings_eshop);
        }

        // remove page type: Nastavenia (settings)
        $page_type_settings = $this->pageTypesManager()->pageTypeExistsByTag('settings');
        if (($page_type_settings != false) && (is_null($this->getDataKey('page_type_settings_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_settings);
        }

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page tags
        PageIds::generatePageTagsList();

    }


}
