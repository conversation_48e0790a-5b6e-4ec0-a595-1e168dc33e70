<?php

use Buxus\Migration\AbstractMigration;
use Email\Migrations\BasicEmailMigration;

/**
 * Automatic generation from (buxus7_clean_install) at 2018-10-09 13:49:59
 * Page generator: page_id=195,196
 */

class AddEshopBasicEmailPages extends AbstractMigration
{
    public function dependencies()
    {
        return array(
            BasicEmailMigration::class
        );
    }

	public function up()
	{

		// page: Automatická registrácia(ID: 195 TAG: automatic_login_data)
		$page_id = $this->getPageIdByTag('automatic_login_data');
		if ($page_id === NULL) {
			$page_type = $this->pageTypesManager()->getPageTypeByTag('basic_email');
			$page_195 = \PageFactory::create($this->getPageIdByTag('E-maily shop modulu'), $page_type->getId());
		} else {
			$page_195 = \PageFactory::get($page_id);
		}
        $page_195->setPageType($this->pageTypesManager()->getPageTypeByTag('basic_email'));
		$page_195->setPageName('Automatická registrácia');
		$page_195->setPageTag('automatic_login_data');
		$page_195->setPageStateId(2);
		$page_195->setPageClassId(1);
		$page_195->setValue('email_sender', '<EMAIL>');
		$page_195->setValue('email_subject', 'Prihlasovacie údaje pre stránku {{HOSTNAME}}');
		$page_195->setValue('email_body_html', file_get_contents(__DIR__ . '/email_templates/login_data_html_email.mustache'));
		$page_195->setValue('mail_embed_images', 'F');
		$page_195->setValue('attachment_list', array (
));
		// set template on MAIN PAGE index::error404
		$this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('basic_email'), 'index', 'error404');
		$page_195->save();

		// page: Automatická registrácia po objednávke(ID: 196 TAG: automatic_login_data_after_order)
		$page_id = $this->getPageIdByTag('automatic_login_data_after_order');
		if ($page_id === NULL) {
			$page_type = $this->pageTypesManager()->getPageTypeByTag('basic_email');
			$page_196 = \PageFactory::create($this->getPageIdByTag('E-maily shop modulu'), $page_type->getId());
		} else {
			$page_196 = \PageFactory::get($page_id);
		}
        $page_196->setPageType($this->pageTypesManager()->getPageTypeByTag('basic_email'));
		$page_196->setPageName('Automatická registrácia po objednávke');
		$page_196->setPageTag('automatic_login_data_after_order');
		$page_196->setPageStateId(2);
		$page_196->setPageClassId(1);
		$page_196->setValue('email_sender', '<EMAIL>');
		$page_196->setValue('email_subject', 'Prihlasovacie údaje pre stránku {{HOSTNAME}}');
		$page_196->setValue('email_body_html', file_get_contents(__DIR__ . '/email_templates/login_data_after_order_html.mustache'));
		$page_196->setValue('mail_embed_images', 'F');
		$page_196->setValue('attachment_list', array (
));
		// set template on MAIN PAGE index::error404
		$this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('basic_email'), 'index', 'error404');
		$page_196->save();

	}

	public function down()
	{
		// remove page: Automatická registrácia po objednávke (automatic_login_data_after_order)
		$page_id = $this->getPageIdByTag('automatic_login_data_after_order');
		if ($page_id !== NULL) {
			\PageFactory::get($page_id)->delete();
		}

		// remove page: Automatická registrácia (automatic_login_data)
		$page_id = $this->getPageIdByTag('automatic_login_data');
		if ($page_id !== NULL) {
			\PageFactory::get($page_id)->delete();
		}

	}

}
