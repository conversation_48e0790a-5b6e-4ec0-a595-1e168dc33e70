<?php

use Buxus\Migration\AbstractMigration;
use Email\Migrations\BasicEmailMigration;
use Email\Migrations\EmailMigration;

/**
 * Automatic generation from (buxus7_clean_install) at 2018-10-09 13:47:08
 * Page generator: page_id=197,198,199,200,201
 */

class AddEshopEmailPages extends AbstractMigration
{
    public function dependencies()
    {
        return array(
            AddEshopEmailPageType::class
        );
    }
	public function up()
	{

		// page: Nová objednávka(ID: 197 TAG: eshop_nova_objednavka)
		$page_id = $this->getPageIdByTag('eshop_nova_objednavka');
		if ($page_id === NULL) {
			$page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_email');
			$page_197 = \PageFactory::create($this->getPageIdByTag('E-maily shop modulu'), $page_type->getId());
		} else {
			$page_197 = \PageFactory::get($page_id);
		}
        $page_197->setPageType($this->pageTypesManager()->getPageTypeByTag('eshop_email'));
		$page_197->setPageName('Nová objednávka');
		$page_197->setPageTag('eshop_nova_objednavka');
		$page_197->setPageStateId(2);
		$page_197->setPageClassId(1);
		$page_197->setValue('email_sender', 'Buxus <<EMAIL>>');
		$page_197->setValue('email_recipients', '<EMAIL>');
		$page_197->setValue('email_subject', 'Nová objednávka č. {{VARIABLE_SYMBOL}} zo stránky {{HOSTNAME}}');
		$page_197->setValue('email_body_html', file_get_contents(__DIR__ . '/email_templates/nova_objednavka_intro.mustache'));
		$page_197->setValue('mail_embed_images', 'F');
		$page_197->setValue('eshop_email_show_order_product_list', 'T');
		$page_197->setValue('eshop_email_show_delivery_address', 'T');
		$page_197->setValue('eshop_email_show_transport_payment', 'T');
		$page_197->setValue('eshop_email_show_order_details', 'T');
		$page_197->setValue('attachment_list', array (
));
		$page_197->save();

		// page: Akceptovaná objednávka(ID: 198 TAG: eshop_akceptovana_objednavka)
		$page_id = $this->getPageIdByTag('eshop_akceptovana_objednavka');
		if ($page_id === NULL) {
			$page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_email');
			$page_198 = \PageFactory::create($this->getPageIdByTag('E-maily shop modulu'), $page_type->getId());
		} else {
			$page_198 = \PageFactory::get($page_id);
		}
        $page_198->setPageType($this->pageTypesManager()->getPageTypeByTag('eshop_email'));
		$page_198->setPageName('Akceptovaná objednávka');
		$page_198->setPageTag('eshop_akceptovana_objednavka');
		$page_198->setPageStateId(2);
		$page_198->setPageClassId(1);
		$page_198->setValue('email_sender', 'Buxus <<EMAIL>>');
		$page_198->setValue('email_recipients', '');
		$page_198->setValue('email_subject', 'Potvrdenie objednávky č. {{VARIABLE_SYMBOL}} zo stránky {{HOSTNAME}}');
		$page_198->setValue('email_body_html', file_get_contents(__DIR__ . '/email_templates/akceptovana_objednavka_intro.mustache'));
		$page_198->setValue('eshop_email_bottom_text', file_get_contents(__DIR__ . '/email_templates/objednavka_editor_signature_intro.mustache'));
		$page_198->setValue('mail_embed_images', 'F');
		$page_198->setValue('eshop_email_show_order_product_list', 'T');
		$page_198->setValue('eshop_email_show_delivery_address', 'T');
		$page_198->setValue('eshop_email_show_transport_payment', 'T');
		$page_198->setValue('eshop_email_show_order_details', 'T');
		$page_198->setValue('attachment_list', array (
));
		$page_198->save();

		// page: Zrušená objednávka(ID: 199 TAG: eshop_zrusena_objednavka)
		$page_id = $this->getPageIdByTag('eshop_zrusena_objednavka');
		if ($page_id === NULL) {
			$page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_email');
			$page_199 = \PageFactory::create($this->getPageIdByTag('E-maily shop modulu'), $page_type->getId());
		} else {
			$page_199 = \PageFactory::get($page_id);
		}
        $page_199->setPageType($this->pageTypesManager()->getPageTypeByTag('eshop_email'));
		$page_199->setPageName('Zrušená objednávka');
		$page_199->setPageTag('eshop_zrusena_objednavka');
		$page_199->setPageStateId(2);
		$page_199->setPageClassId(1);
		$page_199->setValue('email_sender', 'Buxus <<EMAIL>>');
		$page_199->setValue('email_recipients', '');
		$page_199->setValue('email_subject', 'Informácie o zrušení objednávky {{VARIABLE_SYMBOL}} zo dňa {{ORDER_DATETIME}}');
		$page_199->setValue('email_body_html', file_get_contents(__DIR__ . '/email_templates/zrusena_objednavka_intro.mustache'));
		$page_199->setValue('eshop_email_bottom_text', file_get_contents(__DIR__ . '/email_templates/objednavka_editor_signature_intro.mustache'));
		$page_199->setValue('mail_embed_images', 'F');
		$page_199->setValue('eshop_email_show_order_product_list', 'T');
		$page_199->setValue('eshop_email_show_delivery_address', 'T');
		$page_199->setValue('eshop_email_show_transport_payment', 'T');
		$page_199->setValue('eshop_email_show_order_details', 'T');
		$page_199->setValue('attachment_list', array (
));
		$page_199->save();

		// page: Expedovaná objednávka(ID: 200 TAG: eshop_expedovana_objednavka)
		$page_id = $this->getPageIdByTag('eshop_expedovana_objednavka');
		if ($page_id === NULL) {
			$page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_email');
			$page_200 = \PageFactory::create($this->getPageIdByTag('E-maily shop modulu'), $page_type->getId());
		} else {
			$page_200 = \PageFactory::get($page_id);
		}
        $page_200->setPageType($this->pageTypesManager()->getPageTypeByTag('eshop_email'));
		$page_200->setPageName('Expedovaná objednávka');
		$page_200->setPageTag('eshop_expedovana_objednavka');
		$page_200->setPageStateId(2);
		$page_200->setPageClassId(1);
		$page_200->setValue('email_sender', 'Buxus <<EMAIL>>');
		$page_200->setValue('email_recipients', '');
		$page_200->setValue('email_subject', 'Informácia o expedovaní objednávky {{VARIABLE_SYMBOL}} zo dňa {{ORDER_DATETIME}}');
		$page_200->setValue('email_body_html', file_get_contents(__DIR__ . '/email_templates/expedovana_objednavka_intro.mustache'));
		$page_200->setValue('eshop_email_bottom_text', file_get_contents(__DIR__ . '/email_templates/objednavka_editor_signature_intro.mustache'));
		$page_200->setValue('mail_embed_images', 'F');
		$page_200->setValue('eshop_email_show_order_product_list', 'T');
		$page_200->setValue('eshop_email_show_delivery_address', 'T');
		$page_200->setValue('eshop_email_show_transport_payment', 'T');
		$page_200->setValue('eshop_email_show_order_details', 'T');
		$page_200->setValue('attachment_list', array (
));
		$page_200->save();

		// page: Objednávka čaká na tovar(ID: 201 TAG: eshop_caka_na_tovar_objednavka)
		$page_id = $this->getPageIdByTag('eshop_caka_na_tovar_objednavka');
		if ($page_id === NULL) {
			$page_type = $this->pageTypesManager()->getPageTypeByTag('eshop_email');
			$page_201 = \PageFactory::create($this->getPageIdByTag('E-maily shop modulu'), $page_type->getId());
		} else {
			$page_201 = \PageFactory::get($page_id);
		}
        $page_201->setPageType($this->pageTypesManager()->getPageTypeByTag('eshop_email'));
		$page_201->setPageName('Objednávka čaká na tovar');
		$page_201->setPageTag('eshop_caka_na_tovar_objednavka');
		$page_201->setPageStateId(2);
		$page_201->setPageClassId(1);
		$page_201->setValue('email_sender', 'Buxus <<EMAIL>>');
		$page_201->setValue('email_recipients', '');
		$page_201->setValue('email_subject', 'Objednávka {{VARIABLE_SYMBOL}} zo dňa {{ORDER_DATETIME}} čaká na tovar');
		$page_201->setValue('email_body_html', file_get_contents(__DIR__ . '/email_templates/objednavka_caka_na_tovar_intro.mustache'));
		$page_201->setValue('eshop_email_bottom_text', file_get_contents(__DIR__ . '/email_templates/objednavka_editor_signature_intro.mustache'));
		$page_201->setValue('mail_embed_images', 'F');
		$page_201->setValue('eshop_email_show_order_product_list', 'T');
		$page_201->setValue('eshop_email_show_delivery_address', 'F');
		$page_201->setValue('eshop_email_show_transport_payment', 'T');
		$page_201->setValue('eshop_email_show_order_details', 'T');
		$page_201->setValue('attachment_list', array (
));
		$page_201->save();

	}

	public function down()
	{
		// remove page: Objednávka čaká na tovar (eshop_caka_na_tovar_objednavka)
		$page_id = $this->getPageIdByTag('eshop_caka_na_tovar_objednavka');
		if ($page_id !== NULL) {
			\PageFactory::get($page_id)->delete();
		}

		// remove page: Expedovaná objednávka (eshop_expedovana_objednavka)
		$page_id = $this->getPageIdByTag('eshop_expedovana_objednavka');
		if ($page_id !== NULL) {
			\PageFactory::get($page_id)->delete();
		}

		// remove page: Zrušená objednávka (eshop_zrusena_objednavka)
		$page_id = $this->getPageIdByTag('eshop_zrusena_objednavka');
		if ($page_id !== NULL) {
			\PageFactory::get($page_id)->delete();
		}

		// remove page: Akceptovaná objednávka (eshop_akceptovana_objednavka)
		$page_id = $this->getPageIdByTag('eshop_akceptovana_objednavka');
		if ($page_id !== NULL) {
			\PageFactory::get($page_id)->delete();
		}

		// remove page: Nová objednávka (eshop_nova_objednavka)
		$page_id = $this->getPageIdByTag('eshop_nova_objednavka');
		if ($page_id !== NULL) {
			\PageFactory::get($page_id)->delete();
		}

	}

}
