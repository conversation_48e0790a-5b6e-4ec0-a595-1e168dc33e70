<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Translate\TranslatorFactory;

/**
 * Automatic generation from (ipredplatnedb) at 2017-02-06 13:06:43
 * Translation generator: sections=sk.cart
 */

class AddShoppingCartLoginTranslations extends AbstractMigration {
	public function dependencies() {
		return [
			'Buxus\\Translate\\Migrations\\TranslationTableMigration',
		];
}

	public function up() {
		$translator = TranslatorFactory::get();
		$editor = $translator->getEditor();

		if (!(in_array('sk', $translator->getAvailableLanguages()))) {
			$translator->setAvailableLanguages(array_merge($translator->getAvailableLanguages(), array('sk',)));
		}

		// section: cart, language: sk
		$translations = array(
			'alebo_si_nechajte' => 'Alebo si nechajte',
			'login_popup_activated_user_text' => 'Ak sa prihlásite, nemusíte vyplňovať Vaše údaje. Ak si nepamätáte heslo, môžete si vyžiadať reset hesla.',
			'login_popup_activated_user_title' => 'Konto pre tento e-mail už existuje',
			'login_popup_automatic_registration_text' => 'Prihláste sa heslom, ktoré ste dostali e-mailom.',
			'login_popup_automatic_registration_title' => 'Z tejto e-mailovej adresy ste už uskutočnili nákup',
			'zaslat_docasne_heslo_na_email' => 'zaslať dočasné heslo na e-mail',
			);
		foreach ($translations as $key => $value) {
			$editor->setTranslation($key, $value, 'cart', 'sk', true);
		}
		$editor->save();


	}

	public function down() {
	}

}
