<?php
namespace Layout\Migrations;

use Buxus\Migration\AbstractMigration;
use Buxus\PageType\PageTypePropertyItem;
use Buxus\Property\Types\Input;
use DynamicCategory\DynamicQueryFsSortProperty;

class LayoutDynamicCategoriesSortProperty extends AbstractMigration {
    public function dependencies() {
        return array(
            LayoutDynamicCategories::class,
        );
    }

    public function up() {
        $property_sort = $this->propertyManager()->propertyExistsByTag('fs_sort');
        if ($property_sort === false) {
            $property_sort = new Input();
            $property_sort->setTag('fs_sort');
            $property_sort->setDescription('Zoradenie produktov');
            $property_sort->setExtendedDescription('');
            $property_sort->setName('Zoradenie');
            $property_sort->setClassId('4');
            $property_sort->setShowType(NULL);
            $property_sort->setShowTypeTag('custom_property');
            $property_sort->setValueType('custom_property');
            $property_sort->setDefaultValue('');
            $property_sort->setMultiOperations(false);
            $property_sort->setInputString('');
            $property_sort->setAttribute('tab', '');
            $property_sort->setAttribute('class_name', DynamicQueryFsSortProperty::class);
            $property_sort->setAttribute('inherit_value', 'F');

            $this->propertyManager()->saveProperty($property_sort);

        } else {
            $this->writeLine('Property with tag fs_sort already exists');
            $this->setDataKey('property_fs_sort_existed', true);
        }

        // page type: Abstract layout element (abstract_layout_element)
        $page_type_layout_element_dynamic_query = $this->pageTypesManager()->pageTypeExistsByTag('layout_element_dynamic_query');

        $property = $this->propertyManager()->getPropertyByTag('fs_sort');
        $property_id = $property->getId();
        $tmp = $page_type_layout_element_dynamic_query->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setRequired(false);
            $page_type_layout_element_dynamic_query->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($page_type_layout_element_dynamic_query);

    }

    public function down() {
        // remove property: CSS trieda(css_class)
        $property_sort = $this->propertyManager()->propertyExistsByTag('fs_sort');
        if ($property_sort != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_sort);
            if ((is_null($this->getDataKey('property_fs_sort_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_sort);
            }
        }

        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}