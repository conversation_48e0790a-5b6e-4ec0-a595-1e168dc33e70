<?php

use Buxus\Migration\AbstractMigration;

class AddAuthentificationEmailPages extends AbstractMigration
{
    public function dependencies()
    {
        return [
            \Authentication\Migrations\CreateBaseAuthentificationPages::class,
            \Email\Migrations\BasicEmailMigration::class,
        ];
    }

    public function up()
    {
        $page_id = $this->getPageIdByTag(\Authentication\AuthenticationPages::REGISTRATION_EMAIL_FOR_USER);
        $page_type = $this->pageTypesManager()->getPageTypeByTag('basic_email');
        if ($page_id === null) {
            $page_217 = \PageFactory::create($this->getPageIdByTag('authentification'), $page_type->getId());
        } else {
            $page_217 = \PageFactory::get($page_id);
            $page_217->setPageType($page_type);
        }
        $page_217->setPageType($this->pageTypesManager()->getPageTypeByTag('basic_email'));
        $page_217->setPageName('Autentifikácia - registračný email');
        $page_217->setPageTag(\Authentication\AuthenticationPages::REGISTRATION_EMAIL_FOR_USER);
        $page_217->setPageStateId(2);
        $page_217->setPageClassId(1);
        $page_217->setValue('email_sender', 'Buxus Authentication <<EMAIL>>');
        $page_217->setValue('email_recipients', '');
        $page_217->setValue('email_subject', 'Registrácia zo stránky {{HOSTNAME}}');
        $page_217->setValue(
            'email_body_html',
            file_get_contents(__DIR__ . '/email_templates/registration_activation_email_html.mustache')
        );
        $page_217->setValue('eshop_email_bottom_text', '');
        $page_217->setValue('mail_embed_images', 'F');
        $page_217->setValue('attachment_list', array());
        // set template on MAIN PAGE index::error404
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('basic_email'), 'index', 'error404');
        $page_217->save();

        $page_type = $this->pageTypesManager()->getPageTypeByTag('basic_email');
        $page_id = $this->getPageIdByTag(\Authentication\AuthenticationPages::REGISTRATION_EMAIL_FOR_ADMIN);
        if ($page_id === null) {
            $page_218 = \PageFactory::create($this->getPageIdByTag('authentification'), $page_type->getId());
        } else {
            $page_218 = \PageFactory::get($page_id);
            $page_218->setPageType($page_type);
        }
        $page_218->setPageType($this->pageTypesManager()->getPageTypeByTag('basic_email'));
        $page_218->setPageName('Autentifikácia - registrácia používateľa');
        $page_218->setPageTag(\Authentication\AuthenticationPages::REGISTRATION_EMAIL_FOR_ADMIN);
        $page_218->setPageStateId(2);
        $page_218->setPageClassId(1);
        $page_218->setValue('email_sender', 'Buxus Authentication <<EMAIL>>');
        $page_218->setValue('email_recipients', '<EMAIL>');
        $page_218->setValue('email_subject', 'Registrácia používateľa na stránke {{HOSTNAME}}');
        $page_218->setValue(
            'email_body_html',
            file_get_contents(__DIR__ . '/email_templates/registration_admin_email_html.mustache')
        );
        $page_218->setValue('eshop_email_bottom_text', '');
        $page_218->setValue('mail_embed_images', 'F');
        $page_218->setValue('attachment_list', array());
        // set template on MAIN PAGE index::error404
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('basic_email'), 'index', 'error404');
        $page_218->save();

        $page_type = $this->pageTypesManager()->getPageTypeByTag('basic_email');
        $page_id = $this->getPageIdByTag(\Authentication\AuthenticationPages::FORGOTTEN_PASSWORD_EMAIL);
        if ($page_id === null) {
            $page_219 = \PageFactory::create($this->getPageIdByTag('authentification'), $page_type->getId());
        } else {
            $page_219 = \PageFactory::get($page_id);
            $page_219->setPageType($page_type);
        }
        $page_219->setPageType($this->pageTypesManager()->getPageTypeByTag('basic_email'));
        $page_219->setPageName('Autentifikácia - zabudnuté heslo');
        $page_219->setPageTag(\Authentication\AuthenticationPages::FORGOTTEN_PASSWORD_EMAIL);
        $page_219->setPageStateId(2);
        $page_219->setPageClassId(1);
        $page_219->setValue('email_sender', 'Buxus Authentication <<EMAIL>>');
        $page_219->setValue('email_recipients', '');
        $page_219->setValue('email_subject', 'Zabudnuté heslo na stránke {{HOSTNAME}}');
        $page_219->setValue(
            'email_body_html',
            file_get_contents(__DIR__ . '/email_templates/forgot_password_email_link_html.mustache')
        );
        $page_219->setValue('eshop_email_bottom_text', '');
        $page_219->setValue('mail_embed_images', 'F');
        $page_219->setValue('attachment_list', array());
        // set template on MAIN PAGE index::error404
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('basic_email'), 'index', 'error404');
        $page_219->save();
    }

    public function down()
    {
        // remove page: Autentifikácia - zabudnuté heslo (auth_forgotten_password_email)
        $page_id = $this->getPageIdByTag(\Authentication\AuthenticationPages::FORGOTTEN_PASSWORD_EMAIL);
        if ($page_id !== null) {
            \PageFactory::get($page_id)->delete();
        }

        // remove page: Autentifikácia - registrácia používateľa (auth_registration_email_admin)
        $page_id = $this->getPageIdByTag(\Authentication\AuthenticationPages::REGISTRATION_EMAIL_FOR_ADMIN);
        if ($page_id !== null) {
            \PageFactory::get($page_id)->delete();
        }

        // remove page: Autentifikácia - registračný email (auth_registration_email_user)
        $page_id = $this->getPageIdByTag(\Authentication\AuthenticationPages::REGISTRATION_EMAIL_FOR_USER);
        if ($page_id !== null) {
            \PageFactory::get($page_id)->delete();
        }
    }
}
