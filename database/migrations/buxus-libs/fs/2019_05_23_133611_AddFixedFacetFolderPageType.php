<?php

use Buxus\Migration\AbstractMigration;
use Buxus\PageType\PageType;
use Buxus\Property\Property;
use Buxus\PageType\PageTypePropertyItem;

/**
 * Automatic generation from (koku_new) at 2019-05-23 13:36:11
 * PageType generator: page_type=fixed_facet_folder
 */
class AddFixedFacetFolderPageType extends AbstractMigration
{
    public function up()
    {
        // page type: Priečinok (folder)
        $pageTypeFolder = $this->pageTypesManager()->pageTypeExistsByTag('folder');
        if ($pageTypeFolder === false) {
            $pageTypeFolder = new PageType();
            $pageTypeFolder->setTag('folder');
            $pageTypeFolder->setName('Priečinok');
            $pageTypeFolder->setPageClassId(1);
            $pageTypeFolder->setDefaultTemplateId(1);
            $pageTypeFolder->setDeleteTrigger('');
            $pageTypeFolder->setIncludeInSync(null);
            $pageTypeFolder->setPageDetailsLayout('');
            $pageTypeFolder->setPageSortTypeTag('sort_date_time');
            $pageTypeFolder->setPageTypeOrder(30);
            $pageTypeFolder->setPostmoveTrigger('');
            $pageTypeFolder->setPostsubmitTrigger('');
            $pageTypeFolder->setPresubmitTrigger('');
            $pageTypeFolder->setParent(null);
        } else {
            $this->writeLine('Page type with tag folder already exists');
            $this->setDataKey('page_type_folder_existed', true);
        }
        $this->pageTypesManager()->savePageType($pageTypeFolder);
        // set template on MAIN PAGE index::error404
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('folder'), 'index', 'error404');

        // property: FS cesta(fixed_path)
        $propertyFixedPath = $this->propertyManager()->propertyExistsByTag('fixed_path');
        if ($propertyFixedPath === false) {
            $propertyFixedPath = new Property();
            $propertyFixedPath->setTag('fixed_path');
            $propertyFixedPath->setDescription('Unikátna cesta definijúca fixnovaný fazet');
            $propertyFixedPath->setExtendedDescription('');
            $propertyFixedPath->setName('FS cesta');
            $propertyFixedPath->setClassId(4);
            $propertyFixedPath->setShowType(null);
            $propertyFixedPath->setShowTypeTag('text');
            $propertyFixedPath->setValueType('oneline_text');
            $propertyFixedPath->setDefaultValue('');
            $propertyFixedPath->setMultiOperations(false);
            $propertyFixedPath->setInputString('');
            $propertyFixedPath->setAttribute('tab', '');
            $propertyFixedPath->setAttribute('size', 60);
            $propertyFixedPath->setAttribute('maxlength', '');
            $propertyFixedPath->setAttribute('readonly', '0');
            $propertyFixedPath->setAttribute('pattern', '');
            $propertyFixedPath->setAttribute('inherit_value', '0');
            $propertyFixedPath->setAttribute('onchange-js', '');
            $propertyFixedPath->setAttribute('onkeyup-js', '');
            $propertyFixedPath->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyFixedPath);
        } else {
            $this->writeLine('Property with tag fixed_path already exists');
            $this->setDataKey('property_fixed_path_existed', true);
        }

        // page type: Priečinok fixovaných fazetov (fixed_facet_folder)
        $pageTypeFixedFacetFolder = $this->pageTypesManager()->pageTypeExistsByTag('fixed_facet_folder');
        if ($pageTypeFixedFacetFolder === false) {
            $pageTypeFixedFacetFolder = new PageType();
            $pageTypeFixedFacetFolder->setTag('fixed_facet_folder');
            $pageTypeFixedFacetFolder->setName('Priečinok fixovaných fazetov');
            $pageTypeFixedFacetFolder->setPageClassId(1);
            $pageTypeFixedFacetFolder->setDefaultTemplateId(1);
            $pageTypeFixedFacetFolder->setDeleteTrigger('');
            $pageTypeFixedFacetFolder->setIncludeInSync(null);
            $pageTypeFixedFacetFolder->setPageDetailsLayout('');
            $pageTypeFixedFacetFolder->setPageSortTypeTag('sort_date_time');
            $pageTypeFixedFacetFolder->setPageTypeOrder(0);
            $pageTypeFixedFacetFolder->setPostmoveTrigger('');
            $pageTypeFixedFacetFolder->setPostsubmitTrigger('');
            $pageTypeFixedFacetFolder->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('folder');
            $pageTypeFixedFacetFolder->setParent($parent);
        } else {
            $this->writeLine('Page type with tag fixed_facet_folder already exists');
            $this->setDataKey('page_type_fixed_facet_folder_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('fixed_path');
        $propertyId = $property->getId();
        $tmp = $pageTypeFixedFacetFolder->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(1);
            $tmp->setRequired(false);
            $pageTypeFixedFacetFolder->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($pageTypeFixedFacetFolder);

        if ($this->pageTypeExists('eshop_catalog')) {
            $this->addPageTypeSuperiorPageType('folder', 'eshop_catalog');
        }
        if ($this->pageTypeExists('settings')) {
            $this->addPageTypeSuperiorPageType('folder', 'settings');
        }
        if ($this->pageTypeExists('folder')) {
            $this->addPageTypeSuperiorPageType('folder', 'folder');
        }
        if ($this->pageTypeExists('main_page')) {
            $this->addPageTypeSuperiorPageType('folder', 'main_page');
        }
        if ($this->pageTypeExists('fixed_facet')) {
            $this->addPageTypeSuperiorPageType('fixed_facet_folder', 'fixed_facet');
        }
        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove page type: Priečinok fixovaných fazetov (fixed_facet_folder)
        $pageTypeFixedFacetFolder = $this->pageTypesManager()->pageTypeExistsByTag('fixed_facet_folder');
        if (($pageTypeFixedFacetFolder != false) && (is_null($this->getDataKey('page_type_fixed_facet_folder_existed')))) {
            $this->pageTypesManager()->removePageType($pageTypeFixedFacetFolder);
        }

        // remove property: FS cesta(fixed_path)
        $propertyFixedPath = $this->propertyManager()->propertyExistsByTag('fixed_path');
        if ($propertyFixedPath !== false) {
            $supportedPageTypes = $this->propertyManager()->getSupportedPageTypes($propertyFixedPath);
            if (($this->getDataKey('property_fixed_path_existed') === null) && (count($supportedPageTypes) === 0)) {
                $this->propertyManager()->removeProperty($propertyFixedPath);
            }
        }

        // remove page type: Priečinok (folder)
        $pageTypeFolder = $this->pageTypesManager()->pageTypeExistsByTag('folder');
        if (($pageTypeFolder != false) && (is_null($this->getDataKey('page_type_folder_existed')))) {
            $this->pageTypesManager()->removePageType($pageTypeFolder);
        }
        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
