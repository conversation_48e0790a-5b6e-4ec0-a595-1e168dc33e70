<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

class CreateListViewPriorityBoostWeightProperty extends AbstractMigration
{
    public function up()
    {
        // property: Váha prioritného zoradenia v zozname produktov(list_view_priority_boost_weight)
        $propertyListViewPriorityBoostWeight = $this->propertyManager()->propertyExistsByTag('list_view_priority_boost_weight');
        if ($propertyListViewPriorityBoostWeight === false) {
            $propertyListViewPriorityBoostWeight = new Property();
            $propertyListViewPriorityBoostWeight->setTag('list_view_priority_boost_weight');
            $propertyListViewPriorityBoostWeight->setDescription('Vlastnosť umožňuje prioritizáciu zoradenia produktu v zozname produktov. Štandardne sú produkty zoradené podľa ich dostupnosti a podľa dátumu triedenia stránky v Buxuse. Pomocou tejto vlastnosti je možné prioritizovať zobrazenie určitých produktov - čím väčšia váha, tým skôr je produkt zobrazený.');
            $propertyListViewPriorityBoostWeight->setExtendedDescription('');
            $propertyListViewPriorityBoostWeight->setName('Váha prioritného zoradenia v zozname produktov');
            $propertyListViewPriorityBoostWeight->setClassId(4);
            $propertyListViewPriorityBoostWeight->setShowType(null);
            $propertyListViewPriorityBoostWeight->setShowTypeTag('text');
            $propertyListViewPriorityBoostWeight->setValueType('oneline_text');
            $propertyListViewPriorityBoostWeight->setDefaultValue('0');
            $propertyListViewPriorityBoostWeight->setMultiOperations(false);
            $propertyListViewPriorityBoostWeight->setInputString('');
            $propertyListViewPriorityBoostWeight->setAttribute('tab', '');
            $propertyListViewPriorityBoostWeight->setAttribute('size', '10');
            $propertyListViewPriorityBoostWeight->setAttribute('maxlength', '');
            $propertyListViewPriorityBoostWeight->setAttribute('readonly', 'F');
            $propertyListViewPriorityBoostWeight->setAttribute('pattern', '');
            $propertyListViewPriorityBoostWeight->setAttribute('inherit_value', 'F');
            $propertyListViewPriorityBoostWeight->setAttribute('onchange-js', '');
            $propertyListViewPriorityBoostWeight->setAttribute('onkeyup-js', '');
            $propertyListViewPriorityBoostWeight->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyListViewPriorityBoostWeight);
        } else {
            $this->writeLine('Property with tag list_view_priority_boost_weight already exists');
            $this->setDataKey('property_list_view_priority_boost_weight_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('list_view_priority_boost_weight', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Váha prioritného zoradenia v zozname produktov(list_view_priority_boost_weight)
        $propertyListViewPriorityBoostWeight = $this->propertyManager()->propertyExistsByTag('list_view_priority_boost_weight');
        if ($propertyListViewPriorityBoostWeight !== false) {
            $supportedPageTypes = $this->propertyManager()->getSupportedPageTypes($propertyListViewPriorityBoostWeight);
            if (($this->getDataKey('property_list_view_priority_boost_weight_existed') === null) && (count($supportedPageTypes) === 0)) {
                $this->propertyManager()->removeProperty($propertyListViewPriorityBoostWeight);
            }
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
