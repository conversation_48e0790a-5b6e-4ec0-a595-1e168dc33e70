<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (buxus7_clean_install) at 2018-11-22 08:59:13
 * Property generator: property=rendered_email_bottom_text
 */
class AddBottomTextProperty extends AbstractMigration
{
    public function dependencies()
    {
        return array(
            \Email\Migrations\BasicEmailMigration::class
        );
    }

    public function up()
    {

        // property: Text spodný(rendered_email_bottom_text)
        $property_rendered_email_bottom_text = $this->propertyManager()->propertyExistsByTag('rendered_email_bottom_text');
        if ($property_rendered_email_bottom_text === false) {
            $property_rendered_email_bottom_text = new Property();
            $property_rendered_email_bottom_text->setTag('rendered_email_bottom_text');
            $property_rendered_email_bottom_text->setDescription('Text uvádzaný v spodnej časti e-mailu');
            $property_rendered_email_bottom_text->setExtendedDescription('');
            $property_rendered_email_bottom_text->setName('Text spodný');
            $property_rendered_email_bottom_text->setClassId(4);
            $property_rendered_email_bottom_text->setShowType(NULL);
            $property_rendered_email_bottom_text->setShowTypeTag('textarea');
            $property_rendered_email_bottom_text->setValueType('multiline_text');
            $property_rendered_email_bottom_text->setDefaultValue('');
            $property_rendered_email_bottom_text->setMultiOperations(false);
            $property_rendered_email_bottom_text->setInputString('');
            $property_rendered_email_bottom_text->setAttribute('tab', '');
            $property_rendered_email_bottom_text->setAttribute('cols', '60');
            $property_rendered_email_bottom_text->setAttribute('rows', '3');
            $property_rendered_email_bottom_text->setAttribute('dhtml-edit', '1');
            $property_rendered_email_bottom_text->setAttribute('dhtml-configuration', 'full');
            $property_rendered_email_bottom_text->setAttribute('import-word', '0');
            $property_rendered_email_bottom_text->setAttribute('auto', '');
            $property_rendered_email_bottom_text->setAttribute('inherit_value', 'F');
            $property_rendered_email_bottom_text->setAttribute('onchange-js', '');
            $property_rendered_email_bottom_text->setAttribute('onkeyup-js', '');
            $property_rendered_email_bottom_text->setAttribute('onkeydown-js', '');
            $property_rendered_email_bottom_text->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($property_rendered_email_bottom_text);
        } else {
            $this->writeLine('Property with tag rendered_email_bottom_text already exists');
            $this->setDataKey('property_rendered_email_bottom_text_existed', true);
        }
        if ($this->pageTypeExists('basic_email')) {
            $this->addPropertyToPageType('rendered_email_bottom_text', 'basic_email', false);
        }

        // regenerate property constants
        $this->propertyManager()->generateConstants();

    }

    public function down()
    {
        // remove property: Text spodný(rendered_email_bottom_text)
        $property_rendered_email_bottom_text = $this->propertyManager()->propertyExistsByTag('rendered_email_bottom_text');
        if ($property_rendered_email_bottom_text != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_rendered_email_bottom_text);
            if ((is_null($this->getDataKey('property_rendered_email_bottom_text_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_rendered_email_bottom_text);
            }
        }

        // regenerate property constants
        $this->propertyManager()->generateConstants();

    }

}
