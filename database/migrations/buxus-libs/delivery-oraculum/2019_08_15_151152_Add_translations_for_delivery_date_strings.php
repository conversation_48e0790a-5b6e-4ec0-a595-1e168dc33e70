<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (najlyze) at 2019-08-15 15:11:52
 */
class Add_translations_for_delivery_date_strings extends AbstractMigration
{
    public function up()
    {
        \DB::table('tblTranslations')->insert([
            ['lang' => 'sk', 'collection' => 'eshop', 'tag' => 'on_monday', 'value' => 'v pondelok', 'translated' => 1],
            ['lang' => 'sk', 'collection' => 'eshop', 'tag' => 'on_tuesday', 'value' => 'v utorok', 'translated' => 1],
            ['lang' => 'sk', 'collection' => 'eshop', 'tag' => 'on_wednesday', 'value' => 'v stredu', 'translated' => 1],
            ['lang' => 'sk', 'collection' => 'eshop', 'tag' => 'on_thursday', 'value' => 'vo štvrtok', 'translated' => 1],
            ['lang' => 'sk', 'collection' => 'eshop', 'tag' => 'on_friday', 'value' => 'v piatok', 'translated' => 1],
            ['lang' => 'sk', 'collection' => 'eshop', 'tag' => 'on_saturday', 'value' => 'v sobotu', 'translated' => 1],
            ['lang' => 'sk', 'collection' => 'eshop', 'tag' => 'on_sunday', 'value' => 'v nedeľu', 'translated' => 1],
            ['lang' => 'sk', 'collection' => 'eshop', 'tag' => 'kurierom__s_u_vas__s__s', 'value' => 'Kuriérom %s u Vás %s %s', 'translated' => 1],
        ]);
    }

    public function down()
    {
        \DB::table('tblTranslations')->where('lang', 'sk')->where('collection', 'eshop')->where('tag', 'on_monday')->delete();
        \DB::table('tblTranslations')->where('lang', 'sk')->where('collection', 'eshop')->where('tag', 'on_tuesday')->delete();
        \DB::table('tblTranslations')->where('lang', 'sk')->where('collection', 'eshop')->where('tag', 'on_wednesday')->delete();
        \DB::table('tblTranslations')->where('lang', 'sk')->where('collection', 'eshop')->where('tag', 'on_thursday')->delete();
        \DB::table('tblTranslations')->where('lang', 'sk')->where('collection', 'eshop')->where('tag', 'on_friday')->delete();
        \DB::table('tblTranslations')->where('lang', 'sk')->where('collection', 'eshop')->where('tag', 'on_saturday')->delete();
        \DB::table('tblTranslations')->where('lang', 'sk')->where('collection', 'eshop')->where('tag', 'on_sunday')->delete();
        \DB::table('tblTranslations')->where('lang', 'sk')->where('collection', 'eshop')->where('tag', 'kurierom__s_u_vas__s__s')->delete();
    }
}
