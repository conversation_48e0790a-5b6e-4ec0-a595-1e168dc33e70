<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (rinoparts) at 2022-05-23 14:54:25
 */
class AddSumVatToEnclosuresTableMigrationB extends AbstractMigration
{
    public function up()
    {
        Schema::table('onix_enclosures', function ($table) {
            $table->string('sum_vat');
        });
    }

    public function down()
    {
        Schema::table('onix_enclosures', function ($table) {
            $table->dropColumn('sum_vat');
        });
    }
}
