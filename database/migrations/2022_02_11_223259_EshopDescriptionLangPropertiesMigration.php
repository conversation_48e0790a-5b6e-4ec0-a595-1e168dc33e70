<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

/**
 * Automatic generation from (rinoparts) at 2022-02-11 22:32:59
 * Property generator: property=eshop_description_cz,eshop_description_en
 */
class EshopDescriptionLangPropertiesMigration extends AbstractMigration
{
    public function up()
    {
        // property: Popis [CZ](eshop_description_cz)
        $propertyEshopDescriptionCz = $this->propertyManager()->propertyExistsByTag('eshop_description_cz');
        if ($propertyEshopDescriptionCz === false) {
            $propertyEshopDescriptionCz = new Property();
            $propertyEshopDescriptionCz->setTag('eshop_description_cz');
            $propertyEshopDescriptionCz->setDescription('Popis, ktorý sa zobrazuje používateľovi.');
            $propertyEshopDescriptionCz->setExtendedDescription(null);
            $propertyEshopDescriptionCz->setName('Popis [CZ]');
            $propertyEshopDescriptionCz->setClassId(4);
            $propertyEshopDescriptionCz->setShowType(null);
            $propertyEshopDescriptionCz->setShowTypeTag('textarea');
            $propertyEshopDescriptionCz->setValueType('multiline_text');
            $propertyEshopDescriptionCz->setDefaultValue('');
            $propertyEshopDescriptionCz->setMultiOperations(false);
            $propertyEshopDescriptionCz->setInputString('');
            $propertyEshopDescriptionCz->setAttribute('tab', 'CZ');
            $propertyEshopDescriptionCz->setAttribute('cols', '60');
            $propertyEshopDescriptionCz->setAttribute('rows', '');
            $propertyEshopDescriptionCz->setAttribute('dhtml-edit', '0');
            $propertyEshopDescriptionCz->setAttribute('dhtml-configuration', 'full');
            $propertyEshopDescriptionCz->setAttribute('import-word', '0');
            $propertyEshopDescriptionCz->setAttribute('auto', '1');
            $propertyEshopDescriptionCz->setAttribute('inherit_value', 'F');
            $propertyEshopDescriptionCz->setAttribute('onchange-js', '');
            $propertyEshopDescriptionCz->setAttribute('onkeyup-js', '');
            $propertyEshopDescriptionCz->setAttribute('onkeydown-js', '');
            $propertyEshopDescriptionCz->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($propertyEshopDescriptionCz);
        } else {
            $this->writeLine('Property with tag eshop_description_cz already exists');
            $this->setDataKey('property_eshop_description_cz_existed', true);
        }
        if ($this->pageTypeExists('eshop_transport_type')) {
            $this->addPropertyToPageType('eshop_description_cz', 'eshop_transport_type', false);
        }
        if ($this->pageTypeExists('eshop_payment_type')) {
            $this->addPropertyToPageType('eshop_description_cz', 'eshop_payment_type', false);
        }

        // property: Popis [EN](eshop_description_en)
        $propertyEshopDescriptionEn = $this->propertyManager()->propertyExistsByTag('eshop_description_en');
        if ($propertyEshopDescriptionEn === false) {
            $propertyEshopDescriptionEn = new Property();
            $propertyEshopDescriptionEn->setTag('eshop_description_en');
            $propertyEshopDescriptionEn->setDescription('Popis, ktorý sa zobrazuje používateľovi.');
            $propertyEshopDescriptionEn->setExtendedDescription(null);
            $propertyEshopDescriptionEn->setName('Popis [EN]');
            $propertyEshopDescriptionEn->setClassId(4);
            $propertyEshopDescriptionEn->setShowType(null);
            $propertyEshopDescriptionEn->setShowTypeTag('textarea');
            $propertyEshopDescriptionEn->setValueType('multiline_text');
            $propertyEshopDescriptionEn->setDefaultValue('');
            $propertyEshopDescriptionEn->setMultiOperations(false);
            $propertyEshopDescriptionEn->setInputString('');
            $propertyEshopDescriptionEn->setAttribute('tab', 'EN');
            $propertyEshopDescriptionEn->setAttribute('cols', '60');
            $propertyEshopDescriptionEn->setAttribute('rows', '');
            $propertyEshopDescriptionEn->setAttribute('dhtml-edit', '0');
            $propertyEshopDescriptionEn->setAttribute('dhtml-configuration', 'full');
            $propertyEshopDescriptionEn->setAttribute('import-word', '0');
            $propertyEshopDescriptionEn->setAttribute('auto', '1');
            $propertyEshopDescriptionEn->setAttribute('inherit_value', 'F');
            $propertyEshopDescriptionEn->setAttribute('onchange-js', '');
            $propertyEshopDescriptionEn->setAttribute('onkeyup-js', '');
            $propertyEshopDescriptionEn->setAttribute('onkeydown-js', '');
            $propertyEshopDescriptionEn->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($propertyEshopDescriptionEn);
        } else {
            $this->writeLine('Property with tag eshop_description_en already exists');
            $this->setDataKey('property_eshop_description_en_existed', true);
        }
        if ($this->pageTypeExists('eshop_transport_type')) {
            $this->addPropertyToPageType('eshop_description_en', 'eshop_transport_type', false);
        }
        if ($this->pageTypeExists('eshop_payment_type')) {
            $this->addPropertyToPageType('eshop_description_en', 'eshop_payment_type', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Popis [EN](eshop_description_en)
        $propertyEshopDescriptionEn = $this->propertyManager()->propertyExistsByTag('eshop_description_en');
        if (($propertyEshopDescriptionEn !== false) && ($this->getDataKey('property_eshop_description_en_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyEshopDescriptionEn);
        }

        // remove property: Popis [CZ](eshop_description_cz)
        $propertyEshopDescriptionCz = $this->propertyManager()->propertyExistsByTag('eshop_description_cz');
        if (($propertyEshopDescriptionCz !== false) && ($this->getDataKey('property_eshop_description_cz_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyEshopDescriptionCz);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
