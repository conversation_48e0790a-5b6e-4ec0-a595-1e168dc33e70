<?php

use Buxus\Migration\AbstractMigration;

/**
 * Automatic generation from (buxus_rinoparts_test) at 2022-02-24 08:11:08
 * Page generator: page_id=655819,655820
 */
class TransportTypesPagesMigration extends AbstractMigration
{
    public function up()
    {
        // page: PPL - kuriér(ID: 655819 TAG: ppl_carrier)
        $pageId = $this->getPageIdByTag('ppl_carrier');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('eshop_transport_type');
            $page655819 = \PageFactory::create($this->getPageIdByTag('eshop_delivery_types'), $pageType->getId());
        } else {
            $page655819 = \PageFactory::get($pageId);
        }
        $page655819->setPageName('PPL - kuriér');
        $page655819->setPageTag('ppl_carrier');
        $page655819->setPageStateId('1');
        $page655819->setPageClassId(1);
        $page655819->setValue('exchange_rate_cz', '');
        $page655819->setValue('title', 'Kuriérska služba - PPL');
        $page655819->setValue('title_en', 'Kuriérska služba - PPL');
        $page655819->setValue('title_cz', 'Kurýrska služba - PPL');
        $page655819->setValue('eshop_tag', 'ppl_carrier');
        $page655819->setValue('eshop_description', '');
        $page655819->setValue('eshop_eur_price_including_vat', '4.8');
        $page655819->setValue('testing_active', 'F');
        $page655819->setValue('minimal_price_for_free_delivery', '');
        $page655819->setValue('eshop_eur_price_including_vat_cz', '180');
        $page655819->setValue('eshop_description_cz', '');
        $page655819->setValue('eshop_description_en', '');
        $page655819->setValue('eshop_eur_price_without_vat', '');
        $page655819->setValue('hide_on_domain', 'T');
        $page655819->setValue('hide_on_domain_cz', 'F');
        $page655819->setValue('hide_on_domain_en', 'T');
        $page655819->save();

        // page: Zavolejsikurýra 1(ID: 655820 TAG: zavolejsikuryra_carrier)
        $pageId = $this->getPageIdByTag('zavolejsikuryra_carrier');
        if ($pageId === null) {
            $pageType = $this->pageTypesManager()->getPageTypeByTag('eshop_transport_type');
            $page655820 = \PageFactory::create($this->getPageIdByTag('eshop_delivery_types'), $pageType->getId());
        } else {
            $page655820 = \PageFactory::get($pageId);
        }
        $page655820->setPageName('Zavolejsikurýra 1');
        $page655820->setPageTag('zavolejsikuryra_carrier');
        $page655820->setPageStateId('1');
        $page655820->setPageClassId(1);
        $page655820->setValue('exchange_rate_cz', '');
        $page655820->setValue('title', 'Kuriérska služba - Zavolejsikurýra');
        $page655820->setValue('title_en', 'Kuriérska služba - Zavolejsikurýra');
        $page655820->setValue('title_cz', 'Kurýrska služba - Zavolejsikurýra');
        $page655820->setValue('eshop_tag', 'zavolejsikuryra_carrier');
        $page655820->setValue('eshop_description', '');
        $page655820->setValue('eshop_eur_price_including_vat', '4.8');
        $page655820->setValue('testing_active', 'F');
        $page655820->setValue('minimal_price_for_free_delivery', '');
        $page655820->setValue('eshop_eur_price_including_vat_cz', '180');
        $page655820->setValue('eshop_description_cz', '');
        $page655820->setValue('eshop_description_en', '');
        $page655820->setValue('eshop_eur_price_without_vat', '');
        $page655820->setValue('hide_on_domain', 'T');
        $page655820->setValue('hide_on_domain_cz', 'F');
        $page655820->setValue('hide_on_domain_en', 'T');
        $page655820->save();

        $page655819->setValue('eshop_payment_type', [
            ]);
        $page655819->save();

        $page655820->setValue('eshop_payment_type', [
            ]);
        $page655820->save();
    }

    public function down()
    {
        // remove page: Zavolejsikurýra 1 (zavolejsikuryra_carrier)
        $pageId = $this->getPageIdByTag('zavolejsikuryra_carrier');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }

        // remove page: PPL - kuriér (ppl_carrier)
        $pageId = $this->getPageIdByTag('ppl_carrier');
        if ($pageId !== null) {
            \PageFactory::get($pageId)->delete();
        }
    }
}
