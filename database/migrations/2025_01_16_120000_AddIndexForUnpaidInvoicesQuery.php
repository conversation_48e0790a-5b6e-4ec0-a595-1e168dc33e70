<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Util\DBSchema;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Migration to add optimized index for unpaid invoices query
 * Optimizes query: WHERE payment_remaining > 0 AND due_date <= ? AND partner_id = ?
 */
class AddIndexForUnpaidInvoicesQuery extends AbstractMigration
{
    public function up()
    {
        Schema::table('onix_enclosures', function (Blueprint $table) {
            if (!DBSchema::tableKeyExists('onix_enclosures', ['payment_remaining', 'partner_id', 'due_date'])) {

                Schema::table('onix_enclosures', function (Blueprint $table) {
                    $table->string('payment_remaining', 15)->change();
                });
                Schema::table('onix_enclosures', function (Blueprint $table) {
                    $table->string('due_date', 32)->change();
                });

                $table->index(['payment_remaining', 'partner_id', 'due_date'], 'idx_unpaid_invoices_query');
            }
        });
    }

    public function down()
    {
        Schema::table('onix_enclosures', function (Blueprint $table) {
            if (DBSchema::tableKeyExists('onix_enclosures', ['payment_remaining', 'partner_id', 'due_date'])) {
                $table->dropIndex('idx_unpaid_invoices_query');

                Schema::table('onix_enclosures', function (Blueprint $table) {
                    $table->string('payment_remaining', 191)->change();
                });
                Schema::table('onix_enclosures', function (Blueprint $table) {
                    $table->string('due_date', 191)->change();
                });
            }
        });
    }
}
