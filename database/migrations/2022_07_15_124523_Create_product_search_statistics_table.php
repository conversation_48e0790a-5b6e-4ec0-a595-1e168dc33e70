<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Automatic generation from (rinoparts) at 2022-07-15 12:45:23
 */
class Create_product_search_statistics_table extends AbstractMigration
{
    public function up()
    {
        // create table tblProductSearchStatistics
        Schema::create('tblProductSearchStatistics', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('page_id')->nullable()->foreign()->references('page_id')->on('tblPages');
            $table->string('onix_number')->nullable();
            $table->string('main_code')->nullable();
            $table->string('producer')->nullable();
            $table->string('title')->nullable();
            $table->string('search_term');
            $table->boolean('image')->nullable();
            $table->timestamps();

            $table->index('page_id');
            $table->index('search_term');
        });
    }

    public function down()
    {
        // drop table tblProductSearchStatistics
        Schema::dropIfExists('tblProductSearchStatistics');
    }
}
