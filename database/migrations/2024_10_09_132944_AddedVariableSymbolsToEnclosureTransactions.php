<?php

use Buxus\Migration\AbstractMigration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Automatic generation from (rinoparts) at 2024-10-09 13:29:44
 */
class AddedVariableSymbolsToEnclosureTransactions extends AbstractMigration
{
    public function up()
    {
        // update table enclosure_transactions
        Schema::table('enclosure_transactions', function (Blueprint $table) {
            $table->string('invoice_vs')->after('invoice_enclosure_record_id')->nullable();
            $table->string('credit_note_vs')->after('invoice_vs')->nullable();
        });
    }

    public function down()
    {
        // revert changes to table enclosure_transactions
        Schema::table('enclosure_transactions', function (Blueprint $table) {
            $table->dropColumn('invoice_vs');
            $table->dropColumn('credit_note_vs');
        });
    }
}
