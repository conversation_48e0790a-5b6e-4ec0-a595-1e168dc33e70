<?php

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;
use Buxus\PageType\PageType;
use Buxus\PageType\PageTypePropertyItem;

/**
 * Automatic generation from (rinoparts) at 2022-04-21 11:11:02
 * PageType generator: page_type=rolller_item_supplier
 * Property generator: property=alternative_price_without_vat,alternative_prices_latest_import,alternative_prices_code,alternative_prices_oe_codes,alternative_prices_main_oe_code,delivery_time,supplier
 */
class AlternativePricesImportDependenciesMigration extends AbstractMigration
{
    public function up()
    {
        // property: Titulok(title)
        $propertyTitle = $this->propertyManager()->propertyExistsByTag('title');
        if ($propertyTitle === false) {
            $propertyTitle = new Property();
            $propertyTitle->setTag('title');
            $propertyTitle->setDescription('Štandardný názov stránky. Text, ktorý sa zobrazí v záložke prehliadača. Podľa SEO odporúčaní max. 65 znakov.');
            $propertyTitle->setExtendedDescription('');
            $propertyTitle->setName('Titulok');
            $propertyTitle->setClassId(4);
            $propertyTitle->setShowType(null);
            $propertyTitle->setShowTypeTag('text');
            $propertyTitle->setValueType('oneline_text');
            $propertyTitle->setDefaultValue('');
            $propertyTitle->setMultiOperations(false);
            $propertyTitle->setInputString(null);
            $propertyTitle->setAttribute('tab', '');
            $propertyTitle->setAttribute('size', '60');
            $propertyTitle->setAttribute('maxlength', '');
            $propertyTitle->setAttribute('readonly', 'F');
            $propertyTitle->setAttribute('pattern', '');
            $propertyTitle->setAttribute('inherit_value', 'F');
            $propertyTitle->setAttribute('onchange-js', '');
            $propertyTitle->setAttribute('onkeyup-js', '');
            $propertyTitle->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyTitle);
        } else {
            $this->writeLine('Property with tag title already exists');
            $this->setDataKey('property_title_existed', true);
        }

        // property: Titulok [EN](title_en)
        $propertyTitleEn = $this->propertyManager()->propertyExistsByTag('title_en');
        if ($propertyTitleEn === false) {
            $propertyTitleEn = new Property();
            $propertyTitleEn->setTag('title_en');
            $propertyTitleEn->setDescription('Štandardný názov stránky. Text, ktorý sa zobrazí v záložke prehliadača. Podľa SEO odporúčaní max. 65 znakov.');
            $propertyTitleEn->setExtendedDescription('');
            $propertyTitleEn->setName('Titulok [EN]');
            $propertyTitleEn->setClassId(4);
            $propertyTitleEn->setShowType(null);
            $propertyTitleEn->setShowTypeTag('text');
            $propertyTitleEn->setValueType('oneline_text');
            $propertyTitleEn->setDefaultValue('');
            $propertyTitleEn->setMultiOperations(false);
            $propertyTitleEn->setInputString('');
            $propertyTitleEn->setAttribute('tab', 'EN');
            $propertyTitleEn->setAttribute('size', '60');
            $propertyTitleEn->setAttribute('maxlength', '');
            $propertyTitleEn->setAttribute('readonly', 'F');
            $propertyTitleEn->setAttribute('pattern', '');
            $propertyTitleEn->setAttribute('inherit_value', 'F');
            $propertyTitleEn->setAttribute('onchange-js', '');
            $propertyTitleEn->setAttribute('onkeyup-js', '');
            $propertyTitleEn->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyTitleEn);
        } else {
            $this->writeLine('Property with tag title_en already exists');
            $this->setDataKey('property_title_en_existed', true);
        }

        // property: Titulok [CZ](title_cz)
        $propertyTitleCz = $this->propertyManager()->propertyExistsByTag('title_cz');
        if ($propertyTitleCz === false) {
            $propertyTitleCz = new Property();
            $propertyTitleCz->setTag('title_cz');
            $propertyTitleCz->setDescription('Štandardný názov stránky. Text, ktorý sa zobrazí v záložke prehliadača. Podľa SEO odporúčaní max. 65 znakov.');
            $propertyTitleCz->setExtendedDescription('');
            $propertyTitleCz->setName('Titulok [CZ]');
            $propertyTitleCz->setClassId(4);
            $propertyTitleCz->setShowType(null);
            $propertyTitleCz->setShowTypeTag('text');
            $propertyTitleCz->setValueType('oneline_text');
            $propertyTitleCz->setDefaultValue('');
            $propertyTitleCz->setMultiOperations(false);
            $propertyTitleCz->setInputString('');
            $propertyTitleCz->setAttribute('tab', 'CZ');
            $propertyTitleCz->setAttribute('size', '60');
            $propertyTitleCz->setAttribute('maxlength', '');
            $propertyTitleCz->setAttribute('readonly', 'F');
            $propertyTitleCz->setAttribute('pattern', '');
            $propertyTitleCz->setAttribute('inherit_value', 'F');
            $propertyTitleCz->setAttribute('onchange-js', '');
            $propertyTitleCz->setAttribute('onkeyup-js', '');
            $propertyTitleCz->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyTitleCz);
        } else {
            $this->writeLine('Property with tag title_cz already exists');
            $this->setDataKey('property_title_cz_existed', true);
        }

        // property: Obrázok(image)
        $propertyImage = $this->propertyManager()->propertyExistsByTag('image');
        if ($propertyImage === false) {
            $propertyImage = new Property();
            $propertyImage->setTag('image');
            $propertyImage->setDescription('Obrázok');
            $propertyImage->setExtendedDescription('');
            $propertyImage->setName('Obrázok');
            $propertyImage->setClassId(4);
            $propertyImage->setShowType(null);
            $propertyImage->setShowTypeTag('image_name_upload');
            $propertyImage->setValueType('file');
            $propertyImage->setDefaultValue('');
            $propertyImage->setMultiOperations(false);
            $propertyImage->setInputString('');
            $propertyImage->setAttribute('tab', '');
            $propertyImage->setAttribute('inherit_value', 'F');
            $propertyImage->setAttribute('with_upload', 'T');
            $propertyImage->setAttribute('simple_upload', 'F');
            $propertyImage->setAttribute('show_input_element', 'T');
            $propertyImage->setAttribute('filename', '');
            $propertyImage->setAttribute('show_file_name', 'T');
            $propertyImage->setAttribute('file_type', 'image');
            $propertyImage->setAttribute('pattern', '');
            $propertyImage->setAttribute('show_thumbnail', 'T');
            $propertyImage->setAttribute('max_thumbnail_width', '150');
            $propertyImage->setAttribute('max_thumbnail_height', '80');
            $propertyImage->setAttribute('upload_subdir', '');
            $propertyImage->setAttribute('eval-code', '//$this-&gt;parsed_parm[\'upload_subdir\'] = \'my-dir\';');
            $propertyImage->setAttribute('onchange-js', '');
            $this->propertyManager()->saveProperty($propertyImage);
        } else {
            $this->writeLine('Property with tag image already exists');
            $this->setDataKey('property_image_existed', true);
        }

        // property: Systémový názov číselníkovej hodnoty(roller_value_name)
        $propertyRollerValueName = $this->propertyManager()->propertyExistsByTag('roller_value_name');
        if ($propertyRollerValueName === false) {
            $propertyRollerValueName = new Property();
            $propertyRollerValueName->setTag('roller_value_name');
            $propertyRollerValueName->setDescription('Názov číselníkovej hodnoty cez ktorý je indentifikovaná v kóde. Napríklad pri importoch.');
            $propertyRollerValueName->setExtendedDescription('');
            $propertyRollerValueName->setName('Systémový názov číselníkovej hodnoty');
            $propertyRollerValueName->setClassId(4);
            $propertyRollerValueName->setShowType(null);
            $propertyRollerValueName->setShowTypeTag('text');
            $propertyRollerValueName->setValueType('oneline_text');
            $propertyRollerValueName->setDefaultValue('');
            $propertyRollerValueName->setMultiOperations(false);
            $propertyRollerValueName->setInputString('');
            $propertyRollerValueName->setAttribute('tab', 'Admin');
            $propertyRollerValueName->setAttribute('size', '60');
            $propertyRollerValueName->setAttribute('maxlength', '');
            $propertyRollerValueName->setAttribute('readonly', 'T');
            $propertyRollerValueName->setAttribute('pattern', '');
            $propertyRollerValueName->setAttribute('inherit_value', 'F');
            $propertyRollerValueName->setAttribute('onchange-js', '');
            $propertyRollerValueName->setAttribute('onkeyup-js', '');
            $propertyRollerValueName->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyRollerValueName);
        } else {
            $this->writeLine('Property with tag roller_value_name already exists');
            $this->setDataKey('property_roller_value_name_existed', true);
        }

        // page type: Položka číselníka (ciselnik_item)
        $pageTypeCiselnikItem = $this->pageTypesManager()->pageTypeExistsByTag('ciselnik_item');
        if ($pageTypeCiselnikItem === false) {
            $pageTypeCiselnikItem = new PageType();
            $pageTypeCiselnikItem->setTag('ciselnik_item');
            $pageTypeCiselnikItem->setName('Položka číselníka');
            $pageTypeCiselnikItem->setPageClassId(1);
            $pageTypeCiselnikItem->setDefaultTemplateId(2);
            $pageTypeCiselnikItem->setDeleteTrigger('');
            $pageTypeCiselnikItem->setIncludeInSync(null);
            $pageTypeCiselnikItem->setPageDetailsLayout('');
            $pageTypeCiselnikItem->setPageSortTypeTag('sort_date_time');
            $pageTypeCiselnikItem->setPageTypeOrder(0);
            $pageTypeCiselnikItem->setPostmoveTrigger('');
            $pageTypeCiselnikItem->setPostsubmitTrigger('');
            $pageTypeCiselnikItem->setPresubmitTrigger('');
            $pageTypeCiselnikItem->setParent(null);
        } else {
            $this->writeLine('Page type with tag ciselnik_item already exists');
            $this->setDataKey('page_type_ciselnik_item_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('title');
        $propertyId = $property->getId();
        $tmp = $pageTypeCiselnikItem->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(0);
            $tmp->setRequired(false);
            $pageTypeCiselnikItem->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('title_en');
        $propertyId = $property->getId();
        $tmp = $pageTypeCiselnikItem->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(1);
            $tmp->setRequired(false);
            $pageTypeCiselnikItem->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('title_cz');
        $propertyId = $property->getId();
        $tmp = $pageTypeCiselnikItem->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(2);
            $tmp->setRequired(false);
            $pageTypeCiselnikItem->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('image');
        $propertyId = $property->getId();
        $tmp = $pageTypeCiselnikItem->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(3);
            $tmp->setRequired(false);
            $pageTypeCiselnikItem->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('roller_value_name');
        $propertyId = $property->getId();
        $tmp = $pageTypeCiselnikItem->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(4);
            $tmp->setRequired(false);
            $pageTypeCiselnikItem->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($pageTypeCiselnikItem);

        // property: Doba dodania(delivery_time)
        $propertyDeliveryTime = $this->propertyManager()->propertyExistsByTag('delivery_time');
        if ($propertyDeliveryTime === false) {
            $propertyDeliveryTime = new Property();
            $propertyDeliveryTime->setTag('delivery_time');
            $propertyDeliveryTime->setDescription('');
            $propertyDeliveryTime->setExtendedDescription('');
            $propertyDeliveryTime->setName('Doba dodania');
            $propertyDeliveryTime->setClassId(4);
            $propertyDeliveryTime->setShowType(null);
            $propertyDeliveryTime->setShowTypeTag('text');
            $propertyDeliveryTime->setValueType('oneline_text');
            $propertyDeliveryTime->setDefaultValue('');
            $propertyDeliveryTime->setMultiOperations(false);
            $propertyDeliveryTime->setInputString('');
            $propertyDeliveryTime->setAttribute('tab', '');
            $propertyDeliveryTime->setAttribute('size', '60');
            $propertyDeliveryTime->setAttribute('maxlength', '');
            $propertyDeliveryTime->setAttribute('readonly', 'F');
            $propertyDeliveryTime->setAttribute('pattern', '');
            $propertyDeliveryTime->setAttribute('inherit_value', 'F');
            $propertyDeliveryTime->setAttribute('onchange-js', '');
            $propertyDeliveryTime->setAttribute('onkeyup-js', '');
            $propertyDeliveryTime->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyDeliveryTime);
        } else {
            $this->writeLine('Property with tag delivery_time already exists');
            $this->setDataKey('property_delivery_time_existed', true);
        }
        if ($this->pageTypeExists('rolller_item_supplier')) {
            $this->addPropertyToPageType('delivery_time', 'rolller_item_supplier', false);
        }

        // page type: Dodávateľ (položka číselníka) (rolller_item_supplier)
        $pageTypeRolllerItemSupplier = $this->pageTypesManager()->pageTypeExistsByTag('rolller_item_supplier');
        if ($pageTypeRolllerItemSupplier === false) {
            $pageTypeRolllerItemSupplier = new PageType();
            $pageTypeRolllerItemSupplier->setTag('rolller_item_supplier');
            $pageTypeRolllerItemSupplier->setName('Dodávateľ (položka číselníka)');
            $pageTypeRolllerItemSupplier->setPageClassId(1);
            $pageTypeRolllerItemSupplier->setDefaultTemplateId(1);
            $pageTypeRolllerItemSupplier->setDeleteTrigger(null);
            $pageTypeRolllerItemSupplier->setIncludeInSync(null);
            $pageTypeRolllerItemSupplier->setPageDetailsLayout('');
            $pageTypeRolllerItemSupplier->setPageSortTypeTag('sort_date_time');
            $pageTypeRolllerItemSupplier->setPageTypeOrder(0);
            $pageTypeRolllerItemSupplier->setPostmoveTrigger(null);
            $pageTypeRolllerItemSupplier->setPostsubmitTrigger(null);
            $pageTypeRolllerItemSupplier->setPresubmitTrigger(null);
            $parent = $this->pageTypesManager()->getPageTypeByTag('ciselnik_item');
            $pageTypeRolllerItemSupplier->setParent($parent);
        } else {
            $this->writeLine('Page type with tag rolller_item_supplier already exists');
            $this->setDataKey('page_type_rolller_item_supplier_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('title');
        $propertyId = $property->getId();
        $tmp = $pageTypeRolllerItemSupplier->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(1);
            $tmp->setRequired(false);
            $pageTypeRolllerItemSupplier->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('title_en');
        $propertyId = $property->getId();
        $tmp = $pageTypeRolllerItemSupplier->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(2);
            $tmp->setRequired(false);
            $pageTypeRolllerItemSupplier->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('title_cz');
        $propertyId = $property->getId();
        $tmp = $pageTypeRolllerItemSupplier->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(3);
            $tmp->setRequired(false);
            $pageTypeRolllerItemSupplier->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('image');
        $propertyId = $property->getId();
        $tmp = $pageTypeRolllerItemSupplier->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(4);
            $tmp->setRequired(false);
            $pageTypeRolllerItemSupplier->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('roller_value_name');
        $propertyId = $property->getId();
        $tmp = $pageTypeRolllerItemSupplier->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(5);
            $tmp->setRequired(false);
            $pageTypeRolllerItemSupplier->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('delivery_time');
        $propertyId = $property->getId();
        $tmp = $pageTypeRolllerItemSupplier->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new PageTypePropertyItem($property);
            $tmp->setOrder(6);
            $tmp->setRequired(false);
            $pageTypeRolllerItemSupplier->addPropertyItem($tmp);
        }
        $this->pageTypesManager()->savePageType($pageTypeRolllerItemSupplier);

        if ($this->pageTypeExists('ciselnik')) {
            $this->addPageTypeSuperiorPageType('ciselnik_item', 'ciselnik');
        }
        if ($this->pageTypeExists('ciselnik')) {
            $this->addPageTypeSuperiorPageType('rolller_item_supplier', 'ciselnik');
        }

        // property: Cena z dodávateľského cenníka - EUR bez DPH(alternative_price_without_vat)
        $propertyAlternativePriceWithoutVat = $this->propertyManager()->propertyExistsByTag('alternative_price_without_vat');
        if ($propertyAlternativePriceWithoutVat === false) {
            $propertyAlternativePriceWithoutVat = new Property();
            $propertyAlternativePriceWithoutVat->setTag('alternative_price_without_vat');
            $propertyAlternativePriceWithoutVat->setDescription('');
            $propertyAlternativePriceWithoutVat->setExtendedDescription('');
            $propertyAlternativePriceWithoutVat->setName('Cena z dodávateľského cenníka - EUR bez DPH');
            $propertyAlternativePriceWithoutVat->setClassId(4);
            $propertyAlternativePriceWithoutVat->setShowType(null);
            $propertyAlternativePriceWithoutVat->setShowTypeTag('text');
            $propertyAlternativePriceWithoutVat->setValueType('oneline_text');
            $propertyAlternativePriceWithoutVat->setDefaultValue('');
            $propertyAlternativePriceWithoutVat->setMultiOperations(false);
            $propertyAlternativePriceWithoutVat->setInputString('');
            $propertyAlternativePriceWithoutVat->setAttribute('tab', 'Dod&aacute;vateľsk&yacute; cenn&iacute;k');
            $propertyAlternativePriceWithoutVat->setAttribute('size', '60');
            $propertyAlternativePriceWithoutVat->setAttribute('maxlength', '');
            $propertyAlternativePriceWithoutVat->setAttribute('readonly', 'F');
            $propertyAlternativePriceWithoutVat->setAttribute('pattern', '');
            $propertyAlternativePriceWithoutVat->setAttribute('inherit_value', 'F');
            $propertyAlternativePriceWithoutVat->setAttribute('onchange-js', '');
            $propertyAlternativePriceWithoutVat->setAttribute('onkeyup-js', '');
            $propertyAlternativePriceWithoutVat->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyAlternativePriceWithoutVat);
        } else {
            $this->writeLine('Property with tag alternative_price_without_vat already exists');
            $this->setDataKey('property_alternative_price_without_vat_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('alternative_price_without_vat', 'eshop_product', false);
        }

        // property: Dodávateľský cenník - posledný import(alternative_prices_latest_import)
        $propertyAlternativePricesLatestImport = $this->propertyManager()->propertyExistsByTag('alternative_prices_latest_import');
        if ($propertyAlternativePricesLatestImport === false) {
            $propertyAlternativePricesLatestImport = new Property();
            $propertyAlternativePricesLatestImport->setTag('alternative_prices_latest_import');
            $propertyAlternativePricesLatestImport->setDescription('');
            $propertyAlternativePricesLatestImport->setExtendedDescription('');
            $propertyAlternativePricesLatestImport->setName('Dodávateľský cenník - posledný import');
            $propertyAlternativePricesLatestImport->setClassId(4);
            $propertyAlternativePricesLatestImport->setShowType(null);
            $propertyAlternativePricesLatestImport->setShowTypeTag('text');
            $propertyAlternativePricesLatestImport->setValueType('oneline_text');
            $propertyAlternativePricesLatestImport->setDefaultValue('');
            $propertyAlternativePricesLatestImport->setMultiOperations(false);
            $propertyAlternativePricesLatestImport->setInputString('');
            $propertyAlternativePricesLatestImport->setAttribute('tab', 'Dod&aacute;vateľsk&yacute; cenn&iacute;k');
            $propertyAlternativePricesLatestImport->setAttribute('size', '60');
            $propertyAlternativePricesLatestImport->setAttribute('maxlength', '');
            $propertyAlternativePricesLatestImport->setAttribute('readonly', 'F');
            $propertyAlternativePricesLatestImport->setAttribute('pattern', '');
            $propertyAlternativePricesLatestImport->setAttribute('inherit_value', 'F');
            $propertyAlternativePricesLatestImport->setAttribute('onchange-js', '');
            $propertyAlternativePricesLatestImport->setAttribute('onkeyup-js', '');
            $propertyAlternativePricesLatestImport->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyAlternativePricesLatestImport);
        } else {
            $this->writeLine('Property with tag alternative_prices_latest_import already exists');
            $this->setDataKey('property_alternative_prices_latest_import_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('alternative_prices_latest_import', 'eshop_product', false);
        }

        // property: Dodávateľský cenník - CODE(alternative_prices_code)
        $propertyAlternativePricesCode = $this->propertyManager()->propertyExistsByTag('alternative_prices_code');
        if ($propertyAlternativePricesCode === false) {
            $propertyAlternativePricesCode = new Property();
            $propertyAlternativePricesCode->setTag('alternative_prices_code');
            $propertyAlternativePricesCode->setDescription('');
            $propertyAlternativePricesCode->setExtendedDescription('');
            $propertyAlternativePricesCode->setName('Dodávateľský cenník - CODE');
            $propertyAlternativePricesCode->setClassId(4);
            $propertyAlternativePricesCode->setShowType(null);
            $propertyAlternativePricesCode->setShowTypeTag('text');
            $propertyAlternativePricesCode->setValueType('oneline_text');
            $propertyAlternativePricesCode->setDefaultValue('');
            $propertyAlternativePricesCode->setMultiOperations(false);
            $propertyAlternativePricesCode->setInputString('');
            $propertyAlternativePricesCode->setAttribute('tab', 'Dod&aacute;vateľsk&yacute; cenn&iacute;k');
            $propertyAlternativePricesCode->setAttribute('size', '60');
            $propertyAlternativePricesCode->setAttribute('maxlength', '');
            $propertyAlternativePricesCode->setAttribute('readonly', 'F');
            $propertyAlternativePricesCode->setAttribute('pattern', '');
            $propertyAlternativePricesCode->setAttribute('inherit_value', 'F');
            $propertyAlternativePricesCode->setAttribute('onchange-js', '');
            $propertyAlternativePricesCode->setAttribute('onkeyup-js', '');
            $propertyAlternativePricesCode->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyAlternativePricesCode);
        } else {
            $this->writeLine('Property with tag alternative_prices_code already exists');
            $this->setDataKey('property_alternative_prices_code_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('alternative_prices_code', 'eshop_product', false);
        }

        // property: Dodávateľský cenník - OE CODEs(alternative_prices_oe_codes)
        $propertyAlternativePricesOeCodes = $this->propertyManager()->propertyExistsByTag('alternative_prices_oe_codes');
        if ($propertyAlternativePricesOeCodes === false) {
            $propertyAlternativePricesOeCodes = new Property();
            $propertyAlternativePricesOeCodes->setTag('alternative_prices_oe_codes');
            $propertyAlternativePricesOeCodes->setDescription('');
            $propertyAlternativePricesOeCodes->setExtendedDescription('');
            $propertyAlternativePricesOeCodes->setName('Dodávateľský cenník - OE CODEs');
            $propertyAlternativePricesOeCodes->setClassId(4);
            $propertyAlternativePricesOeCodes->setShowType(null);
            $propertyAlternativePricesOeCodes->setShowTypeTag('textarea');
            $propertyAlternativePricesOeCodes->setValueType('multiline_text');
            $propertyAlternativePricesOeCodes->setDefaultValue('');
            $propertyAlternativePricesOeCodes->setMultiOperations(false);
            $propertyAlternativePricesOeCodes->setInputString('');
            $propertyAlternativePricesOeCodes->setAttribute('tab', 'Dod&aacute;vateľsk&yacute; cenn&iacute;k');
            $propertyAlternativePricesOeCodes->setAttribute('cols', '60');
            $propertyAlternativePricesOeCodes->setAttribute('rows', '3');
            $propertyAlternativePricesOeCodes->setAttribute('dhtml-edit', '0');
            $propertyAlternativePricesOeCodes->setAttribute('dhtml-configuration', 'full');
            $propertyAlternativePricesOeCodes->setAttribute('import-word', '0');
            $propertyAlternativePricesOeCodes->setAttribute('auto', '');
            $propertyAlternativePricesOeCodes->setAttribute('inherit_value', 'F');
            $propertyAlternativePricesOeCodes->setAttribute('onchange-js', '');
            $propertyAlternativePricesOeCodes->setAttribute('onkeyup-js', '');
            $propertyAlternativePricesOeCodes->setAttribute('onkeydown-js', '');
            $propertyAlternativePricesOeCodes->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($propertyAlternativePricesOeCodes);
        } else {
            $this->writeLine('Property with tag alternative_prices_oe_codes already exists');
            $this->setDataKey('property_alternative_prices_oe_codes_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('alternative_prices_oe_codes', 'eshop_product', false);
        }

        // property: Dodávateľský cenník - hlavný OE CODE(alternative_prices_main_oe_code)
        $propertyAlternativePricesMainOeCode = $this->propertyManager()->propertyExistsByTag('alternative_prices_main_oe_code');
        if ($propertyAlternativePricesMainOeCode === false) {
            $propertyAlternativePricesMainOeCode = new Property();
            $propertyAlternativePricesMainOeCode->setTag('alternative_prices_main_oe_code');
            $propertyAlternativePricesMainOeCode->setDescription('');
            $propertyAlternativePricesMainOeCode->setExtendedDescription('');
            $propertyAlternativePricesMainOeCode->setName('Dodávateľský cenník - hlavný OE CODE');
            $propertyAlternativePricesMainOeCode->setClassId(4);
            $propertyAlternativePricesMainOeCode->setShowType(null);
            $propertyAlternativePricesMainOeCode->setShowTypeTag('text');
            $propertyAlternativePricesMainOeCode->setValueType('oneline_text');
            $propertyAlternativePricesMainOeCode->setDefaultValue('');
            $propertyAlternativePricesMainOeCode->setMultiOperations(false);
            $propertyAlternativePricesMainOeCode->setInputString('');
            $propertyAlternativePricesMainOeCode->setAttribute('tab', 'Dod&aacute;vateľsk&yacute; cenn&iacute;k');
            $propertyAlternativePricesMainOeCode->setAttribute('size', '60');
            $propertyAlternativePricesMainOeCode->setAttribute('maxlength', '');
            $propertyAlternativePricesMainOeCode->setAttribute('readonly', 'F');
            $propertyAlternativePricesMainOeCode->setAttribute('pattern', '');
            $propertyAlternativePricesMainOeCode->setAttribute('inherit_value', 'F');
            $propertyAlternativePricesMainOeCode->setAttribute('onchange-js', '');
            $propertyAlternativePricesMainOeCode->setAttribute('onkeyup-js', '');
            $propertyAlternativePricesMainOeCode->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyAlternativePricesMainOeCode);
        } else {
            $this->writeLine('Property with tag alternative_prices_main_oe_code already exists');
            $this->setDataKey('property_alternative_prices_main_oe_code_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('alternative_prices_main_oe_code', 'eshop_product', false);
        }

        // property: Dodávateľ(supplier)
        $propertySupplier = $this->propertyManager()->propertyExistsByTag('supplier');
        if ($propertySupplier === false) {
            $propertySupplier = new Property();
            $propertySupplier->setTag('supplier');
            $propertySupplier->setDescription('');
            $propertySupplier->setExtendedDescription('');
            $propertySupplier->setName('Dodávateľ');
            $propertySupplier->setClassId(4);
            $propertySupplier->setShowType(null);
            $propertySupplier->setShowTypeTag('custom_property');
            $propertySupplier->setValueType('custom_property');
            $propertySupplier->setDefaultValue('');
            $propertySupplier->setMultiOperations(false);
            $propertySupplier->setInputString('');
            $propertySupplier->setAttribute('tab', 'Dod&aacute;vateľsk&yacute; cenn&iacute;k');
            $propertySupplier->setAttribute('class_name', 'App\\Property\\Rollers\\RollerSupplierProperty');
            $propertySupplier->setAttribute('inherit_value', 'F');
            $this->propertyManager()->saveProperty($propertySupplier);
        } else {
            $this->writeLine('Property with tag supplier already exists');
            $this->setDataKey('property_supplier_existed', true);
        }
        if ($this->pageTypeExists('eshop_product')) {
            $this->addPropertyToPageType('supplier', 'eshop_product', false);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();
    }

    public function down()
    {
        // remove property: Dodávateľ(supplier)
        $propertySupplier = $this->propertyManager()->propertyExistsByTag('supplier');
        if (($propertySupplier !== false) && ($this->getDataKey('property_supplier_existed') === null)) {
            $this->propertyManager()->removeProperty($propertySupplier);
        }

        // remove property: Dodávateľský cenník - hlavný OE CODE(alternative_prices_main_oe_code)
        $propertyAlternativePricesMainOeCode = $this->propertyManager()->propertyExistsByTag('alternative_prices_main_oe_code');
        if (($propertyAlternativePricesMainOeCode !== false) && ($this->getDataKey('property_alternative_prices_main_oe_code_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyAlternativePricesMainOeCode);
        }

        // remove property: Dodávateľský cenník - OE CODEs(alternative_prices_oe_codes)
        $propertyAlternativePricesOeCodes = $this->propertyManager()->propertyExistsByTag('alternative_prices_oe_codes');
        if (($propertyAlternativePricesOeCodes !== false) && ($this->getDataKey('property_alternative_prices_oe_codes_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyAlternativePricesOeCodes);
        }

        // remove property: Dodávateľský cenník - CODE(alternative_prices_code)
        $propertyAlternativePricesCode = $this->propertyManager()->propertyExistsByTag('alternative_prices_code');
        if (($propertyAlternativePricesCode !== false) && ($this->getDataKey('property_alternative_prices_code_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyAlternativePricesCode);
        }

        // remove property: Dodávateľský cenník - posledný import(alternative_prices_latest_import)
        $propertyAlternativePricesLatestImport = $this->propertyManager()->propertyExistsByTag('alternative_prices_latest_import');
        if (($propertyAlternativePricesLatestImport !== false) && ($this->getDataKey('property_alternative_prices_latest_import_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyAlternativePricesLatestImport);
        }

        // remove property: Cena z dodávateľského cenníka - EUR bez DPH(alternative_price_without_vat)
        $propertyAlternativePriceWithoutVat = $this->propertyManager()->propertyExistsByTag('alternative_price_without_vat');
        if (($propertyAlternativePriceWithoutVat !== false) && ($this->getDataKey('property_alternative_price_without_vat_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyAlternativePriceWithoutVat);
        }

        // remove page type: Dodávateľ (položka číselníka) (rolller_item_supplier)
        $pageTypeRolllerItemSupplier = $this->pageTypesManager()->pageTypeExistsByTag('rolller_item_supplier');
        if (($pageTypeRolllerItemSupplier != false) && (is_null($this->getDataKey('page_type_rolller_item_supplier_existed')))) {
            $this->pageTypesManager()->removePageType($pageTypeRolllerItemSupplier);
        }

        // remove property: Doba dodania(delivery_time)
        $propertyDeliveryTime = $this->propertyManager()->propertyExistsByTag('delivery_time');
        if (($propertyDeliveryTime !== false) && ($this->getDataKey('property_delivery_time_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyDeliveryTime);
        }

        // remove page type: Položka číselníka (ciselnik_item)
        $pageTypeCiselnikItem = $this->pageTypesManager()->pageTypeExistsByTag('ciselnik_item');
        if (($pageTypeCiselnikItem != false) && (is_null($this->getDataKey('page_type_ciselnik_item_existed')))) {
            $this->pageTypesManager()->removePageType($pageTypeCiselnikItem);
        }

        // remove property: Systémový názov číselníkovej hodnoty(roller_value_name)
        $propertyRollerValueName = $this->propertyManager()->propertyExistsByTag('roller_value_name');
        if (($propertyRollerValueName !== false) && ($this->getDataKey('property_roller_value_name_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyRollerValueName);
        }

        // remove property: Obrázok(image)
        $propertyImage = $this->propertyManager()->propertyExistsByTag('image');
        if (($propertyImage !== false) && ($this->getDataKey('property_image_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyImage);
        }

        // remove property: Titulok [CZ](title_cz)
        $propertyTitleCz = $this->propertyManager()->propertyExistsByTag('title_cz');
        if (($propertyTitleCz !== false) && ($this->getDataKey('property_title_cz_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyTitleCz);
        }

        // remove property: Titulok [EN](title_en)
        $propertyTitleEn = $this->propertyManager()->propertyExistsByTag('title_en');
        if (($propertyTitleEn !== false) && ($this->getDataKey('property_title_en_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyTitleEn);
        }

        // remove property: Titulok(title)
        $propertyTitle = $this->propertyManager()->propertyExistsByTag('title');
        if (($propertyTitle !== false) && ($this->getDataKey('property_title_existed') === null)) {
            $this->propertyManager()->removeProperty($propertyTitle);
        }
        // regenerate property constants
        $this->propertyManager()->generateConstants();

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();
    }
}
