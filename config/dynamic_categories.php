<?php

return [
    /*
     * definition of item types, they map tags to classes which implement `\DynamicCategory\Item\ItemInterface`
     */
    'item_types' => [
        // 'some_tag_name' => '\Item\Type\ClasName',
    ],

    /*
     * list of query types, each query defines `fs_tag`, wich facet instance it will use
     * and `items`, the list of `item_types` which can be used for this query
     */
    'queries' => [
        'basic' => [
            'fs_tag' => null,
            'items' => [
                'and',
                'or',
            ],
        ],
    ],

    /*
     * List of filter tags that can be made visible for the dynamic categories
     * If a wildcard match is wanted, you can specify the tag in `filter_mask_regex_mapping`
     * to validate it via `preg_match`
     */
    'filter_mask_tags' => [
        'price' => 'cena',
        'category' => 'kategória',
        'flags' => 'príznaky produktov',
    ],

    /*
     * List of filter mask tags, that should be validated via `preg_match` instead of direct comparison
     */
    'filter_mask_regex_mapping' => [
        'price' => '/^price_(.{2})$/',
    ]
];
