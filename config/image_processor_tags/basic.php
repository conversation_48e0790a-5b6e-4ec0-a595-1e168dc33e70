<?php
return [
    'product_gallery_list' => [
        'operations' => [
            'resize' => [
                'width' => 60,
                'height' => 60,
                'crop' => true,
            ],
            'watermark' => [
                'image' => public_path(\Buxus\Util\Url::asset('images/watermark_logo.png')),
                'width' => '80%',
                'x' => 'center',
                'y' => 'center',
            ],
        ],
    ],
    'eshop_product_image' => [
        'operations' => [
            'resize' => [
                'width' => 200,
                'height' => 300,
            ],
            'watermark' => [
                'image' => public_path(\Buxus\Util\Url::asset('images/watermark_logo.png')),
                'width' => '80%',
                'x' => 'center',
                'y' => 'center',
            ],
        ],
    ],
    'eshop_full_product_image' => [
        'operations' => [
            'watermark' => [
                'image' => public_path(\Buxus\Util\Url::asset('images/watermark_logo.png')),
                'width' => '80%',
                'x' => 'center',
                'y' => 'center',
            ],
        ],
    ],
    'eshop_product_image_3' => [
        'operations' => [
            'resize' => [
                'width' => 330,
                'height' => 300,
            ],
            'watermark' => [
                'image' => public_path(\Buxus\Util\Url::asset('images/watermark_logo.png')),
                'width' => '80%',
                'x' => 'center',
                'y' => 'center',
            ],
        ],
    ],

    'eshop_product_image_3-iveco_no_image' => [
        'operations' => [
            'resize' => [
                'width' => 330,
                'height' => 300,
            ],
        ],
    ],
    'cart_list_thumbnail' => [
        'operations' => [
            'resize' => [
                'width' => 70,
                'height' => 70,
            ],
        ],
    ],
    'complaint_form_thumbnail' => [
        'operations' => [
            'resize' => [
                'width' => 100,
                'height' => 100,
            ],
            'image_format' => [
                'format' => 'heic',
            ],
        ],
    ],
];
