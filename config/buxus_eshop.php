<?php

return [
    /**
     * list of decorators for the eshop oraculum
     */
    'oraculum' => [
        'item_decorators' => [
            \App\Eshop\Price\ItemPriceDecorator\Init::class, // nakupna cena
            \App\Eshop\Price\ItemPriceDecorator\ExternalPrices::class, // iveco original cenniky
            \App\Eshop\Price\ItemPriceDecorator\Margin::class, // uprava ceny o marzu podla cenovych hladin
            \App\Eshop\Price\ItemPriceDecorator\IndexLevel::class,
            \App\Eshop\Price\ItemPriceDecorator\ActionPrice::class, // akcna cena
            \App\Eshop\Price\ItemPriceDecorator\BonusMargin::class, //bonusova marza
            \App\Eshop\Price\ItemPriceDecorator\Currency::class,
            \App\Eshop\Price\ItemPriceDecorator\FixedPrice::class,
            \App\Eshop\Price\ItemPriceDecorator\CustomerGroup::class,
            \App\Eshop\Price\ItemPriceDecorator\AugustinGroupBulkyParts::class,
            \Buxus\Eshop\Price\ItemPriceDecorator\Vat::class,
            \Buxus\Eshop\Price\ItemPriceDecorator\Amount::class,
            \App\Eshop\Price\ItemPriceDecorator\CurrencySymbol::class,
            \App\Eshop\Price\ItemPriceDecorator\RinopartsDiscountDelivery::class,
            \App\Eshop\Price\ItemPriceDecorator\RinopartsFreeDeliveryTransportItemDecorator::class,
            \App\Eshop\Price\ItemPriceDecorator\RoundUp::class,
        ],

        'list_decorators' => [
            \Buxus\Eshop\Price\ItemListPriceDecorator\Init::class,
            \Buxus\Eshop\Price\ItemListPriceDecorator\Vat::class,
            \Buxus\Eshop\Price\ItemListPriceDecorator\Transport::class,
            \App\Eshop\Price\ItemListPriceDecorator\RinopartsFreeDelivery::class,
            \Buxus\Eshop\Price\ItemListPriceDecorator\Payment::class,
            \App\Eshop\Price\ItemListPriceDecorator\Currency::class,
        ],
    ],

    /**
     *  page_type_id to product class mapping,
     *  accepts also parent page_types
     */
    'product_factory' => [
        \Buxus\Util\PageTypeID::ESHOP_PRODUCT_ID() => \App\Eshop\Product::class,
        \Buxus\Util\PageTypeID::DEPOSIT_ID() => \App\Eshop\Product::class
////        \Buxus\Util\PageTypeID::ESHOP_PRODUCT_VARIANT_ID() => \App\Eshop\SlaveProduct::class,
    ],

    /**
     *  list of all product types, it is not necessary to list child product types as they are
     *  included automatically
     */
    'product_types' => [
        \Buxus\Util\PageTypeID::ESHOP_PRODUCT_ID(),
        \Buxus\Util\PageTypeID::DEPOSIT_ID()
    ],

    'external_prices' => [
        \App\Eshop\Price\ExternalPrices\AlternativePrices::class,
        \App\Eshop\Price\ExternalPrices\IvecoOriginal::class,
        \App\Eshop\Price\ExternalPrices\IvecoBigDb::class,
        \App\Eshop\Price\ExternalPrices\MecDiesel::class,
        \App\Eshop\Price\ExternalPrices\AugustinGroup::class,
        \App\Eshop\Price\ExternalPrices\NRF::class,
        \App\Eshop\Price\ExternalPrices\FebiBilstein::class,
        \App\Eshop\Price\ExternalPrices\Motorservice::class,
        \App\Eshop\Price\ExternalPrices\Eminia::class,
        \App\Eshop\Price\ExternalPrices\Casco::class,
        \App\Eshop\Price\ExternalPrices\SpecialTurbo::class,
        \App\Eshop\Price\ExternalPrices\Martex::class,
        \App\Eshop\Price\ExternalPrices\Sabo::class,
        \App\Eshop\Price\ExternalPrices\Remante::class,
        \App\Eshop\Price\ExternalPrices\OeGermany::class,
        \App\Eshop\Price\ExternalPrices\MeatDoria::class,
        \App\Eshop\Price\ExternalPrices\Abakus::class,
    ],

    'margins' => [
        \App\Eshop\Price\Margin\IvecoOriginalInStock::class,
        \App\Eshop\Price\Margin\IvecoBigDb::class,
        \App\Eshop\Price\Margin\IvecoOriginal::class,
        \App\Eshop\Price\Margin\AugustinGroup::class,
        \App\Eshop\Price\Margin\MecDiesel::class,
        \App\Eshop\Price\Margin\NRF::class,
        \App\Eshop\Price\Margin\FebiBilstein::class,
        \App\Eshop\Price\Margin\Motorservice::class,
        \App\Eshop\Price\Margin\Eminia::class,
        \App\Eshop\Price\Margin\SpecialTurbo::class,
        \App\Eshop\Price\Margin\Martex::class,
        \App\Eshop\Price\Margin\Sabo::class,
        \App\Eshop\Price\Margin\Remante::class,
        \App\Eshop\Price\Margin\OeGermany::class,
        \App\Eshop\Price\Margin\MeatDoria::class,
        \App\Eshop\Price\Margin\Abakus::class,
        \App\Eshop\Price\Margin\DefaultMargin::class,
    ],

    /**
     * default eshop currency
     */
    'currency' => 'EUR',

    'exchange_rate_eur_czk' => 25,

    /**
     * VAT rate in percent
     */
    'vat_rate' => 23,

    /**
     * options for price formatting
     */
    'price_viewer' => [
        'precision' => 2,

        'decimal_separator' => '.',

        'thousands_separator' => ' ',

        'currency_symbols' => [
            'EUR' => '€',
            'CZK' => 'Kč'
        ]
    ],

    /**
     * variable symbol mask for new orders
     */
    'vs_mask' => 'YY######',

    /**
     * invoice mask
     */
    'invoice_mask' => 'YY######',

    'variants' => [
        /**
         * set to false to disable caching
         */
        'use_caching' => true,

        /**
         * allows to set a callback that can be used to validate variant values if they should be included as variants
         * can be any valid PHP callback.
         * the method receives one parameter PageInterface $page and returns true if valid and false if invalid
         */
        //'value_page_validator' => '\App\MyClass::staticValidateMethod',

        /**
         * list of property tags that are not inherited from master page in variant in case it is empty
         */
        'non_inherited_variant_properties' => [
            'eshop_eur_action_price_without_vat',
        ],

        /**
         * variant definitions
         */
        'variant_types' => [
//            \Buxus\Util\PageTypeID::ESHOP_PRODUCT_ID() => [
//                'url_type' => \Buxus\Eshop\Variants\VariantUrlType::MASTER_PRODUCT_WITH_VARIANT_QUERY_PARAM,
//                'append_first_variant_to_master_product_url' => false,
//                'variant_type' => \Buxus\Eshop\Variants\VariantType::VARIANT_TYPE_LINEAR,
//                'variants' => [
//                    'size' => [
//                        'page_type_id' => \Buxus\Util\PageTypeID::ESHOP_PRODUCT_VARIANT_ID(),
//                        'property' => 'size',
//                        'name' => 'Veľkosť',
//                    ],
//                    'color' => [
//                        'page_type_id' => \Buxus\Util\PageTypeID::ESHOP_PRODUCT_VARIANT_ID(),
//                        'property' => 'color',
//                        'name' => 'Farba',
//                    ],
//                ],
//            ],
        ],
    ],


    'uif' => [
        /*
         * list of order detail columns, if no callback is defined, the key from the array denotes which orderTtem or order value is displayed
         * - name: specifies column readable name
         * - linkable: if true, the item is a link to the product page
         * - callback: callable that returns the value to be displayed
         */
        'order_detail_items' => [
            'page_id' => [
                'name' => 'ID',
            ],
            'product_name' => [
                'name' => 'Názov',
                'linkable' => true,
            ],
            'state' => [
                'name' => 'Stav',
                'callback' => function (\Buxus\Eshop\Order\OrderInterface $order, \Buxus\Eshop\Order\OrderItemInterface $item) {
                    try {
                        $itemStockState = $item->getOption('item_stock_state');

                        if (empty($itemStockState)) {
                            $product = \ProductFactory::get($item->getPageId());

                            return $product->renderAvailabilityOrder();
                        }

                        return $itemStockState;
                    } catch (\Exception $e) {
                        \Buxus\Error\ErrorReporter::reportSilent($e);
                    }
                    return '';
                }
            ],
            'producer' => [
                'name' => 'Výrobca',
                'callback' => function (\Buxus\Eshop\Order\OrderInterface $order, \Buxus\Eshop\Order\OrderItemInterface $item) {
                    try {
                        $product = \ProductFactory::get($item->getPageId());

                        return $product->getProducer();
                    } catch (\Exception $e) {
                    }
                    return '';
                }
            ],
        ],
        /*
         * allows you to set the color of individual order item rows in order detail, can be a callable or a fixed value (CSS styles)
         */
        'order_item_row_style' => function (\Buxus\Eshop\Order\OrderInterface $order, \Buxus\Eshop\Order\OrderItemInterface $item) {
            $state = $item->getOption('item_state');
            if (strpos(strtolower($state), 'storno') !== false) {
                return 'text-decoration: line-through; background:#ffeeee;';
            }
            return '';
        },
    ],

    'states' => [
        \App\Eshop\OrderState::STATE_NOVA => [
            'tag' => 'new',
            'label' => 'NOVÁ',
            'follow_states' => [
                \App\Eshop\OrderState::STATE_AKCEPTOVANA,
                \App\Eshop\OrderState::STATE_STORNOVANA,
                \App\Eshop\OrderState::STATE_PRIJATA
            ],
            'icon' => 'system/images/shop_order_state_new.gif',
            'email' => 'eshop_nova_objednavka',
            'state_class' => \Buxus\Eshop\Contracts\ShopOrderState::ORDER_STATE_CLASS_PENDING,
        ],

        \App\Eshop\OrderState::STATE_WAITING_FOR_ONIX => [
            'tag' => 'state_waiting_for_onix',
            'label' => 'ČAKÁ NA PRIJATIE',
            'follow_states' => [
                \App\Eshop\OrderState::STATE_VYBAVENA,
                \App\Eshop\OrderState::STATE_EXPEDOVANA,
                \App\Eshop\OrderState::STATE_STORNOVANA,
                \App\Eshop\OrderState::STATE_NOVA,
                \App\Eshop\OrderState::STATE_RESEND_TO_ONIX,
            ],
            'icon' => 'system/images/shop_order_state_new.gif',
            'email' => null,
            'state_class' => \Buxus\Eshop\Contracts\ShopOrderState::ORDER_STATE_CLASS_PENDING,
        ],


        \App\Eshop\OrderState::STATE_PRIJATA => [
            'tag' => 'state_received',
            'label' => 'PRIJATÁ',
            'follow_states' => [
                \App\Eshop\OrderState::STATE_AKCEPTOVANA,
                \App\Eshop\OrderState::STATE_STORNOVANA,
            ],
            'icon' => 'system/images/shop_order_state_new.gif',
            'email' => 'eshop_nova_objednavka',
            'state_class' => \Buxus\Eshop\Contracts\ShopOrderState::ORDER_STATE_CLASS_PENDING,
        ],

        \App\Eshop\OrderState::STATE_CIASTOCNE_FAKTUROVANA => [
            'tag' => 'ciastocne_fakturovana',
            'label' => 'ČIASTOCNE FAKTUROVANÁ',
            'follow_states' => [
                \App\Eshop\OrderState::STATE_AKCEPTOVANA,
                \App\Eshop\OrderState::STATE_STORNOVANA,
            ],
            'icon' => 'system/images/shop_order_state_new.gif',
            'email' => null,
            'state_class' => \Buxus\Eshop\Contracts\ShopOrderState::ORDER_STATE_CLASS_PENDING,
        ],

        \App\Eshop\OrderState::STATE_CIASTOCNE_POKRYTA => [
            'tag' => 'ciastocne_pokryta',
            'label' => 'ČIASTOCNE POKRYTÁ',
            'follow_states' => [
                \App\Eshop\OrderState::STATE_AKCEPTOVANA,
                \App\Eshop\OrderState::STATE_STORNOVANA,
            ],
            'icon' => 'system/images/shop_order_state_new.gif',
            'email' => null,
            'state_class' => \Buxus\Eshop\Contracts\ShopOrderState::ORDER_STATE_CLASS_PENDING,
        ],

        \App\Eshop\OrderState::STATE_AKCEPTOVANA => [
            'tag' => 'state_akceptovana',
            'label' => 'AKCEPTOVANÁ',
            'follow_states' => [
                \App\Eshop\OrderState::STATE_CAKA_NA_TOVAR,
                \App\Eshop\OrderState::STATE_EXPEDOVANA,
                \App\Eshop\OrderState::STATE_STORNOVANA,
            ],
            'icon' => 'system/images/shop_order_state_new.gif',
            'email' => 'eshop_akceptovana_objednavka',
            'state_class' => \Buxus\Eshop\Contracts\ShopOrderState::ORDER_STATE_CLASS_COMPLETED,
        ],

        \App\Eshop\OrderState::STATE_CAKA_NA_TOVAR => [
            'tag' => 'state_waiting_for_stock',
            'label' => 'ČAKÁ NA TOVAR',
            'follow_states' => [
                \App\Eshop\OrderState::STATE_EXPEDOVANA,
                \App\Eshop\OrderState::STATE_STORNOVANA,
            ],
            'icon' => 'system/images/shop_order_state_payed.gif',
            'email' => 'eshop_caka_na_tovar_objednavka',
            'state_class' => \Buxus\Eshop\Contracts\ShopOrderState::ORDER_STATE_CLASS_PENDING,
        ],

        \App\Eshop\OrderState::STATE_EXPEDOVANA => [
            'tag' => 'state_shipped',
            'label' => 'V EXPEDÍCII',
            'follow_states' => [
                \App\Eshop\OrderState::STATE_VYBAVENA,
                \App\Eshop\OrderState::STATE_STORNOVANA,
            ],
            'icon' => 'system/images/shop_order_state_dispatched.gif',
            'email' => 'eshop_expedovana_objednavka',
            'state_class' => \Buxus\Eshop\Contracts\ShopOrderState::ORDER_STATE_CLASS_COMPLETED,
        ],

        \App\Eshop\OrderState::STATE_VYBAVENA => [
            'tag' => 'state_completed',
            'label' => 'VYBAVENÁ',
            'follow_states' => [
                \App\Eshop\OrderState::STATE_STORNOVANA,
            ],
            'icon' => 'system/images/shop_order_state_completed.gif',
            'email' => null,
            'state_class' => \Buxus\Eshop\Contracts\ShopOrderState::ORDER_STATE_CLASS_COMPLETED,
        ],

        \App\Eshop\OrderState::STATE_STORNOVANA => [
            'tag' => 'state_canceled',
            'label' => 'ZRUŠENÁ',
            'follow_states' => [
            ],
            'icon' => 'system/images/shop_order_state_canceled.gif',
            'email' => 'eshop_zrusena_objednavka',
            'state_class' => \Buxus\Eshop\Contracts\ShopOrderState::ORDER_STATE_CLASS_CANCELLED,
        ],

        \App\Eshop\OrderState::STATE_CAKA_NA_DL => [
            'tag' => 'state_waiting_for_dl',
            'label' => 'ČAKÁ NA DL',
            'follow_states' => [
                \App\Eshop\OrderState::STATE_VYBAVENA,
                \App\Eshop\OrderState::STATE_EXPEDOVANA,
                \App\Eshop\OrderState::STATE_STORNOVANA,
            ],
            'icon' => 'system/images/shop_order_state_payed.gif',
            'email' => null,
            'state_class' => \Buxus\Eshop\Contracts\ShopOrderState::ORDER_STATE_CLASS_PENDING,
        ],

        \App\Eshop\OrderState::STATE_RESEND_TO_ONIX => [
            'tag' => 'state_resend_to_onix',
            'label' => 'ODOSLAŤ DO ONIXU',
            'follow_states' => [
                \App\Eshop\OrderState::STATE_VYBAVENA,
                \App\Eshop\OrderState::STATE_EXPEDOVANA,
                \App\Eshop\OrderState::STATE_STORNOVANA,
                \App\Eshop\OrderState::STATE_NOVA,
            ],
            'icon' => 'system/images/shop_order_state_new.gif',
            'email' => null,
            'state_class' => \Buxus\Eshop\Contracts\ShopOrderState::ORDER_STATE_CLASS_PENDING,
        ],

    ],


    /*
     * list of additional notifications for the eshop detail
     * you can set page_tag => name
     * or only page_tag, then the system loads all chidlren email pages
     * from the tagged page
     */
    'notification_emails' => [
//        'page_tag' => 'Name',
    ],

    /*
     * if set to true, the admin eshop order list will show a subtotal of all currently displayed orders
     */
    'eshop_list_show_subtotal' => false,

    /*
     * modifies if the HTML WYSIWYG editor in order detail should be off
     */
    'eshop_detail_wysiwyg_default_off' => false,


    /*
     * if set to true, in order detail, if user changes the state, the
     * checkbox for email notification will not be set to true by default
     */
    'eshop_detail_do_not_set_email_notification_on' => false,

    'logging' => [
        'orders' => [
            'enabled' => true,
        ],
    ],

    'delivery_countries' => [
        "Afghanistan" => "Afghanistan",
        "Albania" => "Albania",
        "Algeria" => "Algeria",
        "American Samoa" => "American Samoa",
        "Andorra" => "Andorra",
        "Angola" => "Angola",
        "Anguilla" => "Anguilla",
        "Antarctica" => "Antarctica",
        "Antigua and Barbuda" => "Antigua and Barbuda",
        "Argentina" => "Argentina",
        "Armenia" => "Armenia",
        "Aruba" => "Aruba",
        "Australia" => "Australia",
        "Austria" => "Austria",
        "Azerbaijan" => "Azerbaijan",
        "Bahamas" => "Bahamas",
        "Bahrain" => "Bahrain",
        "Bangladesh" => "Bangladesh",
        "Barbados" => "Barbados",
        "Belarus" => "Belarus",
        "Belgium" => "Belgium",
        "Belize" => "Belize",
        "Benin" => "Benin",
        "Bermuda" => "Bermuda",
        "Bhutan" => "Bhutan",
        "Bolivia" => "Bolivia",
        "Bosnia and Herzegovina" => "Bosnia and Herzegovina",
        "Botswana" => "Botswana",
        "Bouvet Island" => "Bouvet Island",
        "Brazil" => "Brazil",
        "British Indian Ocean Territory" => "British Indian Ocean Territory",
        "Brunei Darussalam" => "Brunei Darussalam",
        "Bulgaria" => "Bulgaria",
        "Burkina Faso" => "Burkina Faso",
        "Burundi" => "Burundi",
        "Cambodia" => "Cambodia",
        "Cameroon" => "Cameroon",
        "Canada" => "Canada",
        "Cape Verde" => "Cape Verde",
        "Cayman Islands" => "Cayman Islands",
        "Central African Republic" => "Central African Republic",
        "Chad" => "Chad",
        "Chile" => "Chile",
        "China" => "China",
        "Christmas Island" => "Christmas Island",
        "Cocos (Keeling) Islands" => "Cocos (Keeling) Islands",
        "Colombia" => "Colombia",
        "Comoros" => "Comoros",
        "Congo" => "Congo",
        "Congo, the Democratic Republic of the" => "Congo, the Democratic Republic of the",
        "Cook Islands" => "Cook Islands",
        "Costa Rica" => "Costa Rica",
        "Cote D'Ivoire" => "Cote D'Ivoire",
        "Croatia" => "Croatia",
        "Cuba" => "Cuba",
        "Cyprus" => "Cyprus",
        "Czech Republic" => "Czech Republic",
        "Denmark" => "Denmark",
        "Djibouti" => "Djibouti",
        "Dominica" => "Dominica",
        "Dominican Republic" => "Dominican Republic",
        "Ecuador" => "Ecuador",
        "Egypt" => "Egypt",
        "El Salvador" => "El Salvador",
        "Equatorial Guinea" => "Equatorial Guinea",
        "Eritrea" => "Eritrea",
        "Estonia" => "Estonia",
        "Ethiopia" => "Ethiopia",
        "Falkland Islands (Malvinas)" => "Falkland Islands (Malvinas)",
        "Faroe Islands" => "Faroe Islands",
        "Fiji" => "Fiji",
        "Finland" => "Finland",
        "France" => "France",
        "French Guiana" => "French Guiana",
        "French Polynesia" => "French Polynesia",
        "French Southern Territories" => "French Southern Territories",
        "Gabon" => "Gabon",
        "Gambia" => "Gambia",
        "Georgia" => "Georgia",
        "Germany" => "Germany",
        "Ghana" => "Ghana",
        "Gibraltar" => "Gibraltar",
        "Greece" => "Greece",
        "Greenland" => "Greenland",
        "Grenada" => "Grenada",
        "Guadeloupe" => "Guadeloupe",
        "Guam" => "Guam",
        "Guatemala" => "Guatemala",
        "Guinea" => "Guinea",
        "Guinea-Bissau" => "Guinea-Bissau",
        "Guyana" => "Guyana",
        "Haiti" => "Haiti",
        "Heard Island and Mcdonald Islands" => "Heard Island and Mcdonald Islands",
        "Holy See (Vatican City State)" => "Holy See (Vatican City State)",
        "Honduras" => "Honduras",
        "Hong Kong" => "Hong Kong",
        "Hungary" => "Hungary",
        "Iceland" => "Iceland",
        "India" => "India",
        "Indonesia" => "Indonesia",
        "Iran, Islamic Republic of" => "Iran, Islamic Republic of",
        "Iraq" => "Iraq",
        "Ireland" => "Ireland",
        "Israel" => "Israel",
        "Italy" => "Italy",
        "Jamaica" => "Jamaica",
        "Japan" => "Japan",
        "Jordan" => "Jordan",
        "Kazakhstan" => "Kazakhstan",
        "Kenya" => "Kenya",
        "Kiribati" => "Kiribati",
        "Korea, Democratic People's Republic of" => "Korea, Democratic People's Republic of",
        "Korea, Republic of" => "Korea, Republic of",
        "Kuwait" => "Kuwait",
        "Kyrgyzstan" => "Kyrgyzstan",
        "Lao People's Democratic Republic" => "Lao People's Democratic Republic",
        "Latvia" => "Latvia",
        "Lebanon" => "Lebanon",
        "Lesotho" => "Lesotho",
        "Liberia" => "Liberia",
        "Libyan Arab Jamahiriya" => "Libyan Arab Jamahiriya",
        "Liechtenstein" => "Liechtenstein",
        "Lithuania" => "Lithuania",
        "Luxembourg" => "Luxembourg",
        "Macao" => "Macao",
        "Macedonia, the Former Yugoslav Republic of" => "Macedonia, the Former Yugoslav Republic of",
        "Madagascar" => "Madagascar",
        "Malawi" => "Malawi",
        "Malaysia" => "Malaysia",
        "Maldives" => "Maldives",
        "Mali" => "Mali",
        "Malta" => "Malta",
        "Marshall Islands" => "Marshall Islands",
        "Martinique" => "Martinique",
        "Mauritania" => "Mauritania",
        "Mauritius" => "Mauritius",
        "Mayotte" => "Mayotte",
        "Mexico" => "Mexico",
        "Micronesia, Federated States of" => "Micronesia, Federated States of",
        "Moldova, Republic of" => "Moldova, Republic of",
        "Monaco" => "Monaco",
        "Mongolia" => "Mongolia",
        "Montserrat" => "Montserrat",
        "Morocco" => "Morocco",
        "Mozambique" => "Mozambique",
        "Myanmar" => "Myanmar",
        "Namibia" => "Namibia",
        "Nauru" => "Nauru",
        "Nepal" => "Nepal",
        "Netherlands" => "Netherlands",
        "Netherlands Antilles" => "Netherlands Antilles",
        "New Caledonia" => "New Caledonia",
        "New Zealand" => "New Zealand",
        "Nicaragua" => "Nicaragua",
        "Niger" => "Niger",
        "Nigeria" => "Nigeria",
        "Niue" => "Niue",
        "Norfolk Island" => "Norfolk Island",
        "Northern Mariana Islands" => "Northern Mariana Islands",
        "Norway" => "Norway",
        "Oman" => "Oman",
        "Pakistan" => "Pakistan",
        "Palau" => "Palau",
        "Palestinian Territory, Occupied" => "Palestinian Territory, Occupied",
        "Panama" => "Panama",
        "Papua New Guinea" => "Papua New Guinea",
        "Paraguay" => "Paraguay",
        "Peru" => "Peru",
        "Philippines" => "Philippines",
        "Pitcairn" => "Pitcairn",
        "Poland" => "Poland",
        "Portugal" => "Portugal",
        "Puerto Rico" => "Puerto Rico",
        "Qatar" => "Qatar",
        "Reunion" => "Reunion",
        "Romania" => "Romania",
        "Russian Federation" => "Russian Federation",
        "Rwanda" => "Rwanda",
        "Saint Helena" => "Saint Helena",
        "Saint Kitts and Nevis" => "Saint Kitts and Nevis",
        "Saint Lucia" => "Saint Lucia",
        "Saint Pierre and Miquelon" => "Saint Pierre and Miquelon",
        "Saint Vincent and the Grenadines" => "Saint Vincent and the Grenadines",
        "Samoa" => "Samoa",
        "San Marino" => "San Marino",
        "Sao Tome and Principe" => "Sao Tome and Principe",
        "Saudi Arabia" => "Saudi Arabia",
        "Senegal" => "Senegal",
        "Serbia and Montenegro" => "Serbia and Montenegro",
        "Seychelles" => "Seychelles",
        "Sierra Leone" => "Sierra Leone",
        "Singapore" => "Singapore",
        "Slovakia" => "Slovakia",
        "Slovenia" => "Slovenia",
        "Solomon Islands" => "Solomon Islands",
        "Somalia" => "Somalia",
        "South Africa" => "South Africa",
        "South Georgia and the South Sandwich Islands" => "South Georgia and the South Sandwich Islands",
        "Spain" => "Spain",
        "Sri Lanka" => "Sri Lanka",
        "Sudan" => "Sudan",
        "Suriname" => "Suriname",
        "Svalbard and Jan Mayen" => "Svalbard and Jan Mayen",
        "Swaziland" => "Swaziland",
        "Sweden" => "Sweden",
        "Switzerland" => "Switzerland",
        "Syrian Arab Republic" => "Syrian Arab Republic",
        "Taiwan, Province of China" => "Taiwan, Province of China",
        "Tajikistan" => "Tajikistan",
        "Tanzania, United Republic of" => "Tanzania, United Republic of",
        "Thailand" => "Thailand",
        "Timor-Leste" => "Timor-Leste",
        "Togo" => "Togo",
        "Tokelau" => "Tokelau",
        "Tonga" => "Tonga",
        "Trinidad and Tobago" => "Trinidad and Tobago",
        "Tunisia" => "Tunisia",
        "Turkey" => "Turkey",
        "Turkmenistan" => "Turkmenistan",
        "Turks and Caicos Islands" => "Turks and Caicos Islands",
        "Tuvalu" => "Tuvalu",
        "Uganda" => "Uganda",
        "Ukraine" => "Ukraine",
        "United Arab Emirates" => "United Arab Emirates",
        "United Kingdom" => "United Kingdom",
        "United States" => "United States",
        "United States Minor Outlying Islands" => "United States Minor Outlying Islands",
        "Uruguay" => "Uruguay",
        "Uzbekistan" => "Uzbekistan",
        "Vanuatu" => "Vanuatu",
        "Venezuela" => "Venezuela",
        "Viet Nam" => "Viet Nam",
        "Virgin Islands, British" => "Virgin Islands, British",
        "Virgin Islands, U.s." => "Virgin Islands, U.s.",
        "Wallis and Futuna" => "Wallis and Futuna",
        "Western Sahara" => "Western Sahara",
        "Yemen" => "Yemen",
        "Zambia" => "Zambia",
        "Zimbabwe" => "Zimbabwe"
    ],
];
