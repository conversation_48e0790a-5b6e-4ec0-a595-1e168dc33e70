<?php

use App\FulltextSearch\ExtendedSolrBackend;
use App\FulltextSearch\RinopartsSolrSuggestProcessor;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;
use FullTextSearch\Backend;
use FullTextSearch\FullTextSearch;
use FullTextSearch\FullTextSearchManager;
use App\FulltextSearch\IndexerAspect\ExtendedBuxusPagesIndexerAspect;
use App\FulltextSearch\ExtendedSearchIndex;

return [
    function (FullTextSearchManager $manager) {
        $sites = BuxusSite::getAvailableSites();

        $allCodes = [
            PropertyTag::ONIX_CODES_TAG(),
            PropertyTag::IVECO_BIG_DB_IMPORT_CODE_TAG(),
            PropertyTag::ONIX_SEARCH_KEYWORDS_TAG(),
            PropertyTag::ALTERNATIVE_PRICES_OE_CODES_TAG(),
            PropertyTag::ALTERNATIVE_PRICES_CODE_TAG(),
            PropertyTag::AUGUSTIN_GROUP_OE_NUMBERS_TAG(),
            PropertyTag::AUGUSTIN_GROUP_OE_NUMBER_TAG(),
            PropertyTag::MEC_DIESEL_OE_NUMBER_TAG(),
            PropertyTag::MEC_DIESEL_OE_NUMBERS_TAG(),
            PropertyTag::MEC_DIESEL_SUPPLIER_CODE_TAG(),
            PropertyTag::NRF_OE_NUMBER_TAG(),
            PropertyTag::NRF_OE_NUMBERS_TAG(),
            PropertyTag::MOTORSERVICE_OE_NUMBER_TAG(),
            PropertyTag::MOTORSERVICE_OE_NUMBERS_TAG(),
            PropertyTag::MOTORSERVICE_SUPPLIER_CODE_TAG(),
            PropertyTag::FEBI_BILSTEIN_OE_NUMBER_TAG(),
            PropertyTag::FEBI_BILSTEIN_OE_NUMBERS_TAG(),
            PropertyTag::FEBI_BILSTEIN_SUPPLIER_CODE_TAG(),
            PropertyTag::NRF_SUPPLIER_CODE_TAG(),
            PropertyTag::EMINIA_OE_NUMBER_TAG(),
            PropertyTag::EMINIA_OE_NUMBERS_TAG(),
            PropertyTag::CASCO_OE_NUMBER_TAG(),
            PropertyTag::CASCO_OE_NUMBERS_TAG(),
            PropertyTag::CASCO_SUPPLIER_CODE_TAG(),
            PropertyTag::SPECIAL_TURBO_OE_NUMBER_TAG(),
            PropertyTag::SPECIAL_TURBO_OE_NUMBERS_TAG(),
            PropertyTag::SPECIAL_TURBO_SUPPLIER_CODE_TAG(),
            PropertyTag::MARTEX_OE_NUMBER_TAG(),
            PropertyTag::MARTEX_OE_NUMBERS_TAG(),
            PropertyTag::SABO_OE_NUMBERS_TAG(),
            PropertyTag::SABO_SUPPLIER_CODE_TAG(),
            PropertyTag::REMANTE_OE_NUMBER_TAG(),
            PropertyTag::REMANTE_OE_NUMBERS_TAG(),
            PropertyTag::IVECO_STOCK_OLD_CODES_TAG(),
            PropertyTag::ONIX_SUPPLIER_CODES_DATA_TAG(),
            PropertyTag::OE_GERMANY_OE_NUMBER_TAG(),
            PropertyTag::OE_GERMANY_OE_NUMBERS_TAG(),
            PropertyTag::OE_GERMANY_SUPPLIER_CODE_TAG(),
            PropertyTag::MEAT_DORIA_OE_NUMBER_TAG(),
            PropertyTag::MEAT_DORIA_OE_NUMBERS_TAG(),
            PropertyTag::MEAT_DORIA_SUPPLIER_CODE_TAG(),
            PropertyTag::ABAKUS_OE_NUMBER_TAG(),
            PropertyTag::ABAKUS_OE_NUMBERS_TAG(),
            PropertyTag::ABAKUS_SUPPLIER_CODE_TAG(),
        ];

        $fieldTypes = [];

        foreach ($allCodes as $code) {
            $fieldTypes[$code] = ExtendedSearchIndex::FIELD_TYPE_MULTIVALUED;
        }

        foreach ($sites as $site) {
            $backend_tag = 'products_' . $site;
            $index_tag = 'products_' . $site;
            $search_tag = 'products_' . $site;
            $suggest_tag = 'products_suggest_' . $site;
            $search_core = env('SEARCH_CORE_' . strtoupper($site)) ?: env('SEARCH_CORE');

            $backend = new ExtendedSolrBackend(env('SEARCH_HOST'), env('SEARCH_PORT'), $search_core, $backend_tag);
            $backend->setOption(Backend::OPTION_TIMEOUT, '240');
            $backend->setImmediateCommit(false);
            $manager->addBackend($backend);

            $indexer = new ExtendedSearchIndex($index_tag);
            $indexer->addBackend($manager->getBackend($backend_tag));
            $indexer->setFieldTypes($fieldTypes);

            $page_types = \ProductFactory::getAllProductTypes();

            $include_roots = [];
            if (PageIds::exists('eshop_catalog')) {
                $include_roots[] = PageIds::getPageId('eshop_catalog');
            }

            $aspect = new ExtendedBuxusPagesIndexerAspect([
                'site' => $site,
                'page_types' => $page_types,
                'properties' => $allCodes,
                'include_roots' => $include_roots,
            ]);

            $aspect->setFieldTypes($fieldTypes);

            $indexer->addAspect($aspect);

            $manager->addIndex($indexer);

            $searchProvider = new RinopartsSolrSuggestProcessor([
                'backend' => $manager->getBackend($backend_tag),
                'index' => $manager->getIndex($index_tag),
            ]);

            $fieldTypesMapping = [
                ExtendedSearchIndex::FIELD_TYPE_SYSTEM => '',
                ExtendedSearchIndex::FIELD_TYPE_TEXT => '_ft',
                ExtendedSearchIndex::FIELD_TYPE_DATETIME => '_fd',
                ExtendedSearchIndex::FIELD_TYPE_INTEGER => '_fi',
                ExtendedSearchIndex::FIELD_TYPE_LITERAL => '_fl',
                ExtendedSearchIndex::FIELD_TYPE_FLOAT => '_ff',
                ExtendedSearchIndex::FIELD_TYPE_BOOLEAN => '_fb',
                ExtendedSearchIndex::FIELD_TYPE_MULTIVALUED => '_ftm',
            ];

            $searchFields = [];

            foreach ($fieldTypes as $field => $type) {
                $searchFields[$field . $fieldTypesMapping[$type]] = '1';
            }

            $searchProvider->setSearchFields($searchFields);

            $search = new FullTextSearch($search_tag);
            $search->setProvider($searchProvider);
            $search->setLimit(9000);
            $search->setHighlight(true);
            $search->setHighlightFields($allCodes);
            $manager->addSearch($search);


            $search = new FullTextSearch($suggest_tag);
            $search->setProvider($searchProvider);
            $search->setLimit(20);
            $manager->addSearch($search);
        }
    },
];
