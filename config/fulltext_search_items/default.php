<?php

use Buxus\Util\PageIds;
use FullTextSearch\Backend;
use FullTextSearch\Backend\SolrBackend;
use FullTextSearch\FullTextSearch;
use FullTextSearch\FullTextSearchManager;
use FullTextSearch\IndexerAspect\BuxusPagesIndexerAspect;
use FullTextSearch\Provider\SolrFullTextProvider;
use FullTextSearch\SearchIndex;

return [
    function (FullTextSearchManager $manager) {
        $sites = BuxusSite::getAvailableSites();
        // if the web is using languages instead of domains
        // $sites = \Buxus\Translate\LanguageManager::getInstance()->getAvailableLanguages();

        foreach ($sites as $site) {
            $backendTag = FullTextSearchManager::DEFAULT_BACKEND . '_' . $site;
            $indexTag = FullTextSearchManager::DEFAULT_INDEX . '_' . $site;
            $search_tag = FullTextSearchManager::DEFAULT_SEARCH . '_' . $site;
            $suggestTag = "suggest_{$site}";
            $search_core = env('SEARCH_CORE_' . strtoupper($site)) ?: env('SEARCH_CORE');

            /**
             * BACKEND definitions
             */
            $backend = new SolrBackend(env('SEARCH_HOST'), env('SEARCH_PORT'), $search_core, $backendTag);
            $backend->setOption(Backend::OPTION_TIMEOUT, '240');
            $backend->setImmediateCommit(false);
            $manager->addBackend($backend);

            /**
             * INDEXER definitions
             */
            $indexer = new SearchIndex($indexTag);
            $indexer->addBackend($manager->getBackend($backendTag));
            $indexer->setFieldTypes([
                \Buxus\Util\PropertyTag::TITLE_TAG() => SearchIndex::FIELD_TYPE_TEXT,
                \Buxus\Util\PropertyTag::TEXT_TAG() => SearchIndex::FIELD_TYPE_TEXT,
            ]);

            $pageTypes = \ProductFactory::getAllProductTypes();
            $pageTypes[] = \Buxus\Util\PageTypeID::SECTION_ID();
            $pageTypes[] = \Buxus\Util\PageTypeID::RUBRIC_ID();
            $pageTypes[] = \Buxus\Util\PageTypeID::ARTICLE_ID();
            $pageTypes[] = \Buxus\Util\PageTypeID::ESHOP_PRODUCT_ID();
            $pageTypes[] = \Buxus\Util\PageTypeID::ESHOP_CATEGORY_ID();
            $pageTypes[] = \Buxus\Util\PageTypeID::ESHOP_SUBCATEGORY_ID();
            $pageTypes[] = \Buxus\Util\PageTypeID::ESHOP_SUBCATEGORY_3_ID();

            /**
             * Root page IDs of which children are valid for indexing
             */
            $includeRoots = [];
            if (PageIds::exists('eshop_catalog')) {
                $includeRoots[] = PageIds::getPageId('eshop_catalog');
            }

            $indexer->addAspect(
                new BuxusPagesIndexerAspect([
                    'site' => $site,
                    'page_types' => $pageTypes,
                    'properties' => [
                        \Buxus\Util\PropertyTag::TITLE_TAG(),
                        \Buxus\Util\PropertyTag::TEXT_TAG(),
                    ],
                    'include_roots' => $includeRoots,

                    /*
                     * enable the following lines to make the text property to contain the
                     * downloaded contents of the page through web server
                     * the content can be delimited by:
                     * <!--FULLTEXT_CONTENT_BEGIN-->
                     * and
                     * <!--FULLTEXT_CONTENT_END-->
                     */
                    // 'downloaded_properties' => [
                    //      \Buxus\Util\PropertyTag::TEXT_TAG(),
                    // ],
                    // 'remove_html' => true,

                    /*'exclude_roots' => array(
                        1,2,3,
                    ),*/
                ])
            );

            // use DocumentFileIndexerAspect to index rich-text documents
            /*
            $indexer->addAspect(
                new \FullTextSearch\IndexerAspect\DocumentFileIndexerAspect([
                    // typy stranok s dokumentami
                    'page_types' => [
                        \Buxus\Util\PageTypeID::DOCUMENT_ID,
                    ],
                    // SOLR backend
                    'backend' => $backend,
                    // vlastnosti, ktore sa normalne indexuju
                    'properties' => [
                        \Buxus\Util\PropertyTag::TITLE_TAG,
                        \Buxus\Util\PropertyTag::TEXT_TAG,
                    ],
                    // zoznam vlastnosti ktore obsahuju linky na dokumenty v BUXUSe v /buxus/docs
                    'file_properties' => [
                        \Buxus\Util\PropertyTag::DOCUMENT_FILE_PDF_TAG,
                        \Buxus\Util\PropertyTag::DOCUMENT_FILE_RTF_TAG,
                        \Buxus\Util\PropertyTag::DOCUMENT_FILE_HTML_TAG,
                        \Buxus\Util\PropertyTag::DOCUMENT_FILE_ODT_TAG,
                        \Buxus\Util\PropertyTag::DOCUMENT_FILE_TXT_TAG,
                    ],
                ])
            );
            */
            $manager->addIndex($indexer);

            /**
             * SEARCH definitions
             */
            $search = new FullTextSearch($search_tag);
            $search->setProvider(new SolrFullTextProvider([
                'backend' => $manager->getBackend($backendTag),
                'index' => $manager->getIndex($indexTag),
            ]));
            $search->setLimit(9000);
            $search->setHighlight(true);
            $search->setHighlightFields([
                \Buxus\Util\PropertyTag::TITLE_TAG(),
                \Buxus\Util\PropertyTag::TEXT_TAG(),
            ]);
            $manager->addSearch($search);


            $search = new FullTextSearch($suggestTag);
            $search->setProvider(new \FullTextSearch\Provider\SolrSuggestProvider([
                'backend' => $manager->getBackend($backendTag),
                'index' => $manager->getIndex($indexTag),
            ]));
            $search->setLimit(20);
            $manager->addSearch($search);
        }
    },
];
