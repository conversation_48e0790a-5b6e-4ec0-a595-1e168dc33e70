<?php

return [
    /*
     * number of image items displayed on a gallery page
     */
    'item_count_per_page' => 12,

    /*
     * number of pages shown in the pager for gallery pages
     */
    'page_range' => 5,

    /*
     * CSS class for the image thumbnails on the gallery page
     */
    'thumbnail_class' => 'col-xs-3 col-md-2',

    /*
     * class that represents individual image elements in a gallery,
     * this class must implement the `\Buxus\Page\WrappedPageInterface` interface
     */
    'list_item_class' => \PhotoGallery\PhotoGalleryListItem::class,
];
