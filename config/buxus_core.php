<?php
return [
    'base_url' => '/buxus/',
    'image_upload_path' => public_path('buxus/images/'),
    'image_upload_url' => '/buxus/images/',
    'doc_upload_path' => public_path('buxus/docs/'),
    'doc_upload_url' => '/buxus/docs/',
    'upload_tmp_path' => public_path('buxus/tmp/'),
    'upload_tmp_url' => '/buxus/tmp/',
    'assets_path' => public_path('buxus/assets/'),
    'assets_url' => '/buxus/assets/',

    'memcache_host' => 'localhost',
    'memcache_port' => '11211',
    'memcache_socket' => '',

    'memcached_buxus_page_properties' => true,

    'hostname' => env('DEFAULT_HOSTNAME'),
    'static_hostname' => env('DEFAULT_HOSTNAME'),

    /*
     * time validity for the login expiration for BUXUS users without activity,
     * set to 0 for no expiration (only session life will limit the expiration)
     */
    'buxus_login_expiration_minutes' => 480,
];
