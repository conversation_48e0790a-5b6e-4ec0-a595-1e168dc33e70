<?php
return [
    /**
     * default class to be used for page factory if no other mapping is found
     */
    'default_page_class' => \App\Page\BasicPage::class,

    /**
     * mapping page types to objects, the mapping gets inherited, so if a page
     * mapping for parent page_type is set and not for the child, the child
     * will get the mapping from the parent
     */
    'page_type_mapping' => [

    ],

    /**
     * enable caching event when a BUXUS user is logged in
     */
    'enable_cache_for_buxus_users' => false,
];