<?php
return [
    /*
     * mapping of page types to Layout classes
     */
    'elements' => [
        //\Buxus\Util\PageTypeID::LAYOUT_ELEMENT_NEWSLETTER_ID() => '\App\LayoutItems\NewsletterSignUp',
        \Buxus\Util\PageTypeID::LAYOUT_ELEMENT_BRAND_LIST_ID() => \App\Layout\Items\BrandList::class,
        \Buxus\Util\PageTypeID::LAYOUT_ELEMENT_SEARCH_BANNER_ID() => \App\Layout\Items\SearchBanner::class,
        \Buxus\Util\PageTypeID::LAYOUT_ELEMENT_HOMEPAGE_BANNER_ID() => \App\Layout\Items\ExtendedBanner::class,
        \Buxus\Util\PageTypeID::LAYOUT_ELEMENT_BANNER_LIST_ID() => \App\Layout\Items\ExtendedBannerList::class,
    ],

    /**
     * Page tags or ids, which should be considered for auto decaching
     * Used when automatically decaching parent layout pages & the page type is not mapped in `config/layouts.elements`
     */
    'auto_decaching_aware_page_tags' => [
        'homepage',
    ],

    /*
     * If set to TRUE, the cached layouts will be automatically invalidated
     * on page save. This is better left disabled on production sites for
     * performance reasons.
     */
    'enable_automatic_invalidation_on_page_save' => true,

    /*
     * If cache is disabled, \Layouts::getLayoutCached() will return uncached version of layout instead
     */
    'disable_layout_cache' => true,

    /*
     * tags for prewarming URLs, they can be specified in several ways
     * the keys can then be used with the 'layout:cache-prewarm' command
     */
    'prewarm_urls' => [
        // page tag
        //'hp' => 'homepage',

        // a concrete page_id
        //'basket' => 123,

        // a static URL
        //'static' => 'https://example.com/document',

        // an array where you can specify 'site' or 'langauge'
        // the 'url' parameter can be any of the previous forms
        //'site_specific' => [
        //    'url' => 123,
        //    'site' => 'sk',
        //    'language' => 'en',
        //],
    ],
];
