<?php

use FullTextSearch\FullTextSearchManager;

return [
    /**
     * Queue used for fulltext SolrBulk generation
     */
    'queue' => env('DB_DATABASE', 'buxus') . '_fulltext',
    'setup_function' => function (FullTextSearchManager $manager) {
        // here you can specify custom project fulltext settings
        // additionally can other configs be added by creating files
        // returning array of callbacks in `config/fulltext_search_items`
    },

    'sorters' => [
        \App\FulltextSearch\Sorters\OnixProducts::class,
        \App\FulltextSearch\Sorters\IvecoBigDbProducts::class,
        \App\FulltextSearch\Sorters\IvecoProducts::class,
        \App\FulltextSearch\Sorters\OtherProducts::class
    ]
];
