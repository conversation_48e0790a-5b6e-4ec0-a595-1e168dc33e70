<?php
return [
    /**
     * List of allowed languages on this site
     */
    'languages' => [
        'sk',
        'en',
        'cz'
    ],

    /**
     * List of public or `live` languages, if not specified the `languages` list is used
     */
    'public_languages' => [
        'sk',
        'en',
        'cz'
    ],

    /**
     * The default fallback language
     */
    'default_language' => 'sk',

    /**
     * Enter callback returning CollectionStorageInterface or use available storages: db, db_cached, file, file_cached
     */
    'storage' => 'db',

    /**
     *  Callback to determine the active language. The callback must return a language code string, eg. 'en'
     *  For session based language setting you can use: \Buxus\Translate\LanguageManager::getActiveLanguageFromSession
     *  For subtree language mapping you can use: \Buxus\Translate\LanguageManager::getActiveLanguageBySubtree
     *  For site base mapping: \Buxus\Translate\LanguageManager::getActiveLanguageFromSite
     *  For language from SEO url: \Buxus\Translate\LanguageManager::getActiveLanguageFromSeoURL
     */

    'active_lang_callback' => '\Buxus\Translate\LanguageManager::getActiveLanguageFromSite',

    /**
     * Defines the mapping of subtrees to languages, the keys define page_tags and the values are language codes
     * This setting is used in the \Buxus\Translate\LanguageManager::getActiveLanguageBySubtree callback
     */
    'subtree_lang_mapping' => [
        'subtree_sk' => 'sk',
        'subtree_en' => 'en',
    ],

    /**
     * Mapping used by the \Buxus\Translate\LanguageManager::getActiveLanguageFromSite method to
     * Maps sites to languages
     */
    'site_to_lang_mapping' => [
        'sk' => 'sk',
        'en' => 'en',
        'cz' => 'cz',
    ],

    /**
     * Properties that should be translated for a new language when calling the `translation:add-new-lang` command without source language argument
     * Array of property tags
     */

    'translatable_properties' => [

    ],

    'language_dependent_property_tags' => [
        // Basic
        \Buxus\Util\PropertyTag::TITLE_TAG(),
        \Buxus\Util\PropertyTag::ANNOTATION_TAG(),
        \Buxus\Util\PropertyTag::TEXT_TAG(),

        // SEO
        \Buxus\Util\PropertyTag::SEO_URL_NAME_TAG(),
        \Buxus\Util\PropertyTag::META_TITLE_TAG(),
        \Buxus\Util\PropertyTag::META_DESCRIPTION_TAG(),
    ],

    /**
     * ISO 639-1 codes mapping, if a mapping is not defined here,
     * the language code is returned as-is
     */
    'iso_639_1_codes' => [
        'sk' => 'sk',
        'cz' => 'cs',
        'en' => 'en',
        'hu' => 'hu',
        'ro' => 'ro',
    ],

    /**
     * Language code to language name mapping
     * Used in the substrate language picker
     */
    'language_names' => [
        'sk' => 'Slovenčina',
        'cz' => 'Čeština'
    ]
];
