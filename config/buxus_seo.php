<?php

return [
    /*
     * Toggles whether to enable or disable all exclude rules listed below
     * If set to false, all `exclude_rules` will be ignored
     */
    'add_exclude_rules_to_robots_txt' => true,

    /*
     *
     * List of exclude definitions for the `noindex,nofollow` rule for robots meta HTML tag
     * and also robot indexing (robots.txt)
     *
     * Some rules do not affect the generation of HTML meta tags (e.g. literal_robots_txt_rules)
     * - other robots.txt rules can be added by using the `\Buxus\SEO\Robots\RobotsTxtRulesProviderInterface`
     * - other robots HTML meta tag rules can be added by using the `\Buxus\SEO\Robots\CrawlerPageRuleProviderInterface`
     */
    'exclude_rules' => [
        /*
         * List of excluded page type tags or IDs,
         * inherited page types are also excluded
         *
         * - not added into robots.txt
         */
        'page_types' => [
            \Buxus\Util\PageTypeID::SERVICE_PAGE_ID(),
            \Buxus\Util\PageTypeID::FOLDER_ID(),
        ],

        /*
         * List of excluded page tags or IDs,
         * will be also included in robots.txt (the url for the current site and all language alternatives)
         */
        'pages' => [
            // 'my_custom_page_tag'
        ],

        /*
         * List of page tags or IDs for which all children are automatically excluded
         *
         * - not added into robots.txt
         */
        'page_roots' => [
            'admin_settings', // Administrátorské nastavenia
        ],

        /*
         * List of GET parameters which make the page excluded from crawling
         * these also affect generation of robots meta tags and also
         * rules for the robots.txt file
         *
         * Use * to exclude all pages with parameters
         */
        'parameters' => [
//            '*', exclude any page that has a query parameter, careful !!! it also excludes `/buxus/generate_page.php?page_id=123`
        ],

        /*
         * List of GET parameters, that are whitelisted, e.g. you could
         * exclude all parameters except the ones specifically provided in this list
         * this also generates rules for robots.txt
         */
        'parameters_whitelist' => [
            'page_id',
            'page',
        ],

        /*
         * Literal rules defined, e.g. `/test` or `/*param=`
         *
         * - used only for the robots.txt file
         */
        'literal_robots_txt_rules' => [
//            '/custom-script',
//            '/*?*myparam=',
        ],
    ],

    'crawlers' => [
        /*
         * Fallback option for default HTML for robots meta HTML tag
         *
         * If a specific rule cannot be resolved for a page, then this fallback option is used
         */
        'default_crawler_page_rules' => 'index,follow',

        /*
         * generate the <meta name="robots" ...> header rules
         */
        'generate_meta_crawler_rules' => true,
    ],

    /*
     * Settings for pre-defined Schema provided by the module
     */
    'schema' => [
        /*
         * Toggle whether to generate organization schema on homepage
         */
        'generate_organization' => true,

        /*
         * Organization schema configuration
         */
        'organization' => [
            'name' => 'BUXUS',
            'logo' => 'c.jpg',
            'social_media' => [
                'https://www.facebook.com/company',
                'https://www.twitter.com/company',
                'https://www.instagram.com/company',
            ],
        ],
    ],

    /*
     * Settings for the default canonical URLs generator
     */
    'canonical_urls' => [
        /*
         * Toggle whether canonical URLs should be added or not
         */
        'enabled' => true,

        /*
         * Global GET parameters that are persisted in canonical urls
         */
        'global_parameters' => [
            'page',
            'page_id'
        ],

        /**
         * Pushed site & language which are used for the `x-default` alternate link generation
         * The site & language combination should refer to the site that is understandable by anyone, thus using
         * a domain & language which use the english localization is most common
         */
        'x_default' => [
            'site' => 'sk',
            //'language' => 'sk',
        ],

        /*
         * Array of additional parameters persisted for a specific page type id / tag
         *
         * Inherited page types are automatically considered
         */
        'page_type_parameters_whitelist' => [
//            \Buxus\Util\PageTypeID::ESHOP_PRODUCT_ID() => [
//                'parameter1'
//            ]
        ]
    ]
];
