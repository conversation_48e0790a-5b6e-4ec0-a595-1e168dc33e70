<?php
return array(
    'modules' => array(
            'faceted_search' => 0,
            'left_menu' => 0,
            'left_menu_hp' => 0,
            'megamenu' => 0,
            'pagenavigator' => 1,
            'search' => 1,
            'search_suggest' => 0,
            'authentication' => 1,
            'cart' => 1,
            'multilang' => 1,
        ),
    'product_list' => array(
            'text_length' => 150,
            'products_per_page' => 24,
            'before_fade_per_page' => 20,
            'paging_page_limit' => 5,
            'homepage_product_columns' => 4,
            'product_columns' => 4,
            'allowed_page_types' => array(
                'eshop_product',
            ),
            'subcategories_thumbnails' => 1,
        ),
    'breadcrumbs' => array(
            'excluded_page_types' => array(
                'eshop_catalog',
                'main_page',
            )
        ),
    'top_menu' => array(
            'is_sticky' => 1,
        ),
    'left_menu' => array(
            'max_nested_level' => 3,
        ),
    'faceted_search' => array(
            'horizontal' => 0,
        ),
);
