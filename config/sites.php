<?php
return [
    // zoznam povolených domén v tvare kód => doména/regulárny výraz
    // doména môže byť aj array pre podporu aliasov
    'sites' => [
        'sk' => env('HOST'),
        'en' => env('HOST_EN'),
        'cz' => env('HOST_CZ'),
    ],

    // list of actively used `live` sites, if it is not defined, the default
    // site list is used
    'active_sites' => [
        'sk',
        'en',
        'cz'
    ],

    // allows to define aliases for main domains
//    'site_aliases' => [
//        'sk' => ['domain2.sk', 'domain3.sk',
//    ],

    /*
     * automatically redirect aliases to main domains
     */
    'redirect_aliases' => true,

    // podpora pre pretazovanie sablon pre jednotlive sites pridanim site key do sablony
    // napr.: index/index.cz.phtml
    'support_site_templates' => false,

    // ak je nastavené na true, tak záznamy v 'sites' sú interpretované ako regulárne výrazy
    'use_regexp_detection' => false,

    // definícia názvov pre domény, ak nie je názov pre doménu definovaný,
    // zoberie sa z poľa 'sites'
    'site_names' => [
        'sk' => 'Slovensko',
    ],

    // default doména v prípade, že nie je možné detegovať (napr CLI)
    'default_site' => 'sk',

    // path pre ukladanie site suborov dostupnych cez BuxusSite::includeSiteFile()
    'site_files_path' => config_path('site_files/'),

    // zoznam zablokovanych features, mozne detekovat cez BuxusSite::feature('abc')
    'disabled_features' => [
        'sk' => [

        ],
    ],

    /*
     * mapping of domains to ISO 3166-1 alpha-2 codes
     */
    'iso_3166_1_codes' => [
        'sk' => 'SK',
        'cz' => 'CZ',
        'hu' => 'HU',
        'ro' => 'RO',
    ],

    // pre definovanie hodnot pre jednotlive domeny je potrebne
    // vyrobit v config adresari adresa site-config a v nom
    // subory pre jednotlive domeny, napr. site-config/sk.php
    // potom napr. site_config('my_site_value') vytiahne hondotu podľa
    // aktuálnej domény z konkretneho suboru v adresari site-config
];
