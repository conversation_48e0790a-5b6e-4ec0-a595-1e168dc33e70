<?php

use Buxus\Util\PropertyTag;
use FS\FixedFacet\PageNamePropertyTextGenerator;
use FS\FixedFacet\TitlePropertyTextGenerator;

return [
    'root_page_id' => 'eshop_catalog',

    'use_push_state' => true,

    'instances' => [
        'fsi' =>
            [
                'facets' =>
                    [
                        ['category',],
                    ],
                'root_title' => 'Produkty'
            ],
    ],

    /**
     * You can skip generation of given 'facets' combinations
     * by supplying indexes of the 'facets' configuration array
     *
     */
    'skip_generating_combinations' => [
    ],

    /**
     * This list of properties specifies properties that can be updated on existing fixed facet page generation
     * every property change is first checked against this list (even when updated through property generator)
     *
     */
    'properties_rewritable_on_update' => [
        PropertyTag::FS_INSTANCE_TAG(),
        PropertyTag::FIXED_PATH_TAG(),
        PropertyTag::FIXED_VALUE_TAG(),
        'parent_page_id',
    ],

    /**
     * Property generators allow changing/setting any page property during fixed facet page generation/creation
     */
    'property_generators' => [
        PropertyTag::TITLE_TAG() => TitlePropertyTextGenerator::class,
        'page_name' => PageNamePropertyTextGenerator::class,
    ],

    /**
     * Name of an "All Values Root" page for defined property
     * of name of "All values Root Folder"
     * for definition of "All Values Root" refer to README.md
     */
    'all_values_root_name' => [
        // PropertyTag::BRAND_TAG() => 'Products of all brands',
    ],

    /**
     * Suffix for all children of an "All Values Root" page for defined property
     */
    'all_values_suffix' => [
        // PropertyTag::GENDER_TAG() => 'for any gender',
    ],

    /**
     * Property conjunctions are used as prefix for parent fixed facet name
     * the behaviour is defined in NameTextGenerator by default
     * for examples see the config section of README.md
     */
    'property_conjunctions' => [
    ],


    /**
     * Whether to generate seo url of the Instance Root Page through property generator
     */
    'generate_seo_url_of_root' => true,

    /**
     * Whether to generate seo url of other Fixed Facet Pages through property generator
     */
    'generate_seo_url_of_values' => true,

    'skip_parent_property_subfolder_when_generating_seo_url_of_values' => false,
];
