<?php
return [
    /*
     * default provider for creating image operations, must implement
     * the \Buxus\ImageProcessor\ImageProcessorProviderInterface interface
     */
    'default_provider' => '\\Buxus\\ImageProcessor\\Imagick\\ImagickProvider',

    /*
     * filesystem path to the image which is used as content for processed
     * images which are not found
     */
    'no_image' => public_path('buxus/images/image-not-found.jpg'),

    /*
     * if an empty image name is fed to the resizer, replace it with this
     * filename to prevent broken images
     */
    'injected_file_if_empty_image' => '__no_image_defined.png',

    /*
     * list of operations which are prepended to the operations list of
     * each defined processor tag, individual operations can be overridden
     * in tag config when you specify a `pre_operations` array and the
     * specific `tag` in it. Also it can be extended for individual tags
     * by speciying a `pre_operations` array with new operations
     */
    'pre_operations' => [
        /*
         * opens the source image and reads basic metadata
         */
        '__pre_open_image' => [
            'type' => 'open_image',
            'no_image' => '%no_image%',
            'source_path' => '%source_path%',
        ],

        /*
         * sets the resulting image format and extension
         * to match the source image
         */
        '__pre_image_format' => [
            'type' => 'image_format',
            'extension' => '%result_extension%',
            'format' => '%result_format%',
        ],

        /*
         * makes a carbon copy for SVGs as it is not suitable to resize them
         */
        '__pre_convert_svg' => [
            'type' => 'convert_svg',
        ],
    ],

    /*
     * base URL path for source images
     */
    'source_url' => '/buxus/images/',

    /*
     * filesystem base path for the source images
     */
    'source_path' => public_path('buxus/images/'),

    /*
     * destination (processed) URL base
     */
    'destination_url' => '/buxus/images/cache/%tag%/',

    /*
     * filesystem base path for storing processed images
     */
    'destination_path' => public_path('buxus/images/cache/%tag%/'),

    /*
     * list of global tag config modifiers that can change the config
     * for specific tag when the tag is called in the form `<tag>@<modifier1>,<modifier2>...`
     * for example '/buxus/images/cache/product@2x/product.jpg`
     *
     * this option can be specified also on the tag level
     */
    'tag_config_modifiers' => [
        '2x' => [
            'operations.resize.scale' => 2,
        ],
    ],

    /*
     * list of operations which are appended to the operations list of
     * each defined processor tag, individual operations can be overridden
     * in tag config when you specify a `post_operations` array and the
     * specific `tag` in it. Also it can be extended for individual tags
     * by speciying a `post_operations` array with new operations
     */
    'post_operations' => [
//        /*
//         * fix the orientation of image according to image metadata
//         */
//        '__post_fix_orientation' => [
//            'type' => 'fix_orientation',
//        ],

        /*
         * saves the image to disk
         */
        '__post_save_image' => [
            'type' => 'save_image',
            'destination_url' => '%destination_url%',
            'destination_path' => '%destination_path%',

            /*
             * enable the following option to not remove metadata from images
             * this is especially usefull if source images contain ICC color profiles
             */
            // 'skip_strip_image' => true,

            /*
             * by default, all unfound images are persisted to a single file (the file is defined in
             * the option no_image_internal_name) which is sent to the client for any unfound image.
             * if you set this option to TRUE, a separate file is saved for each unfound image. This
             * can have some performance benefits if there are many missing images, but on the other
             * hand, it can lead to a DOS attack as the attacker can fill the disk with unfound images.
             */
            // 'save_individual_unfound_images' => true,

            /*
             * the internal name for the resized unfound image, it is saved relatively to the
             * current tag destination folder
             */
            // 'no_image_internal_name' => '__no_image.png',
        ],
    ],

    /*
     * list of base image-processor tags, these are used
     * without prefix as opposed to the namespaced tags
     * that are defined in the `image_processor_tags` subfolder
     *
     */
    'tags' => [
        /*
         * example for a dynamic tag secured with automatic hash
         * if you want to create a link for size eg. 150x230, use:
         * \Buxus\Util\Url::image('path/to/image.png', 'dyn_150_230_hash');
         */
//        'dyn_(?<width>[\\d]+)_(?<height>[\\d]+)_(?<hash>[a-zA-Z0-9]+)' => [
//            'operations' => [
//                'resize' => [
//                    'width' => '%width%',
//                    'height' => '%height%',
//                ],
//            ],
//            'post_operations' => [
//                'security_check' => [
//                    'salt' => 'very secret something',
//                    'hash' => '%hash%',
//                    'algorithm' => 'md5',
//                ],
//            ],
//        ],
    ],
];
