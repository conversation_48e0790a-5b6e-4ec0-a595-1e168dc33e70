<?php
/**
 * @var \Buxus\WebUser\WebUser $user
 */
$user = $this->user;
$manager = new \App\Form\Manager\FormManager();
?>
<div class="container">
    <?= $this->clientZoneTitle() ?>
    <div class="row">
        <?= $this->action('user-menu', $this->controller_name) ?>
        <div class="col-sm-9 col-xs-12">
            <div class="container">
                <div>
                    <h1>
                        <?= $this->renderEditableProperty($this->page, \Buxus\Util\PropertyTag::TITLE_TAG()) ?>
                    </h1>
                    <div>
                        <?= $this->page->getValue(\Buxus\Util\PropertyTag::TEXT_TAG()) ?>
                    </div>
                </div>
            </div>

            <form method="POST" action="<?= $this->form->getAction() ?>">
                <?= csrf_field() ?>
                <label class="mt-3" for="company_name"><?= $this->elements['company_name']->getLabel() ?></label>
                <?= $this->elements['company_name'] ?>

                <label class="mt-3" for="invoice_number"><?= $this->elements['invoice_number']->getLabel() ?></label>
                <?= $this->elements['invoice_number'] ?>

                <label class="mt-3" for="internal_number"><?= $this->elements['internal_number']->getLabel() ?></label>
                <?= $this->elements['internal_number'] ?>

                <label class="mt-3" for="original_number"><?= $this->elements['original_number']->getLabel() ?></label>
                <?= $this->elements['original_number'] ?>

                <label class="mt-3" for="count"><?= $this->elements['count']->getLabel() ?></label>
                <?= $this->elements['count'] ?>

                <label class="mt-3" for="reason"><?= $this->elements['reason']->getLabel() ?></label>
                <?= $this->elements['reason'] ?>

                <?php if (isset($this->elements['order_pickup'])): ?>
                    <label class="mt-3" for="order_pickup"><?= $this->elements['order_pickup']->getLabel() ?></label>
                    <?= $this->elements['order_pickup'] ?>
                <?php endif; ?>

                <label class="mt-3" for="contact_person"><?= $this->elements['contact_person']->getLabel() ?></label>
                <?= $this->elements['contact_person'] ?>

                <label class="mt-3" for="phone"><?= $this->elements['phone']->getLabel() ?></label>
                <?= $this->elements['phone'] ?>

                <label class="mt-3" for="email"><?= $this->elements['email']->getLabel() ?></label>
                <?= $this->elements['email'] ?>

                <div class="mt-3">
                    <?= $this->elements['submit'] ?>
                </div>

                <?= $this->elements['is_submit'] ?>

                <?= $this->elements['webuser_id']; ?>
            </form>
            <div class="row mt-3">
                <div class="col-xs-12">
                    <h2><?= Trans::str('eshop', 'Vaše žiadosti o vrátenie tovaru') ?></h2>
                    <?php if ($this->submitted->isNotEmpty()): ?>
                        <div class="table-responsive">
                            <table class="table return-table">
                                <tr>
                                    <th></th>
                                    <th class="pr-4"><?= Trans::str('eshop', 'Číslo vratky') ?></th>
                                    <th class="pr-4"><?= Trans::str('eshop', 'Číslo faktúry') ?></th>
                                    <th class="pr-4"><?= Trans::str('eshop', 'Originálne číslo dielu') ?></th>
                                    <th class="pr-4"><?= Trans::str('eshop', 'Čas žiadosti') ?></th>
                                    <th class="pr-4"><?= Trans::str('eshop', 'Stav') ?></th>
                                    <th></th>
                                </tr>
                                <?php foreach ($this->submitted as $submitted): ?>
                                    <tr class="<?= \App\Models\WebUserNotification::hasNotification(config('notifications.types.return'), $submitted->form_submit_id) ? 'has-notification':'' ?>" data-item-id="<?= $submitted->form_submit_id ?>">
                                        <td class="notification-td"><span></span></td>

                                        <td class="pr-4"><?= $submitted->parsed_properties->form_number ?? $submitted->form_submit_id ?></td>
                                        <td class="pr-4"><?= $submitted->parsed_properties->invoice_number ?></td>
                                        <td class="pr-4"><?= $submitted->parsed_properties->original_number ?></td>
                                        <td class="pr-4"><?= date('d. m. Y H:i:s', strtotime($submitted->form_submit_time)) ?></td>
                                        <td class="pr-4"><?= $manager->getStateByKey($submitted->form_state_flag) ?></td>
                                        <td class="pr-4"><a target="_blank"
                                                            href="<?= route('download.returns-form', ['returnsFormId' => $submitted->form_submit_id]) ?>">
                                                <?= \Trans::str('common', 'Prejsť na PDF') ?></a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </table>
                        </div>
                    <?php else: ?>
                        <p><?= Trans::str('eshop', 'Nemáte žiadne žiadosti o vrátenie tovaru.') ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
