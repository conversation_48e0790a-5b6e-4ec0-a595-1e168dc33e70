<?php
/**
 * @var \Buxus\Eshop\Order\OrderInterface $order
 */
$order = $this->detail;

$showVat = new \App\Eshop\Price\ShowVAT();
?>
    <h4><?= $this->str('user', 'Detail objednávky') ?> <?= $order->getVariableSymbol() ?></h4>
<?php if ($order->paymentCanBeRepeated()): ?>
    <p>
        <a href="<?= $order->getPaymentRepeatLink() ?>" class="btn btn-danger"><?= $this->str('user', 'Zaplatiť') ?></a>
    </p>
<?php endif; ?>
    <table class="watchdog table">
        <tr>
            <th><?= $this->str('user', 'Názov tovaru') ?></th>
            <th><?= $this->str('user', 'Počet kusov') ?></th>
            <?php if ($showVat->shouldShowVAT()): ?>
                <th><?= $this->str('user', 'Cena s DPH') ?></th>
            <?php endif; ?>
            <th><?= $this->str('user', 'Cena celkom bez DPH') ?></th>
            <?php if ($showVat->shouldShowVAT()): ?>
                <th><?= $this->str('user', 'Cena celkom') ?></th>
            <?php endif; ?>
            <th><?= $this->str('user', 'Stav položky') ?></th>
        </tr>
        <?php foreach ($order->getItems() as $item): ?>
            <?php
            /**
             * @var \Buxus\Eshop\Order\OrderItemInterface $item
             */
            ?>
            <tr>
                <td class="prod <?= strpos(strtolower($item->getOption('item_state')), 'storno') !== false
                    ? 'line-through' : '' ?>"><?= $item->getProductName() ?></td>
                <td><?= $item->getAmount() ?></td>
                <?php if ($showVat->shouldShowVAT()): ?>
                    <td><?= $this->formatPrice($item->getItemPrice()) ?></td>
                <?php endif; ?>
                <td><?= $this->formatPrice($item->getTotalPriceWithoutVAT()) ?></td>
                <?php if ($showVat->shouldShowVAT()): ?>
                    <td><?= $this->formatPrice($item->getTotalPriceIncludingVAT()) ?></td>
                <?php endif; ?>
                <td><?= $this->str('eshop', $item->getOption('item_state')) ?></td>
            </tr>
        <?php endforeach; ?>
    </table>

<?php if (!empty($order->getNote())): ?>
    <p><strong><?= \Trans::str('eshop', 'Poznámka') ?>: </strong><?= $order->getNote() ?></p>
<?php endif; ?>
