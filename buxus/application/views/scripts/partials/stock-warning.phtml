<?php
$stock_balance = ($this->stock_balance < 0) ? 0 : $this->stock_balance;
$product = $this->product;
?>

<div class="modal fade" id="insufficientStockQuantityPopup" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
     aria-hidden="true">
    <div class="modal-dialog" style="transform: translate(0, 50vh) translate(0, -50%);">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title"><?= $this->str('availability', 'Predĺžená doba dodania') ?></h4>
            </div>
            <div class="modal-body">
                <?= \Trans::strParamed('availability', 'Na sklade máme len %s ks tohto produktu. Pre zvyšné kusy (%s ks) môže byť doba dodania
                dlhšia.', [$stock_balance, '<span class="product-cart-amount"></span>']) ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default"
                        data-dismiss="modal"><?= \Trans::str('common', 'Späť k nákupu') ?></button>
                <a href="<?= \Buxus\Util\Url::page(\Buxus\Util\PageIds::getCart1Contents()) ?>"
                   class="btn btn-primary"><?= \Trans::str('common', 'Prejsť
                    do košíka') ?></a>
            </div>
        </div>
    </div>
</div>

<?php js_begin() ?>
<script>
    require(['jquery'], function ($) {
        $(function () {
            $(document).on('eshop.command', function (e, command, payload, element) {
                if (command === 'product-add' && payload.tracker.id === <?= $product->getPageId() ?>) {
                    if (!isNaN(payload.item_count_in_cart) && <?= $stock_balance ?> > 10 && payload.item_count_in_cart > <?= $stock_balance ?>) {
                        $('.product-cart-amount').html(payload.item_count_in_cart - <?= $stock_balance ?>);
                        $('#insufficientStockQuantityPopup').modal();
                    }
                }
            })
        });
    });
</script>
<?php js_end() ?>
