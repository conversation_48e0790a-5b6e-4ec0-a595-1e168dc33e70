<?php
$product = $this->product;
$depositValue = ($this->deposit_value <= 0) ? 0 : $this->deposit_value;
?>

<div class="modal fade" id="depositInfoPopup<?= $product->getPageId() ?>" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
     aria-hidden="true">
    <div class="modal-dialog" style="transform: translate(0, 50vh) translate(0, -50%);">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title"><?= $this->str('availability', 'Vratná záloha') ?></h4>
            </div>
            <div class="modal-body">
                <?= \Trans::strParamed('eshop', 'Tento diel je repasovaný. Za tento vratný diel sa účtuje záloha vo výške <strong>%s</strong>.', [
                    $this->formatPrice($depositValue)
                ]) ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default"
                        data-dismiss="modal"><?= \Trans::str('common', 'Späť k nákupu') ?></button>
                <a href="<?= \Buxus\Util\Url::page(\Buxus\Util\PageIds::getCart1Contents()) ?>"
                   class="btn btn-primary"><?= \Trans::str('common', 'Prejsť
                    do košíka') ?></a>
            </div>
        </div>
    </div>
</div>

<?php js_begin() ?>
<script>
    require(['jquery'], function ($) {
        $(function () {
            $(document).on('eshop.command', function (e, command, payload, element) {
                console.log(e, command, payload, element);
                if (command === 'product-add' && payload.tracker.id === <?= $product->getPageId() ?>) {
                    if (<?= $depositValue ?> > 0) {
                        $('#depositInfoPopup<?= $product->getPageId() ?>').modal();
                    }
                }
            })
        });
    });
</script>
<?php js_end() ?>
