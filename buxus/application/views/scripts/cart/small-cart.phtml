<?php
/**
 * @var ShoppingCart $shopping_cart
 */
use Buxus\Eshop\Contracts\ShoppingCart;
$shopping_cart = $this->shopping_cart;
try {
    $cartPrice = $shopping_cart->getFinalPriceObject();
} catch (\Exception $e) {
    $cartPrice = 0;
}
?>
                    <div id="mini-cart" class="cartMenu">
                        <a href="<?= $shopping_cart->getCheckout()->getCartUrl() ?>" title="<?= $this->str('cart', 'Zobraziť košík')?>" class="box basket eshop-small-cart pull-right kosik_container_active collapse navbar-collapse" id="kosik_container">
                            <div class="title pull-right"><?= $this->str('cart', 'V košíku máte')?><br>
                                <b class="eshop-product-amount"><?= $shopping_cart->getProductAmount() ?></b> <span class="eshop-product-amount-text" style="font-size: inherit;"><?= $this->eshopProductAmountText($shopping_cart->getProductAmount()) ?></span>
                                (<span class="mini-cart-price"><?= $this->formatPrice($cartPrice) ?></span>)
                            </div>
                            <span class="glyphicon glyphicon-shopping-cart icon-cart pull-left"></span>
                        </a>
                    </div>
