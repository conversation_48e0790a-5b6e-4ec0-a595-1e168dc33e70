<main>
    <section class="subpage-cover" style="background-image:url('/buxus/assets/images/img/subpage-banner.jpg');">
        <div class="container">
            <?php if (\BuxusSite::site() == 'en'): ?>
                <p class="cover-title"><strong>Fast delivery across the EU for a great prices!</strong></p>
            <?php else: ?>
                <p class="cover-title"><strong><?= $this->str('eshop', 'Doprava zdarma pri nákupe') ?>
                        <em><?= $this->str('eshop', 'nad 100 €') ?></em></strong></p>
            <?php endif; ?>
        </div>
    </section>

    <section class="product-list">
        <div class="section product-list-header">
            <div class="container">

                <?= $this->action('page-navigator', 'index') ?>

                <h1 class="mb-4 font-weight-bold"><?= $this->renderEditableProperty($this->page, \Buxus\Util\PropertyTag::TITLE_TAG()) ?></h1>

                <?php if (is_array($this->subcategories) && count($this->subcategories)): ?>
                    <div class="suggestions-list">
                        <?php foreach ($this->subcategories as $subcategory): ?>
                            <a href="<?= $subcategory->getUrl() ?>"
                               class="suggestion"><h2
                                    class="suggestion-title"><?= $this->renderProperty($subcategory, \Buxus\Util\PropertyTag::TITLE_TAG()) ?></h2>
                            </a>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <?php if ($this->list->hasItems()): ?>
            <div class="section">
                <div class="container">
                    <div class="row">
                        <?= $this->renderEditableHtmlProperty($this->page, \Buxus\Util\PropertyTag::TEXT_TAG()) ?>
                        <?= $this->partial('product-catalog/partials/product-list.phtml', array('list' => $this->list, 'modify_image' => $this->modify_image)) ?>
                    </div>
                </div>
                <?php if (\BuxusAuthentication::isAuthenticated()): ?>
                    <div class="container">
                        <div class="row">
                            <?= $this->partial('product-catalog/partials/hidden-products.phtml', array('items' => $this->list->hidden, 'modify_image' => $this->modify_image)) ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </section>

    <?php if ($this->list->countItems() >= (config('krabica.product_list.before_fade_per_page'))): ?>
    <section class="section no-more-section">
        <?php else: ?>
        <section class="section no-more-section-empty">
            <?php endif; ?>
            <div class="container">
                <?php if ($this->list->countItems() <= 0): ?>
                    <div class="section-header">
                        <?php if ($this->list->term): ?>
                            <?= \App\Product\DemandManager::render($this->list->term) ?>
                        <?php else: ?>
                            <h4 class="section-title"><?= $this->str('eshop', 'Nenašli sa žiadne výsledky') ?></h4>
                            <p class="section-description"><?= $this->str('eshop', 'Vyhľadajte ďalšie produkty podľa OE čísla') ?></p>
                        <?php endif; ?>
                    </div>

                <?php else: ?>
                    <div class="section-header">
                        <p class="section-description"><?= $this->str('eshop', 'Vyhľadajte ďalšie produkty podľa OE čísla') ?></p>
                    </div>
                <?php endif; ?>

                <form class="search" method="get"
                      action="<?= \Buxus\Util\Url::page(Buxus\Util\PageIds::getPageId('search_results')) ?>">
                    <input id="product-list-search" type="text" name="term"
                           placeholder="<?= $this->str('eshop', 'Hľadať podľa OE čísla') ?>">

                    <button type="submit" class="btn btn-icon">
                        <svg>
                            <use xlink:href="#sprite-lens"></use>
                        </svg>
                        <span class="button-label"><?= $this->str('eshop', 'Hľadať') ?></span>
                    </button>
                </form>


            </div>
        </section>
</main>

