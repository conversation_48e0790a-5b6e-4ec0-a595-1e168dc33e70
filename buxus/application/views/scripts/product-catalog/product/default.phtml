<?php
/**
 * @var \App\Eshop\Product $product
 */
$product = $this->product;

$categories = $product->getStackedParentCategories();
$category = end($categories);
?>

    <section class="product-detail">
        <div class="container">
            <?php if ($category !== false): ?>
                <?= $this->action('page-navigator', 'index') ?>
            <?php endif; ?>

            <div class="row row-align-start">
                <div class="col-xs-12 col-md-6 col-lg-6 col-xl-5 mb-5">

                    <div id="product-detail-gallery" class="product-detail-gallery">
                        <?php if ($product->hasHlavnyObrazok()): ?>
                            <a href="<?= $product->getImageUrl(\Buxus\Util\PropertyTag::PHOTO_ALBUM_THUMBNAIL_TAG(), 'basic.eshop_full_product_image'); ?>"
                               class="product-detail-gallery-item"
                               title="<?= $this->escape($product->getPage()->getValue(\Buxus\Util\PropertyTag::TITLE_TAG())) ?>">
                                <img
                                        src="<?= $product->getImageUrl(\Buxus\Util\PropertyTag::PHOTO_ALBUM_THUMBNAIL_TAG(), 'basic.eshop_product_image_3') ?>" <?php if ($product->getMainImage() != null) echo $this->altTitle($product->getMainImage(), \Buxus\Util\PropertyTag::PHOTO_ALT_TAG()); ?> />
                            </a>
                        <?php endif; ?>

                        <?php if ($product->hasObrazky()): ?>
                            <?php foreach ($product->getObrazky() as $index => $list_item): /* @var $list_item App\Page\BasicPage */ ?>
                                <?php if ($product->getImageUrl(\Buxus\Util\PropertyTag::PHOTO_ALBUM_THUMBNAIL_TAG()) === $list_item->getImageUrl(\Buxus\Util\PropertyTag::PHOTO_FILE_TAG())) {
                                    continue;
                                } ?>
                                <?php if ($list_item->imageExist(\Buxus\Util\PropertyTag::PHOTO_FILE_TAG())): ?>
                                    <a href="<?= $list_item->getImageUrl(\Buxus\Util\PropertyTag::PHOTO_FILE_TAG(), 'basic.eshop_full_product_image') ?>"
                                       class="product-detail-gallery-item">
                                        <img
                                                src="<?= $list_item->getImageUrl(\Buxus\Util\PropertyTag::PHOTO_FILE_TAG(), 'basic.eshop_product_image_3') ?>"
                                                alt="<?= $this->escape($list_item->getValue(\Buxus\Util\PropertyTag::PHOTO_ALT_TAG())) ?>"
                                                title="<?= $this->escape($list_item->getValue(\Buxus\Util\PropertyTag::TITLE_TAG())) ?>"/>
                                    </a>
                                <?php else: ?>
                                    <a href="<?= \Buxus\Util\Url::image($this->no_image) ?>"
                                       class="product-detail-gallery-item">
                                        <img
                                                src="<?= \Buxus\Util\Url::image($this->no_image, 'basic.eshop_product_image_3') ?>"
                                                alt="<?= $this->escape($list_item->getValue(\Buxus\Util\PropertyTag::PHOTO_ALT_TAG())) ?>"
                                                title="<?= $this->escape($list_item->getValue(\Buxus\Util\PropertyTag::ANNOTATION_TAG())) ?>"/>
                                    </a>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        <?php endif; ?>

                    </div>

                    <div id="product-detail-gallery-thumbnails" class="product-detail-gallery-thumbnails">

                        <?php if ($product->hasHlavnyObrazok()): ?>
                            <a href="<?= $product->getImageUrl(\Buxus\Util\PropertyTag::PHOTO_ALBUM_THUMBNAIL_TAG(), 'basic.eshop_full_product_image'); ?>"
                               class="product-detail-gallery-thumbnail-item"" title="<?= $this->escape($product->getPage()->getValue(\Buxus\Util\PropertyTag::TITLE_TAG())) ?>">
                            <img
                                    src="<?= $product->getImageUrl(\Buxus\Util\PropertyTag::PHOTO_ALBUM_THUMBNAIL_TAG(), 'basic.product_gallery_list') ?>" <?php echo $this->altTitle($product->getPage()) ?> />
                            </a>
                        <?php endif; ?>

                        <?php if ($product->hasObrazky()): ?>
                            <?php foreach ($product->getObrazky() as $index => $list_item): /* @var $list_item App\Page\BasicPage */ ?>
                                <?php if ($product->getImageUrl(\Buxus\Util\PropertyTag::PHOTO_ALBUM_THUMBNAIL_TAG()) === $list_item->getImageUrl(\Buxus\Util\PropertyTag::PHOTO_FILE_TAG())) {
                                    continue;
                                } ?>
                                <?php if ($list_item->imageExist(\Buxus\Util\PropertyTag::PHOTO_FILE_TAG())): ?>
                                    <a href="<?= $list_item->getImageUrl(\Buxus\Util\PropertyTag::PHOTO_FILE_TAG(), 'basic.eshop_full_product_image') ?>"
                                       class="product-detail-gallery-thumbnail-item"">
                                    <img
                                            src="<?= $list_item->getImageUrl(\Buxus\Util\PropertyTag::PHOTO_FILE_TAG(), 'basic.product_gallery_list') ?>"
                                            alt="<?= $this->escape($list_item->getValue(\Buxus\Util\PropertyTag::PHOTO_ALT_TAG())) ?>"
                                            title="<?= $this->escape($list_item->getValue(\Buxus\Util\PropertyTag::TITLE_TAG())) ?>"/>
                                    </a>
                                <?php else: ?>
                                    <a href="<?= \Buxus\Util\Url::image($this->no_image) ?>"
                                       class="product-detail-gallery-thumbnail-item"">
                                    <img
                                            src="<?= \Buxus\Util\Url::image($this->no_image, 'basic.product_gallery_list') ?>"
                                            alt="<?= $this->escape($list_item->getValue(\Buxus\Util\PropertyTag::PHOTO_ALT_TAG())) ?>"
                                            title="<?= $this->escape($list_item->getValue(\Buxus\Util\PropertyTag::ANNOTATION_TAG())) ?>"/>
                                    </a>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>


                <?php
                /** @var \App\Eshop\Product $product */
                $product = $this->product;
                $showVat = new \App\Eshop\Price\ShowVAT();
                $price = $this->formatPrice($this->product->getFinalPriceObject());
                ?>

                <div class="col-xs-12 col-md-6 col-lg-6 col-xl-5 product-detail-main-content">
                    <div class="card">
                        <h1 class="product-detail-title">
                            <?= $this->renderEditableProperty($product->getPage(), \Buxus\Util\PropertyTag::TITLE_TAG()) ?>
                        </h1>

                        <div class="product-detail-description">
                            <?= $this->renderEditableHtmlProperty($product->getPage(), \Buxus\Util\PropertyTag::ESHOP_DETAIL_TEXT_TAG()) ?>
                        </div>

                        <table class="table mb-5">
                            <?php if ($product->getMainCategoryPage()): ?>
                                <?php if ($product->getPage()->getParentPageId() != \Buxus\Util\PageIds::getNezaradene() && $product->getPage()->getParentPageId() != \Buxus\Util\PageIds::getNezaradene()): ?>
                                    <tr>
                                        <th><?= $this->str('eshop', 'Kategória') ?>:</th>
                                        <td class="text-right"><a
                                                    href="<?= $product->getMainCategoryPage()->getUrl() ?>"><?= $product->getMainCategoryPage()->getValue(\Buxus\Util\PropertyTag::TITLE_TAG()) ?></a>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            <?php endif; ?>

                            <?php if (!empty($product->getPage()->getValue(\Buxus\Util\PropertyTag::ESHOP_ROLLER_PRODUCER_TAG()))): ?>
                                <tr>
                                    <th><?= $this->str('eshop', 'Výrobca') ?>:</th>
                                    <td class="text-right">
                                        <p><?= Ciselniky::get('product_catalog.producer')->getValueById($product->getPage()->getValue(\Buxus\Util\PropertyTag::ESHOP_ROLLER_PRODUCER_TAG()))->getName(); ?></p>
                                    </td>
                                </tr>
                            <?php endif; ?>

                            <?php if (!empty($product->getOnixNsNumber())): ?>
                                <tr>
                                    <th><?= $this->str('eshop', 'Interné číslo') ?>:</th>
                                    <td class="text-right"><?= $product->getOnixNsNumber() ?></td>
                                </tr>
                            <?php endif; ?>

                            <tr>
                                <th><?= $this->str('eshop', 'Číslo produktu') ?>:</th>
                                <td class="text-right"><?= $product->getCodesAsString() ?></td>
                            </tr>

                            <?php if (!empty($product->getWeightInKilograms())): ?>
                                <tr>
                                    <th><?= $this->str('eshop', 'Hmotnosť') ?>:</th>
                                    <td class="text-right"><?= $product->getWeightInKilograms() ?> kg</td>
                                </tr>
                            <?php endif; ?>
                        </table>

                        <?php if ($product->isFebiBilsteinSupplier()): ?>
                            <div class="febi-bilstein-guarantee mb-3">
                                <small><?= \Trans::str('eshop', 'Záruka 36 mesiacov'); ?></small>
                            </div>
                        <?php endif; ?>

                        <?php if (!\WebUserAuthentication::isAuthenticated() || \WebUserAuthentication::getUser()->getInvoiceAfterDueDate() == \Buxus\Core\Constants::C_True_Char): ?>
                            <div class="product-detail-price-table mb-4">
                                <div class="row mb-1">
                                    <div class="col-xs-6"><small
                                                class="text-muted"><?= $this->str('eshop', 'Dostupnosť') ?>:</small>
                                    </div>
                                    <div class="col-xs-6 text-muted"><small
                                                class="text-muted"><?= $this->str('eshop', 'Cena'); ?>:</small></div>
                                </div>
                                <div class="row mb-1">
                                    <div class="col-xs-6">
                                        <strong class="product-detail-text-larger">
                                            <div class="availability"><?= $this->dostupnostProduktu($product, true) ?></div>
                                        </strong>
                                    </div>
                                    <div class="col-xs-6">
                                        <strong class="product-detail-text-larger">
                                            <?= !WebUserAuthentication::isAuthenticated() ? $this->str('eshop', 'Po prihlásení') : $this->str('eshop', 'Po úhrade splatných faktúr') ?>
                                        </strong>
                                    </div>
                                </div>
                            </div>

                            <?php if (!WebUserAuthentication::isAuthenticated()): ?>
                                <div class="product-detail-add-to-cart-section">
                                    <a href="<?= \Buxus\Util\Url::taggedPage(\Authentication\AuthenticationPages::LOGIN) ?>"
                                       class="btn btn-light product-detail-button background-less"><?= $this->str('authenticate', 'Prihlásiť sa') ?></a>
                                    <a href="<?= \Buxus\Util\Url::taggedPage(\Authentication\AuthenticationPages::REGISTRATION) ?>"
                                       class="btn btn-primary product-detail-button"><?= $this->str('authenticate', 'Registrácia') ?></a>
                                </div>
                            <?php endif; ?>

                        <?php else: ?>
                            <div class="product-detail-price-table mb-4">
                                <div class="row mb-1">
                                    <div class="col-xs-6"><small
                                                class="text-muted"><?= $this->str('eshop', 'Dostupnosť') ?>:</small>
                                    </div>
                                    <div class="col-xs-6 text-muted"><small
                                                class="text-muted"><?= $this->str('eshop', 'Cena'); ?>
                                            :</small></div>
                                </div>
                                <div class="row mb-1">
                                    <div class="col-xs-6"><strong class="product-detail-text-larger">
                                            <div class="availability"><?= $this->dostupnostProduktu($product, true) ?></div>
                                        </strong></div>
                                    <div class="col-xs-6"><strong
                                                class="product-detail-text-larger"><?= $product->isPriceValid() ? $this->formatPrice($product->getFinalPriceObjectWithoutVat()) : $this->str('eshop', 'Na vyžiadanie') ?></strong>
                                    </div>
                                </div>
                                <?php if ($product->isPriceValid()): ?>
                                    <div class="row mb-1">
                                        <div class="col-xs-6"><span
                                                    class="product-detail-text-larger text-primary font-weight-medium"></span>
                                        </div>
                                        <div class="col-xs-6">
                                            <?php if ($showVat->shouldShowVAT()): ?>
                                                <span class="product-detail-text-larger font-weight-medium">
                            <div class="price" data-variant-property="price"><?= $price ?></div> s DPH
                            <?php if ($product->isPriceDefined(\Buxus\Eshop\Price\ItemPriceDecorator\ItemDiscount::NON_DISCOUNT_PRICE_INCLUDING_VAT)): ?>
                                <div class="price-old"
                                     data-variant-property="price-standard"><?= $this->formatPrice($product->getPriceObject(\Buxus\Eshop\Price\ItemPriceDecorator\ItemDiscount::NON_DISCOUNT_PRICE_INCLUDING_VAT)) ?></div>
                            <?php endif; ?>
                        </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <?php if ($this->product->shouldShowDeliveryTime()): ?>
                                        <div class="row mb-1">
                                            <div class="col-xs-6"><small
                                                        class="text-muted"><?= $this->str('eshop', 'Doba dodania') ?>
                                                    :</small></div>
                                        </div>
                                        <div class="row mb-1">
                                            <div class="col-xs-6"><strong
                                                        class="product-detail-text-larger"><?= $this->product->getDeliveryTime() ?></strong>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php endif; ?>

                                <?php if ($this->product->isRefurbishedPart() && $this->product->getItemDepositValue() > 0): ?>
                                    <div class="row mb-1">
                                        <div class="col-xs-6"><small
                                                    class="text-muted"><?= $this->str('eshop', 'Vratná záloha') ?>
                                                :</small></div>
                                    </div>
                                    <div class="row mb-1">
                                        <div class="col-xs-6"><strong
                                                    class="product-detail-text-larger"><?= $this->formatPrice($this->product->getItemDepositValue()) ?></strong>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <?php
                            $enabled = !($this->product instanceof \Buxus\Eshop\Variants\VariantAwareProductInterface && $this->product->hasVariants() && $this->product->isMasterProduct()) && $this->product->isOrderable();
                            $command = $enabled ? $this->cartCommand('product-add', ['product_id' => $this->product->getPageId(), 'render-minicart' => 1]) : '';
                            ?>
                            <?php if ($product->isPriceValid()): ?>
                                <div class="product-amount__notification hidden">
                                    <span><strong><?= $this->str('eshop', 'Uvedený produkt je možné zakúpiť len v párnom počte.') ?></strong></span>
                                </div>
                                <div class="product-detail-add-to-cart-section">
                                    <div class="input-number-with-controls">
                                        <button class="button-minus" data-toggle="input-number-decrease"
                                                data-target="#product-detail-amount">
                                            <svg class="icon fill-primary">
                                                <use xlink:href="#sprite-minus"></use>
                                            </svg>
                                        </button>
                                        <input id="product-detail-amount" type="number"
                                               min="<?= $product->shouldOnlySellInPair() ? '2' : '1' ?>"
                                               value="<?= $product->shouldOnlySellInPair() ? '2' : '1' ?>"
                                               step="<?= $product->shouldOnlySellInPair() ? '2' : '1' ?>"
                                               class="product-amount">
                                        <button class="button-plus" data-toggle="input-number-increase"
                                                data-target="#product-detail-amount">
                                            <svg class="icon fill-primary">
                                                <use xlink:href="#sprite-plus"></use>
                                            </svg>
                                        </button>
                                    </div>
                                    <a href="<?= $command ?>"
                                       class="btn btn-primary product-detail-button" <?= $enabled ? '' : ' disabled="disabled"' ?>
                                       id="add_to_cart_button"
                                       data-cart-command="product-add"><?= $this->str('eshop', 'Do košíka') ?></a>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>

                    </div>

                    <?php if (!$this->product->isInStock()): ?>
                        <div class="card text-center">
                            <svg class="icon mb-3" style="width: 2.6rem; height: 2.6rem">
                                <use xlink:href="#sprite-warning"></use>
                            </svg>

                            <h6><?= $this->str('eshop', 'Tento tovar je na objednávku a nie je možné ho vrátiť ani stornovať z odoslanej objednávky.') ?></h6>
                            <p class="small line-height-large"><?= $this->str('eshop', 'Dodacia doba tovaru na objednávku je orientačná a môže sa predĺžiť ak tovar nie je skladom u nášho dodávateľa alebo pri nepredvídateľných udalostiach napr. počas prepravy. Pre upresnenie doby dodania nás prosím kontaktujte.') ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <?= app('buxus:related-products:manager')->showRelatedBoxForProduct($product) ?>
    </section>

    <div>
        <?= $this->partial('partials/stock-warning.phtml', [
            'stock_balance' => $product->getStockBalance(),
            'product' => $product,
        ]) ?>

        <?php if ($product->isRefurbishedPart()): ?>
            <?= $this->partial('partials/deposit-warning.phtml', [
                'deposit_value' => $product->getItemDepositValue(),
                'product' => $product,
            ]) ?>
        <?php endif; ?>
    </div>

<?php
if ($product instanceof \Analytics\EcProductInterface) {
    $ecProduct = $this->product->getEcProduct();
    $ecProductDetails = new Analytics\EnhancedEcommerce\ProductDetails();
    $ecProductDetails->setPageType("product");
    $ecProductDetails->addProduct($ecProduct);
    \Analytics\EnhancedEcommerce\Manager::getInstance()->getAggregator()->add($ecProductDetails);
}
?>