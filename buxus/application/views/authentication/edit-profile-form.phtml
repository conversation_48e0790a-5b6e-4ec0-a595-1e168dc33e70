<form class="form-horizontal" action="<?= $this->escape($this->form->getAction()) ?>"
      method="<?= $this->escape($this->form->getMethod()) ?>" enctype="application/x-www-form-urlencoded">

    <?= $this->formErrorsList($this->form->getMessages()); ?>

    <div class="form-group">
        <label for="first_name" class="col-sm-3 control-label required"><?= $this->str('user', 'Email') ?></label>
        <div class="col-sm-6">
            <p class="form-control-static"><?= $this->user->getEmail() ?></p>
        </div>
    </div>

    <?= $this->elements['password'] ?? '' ?>
    <?= $this->elements['confirm_password'] ?? '' ?>

    <h3><?= $this->str('user', 'Fakturačné údaje') ?></h3>
    <?= $this->elements['customer_type'] ?>

    <?= $this->elements['first_name'] ?>
    <?= $this->elements['surname'] ?>

    <?= $this->elements['company_name'] ?>
    <?= $this->elements['ico'] ?>
    <?= $this->elements['dic'] ?>
    <?= $this->elements['drc'] ?>

    <?= $this->elements['phone'] ?>

    <?= $this->elements['street'] ?>
    <?= $this->elements['city'] ?>
    <?= $this->elements['zip'] ?>
    <?php if (isset($this->elements['country'])): ?>
        <?= $this->elements['country'] ?>
    <?php endif; ?>

    <?php js_begin() ?>
    <script type="text/javascript">
        require(['user-profile-data']);
    </script>
    <?php js_end() ?>

    <?= csrf_field() ?>
</form>
