<?php
/**
 * @var \Buxus\Eshop\Product\Product $product
 */
$product = $this->product;
?>
<tr class="bt-minicart-item">
	<td class="text-center">
		<a href="<?= $product->getUrl() ?>" title="<?= $product->getName() ?>"><img src="<?= $product->getImage('eshop_product_minicart') ?>" alt="<?= $product->getName() ?>" title="<?= $product->getName() ?>" /></a>
	</td>
	<td>
		<a href="<?= $product->getUrl() ?>" title="<?= $product->getName() ?>"><?= $product->getName(); ?></a>
		<br />
		<span class="label label-success"><?= $this->formatPrice($product->getFinalPriceObject()) ?></span>
	</td>
	<td class="border-left">
		<?= $product->getAmount() ?>
        <?php if ($product->canBeManuallyRemoved()): ?>x<br />
		<a href="<?= $this->cartCommand('product-remove', array('product_id' => $product->getTag(), 'render-minicart' => 1)) ?>" data-cart-command="product-remove"><span class="icon icon-circle-cross"></span></a>
        <?php endif; ?>
	</td>
</tr>
<tr class="bt-minicart-item-separator">
	<td colspan="3">
	</td>
</tr>
<tr class="bt-minicart-item-separator lower">
	<td colspan="3">
	</td>
</tr>
