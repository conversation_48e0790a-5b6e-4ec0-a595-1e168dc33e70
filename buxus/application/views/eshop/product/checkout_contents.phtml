<?php
/**
 * @var \Buxus\Eshop\Product\Product $product
 */

use App\Eshop\Cart\AvailabilityRenderer;

$product = $this->product;
$availability = new AvailabilityRenderer($product, $product->getAmount());
try {
    $producer = Ciselniky::get('product_catalog.producer')->getValueById($product->getPage()->getValue(\Buxus\Util\PropertyTag::ESHOP_ROLLER_PRODUCER_TAG()))->getName();
} catch (\Exception $e) {
    $producer = '';
}
?>
<tr data-cart-item="<?= $product->getTag() ?>">
    <td class="col-xs-2 col-sm-1 basket_item_image">
        <?php if (!empty($product->getImage('eshop.eshop_product_basket'))): ?>
            <a href="<?= $product->getUrl() ?>" title="<?= $product->getName() ?>">
                <img
                    src="<?= $product->getImage('eshop.eshop_product_basket') ?>"
                    alt="<?= $product->getName() ?>"
                    title="<?= $product->getName() ?>"/>
            </a>
        <?php endif; ?>
    </td>

    <td class="col-xs-10 col-sm-11 basket_item_details">
        <div class="col-xs-12 col-sm-4 col-md-3 product-name-wrapper">
            <h3 class="cart-product-name">
                <a href="<?= $product->getUrl() ?>" title="<?= $product->getName() ?>">
                    <?= $product->getName(); ?>
                </a>
                <br>
                <p class="cart-product-producer"><?= $producer ?></p>
                <?php if (isset($variant) && $variant): ?>
                    <h4 class="cart-product-variant-name hidden-xs hidden-md hidden-lg"><?= $variant ?></h4>
                <?php endif; ?>
            </h3>
        </div>
        <div class="col-xs-12 col-sm-4 col-md-2 bt-cart-item-amount">
            <?php if ($product->amountCanBeChanged()): ?>
                <?php js_begin() ?>
                <script type="text/javascript">
                    require(['jquery', 'bootstrap-touchspin'], function ($) {
                        $(function () {
                            $("#product_amount_<?= $product->getTag() ?>").TouchSpin({
                                min: 1,
                                max: 999,
                                step: 1
                            });
                        });
                    });
                </script>
            <?php js_end() ?>
                <input size="5" class="text-center amount-cart" type="text" data-item-id="<?= $product->getTag() ?>"
                       id="product_amount_<?= $product->getTag() ?>" data-value="<?= $product->getAmount() ?>"
                    <?php if ($product->shouldOnlySellInPair()): ?> step="2" min="2" data-sell-in-pair="true" <?php endif; ?>
                       name="product_amount[<?= $product->getTag() ?>]" value="<?= $product->getAmount() ?>"/>
            <?php else: ?>
                <?= $product->getAmount() ?>
            <?php endif; ?>
        </div>
        <div class="col-md-2 visible-md visible-lg bt-cart-item-price">
            <?php if ($product->isPriceDefined(\Buxus\Eshop\Price\ItemPriceDecorator\ItemDiscount::NON_DISCOUNT_PRICE_INCLUDING_VAT)): ?>
                <div
                    class="price-old"><?= $this->formatPrice($product->getPriceObject(\Buxus\Eshop\Price\ItemPriceDecorator\ItemDiscount::NON_DISCOUNT_PRICE_INCLUDING_VAT)) ?></div>
            <?php endif; ?>
            <?= $this->formatPrice($product->getPriceObject(\Buxus\Eshop\Price\PriceType::ITEM_PRICE_WITHOUT_VAT)) ?>
        </div>

        <div class="col-xs-12 col-sm-4 col-md-2 bt-cart-item-total-price item-total-price-formated">
            <?= $this->formatPrice($product->getFinalPriceObjectWithoutVat()) ?>
        </div>

        <div class="col-xs-12 col-sm-4 col-md-2">
            <?= $availability->render() ?>
        </div>

        <?php if ($product->canBeManuallyRemoved()): ?>
            <a href="<?= $this->cartCommand('product-remove', array('product_id' => $product->getTag())) ?>"
               title="<?= $this->str('cart', 'Odstrániť z košíka') ?>" class="btn btn-delete">
                <div class="cart-delete-button__icon__wrapper">
                    <img src="<?= \Buxus\Util\Url::asset('images/cart_bin.svg') ?>" alt="">
                </div>
            </a>
        <?php endif; ?>
    </td>
</tr>
