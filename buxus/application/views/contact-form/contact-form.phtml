<form class="form-horizontal" action="<?=$this->escape($this->form->getAction())?>" method="<?=$this->escape($this->form->getMethod())?>" enctype="application/x-www-form-urlencoded">

    <?php if ($this->form->hasErrorMessages()): ?>
        <div class="row">
            <div class="col-md-8 col-md-offset-4">
                <?=$this->formErrorsList($this->form->getMessages());?>
            </div>
        </div>
    <?php endif; ?>

    <div class="form-group <?php if ($this->form->hasElementErrors($this->form->first_name)): ?>has-error has-feedback<?php endif;?>">
        <label class="col-md-4 control-label" for="first_name">*<?= $this->form->first_name->getLabel() ?></label>
        <div class="col-md-5">
            <?php echo $this->form->first_name;?>
            <?php if ($this->form->hasElementErrors($this->form->first_name)): ?>
                <span class="glyphicon glyphicon-warning-sign form-control-feedback" aria-hidden="true"></span>
            <?php endif; ?>
        </div>
    </div>

    <div class="form-group <?php if ($this->form->hasElementErrors($this->form->surname)): ?>has-error has-feedback<?php endif;?>">
        <label class="col-md-4 control-label" for="surname">*<?= $this->form->surname->getLabel() ?></label>
        <div class="col-md-5">
            <?php echo $this->form->surname; ?>
            <?php if ($this->form->hasElementErrors($this->form->surname)): ?>
                <span class="glyphicon glyphicon-warning-sign form-control-feedback" aria-hidden="true"></span>
            <?php endif; ?>
        </div>
    </div>

    <div class="form-group <?php if ($this->form->hasElementErrors($this->form->email)): ?>has-error has-feedback<?php endif;?>">
        <label class="col-md-4 control-label" for="email">*<?= $this->form->email->getLabel() ?></label>
        <div class="col-md-5">
            <?php echo $this->form->email; ?>
            <?php if ($this->form->hasElementErrors($this->form->email)): ?>
                <span class="glyphicon glyphicon-warning-sign form-control-feedback" aria-hidden="true"></span>
            <?php endif; ?>
        </div>
    </div>

    <div class="form-group <?php if ($this->form->hasElementErrors($this->form->text)): ?>has-error has-feedback<?php endif;?>">
        <label class="col-md-4 control-label" for="text">*<?= $this->form->text->getLabel() ?></label>
        <div class="col-md-5">
            <?php echo $this->form->text; ?>
            <?php if ($this->form->hasElementErrors($this->form->text)): ?>
                <span class="glyphicon glyphicon-warning-sign form-control-feedback" aria-hidden="true"></span>
            <?php endif; ?>
        </div>
    </div>

    <?= $this->form->honeypot ?>

    <div class="form-group">
        <div class="col-md-8 col-md-offset-4">
            <?= $this->form->{$this->form->getFormTag()}; ?>
            <?= $this->form->user_full_name; ?>
            <?= $this->form->redirect; ?>
        </div>
    </div>

    <?= csrf_field() ?>
</form>
