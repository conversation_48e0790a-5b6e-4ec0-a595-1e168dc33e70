<?php

class Zend_View_Helper_UserTitle extends Zend_View_Helper_Abstract
{
    public function userTitle(\Buxus\WebUser\Contracts\WebUser $user)
    {
        if ($user->getCustomerType() == \Buxus\WebUser\WebUser::USER_TYPE_PERSON) {
            $name = htmlspecialchars(mb_strtoupper($user->getDisplayName()));
        } else {
            $name = htmlspecialchars(mb_strtoupper($user->getCompanyName()));
        }
        $html = $name . '&nbsp;(' . $user->getUsername() . ')';

        return $html;
    }
}
