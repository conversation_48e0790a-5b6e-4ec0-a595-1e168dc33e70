<?php
$shopping_cart = \ShoppingCart::get();
$checkout = $shopping_cart->getCheckout();
/**
 * @var $form \Eshop\ShoppingCart\Checkout\Process\Form\CartForm
 */
$form = $this->form;
$showVat = new \App\Eshop\Price\ShowVAT();
?>
<form id="<?= $form->getId() ?>" name="<?= $form->getName() ?>" class="<?= $form->getAttrib('class') ?>"
      enctype="<?= $form->getEncType() ?>" action="<?= $form->getAction() ?>" method="<?= $form->getMethod() ?>">

    <div class="col-xs-12">
        <div class="row cart-form">
            <div class="col-xs-12 col-sm-12 col-md-6 col-xl-8">
                <?= $shopping_cart->renderCartActions() ?>
                <?= $shopping_cart->renderPriceOffers() ?>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-xl-4 cart-form-submit">
                <div class="row cart-total-price">
                    <div class="cart-price-row col-xs-12">
                        <div class="row">
                            <div class="col-xs-8">
                                <span><?= $this->str('cart', 'Cena celkom bez DPH') ?></span>
                            </div>
                            <div class="col-xs-4 price cart-total-price-formated" id="cart-total-price-formated">
                                <h3 class="price-without-vat">
                                    <?= str_replace(' ', '&nbsp;', $this->formatPrice($shopping_cart->getFinalPriceObjectWithoutVat())) ?>
                                </h3>
                            </div>
                        </div>
                    </div>
                    <?php if ($showVat->shouldShowVAT()): ?>
                        <div class="cart-price-row col-xs-12">
                            <div class="row">
                                <div class="col-xs-8 with-vat">
                                    <span><?= $this->str('cart', 'Cena celkom vrátane DPH') ?></span>
                                </div>
                                <div class="col-xs-4 price cart-total-price-formated price-small"
                                     id="cart-total-price-formated">
                                    <span><?= str_replace(' ', '&nbsp;', $this->formatPrice($shopping_cart->getFinalPriceObject())) ?></span>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                    <div class="col-xs-12"> <?= $this->elements[$form->getFormTag()] ?></div>
                </div>
                <?= $this->elements['back'] ?>
            </div>
        </div>
    </div>

    <?= csrf_field() ?>
</form>

<?= $shopping_cart->renderCartSaveModal() ?>
