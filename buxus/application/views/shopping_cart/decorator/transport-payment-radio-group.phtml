<h2 class="cart_header"><?= $this->label ?></h2>
<?php if (is_array($this->messages) && count($this->messages)): ?>
    <?php foreach ($this->messages as $message): ?>
        <div class="alert alert-danger" role="alert">
            <span class="glyphicon glyphicon-exclamation-sign" aria-hidden="true"></span>
            <?= $message ?>
        </div>
    <?php endforeach; ?>
<?php endif; ?>
<ul class="bt-cart-tp-list">
    <?php foreach ($this->items as $item): ?>
        <?php
        /**
         * @var \Buxus\Eshop\Item\SpecialShopItemInterface|\Buxus\Eshop\Item\RadioSelectableRenderableInterface| \Buxus\Eshop\Item\AbstractSpecialShopItem $item
         */
        $shoppingCart = \ShoppingCart::get();
        $disabled = false;
        if ($item instanceof \Buxus\Eshop\Item\Delivery\GenericDeliveryType || $item instanceof \Buxus\Eshop\Item\Payment\GenericPaymentType) {
            $disabled = !$item->isEnabled($shoppingCart);
        }
        ?>
        <li class="row bt-cart-tp-item">
            <div class="col-xs-12">
            <input id="<?= $this->name ?>_<?= $item->getTag() ?>"
                   class="legacy-checkbox<?= $this->class ? " {$this->class}" : '' ?>"
                   type="radio"
                   name="<?= $this->name ?>"
                   value="<?= $item->getTag() ?>"
                <?= $disabled ? 'disabled="disabled" ' : '' ?>
                <?= (!$disabled && $item->getTag() == $this->value ? 'checked="checked"' : '') ?>
            />
            <label for="<?= $this->name ?>_<?= $item->getTag() ?>">
                <span class="bt-cart-tp-name"><?= $item->getName() ?><span
                        class="bt-cart-tp-price"><?= $item->getPriceLabel() ?></span></span>
                <span class="bt-cart-tp-description"><?= $item->getDescription() ?></span>
            </label>
            </div>

            <?php if ($item->getFormContent() != ''): ?>
                <div class="col-xs-12">
                    <?= $item->getFormContent() ?>
                </div>
            <?php endif; ?>
        </li>
    <?php endforeach; ?>
</ul>
