<?php

use Buxus\Util\PageIds;
use Buxus\Util\Url;
use App\Authentication\FakeAuthentication;
use App\Authentication\Form\RegistrationCountryForm;
use Buxus\Page\PageInterface;
use Illuminate\Http\Request;

class AuthenticationController extends \Authentication\Controllers\AuthenticationController
{
    public function logoutAction()
    {
        $fakeAuth = new FakeAuthentication();
        if (!empty($superuser = $fakeAuth->getSuperuser())) {
            \WebUserAuthentication::fakeLogin($superuser);
            $fakeAuth->forgetSuperuser();
        } else {
            \WebUserAuthentication::logout();
        }

        $this->redirect(Url::page(PageIds::getHomepage()));
    }

    public function registrationCountryAction(PageInterface $page, Request $request)
    {
        $form = new RegistrationCountryForm();

        $form->actionHandler($request);
        $this->view->page = $page;
        $elements = $form->getElements();
        $this->view->elements = $elements;
        $this->view->form = $form;
    }
}

