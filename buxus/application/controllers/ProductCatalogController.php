<?php

use App\Eshop\Product;
use Buxus\Page\PageInterface;
use Buxus\TemplateFunctions;
use Buxus\Util\PageIds;
use Buxus\Util\PageTypeID;
use App\Eshop\Catalog\Product\ProductList;
use Buxus\Util\Url;
use Eshop\Catalog\FS\Manager\Search;
use FS\Facades\FSInstanceResolver;
use FS\Facades\FSManager;
use FS\FSSearchInterface;
use App\Eshop\Catalog\Product\SearchProductList;
use App\FulltextSearch\Event\WebUserSearchEvent;

class ProductCatalogController extends \Eshop\Catalog\Controllers\ProductCatalogController
{
    public function productAction(PageInterface $page)
    {
        if (($this->view->product = ProductFactory::get($page->getPageId()))->isOrderable()) {
            $this->setPage();

            $this->render('product/default');
        } else {
            return redirect(\Buxus\Util\Url::page(\Buxus\Util\PageIds::getHomepage()));
        }
    }

    public function productListAction(PageInterface $page)
    {
        $this->setPage();

        $subcategories = [];
        if (\BuxusMVC::pageId() != PageIds::getNezaradene()) {
            $subpages = TemplateFunctions::SelectOneTypeChildren(\BuxusMVC::pageId(), [
                PageTypeID::ESHOP_CATEGORY_ID(),
                PageTypeID::ESHOP_SUBCATEGORY_ID(),
                PageTypeID::ESHOP_SUBCATEGORY_3_ID(),
            ]);

            foreach ($subpages as $subpage) {
                $subcategories[] = \PageFactory::get($subpage['page_id']);
            }
        }
        $this->view->subcategories = $subcategories;


        $product_list = new ProductList($this->_request);
        $this->view->list = $product_list;

    }

    public function productSearchAction()
    {
        $term = trim($this->getRequest()->getParam('term'));

        if (empty($term)) {
            $this->redirect(Url::page(PageIds::getHomepage()));
        }

        if (\WebUserAuthentication::isAuthenticated()) {
            $user = \WebUserAuthentication::getUser();
            $searchEvent = new WebUserSearchEvent($user, $term);
            BuxusEvent::fire($searchEvent);
        }

        $this->setPage();
        $this->view->subcategories = [];

        if (config('krabica.modules.faceted_search')) {
            /**
             * @var FSSearchInterface $fs
             */
            $fs = FSInstanceResolver::getChecked();

            $manager = new Search($term);

            FSManager::process($fs, $this->view->page, $manager);

            $fs->setPrivateProperty('meta_title', $this->view->headTitleCustom()->getTitle());

            $response = $fs->processRequest(app('request'));
            if (is_object($response)) {
                return $response;
            }

            $this->view->data = $fs->search();
            $this->view->fs = $fs;

            $this->view->jsModules()->addLayer('fs');

            $this->render('productListFs');
        } else {
            $product_list = new SearchProductList($term, $this->getRequest());
            $page_properties = \PageFactory::get(\BuxusMVC::pageId());

            $this->view->page_properties = $page_properties;
            $this->view->list = $product_list;

            $this->render('searchProductList');
        }
    }
}
