<?php

use Buxus\Eshop\Contracts\DeliveryTypeFactory;
use Buxus\Eshop\Contracts\PaymentTypeFactory;
use Buxus\Eshop\Order\OrderInterface;
use Buxus\Eshop\Order\OrderItemInterface;

require_once 'includes/functions.php';
require_once 'includes/validate.php';
require_once 'includes/uif_functions.php';
require_once 'includes/functions/tf_tolowerwithdiacritic.php';
require_once 'includes/local_property_functions.php';
require_once 'lib/authenticate/authentication.php';
require_once 'includes/classes/importPage.inc.php';
require_once 'lib/shop/shop.inc.php';

\Buxus\Core\Facades\BuxusMenu::setActiveContext('eshop');

$have_permission = false;
$user_id = \Buxus::userId();
$order_id = (int)(isset($_REQUEST['order_id']) ? $_REQUEST['order_id'] : 0);

$sql_qry = "SELECT admin_right,eshop_right ";
$sql_qry .= " FROM tblUsers  WHERE user_id = $user_id";
$data_row = BuxusDB::get()->fetchRow($sql_qry, array(':user_id' => $user_id));
if ($data_row) {
    if (trim($data_row['eshop_right']) == \Buxus\Core\Constants::C_True_Char) {
        $have_permission = true;
    }
    if (trim($data_row['admin_right']) == \Buxus\Core\Constants::C_True_Char) {
        $have_permission = true;
    }
}

echo "

<style>
    .cart-availability-tag {
        border-radius: 15px;
        padding: 0 10px;
        align-items: center;
        white-space: nowrap;
        margin: 5px;
        font-size: 14px;
        font-weight: 700;
        width: fit-content;
        display: flex;
    }

    .cart-availability-tag.cart-availability-tag-stock {
        color: #007243;
    }

    .cart-availability-tag.cart-availability-tag-supplier {
        color: #ff8400;
    }

    .cart-availability-tag.cart-availability-tag-unavailable {
        color: #f00;
    }

    .cart-availability-tag .availability-state-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 5px;
    }

    .cart-availability-tag .availability-state-dot.availability-state-stock {
        background: #00af50;
    }

    .cart-availability-tag .availability-state-dot.availability-state-supplier {
        background: #f98f17;
    }

    .cart-availability-tag .availability-state-dot.availability-state-unavailable {
        background: #f91717;
    }

</style>
";

function formatCurrency($currency)
{
    $mapping = config('buxus_eshop.price_viewer.currency_symbols');

    if (isset($mapping[$currency])) {
        return $mapping[$currency];
    }

    return $currency;
}

function process_price_display($price)
{
    return round($price, 2);
}

function getMargin(\Buxus\Eshop\Order\OrderItemInterface $orderItem)
{
    $initialPrice = $orderItem->getOption('external_initial_price_without_vat');
    if (empty($initialPrice)) {
        $initialPrice = $orderItem->getOption('initial_price_without_vat');
    }
    if (empty($initialPrice)) {
        return 0;
    } else {
        $initialPrice *= $orderItem->getAmount();
    }

    return round($orderItem->getTotalPriceWithoutVAT(), 2) - $initialPrice;
}

function getPieceMargin(\Buxus\Eshop\Order\OrderItemInterface $orderItem)
{
    $initialPrice = $orderItem->getOption('external_initial_price_without_vat');
    if (empty($initialPrice)) {
        $initialPrice = $orderItem->getOption('initial_price_without_vat');
    }
    if (empty($initialPrice)) {
        return 0;
    }

    return round($orderItem->getItemPriceWithoutVAT(), 2) - $initialPrice;
}

function getMarginPercent(\Buxus\Eshop\Order\OrderItemInterface $orderItem)
{
    $initialPrice = $orderItem->getOption('external_initial_price_without_vat');
    if (empty($initialPrice)) {
        $initialPrice = $orderItem->getOption('initial_price_without_vat');
    }
    if (empty($initialPrice)) {
        return 0;
    }

    return round(($orderItem->getItemPriceWithoutVAT() / $initialPrice - 1) * 100, 2);
}

function getPieceItemInitialPrice(OrderItemInterface $orderItem)
{
    $initialPrice = $orderItem->getOption('external_initial_price_without_vat');
    if (empty($initialPrice)) {
        $initialPrice = $orderItem->getOption('initial_price_without_vat');
    }
    if (empty($initialPrice)) {
        return $orderItem->getTotalPriceWithoutVAT();
    }

    return $initialPrice;
}

function getItemInitialPrice(OrderItemInterface $orderItem)
{
    $initialPrice = $orderItem->getOption('external_initial_price_without_vat') * $orderItem->getAmount();
    if (empty($initialPrice)) {
        $initialPrice = $orderItem->getOption('initial_price_without_vat') * $orderItem->getAmount();
    }
    if (empty($initialPrice)) {
        return $orderItem->getTotalPriceWithoutVAT();
    }

    return $initialPrice;
}

function getOrderInitialPrices(OrderInterface $order)
{
    $initialPrice = 0;
    foreach ($order->getItems() as $orderItem) {
        $itemInitialPrice = ($orderItem->getOption('external_initial_price_without_vat') * $orderItem->getAmount());
        if (empty($itemInitialPrice)) {
            $itemInitialPrice = ($orderItem->getOption('initial_price_without_vat') * $orderItem->getAmount());
        }
        if (empty($itemInitialPrice)) {
            $itemInitialPrice = ($orderItem->getItemPriceWithoutVAT() * $orderItem->getAmount());
        }

        $initialPrice += $itemInitialPrice;
    }

    return $initialPrice;
}

function getOrderMargin(OrderInterface $order)
{
    if (!$order instanceof \App\Eshop\Order\RinopartsOrder) {
        return '';
    }

    return $order->getItemsPriceWithoutVat() - getOrderInitialPrices($order);
}

function getOrderMarginInEur(OrderInterface $order)
{
    if (!$order instanceof \App\Eshop\Order\RinopartsOrder) {
        return '';
    }

    $orderMargin = getOrderMargin($order);

    if ($order->getCurrency() == 'CZK') {
        if (!empty($order->getExchangeRate())) {
            return round($orderMargin / $order->getExchangeRate(), 2);
        }

        return round($orderMargin / config('buxus_eshop.exchange_rate_eur_czk'), 2);
    }

    return round($orderMargin, 2);
}

function getOrderPercentMargin(OrderInterface $order)
{
    if (!$order instanceof \App\Eshop\Order\RinopartsOrder) {
        return '';
    }

    $percentMargin = ($order->getItemsPriceWithoutVat() / getOrderInitialPrices($order) - 1) * 100;

    return round($percentMargin, 2);
}

$content = '';
if ($have_permission) {
    if (class_exists('ShopConfiguration', false)) {
        $configuration = new ShopConfiguration();
    } else {
        $configuration = new ShopConfigurationBasic();
    }

    /**
     * @var \Buxus\Eshop\Order\OrderDetailExtensionsManager $manager
     */
    $manager = app(\Buxus\Eshop\Order\OrderDetailExtensionsManager::class);

    $order_detail_extensions_objects = $manager->getOrderDetailExtensions();

    $order_data = $configuration->getOrderData();

    $order = new ShopOrder($configuration, $order_id);

    $order_types = $order->getOrderTypes();

    $orderNewObject = \OrderFactory::getById($order->getOrderId());

    if (!empty($_GET['ajax_call'])) {

        switch ($_GET['ajax_call']) {
            case 'form':
                $content = '
						<div class="item-form" id="item-form">
							<table>
								<tr>
									<th>
										Typ položky:
									</th>

									<td>';


                if (count($order_types)) {
                    foreach ($order_types as $order_type) {
                        $content .= '<input type="radio" name="radio_items_type" id="items_type_' . $order_type['page_type_id'] . '" value="' . $order_type['page_type_id'] . '" onclick="loadList(' . $order_type['page_type_id'] . ', 0);" /> ' . $order_type['page_type_name'];
                    }
                }

                $content .= '
									</td>
								</tr>
								<tr>
									<th>
										ID alebo názov:
									</th>

									<td>
										<input type="text" class="textfield" style="width: 300px;" id="order_form_query2" onkeyup="loadList(active_type, 500);"/>
										<input class="button" type="button" id="save_new_item" value="Uložiť novú položku" onclick="createNewItem();" />
									</td>
								</tr>
								<tr>
									<th style="vertical-align: top;">
										Zoznam položiek:
										<img src="../../../system/images/wait.gif" alt="Čakajte, načítava sa zoznam" title="Čakajte, načítava sa zoznam" id="wait" />
									</th>

									<td id="items_list_holder">
										&nbsp;
									</td>
								</tr>
								<tr>
									<th>
										&nbsp;
									</th>

									<td style="padding-top: 6px;">
										<input class="button" type="submit" value="Potvrdiť" onclick="closeItemEdit(); return false;" />
									</td>
								</tr>
							</table>
						</div>';


                header("Content-type: text/css; charset=utf-8");
                break;

            case 'items_list':

                $value_keys = explode("&", $GLOBALS['HTTP_RAW_POST_DATA']);
                $_MYPOST = array();
                foreach ($value_keys as $value_key) {
                    list($key, $value) = explode("=", $value_key);
                    $_MYPOST[$key] = unescape($value);
                }

                $query = "
						SELECT
							page_id, page_name
						FROM
							tblPages
						WHERE
							page_type_id = :page_type_id  AND page_type_id != ''
								AND
							(
								page_name LIKE :page_name
									OR
								page_id LIKE :page_id
							)
							AND
								page_state_id = '1'
						ORDER BY
							page_id";

                $items = BuxusDB::get()->fetchAll($query, array(
                    ':page_type_id' => $_MYPOST['items_type'],
                    ':page_name' => '%' . $_MYPOST['query'] . '%',
                    ':page_id' => $_MYPOST['query'] . '%'
                ));

                $content = '<select size="10" id="items_list" onchange="storeActiveSettings();" ondblclick="storeActiveSettings(); closeItemEdit();">';

                $selected = false;
                $items_temp = $items;
                $items = array();

                foreach ($items_temp as $item) {

                    if ($_MYPOST['selected_item_id'] == $item['page_id']) {
                        $item['selected'] = ' selected="selected"';
                        $selected = true;
                    } else {
                        $item['selected'] = '';
                    }

                    $items[] = $item;
                }

                if (!$selected && count($items)) {
                    $items[0]['selected'] = ' selected="selected"';
                }

                foreach ($items as $item) {
                    $content .= '
							<option value="' . $item['page_id'] . '"' . $item['selected'] . '>' . $item['page_id'] . ' - ' . $item['page_name'] . '</option>';
                }
                $content .= '</select>';

                header("Content-type: text/css; charset=utf-8");
                break;

            case 'save_item':

                $value_keys = explode("&", $GLOBALS['HTTP_RAW_POST_DATA']);
                $_MYPOST = array();
                foreach ($value_keys as $value_key) {
                    list($key, $value) = explode("=", $value_key);
                    $_MYPOST[$key] = unescape($value);
                }

                setUpObjectFactory();

                //references
                $pf = new PropertyFactory();
                $fu = new FileUploader($pf);

                //IMPORTANT - this must be assigned via reference! ( & )
                $page = new ImportNewPage($order->getCustomType(), C_CUSTOM_ORDER_ITEMS_ROOT_PAGE, $pf, $fu, null, $user_id);

                //set the page name
                $data2['page_name'] = $_MYPOST['name'];

                //set the page state (optional, default is passive)
                $data2['page_state_id'] = C_active_page_state_id;

                //update the data
                $page->doTagDataExchange($data2);

                //save the page (now the trigger is run!)
                $page->savePage();

                $content = 'OK';

                header("Content-type: text/css; charset=utf-8");
                break;
        }

        header("Content-length: " . strlen($content));
        header('Connection: close');

        echo $content;
        exit;
    }

    $active_panel = Session::get('buxus.active_panel', 'customer');

    $tabs = array();
    $tabs['customer'] = array('caption' => __bx('eshop::shop_orders_details.Customer'));
    $tabs['items'] = array('caption' => __bx('eshop::shop_orders_details.ShoppingItems'));
    $tabs['documents'] = array('caption' => __bx('eshop::shop_orders_details.Documents'));
    $tabs['note'] = array('caption' => __bx('eshop::shop_orders_details.Note'));
    $tabs['invoices'] = array('caption' => __bx('eshop::shop_orders_details.Invoices'));
    $tabs['notification'] = array('caption' => __bx('eshop::shop_orders_details.EmailNotification'));
    $tabs['logs'] = array('caption' => __bx('eshop::shop_orders_details.ChangesLog'));
    $tabs['email_logs'] = array('caption' => __bx('eshop::shop_orders_details.EmailLog'));
    $custom_items_list = $custom_items = array();

    if (!empty($order_id)) {
        $stmt = "SELECT variable_symbol, order_datetime, order_web_user_id, payment_datetime, payment_web_user_id, WU1.username AS order_user, WU2.username AS payment_user, ";
        $stmt .= "title, OP.first_name AS first_name, OP.surname AS surname, OP.company_name, street, city, zip, ico, drc, dic, phone, fax, email, OP.inv_company_name, inv_street, inv_city, inv_zip, ";
        $stmt .= "account_number, bank_number,note, ";
        $stmt .= "post_price, post_price_vat, paid_price, order_type, payment_type, delivery_type, currency, order_state, is_active, invoice_number, OP.site ";
        $stmt .= " FROM (tblShopOrders AS OP ";
        $stmt .= " LEFT OUTER JOIN tblWebUsers WU1 ON WU1.user_id = order_web_user_id) ";
        $stmt .= " LEFT OUTER JOIN tblWebUsers WU2 ON WU2.user_id = payment_web_user_id ";
        $stmt .= " WHERE order_id = :order_id ";
        $default_items = BuxusDB::get()->fetchRow($stmt, array(':order_id' => $order_id));
        if (!$default_items) {
            header("Location: shop_orders_list.php");
            exit;
        }
        $variable_symbol = $default_items['variable_symbol'];
        $order_datetime = $default_items['order_datetime'];
        $order_web_user_id = $default_items['order_web_user_id'];
        $payment_datetime = $default_items['payment_datetime'];
        $payment_web_user_id = $default_items['payment_web_user_id'];
        $order_user = $default_items['order_user'];
        $payment_user = $default_items['payment_user'];
        $paid_price = $default_items['paid_price'];
        $order_state = $default_items['order_state'];
        $is_active = $default_items['is_active'];
        $invoice_number = $default_items['invoice_number'];
        $site = $default_items['site'];

        if ($is_active != 'T') {
            DisplayHtmlHeader(__bx('eshop::shop_orders_details.EShopPageTitle'), C_MenuType_EShop);
            echo HTML_PageTitle(__bx('eshop::shop_orders_details.PageTitle'), __bx('eshop::shop_orders_details.PageAnotation'));
            echo '<div style="margin-top:10px; color:red; font-weight:bold; ">' . __bx('eshop::shop_orders_details.Order_Deleted') . '</div>';
            DisplayHtmlFooter();
            exit;
        }

        $sql_query = "SELECT OPO.order_tag, OPO.order_value,  SE.entity_name FROM tblShopOrderOptions OPO
        LEFT OUTER JOIN tblSimpleEntities SE ON TRIM(OPO.order_tag)=TRIM(SE.entity_tag) AND SE.entity_type_tag = 'order_option'
        WHERE OPO.order_id = :order_id  ORDER BY OPO.sort_number";

        $res = BuxusDB::get()->fetchAll($sql_query, array(':order_id' => $order_id));

        foreach ($res as $item) {
            $custom_items_list[$item['order_tag']] = $item;
        }

        $orderItems = $order->getItemsFromStorage();
    }

    // main items
    $main_parts = $customer_parts = $datas = array();
    $main_data = $configuration->getOrderData('main');

    $main_parts[] = array(__bx('eshop::shop_orders_details.OrderSource'), (isset($site) && !empty($site) ? \BuxusSite::getSiteHostname($site) : ''));
    $main_parts[] = array(__bx('eshop::shop_orders_details.VariableSymbol'), $variable_symbol);
    $main_parts[] = array(__bx('eshop::shop_orders_details.InvoiceID'), $invoice_number);

    $shop_order_states = \Buxus\Eshop\Facades\OrderStateManager::get();
    if (is_a($shop_order_states->getStateById($order_state), 'Buxus\Eshop\OrderState\GenericShopOrderState')) {
        $order_state_object = $shop_order_states->getStateById($order_state);
        $main_parts[] = array(
            __bx('eshop::shop_orders_details.OrderState'),
            $order_state_object->getLayout($shop_order_states->getAllStates(), 'order_state', $order_state, $order_id, 'setChange()')
        );
    } elseif (is_a($shop_order_states->getStateById(0), 'Buxus\Eshop\OrderState\GenericShopOrderState')) {
        blog('ORDERS: Warning, state ID [' . $order_state . '] is not defined in state configuration! Default state will be used.');

        $order_state = 0;
        $order_state_object = $shop_order_states->getStateById(0);
        $main_parts[] = array(
            __bx('eshop::shop_orders_details.OrderState'),
            $order_state_object->getLayout($shop_order_states->getAllStates(), 'order_state', $order_state, $order_id, 'setChange()')
        );
    } else {
        $main_parts[] = array(__bx('eshop::shop_orders_details.OrderState'), '<em>Error in state configuration.</em>');
        blog('ORDERS: Error in state configuration.!');
    }

    $main_parts[] = array(__bx('eshop::shop_orders_details.Order_date'), $order_datetime);
    // XXX FIX: #2757 Bad overwrite final price
    //$main_parts[] = array(__bx('eshop::shop_orders_details.OrderPrice'), $paid_price);
    $main_parts[] = array('Cena bez DPH',
        HTML_TextField('paid_price_without_vat', process_price_display($orderNewObject->getTotalPriceWithoutVat()), C_HTML_TextFieldSize, '', '', false, -1, null, 'readonly="readonly"')
    );
    $main_parts[] = array(
        __bx('eshop::shop_orders_details.OrderPrice'),
        HTML_TextField('paid_price', process_price_display($paid_price), C_HTML_TextFieldSize, '', '', false, -1, null, 'onkeyup="setChange();"')
    );


    \BuxusSite::pushSite($site);

    $payments = app(PaymentTypeFactory::class)->getPaymentTypes();
    $transports = app(DeliveryTypeFactory::class)->getAllDeliveryTypes();

    \BuxusSite::popSite();

    $payments_list = array();
    foreach ($payments as $tag => $payment) {
        /**
         * @var \Buxus\Eshop\Item\Payment\GenericPaymentType $payment
         */
        $payments_list[] = array(
            'tag' => $payment->getTag(),
            'name' => $payment->getName(),
        );
    }

    $transport_list = array();
    foreach ($transports as $tag => $transport) {
        /**
         * @var \Buxus\Eshop\Item\Delivery\GenericDeliveryType $transport
         */
        $transport_list[] = array(
            'tag' => $transport->getTag(),
            'name' => $transport->getName(),
        );
    }

    foreach ($main_data as $key => $value) {
        // if it is payment or delivery type, we show the page title
        if (in_array($key, array('payment_type', 'delivery_type'))) {
            if ($key == 'payment_type') {
                $main_parts[] = array($value, HTML_Select($key, $payments_list, 'tag', 'name', $default_items[$key], null, 'setChange()', ' class="form-control"'));
            }
            if ($key == 'delivery_type') {
                $main_parts[] = array($value, HTML_Select($key, $transport_list, 'tag', 'name', $default_items[$key], null, 'setChange()', ' class="form-control"'));
            }
        } else {
            $main_parts[] = [
                $value,
                HTML_TextField($key, isset($default_items[$key]) ? $default_items[$key] : (isset($custom_items_list[$key]) ? $custom_items_list[$key]['order_value'] : ''), C_HTML_TextFieldSize, '', '', false, -1, null, 'onkeyup="setChange();"')
            ];
        }
        $datas[] = array('name' => $key, 'value' => $default_items[$key]);

    }

    $main_parts[] = array(
        __bx('eshop::shop_orders_details.Payment_date'),
        '<div class="row">' .
        '<div class="col-xs-6">' .

        HTML_TextField('payment_datetime', $payment_datetime, C_HTML_TextFieldSize, '', '', false, -1, null, 'onkeyup="setChange();"')
        . '</div>'
        . '<div class="col-xs-6">'
        . (empty($payment_datetime) ? '&nbsp;' . HTML_Button('set_order_as_paid', __bx('eshop::shop_orders_details.oznacit_ako_zaplatene')) : '')
        . '</div>'
    );

    $main_parts[] = array(__bx('eshop::shop_orders_details.Payment_user'), HTML_Link("web_user_details.php?web_user_id=$payment_web_user_id", $payment_user));

    $main_parts[] = array('Celkový zisk (€)', getOrderMarginInEur($orderNewObject) . ' €');
    $main_parts[] = array('Celkový zisk (%)', getOrderPercentMargin($orderNewObject) . ' %');
    $main_parts[] = array('Vytvorené', !empty(trim($orderNewObject->getData('superuser_name'))) ? '<strong>' . $orderNewObject->getData('superuser_name') . '</strong>' : ($orderNewObject->getInvoiceCompanyName()));


    $custom_notifications = config('buxus_eshop.notification_emails', []);

    if (count($custom_notifications)) {
        $_notfis = [
            [
                'tag' => '',
                'name' => '<Vyberte notifikáciu>',
            ]
        ];
        foreach ($custom_notifications as $tag => $name) {
            if (is_numeric($tag)) {
                // try to load subpages from tagged page
                $page_id = \Buxus\Util\PageIds::getPageId($name, false);
                if (empty($page_id)) {
                    continue;
                }

                $subemails = \Buxus\TemplateFunctions::SelectOneTypeChildren($page_id, \Buxus\Util\PageTypeID::E_MAIL_ID());
                foreach ($subemails as $row) {
                    try {
                        $p = \PageFactory::get($row['page_id']);
                        $_notfis[] = [
                            'tag' => $p->getPageId(),
                            'name' => $p->getPageName(),
                        ];
                    } catch (Exception $e) {
                    }
                }
                continue;
            }


            $_notfis[] = [
                'tag' => $tag,
                'name' => $name,
            ];
        }

        $main_parts[] = array(
            'Notifikácia',
            HTML_Select('custom_notification', $_notfis, 'tag', 'name', '', null, 'loadNotificationForNotification(); setChange()')
        );
    }

    foreach ($order_detail_extensions_objects as $detail_extensions_object) {
        $detail_extensions_object->processMainPart($order_id, $main_parts);
    }

    // customer items
    if (empty($order_web_user_id)) {
        $customer_parts[] = array(__bx('eshop::shop_orders_details.Order_user'), '&nbsp;');
    } else {
        $webuser_info = GetUserInfo((int)$order_web_user_id);

        $personalData = \GDPR::processPersonalData('webuser', array_merge($webuser_info[0], $webuser_info[1]));
        if (!is_null($personalData)) {
            \GDPR::view(
                'webuser',
                $personalData->getCustomerIdentifier(),
                $personalData->getPersonDataItems()
            );
        }

        $webuser_info_text = $webuser_info[0]['first_name'] . ' ' . $webuser_info[0]['surname'];
        if (!empty($webuser_info[0]['e_mail'])) {
            $webuser_info_text .= ' <' . $webuser_info[0]['e_mail'] . '>';
        }

        $webuser_info_card = '<div style="background:#2B528A; color: white; padding:2px; font-weight: bold;">' . $webuser_info_text . '</div>';
        foreach ($webuser_info[1] as $key => $value) {
            $webuser_info_card .= '<strong>' . $key . ': </strong>' . $value . '<br />';
        }
        //$webuser_info_card = htmlspecialchars($webuser_info_card);

        $webuser_info_card .= HTML_HiddenField('order_web_user_id', (int)$order_web_user_id);

        /**
         * @var \Buxus\WebUser\Labels\WebUserLabelsManager $user_labels_manager
         */
        $user_labels_manager = app('buxus:webuser:labels-manager');
        $labels = $user_labels_manager->getLabels();

        $labels_data = array();

        foreach ($labels as $tag => $label) {
            $labels_data[] = '{id: "' . $tag . '", name: "' . $label->getName() . '"}';
        }
        $actual_labels = array();
        /**
         * @var \Buxus\WebUser\Labels\WebUserLabelsManager $user_labels_manager
         */
        $user_labels_manager = app('buxus:webuser:labels-manager');
        $user_labels = $user_labels_manager->getUserLabels((int)$order_web_user_id);
        if (!is_null($user_labels)) {
            foreach ($user_labels as $tag => $label) {
                $actual_labels[$tag] = true;
            }
        }

        $labels_html = '
<select name="user_labels[]" id="user-labels" onchange="setChange();" data-placeholder="' . htmlspecialchars(__bx('eshop::shop_orders_details.Choose_Labels')) . '" style="width:350px;" multiple class="chzn-select">
                <option value=""></option>
';

        foreach ($labels as $tag => $label) {
            $labels_html .= '<option value="' . htmlspecialchars($tag) . '"' . (isset($actual_labels[$tag]) ? ' selected="selected"' : '') . '>' . htmlspecialchars($label->getName()) . '</option>';
        }

        $labels_html .= '
              </select>
        ';

        $customer_parts[] = array(
            __bx('eshop::shop_orders_details.Order_user'),
            '<span onMouseover="ddrivetip(\'' . addslashes(htmlspecialchars($webuser_info_card)) . '\')"; onMouseout="hideddrivetip()" style="cursor:help;"><img src="' . config('buxus_core.base_url') . 'system/images/webuser.gif" vspace="0" style="vertical-align:bottom;" alt="' . $webuser_info_text . '" /> ' . HTML_Link(config('buxus_core.base_url') . "lib/authenticate/uif/web_user_details.php?web_user_id=$order_web_user_id",
                $order_user) . '</span>' . $labels_html
        );
        //$customer_parts[] = array(__bx('eshop::shop_orders_details.Order_user'), $labels_html);
    }

    $customer_data = $configuration->getOrderData('customer');

    foreach ($customer_data as $key => $value) {
        if ($key != 'note') {
            $customer_parts[] = array(
                $value,
                HTML_TextField($key, $default_items[$key], C_HTML_TextFieldSize, '', '', false, -1, null, 'onkeyup="setChange();"')
            );
            $datas[] = array('name' => $key, 'value' => $default_items[$key]);
        }

        if (!array_key_exists($key, $default_items) && array_key_exists($key, $custom_items_list)) {
            $title = $value;
            $customer_parts[] = array($title, $value);
            $datas[] = array('name' => $custom_items_list[$key]['order_tag'], 'value' => $custom_items_list[$key]['order_value']);
        }
    }

    $gdprData = $default_items;
    foreach ($custom_items_list as $key => $item) {
        if (isset($item['order_value'])) {
            $gdprData[$key] = $item['order_value'];
        }
    }

    $personalData = \GDPR::processPersonalData('eshop', $gdprData);
    if (!is_null($personalData)) {
        \GDPR::view(
            'eshop',
            $personalData->getCustomerIdentifier(),
            $personalData->getPersonDataItems()
        );
    }

    $customer_data_tags = array_keys($customer_data);
    $customer_data_tags[] = 'payment_price';
    $customer_data_tags[] = 'delivery_price';

    ksort($custom_items_list);
    foreach ($custom_items_list as $tag => $custom_item) {
        if (!in_array($tag, $customer_data_tags)) {
            $title = ($custom_item['entity_name'] ? $custom_item['entity_name'] : $custom_item['order_tag']);
            $value = HTML_TextField($custom_item['order_tag'], $custom_item['order_value'], C_HTML_TextFieldSize, '', '', false, -1, null,
                'onkeyup="setChange();"');

            $custom_items[] = array($title, $value);
            $datas[] = array('name' => $custom_item['order_tag'], 'value' => $custom_item['order_value']);
        }
    }

    foreach ($order_detail_extensions_objects as $detail_extensions_object) {
        $detail_extensions_object->processData($order_id, $customer_parts, $custom_items);
    }

    ##################################################################################################
    ## display #######################################################################################
    ##################################################################################################
    $order_object = \OrderFactory::getById($order_id);

    // customer tab content

    $tabs['customer']['content'] = displayListInTwoColumns(__bx('eshop::shop_orders_details.CustomerInformation'),
            $customer_parts) . displayListInTwoColumns(__bx('eshop::shop_orders_details.CustomerInformationExtended'), $custom_items);
    $tabs['customer']['content'] .= HTML_TableBegin(__bx('eshop::shop_orders_details.Note'), 4);
    $tabs['customer']['content'] .= HTML_RowBegin();
    $tabs['customer']['content'] .= HTML_HeaderCell(__bx('eshop::shop_orders_details.Note'), 1, 1, 'left', '20%');
    $tabs['customer']['content'] .= HTML_ValueCell(HTML_Textarea('note', $default_items['note'], C_HTML_TextAreaRows, 2 * C_HTML_TextAreaCols, '',
            'onkeyup="setChange();" style="width:97%"') . '&nbsp;');
    $tabs['customer']['content'] .= HTML_RowEnd();
    $tabs['customer']['content'] .= HTML_TableEnd();
    // items tab content


    $tabs['items']['content'] = '
			<input type="hidden" name="delete_list" id="delete_list" value="" />';


    $tabs['items']['content'] .= HTML_TableBegin('', 8, 'table table-bordered buxus-table', 'productsTable') .
        HTML_RowBegin();

    /**
     * @var \Buxus\Eshop\ShopUI\OrderDisplayConfig $detailItemsConfig
     */
    $detailItemsConfig = app('buxus:eshop:uif:items-config');

    foreach ($detailItemsConfig->getDetailItemColumnNames() as $column) {
        $tabs['items']['content'] .= HTML_HeaderCell($column, 1, 1, '');
    }

    $tabs['items']['content'] .=
        HTML_HeaderCell(__bx('eshop::shop_orders_details.Discount'), 1, 1, '', 60, 'style="display: none;"') .
        HTML_HeaderCell('Nákupná cena bez DPH / ks',
            1, 1, '', 110) .
        HTML_HeaderCell('Nákupná cena bez DPH',
            1, 1, '', 110) .
        HTML_HeaderCell(__bx('eshop::shop_orders_details.PricePerItem') . ' ' . (__bx('eshop::shop_orders_details.Without')) . ' ' . __bx('eshop::shop_orders_details.Vat'),
            1, 1, '', 110) .

        HTML_HeaderCell(__bx('eshop::shop_orders_details.NrOfItems'), 1, 1, '', 60) .

        HTML_HeaderCell(__bx('eshop::shop_orders_details.PricePerItem') . ' ' . (__bx('eshop::shop_orders_details.With')) . ' ' . __bx('eshop::shop_orders_details.Vat'),
            1, 1, '', 110) .
        HTML_HeaderCell(__bx('eshop::shop_orders_details.VatPerItem'), 1, 1, '', 60);
    if (is_array(ShopItemConfiguration::getStates())) { // There are not any states
        $tabs['items']['content'] .= HTML_HeaderCell(__bx('eshop::shop_orders_details.ItemState'), 1, 1, '', 50);
    }
    $tabs['items']['content'] .=
        HTML_HeaderCell(__bx('eshop::shop_orders_details.DeliveredItems'), 1, 1, '', 50) .
        HTML_HeaderCell('Marža (' . formatCurrency($orderNewObject->getCurrency()) . '/ks)', 1, 1, '', 50) .
        HTML_HeaderCell('Marža (' . formatCurrency($orderNewObject->getCurrency()) . ')', 1, 1, '', 50) .
        HTML_HeaderCell('Marža (%)', 1, 1, '', 50) .
        HTML_HeaderCell('&nbsp;', 1, 1) .
        HTML_RowEnd();

    $newOrderItems = $order_object->getItems();


    for ($i = 0; $i < count($orderItems); $i++) {
        $actions_content = '<div>' .
            HTML_HiddenField('order_item_id_' . $i, $orderItems[$i]['order_item_id'], 'order_item_id_' . $i) .
            HTML_HiddenField('page_id_' . $i, $orderItems[$i]['page_id']) .
            HTML_HiddenField('item_custom_name_' . $i, $orderItems[$i]['order_item_name']) .
            HTML_HiddenField('item_price_type_' . $i, $orderItems[$i]['price_type']);

        // Add item options
        if ((isset($orderItems[$i]['item_options'])) && (is_array($orderItems[$i]['item_options']))) { // There are some options
            foreach ($orderItems[$i]['item_options'] as $option_tag => $option_value) {
                $actions_content .= HTML_HiddenField('item_options_' . $i . '[' . $option_tag . ']', $option_value);
            }
        }

        $actions_content .= '<div class="delete"><a href="javascript:void(0);" onclick="deleteItem(this, ' . $i . ');">' . TF_ToLowerWithDiacritic(__bx('eshop::shop_orders_details.Delete')) . '</a></div></div>';

        $price_type_image = 'edit.gif';
        $price_type_code = '';

        $newOrderItemObject = null;
        foreach ($newOrderItems as $item) {
            if ($item->getOrderItemId() == $orderItems[$i]['order_item_id']) {
                $newOrderItemObject = $item;
                break;
            }
        }

        $rowStyle = $detailItemsConfig->getOrderItemRowStyle($order_object, $newOrderItemObject);

        $tabs['items']['content'] .= HTML_RowBegin('row_' . $i, $rowStyle ?? '');

        /** configurable properties of product **/
        if ($orderItems[$i]['page_id']) {
            try {
                $product = \PageFactory::get($orderItems[$i]['page_id']);
            } catch (Exception $e) {
                $product = null;
            }

            foreach ($detailItemsConfig->getDetailItemColumnTags() as $tag) {
                if ($newOrderItemObject) {
                    $value = $detailItemsConfig->getDetailItemValue($tag, $order_object, $newOrderItemObject);
                    if ($detailItemsConfig->isLinkable($tag)) {
                        $tabs['items']['content'] .= HTML_ValueCell(HTML_Link('../../../system/page_details.php?page_id=' . $orderItems[$i]['page_id'],
                            stripslashes($value)), 1, 1, 'item-name-cell');
                    } else {
                        $tabs['items']['content'] .= HTML_ValueCell($value, 1, 1, 'left ' . $tag);
                    }
                }
            }
        }

        $discount = '';
//        $real_price = $product->getValue(ShopOrderDetailsConfig::getDefaultPriceTag(), null);
//        if (!is_null($real_price) && $real_price != 0) {
//            $discount = round(100 - 100 * $orderItems[$i]['price_per_item'] / $real_price, 3);
//            if ($discount == 0) {
//                $discount = '';
//            }
//        }


        $tabs['items']['content'] .= HTML_ValueCell(HTML_TextField('discount_' . $i, $discount, 4, '', '', false, -1, null,
            'id="discount_per_item_' . $i . '"'),
            1, 1, '', 60, 'style="display: none;"');
        $tabs['items']['content'] .= HTML_ValueCell('<span id="initial_piece_price_' . $i . '">' . process_price_display(getPieceItemInitialPrice($newOrderItemObject)) . '</span>',
            1, 1, '', 60);

        $tabs['items']['content'] .= HTML_ValueCell('<span id="initial_price_' . $i . '">' . process_price_display(getItemInitialPrice($newOrderItemObject)) . '</span>',
            1, 1, '', 60);

        $tabs['items']['content'] .= '<td nowrap="nowrap" style="width: 110px;"><input type="text" class="textfield price-type" name="price_per_item_' . $i . '" id="price_per_item_' . $i . '" value="' . process_price_display($newOrderItemObject->getItemPriceWithoutVAT()) . '" size="6" onkeyup="setChange();recountDiscount(' . $i . ')" id="manual_price_' . $i . '_text"' . $price_type_code . ' /><input type="hidden" name="is_manual_price_' . $i . '" id="manual_price_' . $i . '_input" value="yes" /></td>';
        $tabs['items']['content'] .= HTML_ValueCell(HTML_TextField('nr_of_items_' . $i, $orderItems[$i]['nr_of_items'], 4, '', '', false, -1, null,
            'id = "nr_of_items_' . $i . '" onkeyup="recount(' . $i . ');setChange();"'), 1, 1, '', 40);

        $tabs['items']['content'] .= '<td nowrap="nowrap" style="width: 110px;"><input type="text" class="textfield price-type" name="price_per_item_' . $i . '" id="price_per_item_' . $i . '" value="' . process_price_display($orderItems[$i]['price_per_item']) . '" size="6" onkeyup="setChange();recountDiscount(' . $i . ')" id="manual_price_' . $i . '_text"' . $price_type_code . ' /><input type="hidden" name="is_manual_price_' . $i . '" id="manual_price_' . $i . '_input" value="yes" /></td>';

        $tabs['items']['content'] .= HTML_ValueCell(HTML_TextField('vat_per_item_' . $i, $orderItems[$i]['vat_per_item'], 4, '', '', false, -1, null,
            'onkeyup="setChange();"'), 1, 1, '', 60);
        if (is_array(ShopItemConfiguration::getStates())) { // There are not any states
            $tabs['items']['content'] .= HTML_ValueCell(HTML_SelectSimple('item_state_' . $i, ShopItemConfiguration::getStates(), $orderItems[$i]['item_state'],
                "setChange();"));
        }
        $tabs['items']['content'] .= HTML_ValueCell(HTML_TextField('nr_of_delivered_items_' . $i, $orderItems[$i]['nr_of_delivered_items'], 4, '', '', false,
            -1, null, 'onkeyup="setChange();"'), 1, 1, '', 50);

        $tabs['items']['content'] .= HTML_ValueCell('<span id="margin_piece_' . $i . '">' . process_price_display(getPieceMargin($newOrderItemObject)) . '</span>',
            1, 1, '', 50);

        $tabs['items']['content'] .= HTML_ValueCell('<span id="margin_' . $i . '">' . process_price_display(getMargin($newOrderItemObject)) . '</span>',
            1, 1, '', 50);

        $tabs['items']['content'] .= HTML_ValueCell('<span id="margin_percent' . $i . '">' . getMarginPercent($newOrderItemObject) . '%' . '</span>',
            1, 1, '', 50);

        if (isset($notes) && $notes) {
            $tabs['items']['content'] .= HTML_ValueCell($notes, 1, 1, 'actions-cell');
        }
        $notes = '';
        $tabs['items']['content'] .= HTML_ValueCell($actions_content, 1, 1, 'actions-cell');
        $tabs['items']['content'] .= HTML_RowEnd();
    }

    $tabs['items']['content'] .= HTML_TableEnd();
    $tabs['items']['content'] .= HTML_TableBegin('Pridať produkt', 2);
    $tabs['items']['content'] .= HTML_RowBegin();
    $tabs['items']['content'] .= HTML_HeaderCell('Vyhľadať:');
    $tabs['items']['content'] .= HTML_ValueCell('<input class="ac_input" autocomplete="on" id="ajax_product" style="width: 450px;" type="text" onkeyup=""><img src="ajax-loader.gif" style="display: none;padding-left: 10px;" id="ajax_product_wait" />', 1);
    $tabs['items']['content'] .= HTML_RowEnd();
    $tabs['items']['content'] .= HTML_RowBegin();

    $tabs['items']['content'] .= HTML_TableEnd();


    $real_invoice_number = $order_object->getInvoiceNumber();
    $invoice_exists = !empty($real_invoice_number) && file_exists(\Buxus\Eshop\Facades\InvoiceGenerator::getInvoicePath($order_object, \Buxus\Eshop\InvoiceGenerator\InvoiceGenerator::INVOICE_TYPE_INVOICE));
    $pre_invoice_exists = !empty($real_invoice_number) && file_exists(\Buxus\Eshop\Facades\InvoiceGenerator::getInvoicePath($order_object, \Buxus\Eshop\InvoiceGenerator\InvoiceGenerator::INVOICE_TYPE_ADVANCE));

    $invoices_content = '';
    $invoices_content .= HTML_TableBegin('', 4, 'table table-bordered buxus-table', 'items-table');
    $invoices_content .= HTML_RowBegin();
    $invoices_content .= HTML_HeaderCell(__bx('eshop::shop_orders_details.AdvanceInvoice'));
    if ($pre_invoice_exists) {
        $invoices_content .= HTML_ValueCell('<div id="generate_invoice_link_zf"><img src="' . config('buxus_core.base_url') . 'system/images/cog.gif" style="vertical-align:bottom;" /> <a href="javascript:generateInvoice(\'zf\');">' . __bx('eshop::shop_orders_details.Regenerate') . '</a></div>',
            1, 1, 'left', '150px;');
        $invoices_content .= HTML_ValueCell('<div id="show_invoice_link_zf"><img src="' . config('buxus_core.base_url') . 'system/images/doc_document.gif" style="vertical-align:bottom;" /> <a href="show_invoice.php?vs=' . $variable_symbol . '&type=zf" target="_blank">' . __bx('eshop::shop_orders_details.Show') . '</a></div>',
            1, 1, 'left', '100px;');
        $invoices_content .= HTML_ValueCell('<div id="delete_invoice_link_zf"><img src="' . config('buxus_core.base_url') . 'system/images/delete3.gif" style="vertical-align:bottom;" /> <a href="javascript:deleteInvoice(\'' . $variable_symbol . '\', \'zf\');">' . __bx('eshop::shop_orders_details.Remove') . '</a></div>',
            1, 1, 'left', '100px;');
    } else {
        $invoices_content .= HTML_ValueCell('<div id="generate_invoice_link_zf"><img src="' . config('buxus_core.base_url') . 'system/images/cog.gif" style="vertical-align:bottom;" /> <a href="javascript:generateInvoice(\'zf\');">' . __bx('eshop::shop_orders_details.Generate') . '</a></div>',
            1, 1, 'left', '150px;');
        $invoices_content .= HTML_ValueCell('<div id="show_invoice_link_zf"><img src="' . config('buxus_core.base_url') . 'system/images/doc_document_bw.gif" style="vertical-align:bottom;" /> <span style="color:#AAAAAA;">' . __bx('eshop::shop_orders_details.Show') . '</span></div>',
            1, 1, 'left', '100px;');
        $invoices_content .= HTML_ValueCell('<div id="delete_invoice_link_zf"><img src="' . config('buxus_core.base_url') . 'system/images/delete3_bw.gif" style="vertical-align:bottom;" /> <span style="color:#AAAAAA;">' . __bx('eshop::shop_orders_details.Remove') . '</span></div>',
            1, 1, 'left', '100px;');
    }
    $invoices_content .= HTML_RowEnd();
    $invoices_content .= HTML_RowBegin();
    $invoices_content .= HTML_HeaderCell(__bx('eshop::shop_orders_details.Invoice'));
    if ($invoice_exists) {
        $invoices_content .= HTML_ValueCell('<div id="generate_invoice_link_fa"><img src="' . config('buxus_core.base_url') . 'system/images/cog.gif" style="vertical-align:bottom;" /> <a href="javascript:generateInvoice(\'fa\');">' . __bx('eshop::shop_orders_details.Regenerate') . '</a></div>',
            1, 1, 'left', '150px;');
        $invoices_content .= HTML_ValueCell('<div id="show_invoice_link_fa"><img src="' . config('buxus_core.base_url') . 'system/images/doc_document.gif" style="vertical-align:bottom;" /> <a href="show_invoice.php?vs=' . $variable_symbol . '&type=fa" target="_blank">' . __bx('eshop::shop_orders_details.Show') . '</a></div>',
            1, 1, 'left', '100px;');
        $invoices_content .= HTML_ValueCell('<div id="delete_invoice_link_fa"><img src="' . config('buxus_core.base_url') . 'system/images/delete3.gif" style="vertical-align:bottom;" /> <a href="javascript:deleteInvoice(\'' . $variable_symbol . '\', \'fa\');">' . __bx('eshop::shop_orders_details.Remove') . '</a></div>',
            1, 1, 'left', '100px;');
    } else {
        $invoices_content .= HTML_ValueCell('<div id="generate_invoice_link_fa"><img src="' . config('buxus_core.base_url') . 'system/images/cog.gif" style="vertical-align:bottom;" /> <a href="javascript:generateInvoice(\'fa\');">' . __bx('eshop::shop_orders_details.Generate') . '</a></div>',
            1, 1, 'left', '150px;');
        $invoices_content .= HTML_ValueCell('<div id="show_invoice_link_fa"><img src="' . config('buxus_core.base_url') . 'system/images/doc_document_bw.gif" style="vertical-align:bottom;" /> <span style="color:#AAAAAA;">' . __bx('eshop::shop_orders_details.Show') . '</span></div>',
            1, 1, 'left', '100px;');
        $invoices_content .= HTML_ValueCell('<div id="delete_invoice_link_fa"><img src="' . config('buxus_core.base_url') . 'system/images/delete3_bw.gif" style="vertical-align:bottom;" /> <span style="color:#AAAAAA;">' . __bx('eshop::shop_orders_details.Remove') . '</span></div>',
            1, 1, 'left', '100px;');
    }
    $invoices_content .= HTML_RowEnd();
    $invoices_content .= HTML_TableEnd();
    $tabs['invoices']['content'] = $invoices_content;

    $note_content = __bx('eshop::shop_orders_details.NoteAnnotation') . '<br />';
    $note_content .= HTML_Textarea('internal_note', $order_object->getInternalNote(), 7, 80, 'virtual', 'onkeyup="setChange();"');
    $tabs['note']['content'] = $note_content;

    $notification_content = '';
    $notification_content .= HTML_TableBegin('<label>' . HTML_Checkbox('notification_send', 1, false,
            'toggleEmailNotificationForm(this.checked);setChange();') . ' ' . __bx('eshop::shop_orders_details.SendEmailNotification') . '</label>', 2, null, 'email_notification_table');
    $to = array();
    if (!empty($default_items['email'])) {
        $to[] = mb_strtolower($default_items['email']);
    }
    if (!empty($webuser_info[0]['e_mail']) && mb_strtolower($webuser_info[0]['e_mail']) != mb_strtolower($default_items['email'])) {
        $to[] = mb_strtolower($webuser_info[0]['e_mail']);
    }
    $notification_content .= '<tbody style="display:none">';
    $notification_content .= HTML_RowBegin();
    $notification_content .= HTML_HeaderCell(__bx('eshop::shop_orders_details.EmailRecipient') . ': ');
    $notification_content .= HTML_ValueCell(HTML_TextField('notification_email_to', implode(', ', $to), 80,
            'checkEmail(this, "bad_email_address_warning");setChange()', 'notification_email_to', false, -1, null,
            'disabled') . '<span id="bad_email_address_warning"></span>');
    $notification_content .= HTML_RowEnd();

    $notification_content .= HTML_RowBegin();
    $notification_content .= HTML_HeaderCell(__bx('eshop::shop_orders_details.EmailSubject') . ':');
    $cell_html = HTML_HiddenField('notification_email_from', '', 'notification_email_from');
    $cell_html .= HTML_TextField('notification_email_subject', __bx('eshop::shop_orders_details.EmailSubjectDefault'), 80, '', 'notification_email_subject', false, -1, null, 'disabled');
    $notification_content .= HTML_ValueCell($cell_html);
    $notification_content .= HTML_RowEnd();

    $notification_content .= HTML_RowBegin();
    $notification_content .= HTML_HeaderCell(__bx('eshop::shop_orders_details.EmailBody') . ' HTML:');
    $notification_content .= HTML_ValueCell(HTML_Textarea('notification_email_text_html', '', 12, 80, 'virtual',
            'disabled="disabled" onkeyup="setChange();"') .
        '<br /><input id="notification_email_text_html_button" type="button" name="edithtml" value="' . (config('buxus_eshop.eshop_detail_wysiwyg_default_off') ? 'Zapnúť HTML editor' : 'Vypnúť HTML editor') . '" />');
    $notification_content .= HTML_RowEnd();

    $css_tiny = GetSystemOption("C_html_editor_css_file");
    //$css_tiny = $css_tiny."?refresh=".md5(time());

    $language_tiny = GetSystemOption("C_language");
    if ($language_tiny == "cz") {
        $language_tiny = "cs";
    }

    if (file_exists(config_path('tinymce_configs/full_email.js'))) {
        $tiny_options = file_get_contents(config_path('tinymce_configs/full_email.js'));
    } else {
        $tiny_options = file_get_contents(config_path('tinymce_configs/full.js'));
    }


    $notification_content .= '
                        <script type="text/javascript">
                            require(["jquery"], function($) {
                              var notification_email_html_editor = $("#notification_email_text_html").buxusTiny({
	            						button_value_off: "Vypnúť HTML editor",
	            						button_value_on: "Zapnúť HTML editor",
	            						c_redis_root: "' . config('buxus_core.base_url') . '",
	            						c_redis_root_images: "' . config('buxus_core.image_upload_url') . '",
	            						c_redis_root_docs: "' . config('buxus_core.doc_upload_url') . '",
	            						submit_form_id: "order_details_form",
	            						content_css: "' . $css_tiny . '",
	            						cache_suffix: "?' . \Buxus\Legacy\LegacyMethods::getStaticFileRefresh() . '",
	            						language: "' . $language_tiny . '"},
	  							' . $tiny_options . '
	            					).initClassic(' . (config('buxus_eshop.eshop_detail_wysiwyg_default_off') ? '0' : '1') . ');
                            });
	            		</script>

    ';

    $notification_content .= HTML_RowBegin();
    $notification_content .= HTML_HeaderCell(__bx('eshop::shop_orders_details.EmailBody') . ':');
    $notification_content .= HTML_ValueCell(HTML_Textarea('notification_email_text', '', 12, 80, 'virtual', 'disabled="disabled" onkeyup="setChange();"'));
    $notification_content .= HTML_RowEnd();

    $notification_content .= HTML_RowBegin();
    $notification_content .= HTML_HeaderCell('Embedovať obrázky:');
    $notification_content .= HTML_ValueCell(HTML_Checkbox('notification_embed_images', 1, false));
    $notification_content .= HTML_RowEnd();

    $notification_content .= HTML_RowBegin();
    $notification_content .= HTML_HeaderCell('Prílohy:');
    $notification_content .= HTML_ValueCell('<div id="attachment_list"></div>');
    $notification_content .= HTML_RowEnd();

    $notification_content .= HTML_RowBegin();
    $notification_content .= HTML_HeaderCell(__bx('eshop::shop_orders_details.AdvanceInvoice') . ':');
    if ($pre_invoice_exists) {
        $notification_content .= HTML_ValueCell(HTML_Checkbox('notification_attach_invoice_zf', 1,
                false) . ' <span id="label_notification_attach_invoice_zf">' . __bx('eshop::shop_orders_details.AttachedAdvanceInvoice') . '</span>');
    } else {
        $notification_content .= HTML_ValueCell(HTML_Checkbox('notification_attach_invoice_zf', 1, false, '',
                'disabled') . ' <span style="color:#AAAAAA;" id="label_notification_attach_invoice_zf">' . __bx('eshop::shop_orders_details.AttachedAdvanceInvoice') . '</span>');
    }
    $notification_content .= HTML_RowEnd();

    $notification_content .= HTML_RowBegin();
    $notification_content .= HTML_HeaderCell(__bx('eshop::shop_orders_details.Invoice') . ':');
    if ($invoice_exists) {
        $notification_content .= HTML_ValueCell(HTML_Checkbox('notification_attach_invoice_fa', 1,
                false) . ' <span id="label_notification_attach_invoice_fa">' . __bx('eshop::shop_orders_details.AttachedInvoice') . '</span>');
    } else {
        $notification_content .= HTML_ValueCell(HTML_Checkbox('notification_attach_invoice_fa', 1, false, '',
                'disabled') . ' <span style="color:#AAAAAA;" id="label_notification_attach_invoice_fa">' . __bx('eshop::shop_orders_details.AttachedInvoice') . '</span>');
    }
    $notification_content .= HTML_RowEnd();

    // attach custom file
    $notification_content .= HTML_RowBegin();
    $notification_content .= HTML_HeaderCell(__bx('eshop::shop_orders_details.AttachFile') . ':');
    $notification_content .= HTML_ValueCell(HTML_FileUpload('notification_attach_file', '', 'setChange()'));
    $notification_content .= HTML_RowEnd();
    $notification_content .= '</tbody>';
    $notification_content .= HTML_TableEnd();

//    $notification_content .= HTML_Button('notification_load_default_email_text', __bx('eshop::shop_orders_details.LoadDefaultStateEmail'),
//            'loadNotificationForState(document.getElementById("order_state").options[document.getElementById("order_state").selectedIndex].value, ' . $order_id . ');',
//            -1, 'disabled') . '<br />';
    $tabs['notification']['content'] = $notification_content;

    // logs tab content


    $journal = new ShopJournalView($order_id, 'orders', 'journal_view.xtpl.html');
    $journal->loadRecords();
    $journal->assignXTemplateData();
    $tabs['logs']['content'] = $journal->getView();

    unset($tabs['documents']);

    $tabs['email_logs']['content'] = view('buxus-eshop::buxus.order.email-logs-tab', [
        'logs' => $order->getOrderEmailLog()
    ]);


    // tabs menu

    $tabs_menu = '
        <ul>';
    $tabs_content = '';

    foreach ($tabs as $tab_id => $tab) {
        foreach ($order_detail_extensions_objects as $detail_extensions_object) {
            $tab = $detail_extensions_object->processOrderDetailTab($tab_id, $tab, $order_object);
        }

        if (isset($tab['icon']) && !empty($tab['icon'])) {
            $icon = '<img src="' . $tab['icon'] . '" style="vertical-align:bottom;" /> ';
        } else {
            $icon = '';
        }
        $tabs_menu .= '
            <li>
            <a href="#' . $tab_id . '-tab">
                ' . $icon . $tab['caption'] . '</a>
            </li>';

        $tabs_content .= '
            <div id="' . $tab_id . '-tab">
            ' .

            $tab['content'] . '
            </div>';
    }

    $tabs_menu .= '
				<li id="changed" title="' . __bx('eshop::shop_orders_details.ChangeOrderNotice') . '">
					*
				</li>
			</ul>';

    $quick_navigation = '';

    // whole content

    $content .= HTML_PageTitle(__bx('eshop::shop_orders_details.PageTitle'), __bx('eshop::shop_orders_details.PageAnotation')) . HTML_FormBegin('order_details_form', 'shop_orders_details_submit.php',
            'return onOrderDetailsFormSumbit();', 'POST', 'multipart/form-data') . HTML_HiddenField('order_id', $order_id,
            'order_id') . HTML_HiddenField('active_panel', $active_panel,
            'active_panel') . '<div>' . $quick_navigation . '</div>' . '<div id="shop" style="margin-top: 6px;">' .

        displayListInTwoColumns(__bx('eshop::shop_orders_details.TableHeader'), $main_parts) .

        '<div id="tabs" class="eshop-tabs">' .
        $tabs_menu . $tabs_content .
        '</div>' .
        '

					<div style="margin-top: 6px; text-align: left;">
						<div style="float:left;">
							<div style="margin:2px;" id="email_warning">&nbsp;</div>
							<input type="submit" name="apply_edit" value="' . __bx('eshop::shop_orders_details.SaveOrder') . '" class="btn btn-primary" id="apply_edit" disabled="disabled" />
';

    if (isset($order_detail_extensions_objects)) {
        foreach ($order_detail_extensions_objects as $detail_extensions_object) {
            $content .= $detail_extensions_object->getButtonHTML($order_object);
        }
    }


    $content .= '</div>
						<div style="float:right;">
							<div style="margin:2px;">&nbsp;</div>' . HTML_Button("delete_order", __bx('eshop::shop_orders_details.Delete_Order'), "deleteOrder();") . '	</div>
						<br clear="all" />
					</div>

					<div id="edit_form_holder" style="display: none;"></div>
				</div>' . HTML_FormEnd() . '<script type="text/javascript">
				';

    $content .= 'require(["jquery", "jquery-ui"], function($) {
              $( function() {
                    $( "#tabs" ).tabs();
                  } );
            });

            loadEditForm();</script>';

    $tabs_ids = array_keys($tabs);

    // tinyMCE stuff
    $cr_root_regexp = str_replace('/', '.', config('buxus_core.base_url'));
    $cr_root_images_regexp = str_replace('/', '.', config('buxus_core.image_upload_url'));
    $cr_root_docs_regexp = str_replace('/', '.', config('buxus_core.doc_upload_url'));

    $d_root = $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
    $path = explode("/", $d_root);

    array_pop($path);

    $current_url = '';

    foreach ($path as $value) {
        $current_url .= $value . "/";
    }
    $current_url = \Buxus\Util\Url::getProtocol() . $current_url;
    $tiny_javascript = 'document_root = "' . $current_url . '";
							document_host ="' . $_SERVER['HTTP_HOST'] . '";
							docs_dir ="' . $cr_root_docs_regexp . '";
							imgs_dir ="' . $cr_root_images_regexp . '";
							c_redis_root ="' . config('buxus_core.base_url') . '";
							';
    // end of tinyMCE stuff


    $javascript = '
			<script type="text/javascript" src="shop_orders.js?v=106"></script>
			<script type="text/javascript" src="' . config('buxus_core.base_url') . 'includes/tinymce/tinymce.full.min.js?' . \Buxus\Legacy\LegacyMethods::getStaticFileRefresh() . '"></script>
			<script type="text/javascript" src="' . config('buxus_core.base_url') . 'includes/tinymce/jquery.tinymce.min.js?' . \Buxus\Legacy\LegacyMethods::getStaticFileRefresh() . '"></script>
		    <script type="text/javascript" src="' . config('buxus_core.base_url') . 'includes/javascript/buxus_tiny_browser.js?' . \Buxus\Legacy\LegacyMethods::getStaticFileRefresh() . '"></script>
		    <script type="text/javascript" src="' . config('buxus_core.base_url') . 'includes/javascript/jquery.buxusTiny.js?' . \Buxus\Legacy\LegacyMethods::getStaticFileRefresh() . '"></script>
			<script type="text/javascript">
			//<![CDATA[
				var delete_confirm_message = "' . __bx('eshop::shop_orders_details.Delete_Order_Question') . $variable_symbol . '?\n\n' . __bx('eshop::shop_orders_details.Delete_Order_Explain') . '";
				var order_id = ' . $order_id . ';
				var tabs = new Array(\'' . implode("','", $tabs_ids) . '\');
				var default_tab = \'' . $active_panel . '\';
				var custom_order_type = \'' . $order->getCustomType() . '\';
				var default_order_type = \'' . $order->getDefaultType() . '\';
				var default_tax = \'' . $order->getDefaultTax() . '\';
				var buxus_root = "' . config('buxus_core.base_url') . '";
				var buxus7 = true;
				' . $tiny_javascript . '
			//]]>
			</script>
';
    $javascript .= '<style type="text/css">
            div#shop table td.actions-cell .delete {
	background: url("' . config('buxus_core.base_url') . 'system/images/delete2.gif") no-repeat;
	    padding-left: 18px;
}
    </style>';
} else {
    $content .= __bx('eshop::shop_orders_details.NoAccessRights');
}
DisplayHtmlHeader(__bx('eshop::shop_orders_details.EShopPageTitle'), C_MenuType_EShop, $javascript, '', '', 0, false,
    'if(order_form_changed) { return \'Vo formulári boli vykonané zmeny. Pokračovaním ich stratíte.\nChcete naozaj pokračovať?\' }');
?>


    <script type="text/javascript">
        require(['jquery', 'jquery-ui'], function ($) {
            $(function () {
                $(".toggle_message").toggle(function () {
                    $(".active_message").css('height', '80px');
                    $(".active_message").css('background-color', '#FFF');
                    $(this).next(".mail_message").css('height', 'auto');
                    $(this).next(".mail_message").css('background-color', '#F0F0F0');
                    $(this).next(".mail_message").addClass('active_message');
                }, function () {
                    $(this).next(".mail_message").css('height', '80px');
                    $(this).next(".mail_message").css('background-color', '#FFF');
                    $(this).next(".mail_message").removeClass('active_message');
                });
// Dialog
                $('.dialog').dialog({
                    autoOpen: false,
                    width: 600,
                    buttons: {
                        "Ok": function () {
                            $(this).dialog("close");
                            jQuery.post('<?= config('buxus_core.base_url')?>lib/shop/ajax/note.php',
                                {
                                    order_item_id: $(this).attr("orderItemId"),
                                    note_type: $(this).attr('noteType'),
                                    note: $(this).find('textarea').val()
                                },
                                function (data) {
                                });

                        },
                        "Cancel": function () {
                            $(this).dialog("close");
                        }
                    }
                });

                // Dialog Link
                $('.dialog_link').click(function () {
                    $("#dialog" + '_' + $(this).attr("noteType") + '_' + $(this).attr("item")).dialog('open');
                    // alert("#dialog"+'_'+$(this).attr("noteType")+'_'+$(this).attr("item"));
                    return false;
                });
            });
        });

        function priceRound(price) {
            return Math.round(price * 1000) / 1000;
        }

        function recountDiscount(id) {
            require(['jquery'], function ($) {
                // replace , to .
                price = $('#price_per_item_' + id).val().replace(',', '.');
                $('#price_' + id).text(priceRound(parseFloat(price) * parseInt($('#nr_of_items_' + id).val())));
            });
        }

        function recountPrice(id, db_price) {
            require(['jquery'], function ($) {
                price = db_price - db_price * $('#discount_per_item_' + id).val().replace(',', '.') / 100;
                $('#price_' + id).text(priceRound(parseFloat(price) * parseInt($('#nr_of_items_' + id).val())));
                $('#price_per_item_' + id).val(priceRound(price));
            });
        }

        function recount(id) {
            require(['jquery'], function ($) {
                var previous = parseFloat($('#price_' + id).text());
                price = $('#price_per_item_' + id).val().replace(',', '.');
                var newPrice = priceRound(parseFloat(price) * parseInt($('#nr_of_items_' + id).val()));
                $('#price_' + id).text(newPrice);
                var diff = newPrice - previous;
                var paid_price = parseFloat($('input[name=paid_price]').val().replace(',', '.'));
                $('input[name=paid_price]').val(priceRound(paid_price + diff));
            });
        }
    </script>


<?php
echo $content;

// Show dialog after adding product
$add_product_dialog_javascript = '';
if ((Session::has('buxus.buxus_shop_added_products')) && (is_array(Session::get('buxus.buxus_shop_added_products'))) && (in_array($order_id,
        Session::get('buxus.buxus_shop_added_products')))) { // A product has been added
    $add_product_dialog_javascript = '
	// Request of new final price
	$(".dialog-add-item").dialog("open");';

    // Remove product from session
    Session::set('buxus.buxus_shop_added_products', array_diff(Session::get('buxus.buxus_shop_added_products'), array($order_id)));
}

echo '
<div style="display: none;">
	<div class="dialog-delete-item" title="' . __bx('eshop::shop_orders_details.ChangeFinalPriceDialogTitle') . '">
		<p>' . __bx('eshop::shop_orders_details.ChangeFinalPriceDeleteItem') . '</p>
	</div>
	<div class="dialog-add-item" title="' . __bx('eshop::shop_orders_details.ChangeFinalPriceDialogTitle') . '">
		<p>' . __bx('eshop::shop_orders_details.ChangeFinalPriceAddItem') . '</p>
	</div>
	<div class="dialog-change-order" title="' . __bx('eshop::shop_orders_details.ChangeFinalPriceDialogTitle') . '">
		<p>' . __bx('eshop::shop_orders_details.ChangeFinalPriceChangeOrder') . '</p>
	</div>
</div>

<script type="text/javascript">
//<![CDATA[
require(["jquery", "jquery-ui"], function ($){
$(function() {
 $("#ajax_product").autocomplete({
      source: "../ajax/ajax_product.php",
      delay: 1000,
      minLength: 2,
      search: function() {
            $("#ajax_product_wait").show();
      },
      response: function() {
            $("#ajax_product_wait").hide();
      },
      select: function( event, ui ) {
            $("#ajax_product_wait").hide();
            var orderItemIds = [];
            $("#productsTable td.page_id").each(function(){
                orderItemIds.push($(this).html());
            });

            jQuery.post("../ajax/ajax_add_product.php", {
              \'order_item_ids\': orderItemIds,
              \'page_id\':ui.item.value,
              \'order_id\':$("#order_id").val()
            }, function(data){
                if (data.result == "OK") {
                    alert(data.payload.message);
                    window.location.reload()
                } else {
                    alert(data.message);
                }
            });
            event.preventDefault();
            event.stopPropagation();
      }
    }
  );
' . $add_product_dialog_javascript . '

    $("#set_order_as_paid").on("click", function() {
        $("input[name=payment_datetime]").val("now");
        setChange();
        $("#apply_edit").click();
    });

    $("input[name=payment_datetime]").on("change", function() {
      setChange();
    })
});
});
//]]>
</script>';

if (isset($order_detail_extensions_objects)) {
    foreach ($order_detail_extensions_objects as $detail_extensions_object) {
        echo $detail_extensions_object->getScripts();
    }
}

echo '<div id="dhtmltooltip" style="position:absolute; z-index:100; visibility: hidden; width:300px; background-color: #E2EAF4; border: 1px solid #2B528A; padding: 5px;"></div>';
echo '<script type="text/javascript" src="../../../includes/tooltip.js?' . \Buxus\Legacy\LegacyMethods::getStaticFileRefresh() . '"></script>';
echo '<script type="text/javascript">
require(["jquery"], function($) {
  $(function() {
    $("#dhtmltooltip").appendTo("body");
  });
});
</script>';
DisplayHtmlFooter();

\GDPR::commit();
