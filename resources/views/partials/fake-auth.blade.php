@php
    $fakeAuth = new \App\Authentication\FakeAuthentication();
        if(!($auth = \Buxus\WebUser\Facades\WebUserAuthentication::isAuthenticated())){
            $fakeAuth->forgetSuperuser();
        }
@endphp
@if(\WebUserAuthentication::isAuthenticated() && \WebUserAuthentication::getUser()->getCustomOption('is_sales_representant') === 'T')
    <div class="fake-login-header" data-superuser-id="0">
        <form action="{{ route('fakelogin') }}" class="d-flex flex-row" method="post">
            @csrf
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 col-md-6">
                        <div class="input-group h-100 w-100">
                            <div class="input-group-addon">
                                <span class="input-group-text">Prihlásiť sa ako:</span>
                            </div>
                            <select name="fake_login_id" class="form-control select2" style="min-width: 300px;">
                                @foreach($fakeAuth->getUsersForSite(BuxusSite::site()) as $id => $name)
                                    <option value="{{ $id }}">{{ $name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-sm-12 col-md-3">
                        <button type="submit" class="btn btn-secondary">Prihlásiť sa</button>
                    </div>
                    <div class="col-sm-12 col-md-3">
                        <div class="d-flex">
                            <div class="ml-auto">
                                <button type="button" class="btn btn-secondary" id="superuserCreateProductBtn">Vytvoriť
                                    kartu
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
@endif
@if(!empty($superuser = $fakeAuth->getSuperuser()))
    @php
        $user = WebUserAuthentication::getUser();
    @endphp
    <div class="fake-login-header" data-superuser-id="{{ $superuser->getUserId() }}">
        <div class="container">
            <div class="row">
                <div class="col-sm-12 col-md-9">
                    <h6>Prihlásený z administrátorského účtu:
                        <strong>{{ $superuser->getFirstName() }} {{ $superuser->getSurname() }}</strong> - ako
                        používateľ:
                        <strong>{{ $user->getCompanyName() }}</strong></h6>
                </div>
                <div class="col-sm-12 col-md-3">
                    <div class="d-flex">
                        <div class="ml-auto">
                            <button type="button" class="btn btn-secondary" id="superuserCreateProductBtn">Vytvoriť
                                kartu
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif
