@php
    /** @var \App\WebUser\WebUser $user */
    $user = $data['webuser'];
@endphp
<div class="container" style="display:flex;">
    <div class="row" style="width: 80%; margin: 0 auto;">
        <div style="display:flex;flex-direction:row;">
            <div align="left" style="line-height: 1px; width:65%;float:left;">
                <h1>{{ \Trans::str('form', 'Reklamácia č.') }} {{ $data['form_number'] ?? $data['form_submit_id'] }}</h1>
                <br>
                <p><strong>{{ Trans::str('form', 'Dátum') }}:</strong> {{ $data['form_submit_time'] }}</p>
                <br>
                <p><strong>{{ \Trans::str('form', 'Odberateľ') }}</strong></p>
                <p><strong>{{ $user->getFirstName() }} {{ $user->getSurname() }}</strong></p>
                <p><strong>{{ $data['company_name'] ?: $user->getCompanyName() }}</strong><br></p>

                <p><strong>{{ $user->getStreet() }}</strong></p>
                <p><strong>{{ $user->getZip() }} {{ $user->getCity() }}</strong></p>
                <p><strong>{{ $user->getCountry() }}</strong><br></p>

                <p>{{ \Trans::str('eshop', 'Telefón') }}: <strong>{{ $user->getPhone() }}</strong></p>
                <p>{{ \Trans::str('user', 'IČO') }}: <strong>{{ $user->getIco() }}</strong>
                    | {{ \Trans::str('user', 'IČ DPH') }}: <strong>{{ $user->getDrc() }}</strong></p>
            </div>
            <div align="right" style="width: 30%; float: right;text-align:right;line-height:1px;">
                <img src="{{ \Buxus\Util\Url::asset('images/logo.png') }}" alt="">
                <p><strong>Rinoparts s.r.o.</strong></p>
                <p><strong>Priemyselná 16</strong></p>
                <p><strong>010 01 Žilina</strong></p>
                <p><strong>Slovenská republika</strong></p>
            </div>
        </div>


        <style>
            td {
                padding-right: 50px;
            }
        </style>
        <div style="max-width: 100%">
            <table style="border: none;border-collapse:separate;border-spacing: 0 15px;">
                <tr>
                    <td>
                        <p>{{ \Trans::str('form', 'Číslo faktúry') }}:</p>
                    </td>
                    <td>
                        <strong><p>{{ $data['invoice_number'] }}</p></strong>
                    </td>
                </tr>
                <tr>
                    <td>
                        <p>{{ \Trans::str('form', 'Interné číslo dielu Rinoparts') }}:</p>
                    </td>
                    <td>
                        <strong><p> {{ $data['internal_number'] }}</p></strong>
                    </td>
                </tr>
                <tr>
                    <td>
                        <p>{{ \Trans::str('form', 'Originálne číslo dielu') }}</p>
                    </td>
                    <td>
                        <strong><p>{{ $data['original_number'] }}</p></strong>
                    </td>
                </tr>
                <tr>
                    <td>
                        <p>{{ \Trans::str('form', 'Počet kusov na vrátenie') }}:</p>
                    </td>
                    <td>
                        <strong><p>{{ $data['count'] }}</p></strong>
                    </td>
                </tr>
                <tr>
                    <td>
                        <p>{{ \Trans::str('form', 'Dátum montáže dielu') }}:</p>
                    </td>
                    <td>
                        <strong><p>{{ $data['montage_date'] }}</p></strong>
                    </td>
                </tr>
                <tr>
                    <td>
                        <p>{{ \Trans::str('form', 'VIN číslo podvozku') }}:</p>
                    </td>
                    <td>
                        <strong><p>{{ $data['vin_number'] }}</p></strong>
                    </td>
                </tr>
                @if(isset($data['order_pickup']))
                    <tr>
                        <td>
                            <p>{{ \Trans::str('form', 'Objednať zvoz balíka') }}:</p>
                        </td>
                        <td>
                            <strong><p>{{ $data['order_pickup'] }}</p></strong>
                        </td>
                    </tr>
                @endif
                <tr>
                    <td>
                        <p>{{ \Trans::str('form', 'Popis závady') }}:</p>
                    </td>
                    <td>
                        <strong><p>{{ $data['defect_description'] }}</p></strong>
                    </td>
                </tr>
                <tr>
                    <td>
                        <p>{{ \Trans::str('form', 'Kontaktná osoba') }}:</p>
                    </td>
                    <td>
                        <strong><p>{{ $data['contact_person'] }}</p></strong>
                    </td>
                </tr>
                <tr>
                    <td>
                        <p>{{ \Trans::str('eshop', 'Telefón') }}:</p>
                    </td>
                    <td>
                        <strong><p> {{ $data['phone'] }}</p></strong>
                    </td>
                </tr>
                <tr>
                    <td>
                        <p>{{ \Trans::str('eshop', 'E-mail') }}:</p>
                    </td>
                    <td>
                        <strong><p> {{ $data['email'] }}</p></strong>
                    </td>
                </tr>
            </table>
            @if(isset($data['response']) && !empty($data['response']))
                <p><strong>Vyjadrenie:</strong></p>
                <p>{{ $data['response'] }}</p>
            @endif
        </div>
    </div>
</div>
