@if(!empty($imports))
    @foreach($imports as $supplier => $import)
        <tr>
            <td class="py-1">
                {{ $supplier }}
            </td>
            <td>
                {{ $import->created_at }}
            </td>
            <td>
                @if($import->status == \App\Imports::DONE)
                    <p>Hotovo <span class="status-icon check">&check;</span></p>
                @elseif($import->status == \App\Imports::ERROR)
                    <p>Chyba <span class="status-icon times">&times;</span></p>
                @elseif($import->status == \App\Imports::DELAYED)
                    <p><PERSON>dl<PERSON><PERSON><PERSON>ý <span class="status-icon circle">&xcirc;</span></p>
                @else
                    <p>St<PERSON>le prebieha <span class="status-icon circle">&xcirc;</span></p>
                @endif
            </td>
            <td>
                {{ $import->items_processed }}
            </td>
            <td>
                {{ $import->updates_processed }}
            </td>
            <td>
                {{ $import->creates_processed }}
            </td>
            <td>
                {{ $import->deletes_processed }}
            </td>
            <td>
                {{ $import->errors }}
            </td>
        </tr>
    @endforeach
@endif