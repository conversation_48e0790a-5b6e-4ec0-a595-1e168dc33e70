<div class="modal fade" id="cartSaveModal" tabindex="-1" role="dialog"
     aria-labelledby="cartSaveModalLabel">
    <div class="modal-dialog modal-center" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title"
                    id="cartSaveModalLabel">{{ \Trans::str('cart', 'Uloženie košíka') }}</h4>
            </div>
            <div class="modal-body">
                <form action="{{ route('cart.save') }}" id="cart-save-form" method="post">
                    @csrf
                    <label>
                        {{ \Trans::str('cart', 'Názov košíka') }}
                        <small>({{ \Trans::str('cart', 'nepovinné') }})</small>
                    </label>
                    <input type="text" class="form-control" name="cart_name"
                           placeholder="{{ \Trans::str('cart', 'Názov košíka') }}">

                    <div class="form-check mt-3">
                        <input type="checkbox" class="form-check-input"
                               name="{{ \App\PriceOffers\PriceOffer::WITH_OE_NUMBERS }}" id="checkbox_oe"
                               value="{{ \Buxus\Core\Constants::C_True_Char }}">
                        <label class="form-check-label"
                               for="checkbox_oe">{{ \Trans::str('cart', 'Uložiť s OE kódmi') }}</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default"
                        data-dismiss="modal">{{ \Trans::str('common', 'Zavrieť') }}</button>
                <button type="button" form="cart-save-form" id="cart-save-form-button"
                        class="btn btn-primary">{{ \Trans::str('common', 'Vytvoriť') }}</button>
            </div>
        </div>
    </div>
</div>

@jsBegin
<script type="text/javascript">
    require(['jquery'], function ($) {
        $(function () {
            $('#cart-save-form-button').click(function () {
                fetch('{{ route('cart.download', ['format' => \App\Eshop\Cart\DownloadManager::PDF]) . '?' }}' + new URLSearchParams({
                    {{ \App\PriceOffers\PriceOffer::WITH_OE_NUMBERS }}: $('#checkbox_oe').is(':checked') ? '{{ \Buxus\Core\Constants::C_True_Char }}' : '{{ \Buxus\Core\Constants::C_False_Char }}',
                }))
                    .then(resp => resp.blob())
                    .then(blob => {
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.style.display = 'none';
                        a.href = url;
                        a.download = '{{ Trans::strParamed('cart', 'cenova-ponuka%s', ['.pdf']) }}';
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                    })
                    .catch();

                $.ajax({
                    type: 'POST',
                    url: $("#cart-save-form").attr("action"),
                    data: $("#cart-save-form").serialize(),
                    success: function (response) {
                        $('#cartSaveModal').modal('hide');
                        setTimeout(function () {
                            location.reload();
                            return false;
                        }, 100);
                    },
                });
            });
        });
    });
</script>
@jsEnd
