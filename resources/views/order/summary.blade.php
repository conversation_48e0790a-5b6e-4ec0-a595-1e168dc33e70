<div class="container">
    @include('pdf.partials.header', ['description' => \Trans::strParamed('cart', 'Objednávka č. #%s', [$order->getVariableSymbol()])])
    <div class="row">
        <div class="col-4">
            <p><strong>{{ \Trans::str('cart', 'Faktúra pre') }}:</strong></p>
            <div class="info">
                <p>{{ \Trans::strParamed('common', 'Názov firmy: %s', [$order->getInvoiceCompanyName()]) }}</p>
                <p>{{ \Trans::strParamed('common', 'IČO: %s', [$order->getIco()]) }}</p>
                <p>{{ \Trans::strParamed('common', 'DIČ: %s', [$order->getDic()]) }}</p>
                <p>{{ \Trans::strParamed('common', 'Email: %s', [$order->getEmail()]) }}</p>
                <p>{{ \Trans::strParamed('common', 'Telefón: %s', [$order->getPhone()]) }}</p>
            </div>
        </div>
        <div class="col-3">
            <p><strong>{{ \Trans::str('common', 'Fakturačná adresa') }}:</strong></p>
            <div class="info">
                <p>{{ $order->getInvoiceStreet() }}</p>
                <p>{{ $order->getInvoiceCity() }}</p>
                <p>{{ $order->getInvoiceZip() }}</p>
                <p>{{ $order->getInvoiceCountry() }}</p>
            </div>
        </div>
        <div class="col-3">
            <p><strong>{{ \Trans::str('common', 'Dodacia adresa') }}:</strong></p>
            <div class="info">
                <p>{{ $order->getDeliveryStreet() }}</p>
                <p>{{ $order->getDeliveryCity() }}</p>
                <p>{{ $order->getDeliveryZip() }}</p>
                <p>{{ $order->getDeliveryCountry() }}</p>
            </div>
        </div>
    </div>
    <div class="items-summary">
        @include('order.partials.items-summary', ['items' => $order->getItems()])
    </div>
</div>
