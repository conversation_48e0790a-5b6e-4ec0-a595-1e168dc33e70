<div>
    <div class="ibox" wire:key="{{ $form->form_submit_id }}"
         style="margin: 5px 0 0 0;@if(!$showMore) padding: 0; @endif(!$showMore)">
        <div class="ibox-content">
            <div>
                @if (session()->has('success-' . $form->form_submit_id))
                    <div class="alert alert-success">
                        {{ session('success-' . $form->form_submit_id) }}
                    </div>
                @endif
            </div>
            @if($showMore)
                <div class="table-responsive">
                    <table class="table" style="table-layout:fixed;">
                        <tr class="nohover" wire:click="showMore">
                            <th style="width: 75px;"></th>
                            <th style="width: 75px;">Číslo</th>
                            <th>Čas prijatia vratky</th>
                            <th>Firma</th>
                            <th style="width: 125px;">Telefón</th>
                            <th>Email</th>
                            <th style="width: 125px;">Stav</th>
                            <th><PERSON>as vybavenia vratky</th>
                            <th><PERSON><PERSON> p<PERSON>pravy</th>
                            <th style="width: 50px;"></th>
                        </tr>
                        <tr class="nohover">
                            <td><span class="dot"
                                      style="background-color: {{ $manager->getColorByKey($form->form_state_flag) }};"></span>
                            </td>
                            <td>{{ $form->form_number ?? $form->form_submit_id }}</td>
                            <td>{{ $form->form_submit_time }}</td>
                            @if($form->company_name)
                                <td>{{ $form->company_name }}</td>
                            @else
                                @if($form->webuser instanceof \Buxus\WebUser\Contracts\WebUser)
                                    <td>{{ $form->webuser->getCompanyName() }}</td>
                                @else
                                    <td></td>
                                @endif
                            @endif
                            <td>{{ $form->phone }}</td>
                            <td>{{ $form->email }}</td>
                            <td>{{ $manager->getStateByKey($form->form_state_flag) }}</td>
                            <td>{{ $form->response_time }}</td>
                            <td>{{ $form->latest_update }}</td>
                            <td>
                                @if($showMore)
                                    <span class="clickable cross" wire:click="showMore">x</span>
                                @else
                                    <span class="clickable plus" wire:click="showMore">&plus;</span>
                                @endif
                            </td>
                        </tr>
                        @if($showMore)
                            <tr class="nohover" class="mt-2">
                                <th>Číslo faktúry</th>
                                <th>Interné číslo dielu Rinoparts</th>
                                <th>Originálne číslo dielu</th>
                                <th>Počet kusov na vrátenie</th>
                                <th>Objednať zvoz balíka</th>
                            </tr>
                            <tr class="nohover">
                                <td>{{ $form->invoice_number }}</td>
                                <td>{{ $form->internal_number }}</td>
                                <td>{{ $form->original_number }}</td>
                                <td>{{ $form->count }}</td>
                                <td>{{ $form->order_pickup ?? '-' }}</td>
                            </tr>
                        @endif
                    </table>
                </div>
            @else
                <div>
                    <table style="table-layout:auto;width:100%;">
                        <tr class="nohover" wire:click="showMore">
                            <th style="width: 75px;"></th>
                            <th style="width: 75px;">Číslo</th>
                            <th>Čas prijatia vratky</th>
                            <th>Firma</th>
                            <th style="width: 125px;">Telefón</th>
                            <th>Email</th>
                            <th style="width: 125px;">Stav</th>
                            <th>Čas vybavenia vratky</th>
                            <th>Čas poslednej úpravy</th>
                            <th style="width: 50px;"></th>
                        </tr>
                        <tr class="nohover">
                            <td><span class="dot"
                                      style="background-color: {{ $manager->getColorByKey($form->form_state_flag) }};"></span>
                            </td>
                            <td>{{ $form->form_number ?? $form->form_submit_id }}</td>
                            <td>{{ $form->form_submit_time }}</td>
                            @if($form->company_name)
                                <td>{{ $form->company_name }}</td>
                            @else
                                @if($form->webuser instanceof \Buxus\WebUser\Contracts\WebUser)
                                    <td>{{ $form->webuser->getCompanyName() }}</td>
                                @else
                                    <td></td>
                                @endif
                            @endif
                            <td>{{ $form->phone }}</td>
                            <td>{{ $form->email }}</td>
                            <td>{{ $manager->getStateByKey($form->form_state_flag) }}</td>
                            <td>{{ $form->response_time }}</td>
                            <td>{{ $form->latest_update }}</td>
                            <td>
                                @if($showMore)
                                    <span class="clickable cross" wire:click="showMore">x</span>
                                @else
                                    <span class="clickable plus" wire:click="showMore">&plus;</span>
                                @endif
                            </td>
                        </tr>
                    </table>
                </div>
            @endif
            @if($showMore)
                <div>
                    <div>
                        <div class="d-flex">
                            <input type="hidden" name="form_submit_id" value="{{ $form->form_submit_id }}">
                            <input type="hidden" name="form_tag" value="{{ $this->formTag }}">
                            <div class="w-50 mx-2 my-2">
                                <label for="reason">
                                    Dôvod reklamácie
                                </label>
                                <textarea id="reason" class="form-control reason w-100 h-100"
                                          readonly>{{ $form->reason }}</textarea>
                            </div>
                            <div class="w-50 mx-2 my-2">
                                <label for="response" class="w-100">
                                    Vyjadrenie
                                </label>
                                <textarea name="formResponse" wire:model.defer="formResponse" id="response"
                                          class="form-control h-50"></textarea>

                                <div class="my-2">
                                    <label for="state">
                                        Stav vratky
                                    </label>
                                    <div class="d-flex">
                                        <select name="state" wire:model.defer="formState" id="state"
                                                class="form-control w-100">
                                            @foreach($manager->getFormStates() as $key=>$state)
                                                <option value="{{ $key }}"
                                                        @if($form->form_state_flag == $key)selected @endif>{{ $state }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="my-2">
                                        <div>
                                            <p><strong>Priložené súbory</strong></p>
                                            <div class="d-grid">
                                                @foreach($files as $file)
                                                    <small>
                                                        <span wire:click="removeFile({{ $file->id }})"
                                                              class="clickable cross-file mx-1">x</span><a
                                                            href="{{ route('form.file', ['fileId' => $file->id]) }}">{{ $file->client_original_name }}</a>
                                                    </small>
                                                @endforeach
                                            </div>
                                        </div>

                                        <div class="d-flex">
                                            <div>
                                                <input type="file" multiple wire:model="filesToUpload">
                                                <div wire:loading wire:target="filesToUpload" style="color: red;">
                                                    Nahráva sa
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex">
                                        <div class="ms-auto my-2">
                                            <a href="{{ route('download.returns-form', ['returnsFormId' => $form->form_submit_id]) }}"
                                               target="_blank" class="btn btn-primary">Prejsť na PDF
                                            </a>
                                            <button class="btn btn-primary" wire:click="update">Uložiť</button>
                                            <button class="btn btn-primary" wire:click="updateAndSend">Uložiť a
                                                odoslať
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
