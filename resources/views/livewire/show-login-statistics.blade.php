<div>
    <div class="ibox">
        <div class="ibox-title">
            <h5>Prihlásenia ({{ $logs->total() }})</h5>
        </div>
        <div class="ibox-content">
            <form action="{{ $this->userId
                        ? route('statistics.show-user', ['userId' =>$this->userId ])
                        : route('statistics.show')}}">
                <div class="d-flex">
                    <input class="form-control mx-2" type="date" name="login_from" value="{{ request('login_from') }}">
                    <input class="form-control mx-2" type="date" name="login_to" value="{{ request('login_to') }}">
                    <button class="btn btn-primary" type="submit">Hľadať</button>
                </div>
            </form>

            <table>
                <th>Používateľ</th>
                <th>Čas prihlásenia</th>
                @foreach($logs as $log)
                    <tr>
                        <td><a target="_blank"
                               href="{{ \Buxus\Util\Url::staticUrl('/buxus/lib/authenticate/uif/web_user_details.php?web_user_id='. $log->user_id) }}">{{ $log->company_name ?: $log->user_id }}</a>
                        </td>
                        <td>{{ $log->action_date }}</td>
                    </tr>
                @endforeach
            </table>
            <div class="d-flex">
                <div class="mx-auto">
                    {!! $logs->appends(request()->all())->links() !!}
                </div>
            </div>
        </div>
    </div>
</div>
