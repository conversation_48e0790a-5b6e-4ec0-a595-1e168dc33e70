<div>
    <div class="ibox">
        <div class="ibox-content">
            <table style="width: 50%;">
                <tr>
                    <th></th>
                    <th>Vý<PERSON>b<PERSON></th>
                    <th>Status</th>
                </tr>
                @foreach($producers as $producer)
                    <tr>
                        <td>
                            <x-buxus::forms.button id="pick-delivery-place" class="btn-link" text="X" name="sureToDelete{{ $producer->id }}"
                                                   modal="sureToDeleteModal{{ $producer->id }}" :key="time().$producer->id"/>
                        </td>
                        <td><a href="{{ route('margin-tool-producer.show', ['producer' => $producer->id]) }}">{{ $producer->name }}</a></td>
                        <td>
                            @if($producer->price_levels_on)
                                <p wire:click="changeStatus({{ $producer }})" class="clickable">Cenové hladiny zapnuté<span class="status-icon check">&check;</span></p>
                            @else
                                <p wire:click="changeStatus({{ $producer }})" class="clickable">Cenové hladiny vypnuté<span class="status-icon times">&times;</span></p>
                            @endif
                        </td>
                    </tr>
                    <x-buxus::components.modal wire:ignore.self title="Naozaj chceš zmazať cenové hladiny pre výrobcu {{ $producer->name }}?"
                                               id="sureToDeleteModal{{ $producer->id }}" name="sureToDeleteModal{{ $producer->id }}">
                        <livewire:producer-delete :producer="$producer" :key="time().$producer->id"/>
                    </x-buxus::components.modal>
                @endforeach
            </table>
        </div>
    </div>
</div>
