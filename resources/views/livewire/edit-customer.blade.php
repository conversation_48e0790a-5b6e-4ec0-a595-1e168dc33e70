<div>
    <div>
        @if (session()->has('success'))
            <div class="alert alert-success">
                {{ session('success') }}
            </div>
        @endif
        @if($errors->any())
            <div class="alert alert-danger">
                @foreach($errors->all() as $error)
                    <p>{{ $error }}</p>
                @endforeach
            </div>
        @endif
    </div>

    <div class="ibox">
        <div class="ibox-title">
            <h5>Pridať zákazníkovi koeficient marže</h5>
        </div>
        <div class="ibox-content">
            <form wire:submit.prevent="create">
                <x-buxus::forms.select name="producer" wire:model.lazy="producer" :options="$producers"/>
                <div class="d-flex align-items-center my-3">
                    <p class="px-2"><strong>Koeficient:</strong></p>
                    <x-buxus::forms.number wire:model.lazy="coefficient" name="coefficient"/>
                </div>
                <div class="d-flex">
                    <x-buxus::forms.button text="Pridať" type="submit" class="my-2 ms-auto btn btn-primary"/>
                </div>
            </form>
        </div>
    </div>
</div>
