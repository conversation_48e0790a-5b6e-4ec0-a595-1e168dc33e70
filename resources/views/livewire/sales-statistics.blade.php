<div>
    <div class="ibox">
        <div class="ibox-title">
            <h5>Prehľad</h5>
        </div>
        <div class="ibox-content">
            <div class="my-2">
                <div class="d-flex">
                    <form class="d-flex w-100">
                        <select class="form-control" name="{{ \App\Http\Livewire\SalesStatistics::MONTH_OPTION }}"
                                wire:model.lazy="{{ \App\Http\Livewire\SalesStatistics::MONTH_OPTION }}">
                            <option value="">Celkový prehľad</option>
                            @foreach($months as $month)
                                <option value="{{ $month }}">{{ $month }}</option>
                            @endforeach
                        </select>
                        <input type="date" class="form-control mx-2" name="{{ \App\Http\Livewire\SalesStatistics::DATE_FROM }}"
                               wire:model.lazy="{{ \App\Http\Livewire\SalesStatistics::DATE_FROM }}">
                        <input type="date" class="form-control mx-2" name="{{ \App\Http\Livewire\SalesStatistics::DATE_TO }}"
                               wire:model.lazy="{{ \App\Http\Livewire\SalesStatistics::DATE_TO }}">
                    </form>
                    <button class="btn btn-primary mx-2" wire:click="export">Export</button>
                </div>
            </div>
            <table>
                <th>Meno</th>
                <th>Počet objednávok</th>
                <th>Suma objednávok</th>
                @foreach($data as $item)
                    <tr>
                        <td>{{ $item['webuser_name'] }}</td>
                        <td class="text-right">{{ $item['order_count'] }}</td>
                        <td class="text-right">{{ PriceViewer::formatRawPrice($item['sum'], '€') }}</td>
                    </tr>
                @endforeach
            </table>
        </div>
    </div>
</div>
