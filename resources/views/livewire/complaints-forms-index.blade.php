<div>
    <div>
        <div class="ibox">
            <div class="ibox-title">
                <h5>Preh<PERSON><PERSON> reklamácií</h5>
            </div>
            <div class="ibox-content">
                <form>
                    <div class="d-flex">
                        <input type="text" name="form_number" value="{{ request('form_number') }}"
                               class="form-control mx-2"
                               placeholder="Číslo reklamácie">
                        <input type="text" name="company_name" value="{{ request('company_name') }}"
                               class="form-control mx-2" placeholder="Názov firmy">
                        <input type="text" name="internal_number" value="{{ request('internal_number') }}"
                               class="form-control mx-2" placeholder="Interné číslo dielu">
                        <input type="text" name="original_number" value="{{ request('original_number') }}"
                               class="form-control mx-2" placeholder="Originálne číslo dielu">
                        <select name="form_state_flag" class="form-control mx-2">
                            <option value="">Nezáleží</option>
                            @foreach($manager->getFormStates() as $key => $state)
                                <option
                                        value="{{ $key }}" {{ request('form_state_flag') == $key ? 'selected' : '' }}>{{ $state }}</option>
                            @endforeach
                        </select>
                        <button type="submit" class="btn btn-primary">Hľadať</button>
                        <button type="submit" class="btn btn-primary" form="clear">Vyčistiť</button>
                    </div>
                </form>
                <form id="clear"></form>
            </div>
        </div>

        @foreach($forms as $form)
            <livewire:complaints-form-show wire:key="{{ $form->form_submit_id }}" :formId="$form->form_submit_id"/>
        @endforeach
    </div>

    <div class="d-flex">
        <div class="mx-auto">
            {{ $forms->appends(request()->all())->links() }}
        </div>
    </div>
</div>

<script type="module" >
    require.config({
        paths: {
            nanogallery2: '/buxus/assets/custom_components/nanogallery2/jquery.nanogallery2',
        }
    });

    require(['jquery', 'nanogallery2'], function ($) {
        $('#bazmeg1967').nanogallery2();
        console.log('zrobil som');
    });
</script>
