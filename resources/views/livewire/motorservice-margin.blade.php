<div>
    <div>
        @if (session()->has('success'))
            <div class="alert alert-success">
                {{ session('success') }}
            </div>
        @endif
    </div>

    <div class="ibox">
        <div class="ibox-content">
            <form wire:submit.prevent="create">
                <x-buxus::forms.number name="price_from" wire:model.lazy="price_from" step="0.01" label="Cena od"/>
                @error('price_from') <span class="alert alert-danger">{{ $message }}</span> @enderror
                <x-buxus::forms.number name="margin" wire:model.lazy="margin" step="0.01" label="Marža"/>
                @error('margin') <span class="alert alert-danger">{{ $message }}</span> @enderror
                <x-buxus::forms.number name="margin_eu" wire:model.lazy="margin_eu" step="0.01" label="Marža (EÚ)"/>
                @error('margin_eu') <span class="alert alert-danger">{{ $message }}</span> @enderror
                <div class="d-flex">
                    <div class="ms-auto">
                        <x-buxus::forms.button type="button" wire:click="changeStatus" text="{{ ($producer->price_levels_on ? 'Vypnúť' : 'Zapnúť') . ' cenové hladiny' }}"/>
                        <x-buxus::forms.button type="submit" class="btn btn-primary" text="Pridať cenovú hladinu"/>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
