<div>
    <div class="ibox">
        <div class="ibox-title">
            <h5>Zoznam importov</h5>
        </div>
        <div class="ibox-content">
            <table style="width: 100%;">
                <tr>
                    <th>Výrobca</th>
                    <th>Posledný import</th>
                    <th>Stav</th>
                    <th>Počet záznamov</th>
                    <th>Aktualizovaných záznamov</th>
                    <th><PERSON>ytvoren<PERSON><PERSON> záznamov</th>
                    <th>Zmazaných záznamov</th>
                    <th>Chyby</th>
                </tr>
                @foreach($producers as $producer)
                    <tr>
                        <td class="py-1">
                            {{ $producer['name'] }}
                        </td>
                        <td>
                            {{ $producer['latest'] }}
                        </td>
                        <td>
                            @if($producer['allDone'] == \App\Imports::DONE)
                                <p>Hotovo <span class="status-icon check">&check;</span></p>
                            @elseif($producer['allDone'] == \App\Imports::ERROR)
                                <p>Chyba <span class="status-icon times">&times;</span></p>
                            @else
                                <p>St<PERSON>le prebieha <span class="status-icon circle">&xcirc;</span></p>
                            @endif
                        </td>
                        <td>
                            {{ $producer['items_processed'] }}
                        </td>
                        <td>
                            {{ $producer['updates_processed'] }}
                        </td>
                        <td>
                            {{ $producer['creates_processed'] }}
                        </td>
                        <td>
                            {{ $producer['deletes_processed'] }}
                        </td>
                        <td>
                            {{ $producer['errors'] }}
                        </td>
                    </tr>

                @endforeach
            </table>
        </div>
    </div>
</div>
