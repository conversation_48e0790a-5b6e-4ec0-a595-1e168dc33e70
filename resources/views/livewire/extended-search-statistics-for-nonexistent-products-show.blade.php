<div>
    <div class="ibox">
        <div class="ibox-title">
            <h5>Vyhľadávanie</h5>
        </div>
        <div class="ibox-content">
            <div class="my-2">
                <div class="d-flex">
                    <input type="date" class="form-control mx-2"
                           name="{{ \App\Http\Livewire\ExtendedSearchStatisticsForNonexistentProductsShow::NONEXISTENT_DATE_FROM }}"
                           wire:model="{{ \App\Http\Livewire\ExtendedSearchStatisticsForNonexistentProductsShow::NONEXISTENT_DATE_FROM }}"
                           value="{{ request(\App\Http\Livewire\ExtendedSearchStatisticsForNonexistentProductsShow::NONEXISTENT_DATE_FROM) }}">

                    <input type="date" class="form-control mx-2"
                           name="{{ \App\Http\Livewire\ExtendedSearchStatisticsForNonexistentProductsShow::NONEXISTENT_DATE_TO }}"
                           wire:model="{{ \App\Http\Livewire\ExtendedSearchStatisticsForNonexistentProductsShow::NONEXISTENT_DATE_TO }}"
                           value="{{ request(\App\Http\Livewire\ExtendedSearchStatisticsForNonexistentProductsShow::NONEXISTENT_DATE_TO) }}">

                    <button class="btn btn-primary mx-2">Filtrovať</button>
                    <button class="btn btn-primary mx-2" wire:click="export">Export</button>
                </div>
            </div>
            <table>
                <th>Podľa kódu</th>
                <th>Počet vyhľadávaní</th>
                @foreach($logs as $log)
                    <tr>
                        <td>{{ $log->search_term }}</td>
                        <td>{{ $log->sum }}</td>
                    </tr>
                @endforeach
            </table>
        </div>
        <div class="d-flex">
            <div class="mx-auto">
                {!! $logs->appends(request()->all())->links() !!}
            </div>
        </div>
    </div>
</div>
