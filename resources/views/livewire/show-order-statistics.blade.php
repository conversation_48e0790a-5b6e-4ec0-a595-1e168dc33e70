<div>
    <div class="ibox">
        <div class="ibox-title">
            <h5><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></h5>
        </div>
        <div class="ibox-content">
            <form action="{{ route('statistics.show') }}">
                <div class="d-flex">
                    <input class="form-control mx-2" type="date" name="order_from" value="{{ request('order_from') }}">
                    <input class="form-control mx-2" type="date" name="order_to" value="{{ request('order_to') }}">
                    <button class="btn btn-primary" type="submit">Hľadať</button>
                </div>
            </form>

            <table>
                <th>Používateľ</th>
                <th>Čas prihlásenia</th>
                <th>Počet objednávok</th>
                @foreach($orders as $order)
                    <tr>
                        <td>{{ $order->order_web_user_id }}</td>
                        <td>{{ $order->order_datetime }}</td>
                        <td>{{ $order->count }}</td>
                    </tr>
                @endforeach
            </table>
            <div class="d-flex">
                <div class="mx-auto">
                    {!! $orders->links() !!}
                </div>
            </div>
        </div>
    </div>
</div>
