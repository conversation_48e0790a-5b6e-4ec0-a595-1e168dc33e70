<div>
    <div class="ibox">
        <div class="ibox-title">
            <h5>Vyhľad<PERSON>vanie ({{ $count }})</h5>
        </div>
        <div class="ibox-content">
            <form action="{{ $this->userId
                        ? route('statistics.show-user', ['userId' => $this->userId ])
                        : route('statistics.show')}}">
                <div class="d-flex align-items-baseline">
                    <input class="form-control mx-2 default" type="text" placeholder="Výraz" name="term"
                           value="{{ request('term') }}">
                    <input class="form-control mx-2" type="date" name="search_from"
                           value="{{ request('search_from') }}">
                    <input class="form-control mx-2" type="date" name="search_to" value="{{ request('search_to') }}">
                    <div class="mx-2 d-flex justify-content-between">
                        <input type="checkbox" id="detailed"
                               name="{{ \App\Http\Livewire\ShowSearchStatistics::DETAILED_SEARCH_STATISTICS }}"
                               value="1"
                                {{ request(\App\Http\Livewire\ShowSearchStatistics::DETAILED_SEARCH_STATISTICS) == 1
                                    ? 'checked'
                                    : '' }}>
                        <label class="form-check-label" for="detailed">Detailne</label>
                    </div>
                    <button class="btn btn-primary" type="submit">Hľadať</button>
                </div>
            </form>

            @if(request(\App\Http\Livewire\ShowSearchStatistics::DETAILED_SEARCH_STATISTICS) != 1)
                <table>
                    <th>Používateľ</th>
                    <th>Vyhľadávaný výraz</th>
                    <th>Počet vyhľadávaní</th>
                    @foreach($logs as $log)
                        <tr>
                            <td><a target="_blank"
                                   href="{{ \Buxus\Util\Url::staticUrl('/buxus/lib/authenticate/uif/web_user_details.php?web_user_id='. $log->webuser_id) }}">
                                    {{ \App\WebUser\WebUser::getCachedCompanyName($log->webuser_id) ?: $log->webuser_id }}
                                </a>
                            </td>
                            <td>{{ $log->search_term }}</td>
                            <td>{{ $log->count }}</td>
                        </tr>
                    @endforeach
                </table>
            @else
                <table>
                    <th>Používateľ</th>
                    <th>Vyhľadávaný výraz</th>
                    <th>Čas vyhľadávania</th>
                    @foreach($logs as $log)
                        <tr>
                            <td><a target="_blank"
                                   href="{{ \Buxus\Util\Url::staticUrl('/buxus/lib/authenticate/uif/web_user_details.php?web_user_id='. $log->webuser_id) }}">
                                    {{ \App\WebUser\WebUser::getCachedCompanyName($log->webuser_id) ?: $log->webuser_id }}
                                </a>
                            </td>
                            <td>{{ $log->search_term }}</td>
                            <td>{{ $log->search_time }}</td>
                        </tr>
                    @endforeach
                </table>
            @endif
            <div class="d-flex">
                <div class="mx-auto">
                    {!! $logs->appends(request()->all())->links() !!}
                </div>
            </div>
        </div>
    </div>
</div>
