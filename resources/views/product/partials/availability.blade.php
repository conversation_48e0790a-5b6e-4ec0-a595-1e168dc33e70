@if($product->getStockBalance() > 10)
    <div class="product-list-item-availability">
        <svg class="icon fill-success">
            <use xlink:href="#sprite-tick-circled"></use>
        </svg>
        {{ \Trans::str('dostupnost', 'Viac ako 10 ks') }}

    </div>
@elseif($product->getStockBalance() > 0 && $product->getStockBalance() <= 10)
    <div class="product-list-item-availability">
        <svg class="icon fill-success">
            <use xlink:href="#sprite-tick-circled"></use>
        </svg>
        {{ \Trans::strParamed('dostupnost', '%s ks', [$product->getStockBalance()]) }}
    </div>
@elseif($product->getStockBalance() <= 0)
    @if ($product->getExternalStockBalance() > 10)
        <div class="product-list-item-availability">
            <svg class="icon fill-warning">
                <use xlink:href="#sprite-tick-circled"></use>
            </svg>
            {{ \Trans::str('dostupnost', 'U dodávateľa viac ako 10 ks') }}
        </div>
    @elseif ($product->getExternalStockBalance() > 0 && $product->getExternalStockBalance() <= 10)
        <div class="product-list-item-availability">
            <svg class="icon fill-warning">
                <use xlink:href="#sprite-tick-circled"></use>
            </svg>
            {{ \Trans::strParamed('dostupnost', 'U dodávateľa %s ks', [$product->getExternalStockBalance()]) }}
        </div>
    @elseif($product->getExternalStockBalance() <= 0 && $product->isCeiSupplier())
        <div class="product-list-item-availability">
            <svg class="icon fill-primary">
                <use xlink:href="#sprite-tick-circled"></use>
            </svg>
            {{ \Trans::str('dostupnost', 'Nedostupné') }}
        </div>
    @elseif($product->getExternalStockBalance() <= 0 && $product->isErreviSupplier())
        <div class="product-list-item-availability">
            <svg class="icon fill-warning">
                <use xlink:href="#sprite-tick-circled"></use>
            </svg>
            {{ \Trans::str('dostupnost', 'U dodávateľa') }}
        </div>
    @elseif ($product && $product->isIvecoBigDb() || $product->isIvecoSmallDbStock())
        <div class="product-list-item-availability">
            <svg class="icon fill-warning">
                <use xlink:href="#sprite-tick-circled"></use>
            </svg>
            {{ \Trans::str('eshop', 'U dodávateľa') }}
        </div>
    @elseif ($product->isOnixProduct() && empty($product->getSupplierPage()))
        <div class="product-list-item-availability">
            <svg class="icon fill-primary">
                <use xlink:href="#sprite-tick-circled"></use>
            </svg>
            {{ \Trans::str('eshop', 'Na vyžiadanie') }}
        </div>
    @elseif ($product->isOnixProduct())
        <div class="product-list-item-availability">
            <svg class="icon fill-warning">
                <use xlink:href="#sprite-tick-circled"></use>
            </svg>
            {{ \Trans::str('eshop', 'U dodávateľa') }}
        </div>
    @elseif($product && $product->getExternalStockBalance() !== null && $product->getExternalStockBalance() <= 0)
        <div class="product-list-item-availability">
            <svg class="icon fill-primary">
                <use xlink:href="#sprite-tick-circled"></use>
            </svg>
            {{ \Trans::str('dostupnost', 'Nedostupné') }}
        </div>
    @else
        <div class="product-list-item-availability">
            <svg class="icon fill-primary">
                <use xlink:href="#sprite-tick-circled"></use>
            </svg>
            {{ \Trans::str('eshop', 'Nedostupné') }}
        </div>
    @endif
@endif
