<div class="cart-availability-wrapper">
    @if($total <= 0 && $shouldBeShownAsUnavailable)
        <div class="cart-availability-tag cart-availability-tag-unavailable">
            <div class="availability-state-dot availability-state-unavailable"></div>
            <span>{{ \Trans::str('availability', 'nedostupné') }}</span>
        </div>
    @else
        @if($stock > 0)
            <div class="cart-availability-tag cart-availability-tag-stock">
                @if($supplier > 0)
                    <div class="availability-state-dot availability-state-stock"></div>
                    <span>{{ \Trans::strParamed('availability', '%s ks - skladom', [$stock]) }}</span>
                @else
                    <div class="availability-state-dot availability-state-stock"></div>
                    <span>{{ \Trans::str('availability', 'skladom') }}</span>
                @endif
            </div>
        @endif
        @if($supplier > 0)
            <div class="cart-availability-tag cart-availability-tag-supplier">
                @if($stock > 0)
                    <div class="availability-state-dot availability-state-supplier"></div>
                    <span>{{ \Trans::strParamed('availability', '%s ks - na objednávku', [$supplier]) }}</span>
                @else
                    <div class="availability-state-dot availability-state-supplier"></div>
                    <span>{{ \Trans::str('availability', 'na objednávku') }}</span>
                @endif
            </div>
        @endif
    @endif
    <p>(objednané&nbsp;<strong>{{ $amount }}&nbsp;</strong>ks
        / &nbsp;<strong>{{ $stock_actual }}</strong>&nbsp;ks skladom)</p>
</div>