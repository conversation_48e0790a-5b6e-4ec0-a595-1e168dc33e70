@if($total <= 0 && $shouldBeShownAsUnavailable)
    <div class="cart-availability-tag cart-availability-tag-unavailable">
        <div class="availability-state-dot availability-state-unavailable"></div>
        <span>{{ \Trans::str('availability', 'nedostupné') }}</span>
    </div>
@else
    @if($stock > 0)
        <div class="cart-availability-tag cart-availability-tag-stock">
            @if($supplier > 0)
                <div class="availability-state-dot availability-state-stock"></div>
                <span>{{ \Trans::strParamed('availability', '%s ks - skladom', [$stock]) }}</span>
            @else
                <div class="availability-state-dot availability-state-stock"></div>
                <span>{{ \Trans::str('availability', 'skladom') }}</span>
            @endif
        </div>
    @endif
    @if($supplier > 0 && $availability == \App\Eshop\Product::AVAILABILITY_AT_REQUEST)
        <div class="cart-availability-tag cart-availability-tag-unavailable">
            <div class="availability-state-dot availability-state-unavailable"></div>
            <span>{{ \Trans::str('availability', 'na vyžiadanie') }}</span>
        </div>
    @elseif($supplier > 0 && $availability != \App\Eshop\Product::AVAILABILITY_AT_REQUEST)
        <div class="cart-availability-tag cart-availability-tag-supplier">
            @if($stock > 0)
                <div class="availability-state-dot availability-state-supplier"></div>
                <span>{{ \Trans::strParamed('availability', '%s ks - na objednávku', [$supplier]) }}</span>
            @elseif($availability == \App\Eshop\Product::AVAILABILITY_AT_REQUEST)
                <div class="availability-state-dot availability-state-supplier"></div>
                <span>{{ \Trans::str('availability', 'na vyžiadanie') }}</span>
            @else
                <div class="availability-state-dot availability-state-supplier"></div>
                <span>{{ \Trans::str('availability', 'na objednávku') }}</span>
            @endif
        </div>
    @endif
@endif