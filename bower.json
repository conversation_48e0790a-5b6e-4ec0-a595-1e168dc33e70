{"name": "BUXUS krabica", "version": "0.0.1", "authors": ["ui42 s.r.o."], "moduleType": ["amd"], "license": "MIT", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"], "dependencies": {"requirejs": "~2.1.16", "requirejs-domready": "*", "jquery": "~1", "bootstrap": "^3.3.1", "blueimp-bootstrap-image-gallery": "3.1.1", "jquery-placeholder": "*", "jquery-cookie": "*", "bootstrap-touchspin": "3.1.2", "loadcss": "*", "blockui": "*", "requirejs-text": "^2.0.14", "jgrowl": "*", "seiyria-bootstrap-slider": "~5.3.7", "history.js": "*", "jquery.scrollTo": "~1.4.14"}}