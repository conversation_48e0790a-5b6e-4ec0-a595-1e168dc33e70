<?xml version="1.0" encoding="utf-8"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" data-d2-version="v0.7.0" preserveAspectRatio="xMinYMin meet" viewBox="0 0 2206 905"><svg class="d2-3275039434 d2-svg" width="2206" height="905" viewBox="-101 -101 2206 905"><rect x="-101.000000" y="-101.000000" width="2206.000000" height="905.000000" rx="0.000000" fill="#FFFFFF" class=" fill-N7" stroke-width="0" /><style type="text/css"><![CDATA[
.d2-3275039434 .text-bold {
	font-family: "d2-3275039434-font-bold";
}
@font-face {
	font-family: d2-3275039434-font-bold;
	src: url("data:application/font-woff;base64,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");
}
.d2-3275039434 .text-italic {
	font-family: "d2-3275039434-font-italic";
}
@font-face {
	font-family: d2-3275039434-font-italic;
	src: url("data:application/font-woff;base64,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");
}]]></style><style type="text/css"><![CDATA[.shape {
  shape-rendering: geometricPrecision;
  stroke-linejoin: round;
}
.connection {
  stroke-linecap: round;
  stroke-linejoin: round;
}
.blend {
  mix-blend-mode: multiply;
  opacity: 0.5;
}

		.d2-3275039434 .fill-N1{fill:#0A0F25;}
		.d2-3275039434 .fill-N2{fill:#676C7E;}
		.d2-3275039434 .fill-N3{fill:#9499AB;}
		.d2-3275039434 .fill-N4{fill:#CFD2DD;}
		.d2-3275039434 .fill-N5{fill:#DEE1EB;}
		.d2-3275039434 .fill-N6{fill:#EEF1F8;}
		.d2-3275039434 .fill-N7{fill:#FFFFFF;}
		.d2-3275039434 .fill-B1{fill:#0D32B2;}
		.d2-3275039434 .fill-B2{fill:#0D32B2;}
		.d2-3275039434 .fill-B3{fill:#E3E9FD;}
		.d2-3275039434 .fill-B4{fill:#E3E9FD;}
		.d2-3275039434 .fill-B5{fill:#EDF0FD;}
		.d2-3275039434 .fill-B6{fill:#F7F8FE;}
		.d2-3275039434 .fill-AA2{fill:#4A6FF3;}
		.d2-3275039434 .fill-AA4{fill:#EDF0FD;}
		.d2-3275039434 .fill-AA5{fill:#F7F8FE;}
		.d2-3275039434 .fill-AB4{fill:#EDF0FD;}
		.d2-3275039434 .fill-AB5{fill:#F7F8FE;}
		.d2-3275039434 .stroke-N1{stroke:#0A0F25;}
		.d2-3275039434 .stroke-N2{stroke:#676C7E;}
		.d2-3275039434 .stroke-N3{stroke:#9499AB;}
		.d2-3275039434 .stroke-N4{stroke:#CFD2DD;}
		.d2-3275039434 .stroke-N5{stroke:#DEE1EB;}
		.d2-3275039434 .stroke-N6{stroke:#EEF1F8;}
		.d2-3275039434 .stroke-N7{stroke:#FFFFFF;}
		.d2-3275039434 .stroke-B1{stroke:#0D32B2;}
		.d2-3275039434 .stroke-B2{stroke:#0D32B2;}
		.d2-3275039434 .stroke-B3{stroke:#E3E9FD;}
		.d2-3275039434 .stroke-B4{stroke:#E3E9FD;}
		.d2-3275039434 .stroke-B5{stroke:#EDF0FD;}
		.d2-3275039434 .stroke-B6{stroke:#F7F8FE;}
		.d2-3275039434 .stroke-AA2{stroke:#4A6FF3;}
		.d2-3275039434 .stroke-AA4{stroke:#EDF0FD;}
		.d2-3275039434 .stroke-AA5{stroke:#F7F8FE;}
		.d2-3275039434 .stroke-AB4{stroke:#EDF0FD;}
		.d2-3275039434 .stroke-AB5{stroke:#F7F8FE;}
		.d2-3275039434 .background-color-N1{background-color:#0A0F25;}
		.d2-3275039434 .background-color-N2{background-color:#676C7E;}
		.d2-3275039434 .background-color-N3{background-color:#9499AB;}
		.d2-3275039434 .background-color-N4{background-color:#CFD2DD;}
		.d2-3275039434 .background-color-N5{background-color:#DEE1EB;}
		.d2-3275039434 .background-color-N6{background-color:#EEF1F8;}
		.d2-3275039434 .background-color-N7{background-color:#FFFFFF;}
		.d2-3275039434 .background-color-B1{background-color:#0D32B2;}
		.d2-3275039434 .background-color-B2{background-color:#0D32B2;}
		.d2-3275039434 .background-color-B3{background-color:#E3E9FD;}
		.d2-3275039434 .background-color-B4{background-color:#E3E9FD;}
		.d2-3275039434 .background-color-B5{background-color:#EDF0FD;}
		.d2-3275039434 .background-color-B6{background-color:#F7F8FE;}
		.d2-3275039434 .background-color-AA2{background-color:#4A6FF3;}
		.d2-3275039434 .background-color-AA4{background-color:#EDF0FD;}
		.d2-3275039434 .background-color-AA5{background-color:#F7F8FE;}
		.d2-3275039434 .background-color-AB4{background-color:#EDF0FD;}
		.d2-3275039434 .background-color-AB5{background-color:#F7F8FE;}
		.d2-3275039434 .color-N1{color:#0A0F25;}
		.d2-3275039434 .color-N2{color:#676C7E;}
		.d2-3275039434 .color-N3{color:#9499AB;}
		.d2-3275039434 .color-N4{color:#CFD2DD;}
		.d2-3275039434 .color-N5{color:#DEE1EB;}
		.d2-3275039434 .color-N6{color:#EEF1F8;}
		.d2-3275039434 .color-N7{color:#FFFFFF;}
		.d2-3275039434 .color-B1{color:#0D32B2;}
		.d2-3275039434 .color-B2{color:#0D32B2;}
		.d2-3275039434 .color-B3{color:#E3E9FD;}
		.d2-3275039434 .color-B4{color:#E3E9FD;}
		.d2-3275039434 .color-B5{color:#EDF0FD;}
		.d2-3275039434 .color-B6{color:#F7F8FE;}
		.d2-3275039434 .color-AA2{color:#4A6FF3;}
		.d2-3275039434 .color-AA4{color:#EDF0FD;}
		.d2-3275039434 .color-AA5{color:#F7F8FE;}
		.d2-3275039434 .color-AB4{color:#EDF0FD;}
		.d2-3275039434 .color-AB5{color:#F7F8FE;}.appendix text.text{fill:#0A0F25}.md{--color-fg-default:#0A0F25;--color-fg-muted:#676C7E;--color-fg-subtle:#9499AB;--color-canvas-default:#FFFFFF;--color-canvas-subtle:#EEF1F8;--color-border-default:#0D32B2;--color-border-muted:#0D32B2;--color-neutral-muted:#EEF1F8;--color-accent-fg:#0D32B2;--color-accent-emphasis:#0D32B2;--color-attention-subtle:#676C7E;--color-danger-fg:red;}.sketch-overlay-B1{fill:url(#streaks-darker-d2-3275039434);mix-blend-mode:lighten}.sketch-overlay-B2{fill:url(#streaks-darker-d2-3275039434);mix-blend-mode:lighten}.sketch-overlay-B3{fill:url(#streaks-bright-d2-3275039434);mix-blend-mode:darken}.sketch-overlay-B4{fill:url(#streaks-bright-d2-3275039434);mix-blend-mode:darken}.sketch-overlay-B5{fill:url(#streaks-bright-d2-3275039434);mix-blend-mode:darken}.sketch-overlay-B6{fill:url(#streaks-bright-d2-3275039434);mix-blend-mode:darken}.sketch-overlay-AA2{fill:url(#streaks-dark-d2-3275039434);mix-blend-mode:overlay}.sketch-overlay-AA4{fill:url(#streaks-bright-d2-3275039434);mix-blend-mode:darken}.sketch-overlay-AA5{fill:url(#streaks-bright-d2-3275039434);mix-blend-mode:darken}.sketch-overlay-AB4{fill:url(#streaks-bright-d2-3275039434);mix-blend-mode:darken}.sketch-overlay-AB5{fill:url(#streaks-bright-d2-3275039434);mix-blend-mode:darken}.sketch-overlay-N1{fill:url(#streaks-darker-d2-3275039434);mix-blend-mode:lighten}.sketch-overlay-N2{fill:url(#streaks-dark-d2-3275039434);mix-blend-mode:overlay}.sketch-overlay-N3{fill:url(#streaks-normal-d2-3275039434);mix-blend-mode:color-burn}.sketch-overlay-N4{fill:url(#streaks-normal-d2-3275039434);mix-blend-mode:color-burn}.sketch-overlay-N5{fill:url(#streaks-bright-d2-3275039434);mix-blend-mode:darken}.sketch-overlay-N6{fill:url(#streaks-bright-d2-3275039434);mix-blend-mode:darken}.sketch-overlay-N7{fill:url(#streaks-bright-d2-3275039434);mix-blend-mode:darken}.light-code{display: block}.dark-code{display: none}]]></style><g class="dGl0bGU="><g class="shape" ><rect x="474.000000" y="9.000000" width="397.000000" height="66.000000" stroke="#0D32B2" fill="#F7F8FE" class=" stroke-B1 fill-B6" style="stroke-width:2;" /></g><text x="672.500000" y="47.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Laravel Application Entry Point - public/index.php</text></g><g class="YnJvd3Nlcg=="><g class="shape" ><path d="M 961 29 C 961 30 960 31 959 31 C 943 32 931 43 931 57 C 931 72 945 84 962 84 H 1081 C 1100 84 1115 71 1115 56 C 1115 41 1101 29 1083 28 C 1082 28 1081 27 1080 26 C 1076 11 1058 -0 1036 -0 C 1022 0 1009 5 1001 12 C 1000 13 999 13 998 13 C 995 12 992 12 988 12 C 974 12 963 19 961 29 Z" stroke="#0D32B2" fill="#e0f2f1" class=" stroke-B1" style="stroke-width:2;" /></g><text x="1022.348000" y="63.516000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Client Browser</text></g><g class="aW5kZXhfcGhw"><g class="shape" ><rect x="940.000000" y="205.000000" width="165.000000" height="82.000000" stroke="#ff9800" fill="#fff3e0" style="stroke-width:3;" /></g><text x="1022.500000" y="243.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px"><tspan x="1022.500000" dy="0.000000">public/index.php</tspan><tspan x="1022.500000" dy="18.500000">(Entry Point)</tspan></text></g><g class="bGFyYXZlbF9zdGFydA=="><g class="shape" ><ellipse rx="135.000000" ry="45.000000" cx="135.000000" cy="453.000000" stroke="#0D32B2" fill="#e8f5e8" class="shape stroke-B1" style="stroke-width:2;" /></g><text x="135.000000" y="450.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px"><tspan x="135.000000" dy="0.000000">LARAVEL_START</tspan><tspan x="135.000000" dy="18.500000">(Performance Timer)</tspan></text></g><g class="dmVuZG9yX2F1dG9sb2Fk"><g class="shape" ><path d="M 330 480 L 330 415 L 527 415 L 527 480 C 494 466 461 466 429 480 C 396 495 363 495 330 480 Z" stroke="#0D32B2" fill="#f3e5f5" class=" stroke-B1" style="stroke-width:2;" /></g><text x="428.500000" y="448.610964" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">vendor/autoload.php</text></g><g class="Y29tcG9zZXI="><g class="shape" ><path d="M 329 625 L 429 625 L 429 640 L 529 640 L 529 698 L 329 698 Z" stroke="#0D32B2" fill="#e1f5fe" class=" stroke-B1" style="stroke-width:2;" /></g><text x="429.000000" y="674.300000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Composer Autoloader</text></g><g class="Ym9vdHN0cmFwX2FwcA=="><g class="shape" ><path d="M 676 480 L 676 415 L 855 415 L 855 480 C 825 466 795 466 766 480 C 736 495 706 495 676 480 Z" stroke="#0D32B2" fill="#f3e5f5" class=" stroke-B1" style="stroke-width:2;" /></g><text x="765.500000" y="448.610964" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">bootstrap/app.php</text></g><g class="YXBwX2luc3RhbmNl"><g class="shape" ><path d="M 677 627 L 589 661 L 677 696 L 854 696 L 942 661 L 854 627 Z" stroke="#0D32B2" fill="#e3f2fd" class=" stroke-B1" style="stroke-width:2;" /></g><text x="765.500000" y="667.000000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Buxus\Core\BuxusApplication</text></g><g class="a2VybmVsX2luc3RhbmNl"><g class="shape" ><path d="M 969 419 L 915 453 L 969 488 L 1077 488 L 1131 453 L 1077 419 Z" stroke="#0D32B2" fill="#e3f2fd" class=" stroke-B1" style="stroke-width:2;" /></g><text x="1023.000000" y="459.000000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">App\Http\Kernel</text></g><g class="cmVxdWVzdA=="><g class="shape" ><path d="M 1217 420 L 1537 420 L 1511 486 L 1191 486 L 1191 486 Z" stroke="#0D32B2" fill="#f1f8e9" class=" stroke-B1" style="stroke-width:2;" /></g><text x="1364.000000" y="458.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Illuminate\Http\Request::capture()</text></g><g class="cmVzcG9uc2U="><g class="shape" ><path d="M 1286 627 L 1208 661 L 1286 696 L 1442 696 L 1520 661 L 1442 627 Z" stroke="#0D32B2" fill="#e3f2fd" class=" stroke-B1" style="stroke-width:2;" /></g><text x="1364.000000" y="667.000000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Illuminate\Http\Response</text></g><g class="cmVzcG9uc2Vfc2VuZA=="><g class="shape" ><path d="M 1623 420 L 1825 420 L 1799 486 L 1597 486 L 1597 486 Z" stroke="#0D32B2" fill="#f1f8e9" class=" stroke-B1" style="stroke-width:2;" /></g><text x="1711.000000" y="458.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">$response-&gt;send()</text></g><g class="YnJvd3Nlcl9vdXRwdXQ="><g class="shape" ><path d="M 1649 648 C 1649 649 1648 650 1647 650 C 1631 651 1619 662 1619 676 C 1619 691 1633 703 1650 703 H 1769 C 1788 703 1803 690 1803 675 C 1803 660 1789 648 1771 647 C 1770 647 1769 646 1768 645 C 1764 630 1746 619 1724 619 C 1710 619 1697 624 1689 631 C 1688 632 1687 632 1686 632 C 1683 631 1680 631 1676 631 C 1662 631 1651 638 1649 648 Z" stroke="#0D32B2" fill="#e0f2f1" class=" stroke-B1" style="stroke-width:2;" /></g><text x="1710.348000" y="682.516000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Client Browser</text></g><g class="dGVybWluYXRl"><g class="shape" ><rect x="1885.000000" y="420.000000" width="119.000000" height="66.000000" stroke="#0D32B2" fill="#fce4ec" class=" stroke-B1" style="stroke-width:2;" /></g><text x="1944.500000" y="458.500000" fill="#0A0F25" class="text-bold fill-N1" style="text-anchor:middle;font-size:16px">Terminate</text></g><g class="KGJyb3dzZXIgLSZndDsgaW5kZXhfcGhwKVswXQ=="><marker id="mk-d2-3275039434-3488378134" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" fill="#0D32B2" class="connection fill-B1" stroke-width="2" /> </marker><path d="M 1022.983471 85.999932 C 1022.599976 132.399994 1022.500000 156.699997 1022.500000 201.000000" stroke="#0D32B2" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-d2-3275039434-3488378134)" mask="url(#d2-3275039434)" /><text x="1022.500000" y="150.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">HTTP Request</text></g><g class="KGluZGV4X3BocCAtJmd0OyBsYXJhdmVsX3N0YXJ0KVswXQ=="><path d="M 937.516168 255.783867 C 296.000000 329.100006 135.000000 359.600006 135.000000 404.000000" stroke="#0D32B2" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-d2-3275039434-3488378134)" mask="url(#d2-3275039434)" /><text x="512.500000" y="310.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">define timer</text></g><g class="KGluZGV4X3BocCAtJmd0OyB2ZW5kb3JfYXV0b2xvYWQpWzBd"><path d="M 938.035329 260.918802 C 530.900024 330.100006 428.600006 361.000000 428.970372 411.000110" stroke="#0D32B2" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-d2-3275039434-3488378134)" mask="url(#d2-3275039434)" /><text x="656.500000" y="314.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">autoloader</text></g><g class="KHZlbmRvcl9hdXRvbG9hZCAtJmd0OyBjb21wb3NlcilbMF0="><path d="M 428.987262 481.999959 C 428.600006 542.799988 428.600006 571.799988 428.969926 621.000113" stroke="#0D32B2" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-d2-3275039434-3488378134)" mask="url(#d2-3275039434)" /></g><g class="KGluZGV4X3BocCAtJmd0OyBib290c3RyYXBfYXBwKVswXQ=="><path d="M 937.674254 279.417009 C 800.000000 333.700012 765.000000 361.000000 765.000000 411.000000" stroke="#0D32B2" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-d2-3275039434-3488378134)" mask="url(#d2-3275039434)" /><text x="824.000000" y="330.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">make app</text></g><g class="KGJvb3RzdHJhcF9hcHAgLSZndDsgYXBwX2luc3RhbmNlKVswXQ=="><path d="M 765.000000 482.000000 C 765.000000 542.799988 765.000000 572.200012 765.000000 623.000000" stroke="#0D32B2" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-d2-3275039434-3488378134)" mask="url(#d2-3275039434)" /><text x="765.000000" y="559.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">returns</text></g><g class="KGluZGV4X3BocCAtJmd0OyBrZXJuZWxfaW5zdGFuY2UpWzBd"><path d="M 1022.500000 289.000000 C 1022.500000 335.299988 1022.400024 361.799988 1022.027973 415.000098" stroke="#0D32B2" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-d2-3275039434-3488378134)" mask="url(#d2-3275039434)" /><text x="1022.500000" y="358.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">make kernel</text></g><g class="KGluZGV4X3BocCAtJmd0OyByZXF1ZXN0KVswXQ=="><path d="M 1106.896341 271.212426 C 1311.699951 332.100006 1363.599976 362.000000 1363.972413 416.000095" stroke="#0D32B2" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-d2-3275039434-3488378134)" mask="url(#d2-3275039434)" /><text x="1265.000000" y="324.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">handle HTTP request</text></g><g class="KHJlcXVlc3QgLSZndDsgcmVzcG9uc2UpWzBd"><path d="M 1363.013794 487.999952 C 1363.400024 544.000000 1363.400024 572.200012 1363.029198 623.000107" stroke="#0D32B2" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-d2-3275039434-3488378134)" mask="url(#d2-3275039434)" /><text x="1363.000000" y="562.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">returns</text></g><g class="KGluZGV4X3BocCAtJmd0OyByZXNwb25zZV9zZW5kKVswXQ=="><path d="M 1106.973467 258.863265 C 1589.300049 329.700012 1710.599976 362.000000 1710.972413 416.000095" stroke="#0D32B2" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-d2-3275039434-3488378134)" mask="url(#d2-3275039434)" /><text x="1437.500000" y="313.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">send response</text></g><g class="KHJlc3BvbnNlX3NlbmQgLSZndDsgYnJvd3Nlcl9vdXRwdXQpWzBd"><path d="M 1710.013794 487.999952 C 1710.400024 544.000000 1710.599976 571.000000 1710.967999 617.000128" stroke="#0D32B2" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-d2-3275039434-3488378134)" mask="url(#d2-3275039434)" /><text x="1710.000000" y="559.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">HTTP response</text></g><g class="KGluZGV4X3BocCAtJmd0OyB0ZXJtaW5hdGUpWzBd"><path d="M 1107.485104 255.772503 C 1776.199951 329.100006 1944.000000 362.100006 1944.000000 416.500000" stroke="#0D32B2" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-d2-3275039434-3488378134)" mask="url(#d2-3275039434)" /><text x="1555.000000" y="310.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">terminate</text></g><mask id="d2-3275039434" maskUnits="userSpaceOnUse" x="-101" y="-101" width="2206" height="905">
<rect x="-101" y="-101" width="2206" height="905" fill="white"></rect>
<rect x="494.500000" y="31.500000" width="356" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="967.848000" y="47.516000" width="109" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="960.500000" y="227.500000" width="124" height="37" fill="rgba(0,0,0,0.75)"></rect>
<rect x="59.500000" y="434.500000" width="151" height="37" fill="rgba(0,0,0,0.75)"></rect>
<rect x="350.500000" y="432.610964" width="156" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="349.500000" y="658.300000" width="159" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="696.500000" y="432.610964" width="138" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="658.500000" y="651.000000" width="214" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="961.500000" y="443.000000" width="123" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1237.500000" y="442.500000" width="253" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1270.500000" y="651.000000" width="187" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1643.500000" y="442.500000" width="135" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1655.848000" y="666.516000" width="109" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1905.500000" y="442.500000" width="78" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="974.000000" y="134.000000" width="97" height="21" fill="black"></rect>
<rect x="470.000000" y="294.000000" width="85" height="21" fill="black"></rect>
<rect x="617.000000" y="298.000000" width="79" height="21" fill="black"></rect>
<rect x="789.000000" y="314.000000" width="70" height="21" fill="black"></rect>
<rect x="739.000000" y="543.000000" width="52" height="21" fill="black"></rect>
<rect x="980.000000" y="342.000000" width="85" height="21" fill="black"></rect>
<rect x="1194.000000" y="308.000000" width="142" height="21" fill="black"></rect>
<rect x="1337.000000" y="546.000000" width="52" height="21" fill="black"></rect>
<rect x="1388.000000" y="297.000000" width="99" height="21" fill="black"></rect>
<rect x="1659.000000" y="543.000000" width="102" height="21" fill="black"></rect>
<rect x="1520.000000" y="294.000000" width="70" height="21" fill="black"></rect>
</mask></svg></svg>
