define(['jquery', 'app', 'swiper'], function ($, app, Swiper) {
    let carouselModule = {
        init: function (scopeElement) {
            let scope = (scopeElement && scopeElement.length) ? scopeElement : $(document)

            $(function () {
                scope.find('.js-cover-carousel:not(.js-cover-carousel-initialized)').each(function () {
                    $(this).addClass('js-cover-carousel-initialized')
                    const carousel = $(this)
                    const carouselWrapper = carousel.closest('.swiper-widget')
                    const navPrevEl = carouselWrapper.find('.swiper-button-prev').get(0)
                    const navNextEl = carouselWrapper.find('.swiper-button-next').get(0)
                    const paginationEl = carouselWrapper.find('.swiper-pagination').get(0)
                    new Swiper(carousel.get(0), {
                        spaceBetween: 5,
                        loopedSlides: 4,
                        loop: true,
                        centeredSlides: true,
                        touch: true,
                        breakpoints: {
                            // when window width is >= 320px
                            320: {
                                slidesPerView: 1,
                            },
                            640: {
                                sliderPerView: 1.5,
                            },
                            960: {
                                slidesPerView: 1.7,
                            },
                            1080: {
                                slidesPerView: 2,
                            },
                            1200: {
                                slidesPerView: 2.1,
                            },
                            1280: {
                                slidesPerView: 2.2,
                            },
                            1400: {
                                slidesPerView: 2.5,
                            },
                            1600: {
                                slidesPerView: 2.7,
                            },
                            1800: {
                                slidesPerView: 3.2,
                            }
                        },
                        navigation: {
                            nextEl: navNextEl,
                            prevEl: navPrevEl,
                        },
                        pagination: {
                            el: paginationEl,
                            type: 'bullets',
                            clickable: true,
                            renderBullet: function (index, className) {
                                return '<span class="' + className + '"></span>';
                            },
                        },
                    })
                })
            })
        }
    }
    carouselModule.init()

    return carouselModule
})
