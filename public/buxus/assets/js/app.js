define(['jquery', 'fly-to-element', 'csrf', 'rinoparts', 'rinoparts-detail'], function (jQuery, fly) {
    $(function ($) {
        $(document).on('eshop.command', function (e, command, payload, element) {
            if (command === 'product-add') {
                let product_image = $('#product-detail-gallery').find('img');

                if (product_image.length === 0) {
                    let images = $('.tag-main-product-images').find('img');
                    if (images.length >= 0) {
                        product_image = images.eq(0);
                    }
                }

                if (product_image.length === 0) {
                    console.log(payload);
                    product_image = $('[data-page-id=' + payload.tracker.id + ']').find('img');
                }

                if (product_image.length === 0) {
                    return;
                }

                if ($(window).width() < 992) {
                    fly(product_image, $('#main-nav-toggle-mobile'));
                } else {
                    fly(product_image, $('.site-settings .shopping-cart'));
                }
            }
        })

        $(document).on('mouseover', '.has-notification td', function (e) {
            e.preventDefault();
            var $this = $(this);
            var itemId = $(this).closest('tr').data('item-id');
            var type = '';

            if($('.fake-login-header').length && $('.fake-login-header').data('superuser-id') != 0) {
                return;
            }

            if($(this).closest('table').hasClass('order-table')) {
                type = 'order';
            } else if($(this).closest('table').hasClass('return-table')) {
                type = 'return';
            } else if($(this).closest('table').hasClass('complaint-table')) {
                type = 'complaint';
            } else if($(this).closest('table').hasClass('invoice-table')) {
                type = 'invoice';
            } else if($(this).closest('table').hasClass('credit-note-table')) {
                type = 'credit_note';
            } else if($(this).closest('table').hasClass('delivery-note-table')) {
                type = 'delivery_note';
            }

            $.ajax({
                url: '/notification-mark-as-read',
                type: 'POST',
                data: {
                    item_id: itemId,
                    item_type: type
                },
                success: function (response) {
                    if (response.result === 'success') {
                        if(!response.has_notification) {
                            $this.closest('.has-notification').removeClass('has-notification');
                        }

                        Object.entries(response.notifications).forEach(function (entry) {
                            const [type, count] = entry;
                            if (count > 0) {
                                $('.notification-count-' + type).text(count);
                                $('.notification-count-' + type).show();
                            } else {
                                $('.notification-count-' + type).text('');
                                $('.notification-count-' + type).hide();
                            }
                        });
                    }
                },
            });
        });

    });
});
