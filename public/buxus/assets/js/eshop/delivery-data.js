define(['jquery', 'requirejs-domready'], function ($, domReady) {
    $('.modern-input__input').bind('keyup input', function (e) {
        if ($(this).val() != '') {
            $(this).parent().addClass('modern-input__label--filled');
        } else {
            $(this).parent().removeClass('modern-input__label--filled');
        }
    })

    $('.modern-input__textarea').bind('keyup input focus', function (e) {
        $(this).parent().addClass('modern-input__label--filled');
    })

    $('.modern-input__text').bind('click', function (e) {
        $(this).parent().addClass('modern-input__label--filled');
        $(this).next().focus();
    })

    domReady(function () {
        // Hide company fields
        $("input[name='customer_type']").change(function () {
            if ($("input:checked[name='customer_type']").val() == "person") { // It is a person
                $(".eshop-person-fields").show();
                $(".eshop-body-corporate-fields").hide();
            } else { // It is a company
                $(".eshop-person-fields").hide();
                $(".eshop-body-corporate-fields").show();
            }
        }).change();
        $("input[name='customer_type']").click(function () {
            $("input[name='customer_type']").change();
        });

        // Hide delivery address fields
        $("input[name='delivery_address_choice']").change(function () {
            if ($("input:checked[name='delivery_address_choice']").val() != "different") { // The addresses are identical
                $(".eshop-delivery-address").hide();
            } else {
                $(".eshop-delivery-address").show();
            }
        }).change();

        $("input[name='delivery_address_choice']").click(function () {
            $("input[name='delivery_address_choice']").change();
        });
    });
});
