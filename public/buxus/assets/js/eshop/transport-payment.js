define(['jquery', 'requirejs-domready'], function ($, domReady) {
    domReady(function () {
        var $radioButtons = $('input[type="radio"].bt-cart-tp-radio');
        $radioButtons.click(function () {
            $radioButtons.each(function () {
                $(this).parents('li').toggleClass('active', this.checked);
            });
        });

        $("input[name='transport_type']").change(function () {
            $("input[name='payment_type']").parents("li").show();

            $("input[name='payment_type']").each(function () {
                $(this).parents('li').addClass('disabled');
                $(this).attr('disabled', 'disabled');
            });

            var $selected_payment_types = $(".transport_type_payments-" + $("input:checked[name='transport_type']").val());
            if ($selected_payment_types.length > 0) {
                var selected_payment_types = $selected_payment_types.val().split(";");
                for (var i = 0; i < selected_payment_types.length; i++) {
                    $("input[name='payment_type'][value='" + selected_payment_types[i] + "']").parents("li").removeClass('disabled');
                    $("input[name='payment_type'][value='" + selected_payment_types[i] + "']").removeAttr('disabled');
                }
            }
            $("input[name='payment_type'][disabled]:checked").attr('checked', false);

        }).change();

        $("input[name='transport_type']").click(function () {
            $("input[name='transport_type']").change();
        });
    });
});