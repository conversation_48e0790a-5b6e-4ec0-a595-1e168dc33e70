define(['jquery'], function ($) {
    var eshop = {
        contexts: [],

        pushContext: function (context) {
            eshop.contexts.push(context);
        },

        popContext: function () {
            eshop.contexts.pop();
        },

        setContexts: function (contexts) {
            eshop.contexts = contexts;
        },

        command: function (element, url, data, callback, error_callback) {
            if (typeof data != 'undefined') {
                data.contexts = eshop.contexts;
                var pars = $.param(data);
                if (pars != '') {
                    url += '&' + pars;
                }
            }
            require(['ui42-block'], function (block) {
                block.block();
                $.ajax(url, {
                    type: 'GET',
                    dataType: 'json',
                    success: function (data) {

                        if (data.result == "OK") {
                            if (typeof callback == 'function') {
                                callback.apply(eshop, [data.payload]);
                            }
                            if (data.redirect) {
                                window.location = data.redirect;
                                return;
                            }
                            if (data.reload) {
                                window.location.reload();
                                return;
                            }
                            $(document).trigger('eshop.command', [data.command, data.payload, element]);
                        } else {
                            if (typeof error_callback == 'function') {
                                error_callback.apply(eshop, [data]);
                            }
                        }
                        block.unblock();
                    },
                    error: function () {
                        if (typeof error_callback == 'function') {
                            error_callback.apply(eshop, [data]);
                        }
                        block.unblock();
                    }
                });
            });
        },

        "product-add-command": function (element, url, command) {
            var data = {};

            var amount_elms = $(element.getAttribute('data-target'));

            if (amount_elms.length <= 0) {
                amount_elms = $('input.product-amount');
            }

            if (amount_elms.length) {
                data.amount = $(amount_elms).val();
            }
            eshop.command(element, url, data);
        },

        bindCommands: function (scope) {
            var source;
            if (typeof scope != 'undefined') {
                source = scope.find('[data-cart-command]');
            } else {
                source = $('[data-cart-command]');
            }
            source.each(function () {
                var command = $(this).attr('data-cart-command');

                var trigger = 'click';
                if ($(this).attr('data-cart-command-trigger')) {
                    trigger = $(this).attr('data-cart-command-trigger');
                }

                $(this).on(trigger, function (e) {
                    var url;
                    if ($(this).attr('data-cart-command-url')) {
                        url = $(this).attr('data-cart-command-url');
                    } else {
                        url = $(this).attr('href');
                    }
                    if (typeof eshop[command + "-command"] == 'function') {
                        eshop[command + "-command"].apply(this, [this, url, command]);
                    } else {
                        eshop.command(this, url);
                    }
                    e.preventDefault();
                    e.stopPropagation();
                });
            });
        },

        updateInfo: function (data) {
            for (tag in data) {
                if (typeof data[tag] != 'object' && typeof data[tag] != 'array') {
                    $('.' + tag).html(data[tag]);
                }
                if (typeof data[tag] == 'object') {
                    for (id in data[tag]) {
                        if (typeof data[tag][id] == 'object') {
                            for (subtag in data[tag][id]) {
                                $('[data-' + tag + '=' + id + '] .' + subtag).html(data[tag][id][subtag]);
                            }
                        }
                    }
                }
            }
        },

        init: function () {
            require(['requirejs-domready'], function (domReady) {
                domReady(function () {
                    eshop.bindCommands();

                    var buxus_shop_submitting = false;
                    // Confirm order
                    $(".eshop-confirm-order").click(function () {
                        if (buxus_shop_submitting) {
                            return false;
                        }
                        buxus_shop_submitting = true;
                        $(".eshop-confirm-order").html('<img src="/buxus/assets/images/wait.gif" />');
                        $(".eshop-confirm-order-form").submit();
                        return false;
                    });

                    //eshop.init_tooltips();
                    //eshop.init_form_tooltips();
                });
            });
        }
    };
    return eshop;
});