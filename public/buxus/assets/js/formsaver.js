define(['jquery'], function($) {
    var formsaver = {
        save_url: '',
        last_data: null,

        register: function(save_url, options) {
            options = $.extend({
                selector: '.form_autosave',
                elements: []
            }, options);

            formsaver.save_url = save_url;

            require(['requirejs-domready'], function(domReady) {
                domReady(function() {
                    $(options.selector).on('change', formsaver.handle_change);
                    $(options.selector).next('input').on('change', formsaver.handle_change);
                    for (i = 0; i < options.elements.length; i++) {
                        $(options.elements[i]).on('change', formsaver.handle_change);
                    }
                });
            })
        },

        handle_change: function(e) {
            var elm = $(this);

            setTimeout(function() {
                var form = elm.parents('form');
                var post_data = form.serialize() + "&_save_only=1";

                if (post_data == formsaver.last_data) {
                    return;
                }
                formsaver.last_data = post_data;

                $.ajax(formsaver.save_url, {
                    type: 'POST',
                    data: post_data,
                    dataType: 'json',
                    success: function (result) {
                        if (result.result == 'OK') {
                            if (typeof result.data != 'undefined') {
                                $(document).trigger('formsaver.data', [result.data]);
                            }
                        } else {
                            require(['jgrowl'], function () {
                                $.jGrowl(result.error, {sticky: true});
                            });
                        }
                    },
                    error: function () {
                    }
                });
            }, 200);
        }
    };

    return formsaver;
});
