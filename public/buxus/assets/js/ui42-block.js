define(['jquery', 'module', 'blockui'], function($, src) {
    var uri = src.uri;
    uri = uri.substring(0, uri.lastIndexOf("/") + 1);

    var module = {
        block: function(selector) {
            var options = {
                message: '<img src="/buxus/assets/images/ui42-loader.gif" alt="..."/>',
                css: {
                    top: '48%',
                    left: '48%',
                    border: 'none',
                    fontSize:'24px',
                    padding: '15px',
                    backgroundColor: '#000',
                    '-webkit-border-radius': '10px',
                    '-moz-border-radius': '10px',
                    'border-radius' : '10px',
                    opacity: .5,
                    color: '#fff',
                    width: 'auto'
                },
                overlayCSS:  {
                    backgroundColor: '#000',
                    opacity: 0.4
                }
            };

            if (typeof selector != 'undefined') {
                $(selector).block(options);
            } else {
                $.blockUI(options);
            }
        },

        unblock: function(selector) {
            if (typeof selector != 'undefined') {
                $(selector).unblock();
            } else {
                $.unblockUI();
            }
        }
    };

    return module;
});
