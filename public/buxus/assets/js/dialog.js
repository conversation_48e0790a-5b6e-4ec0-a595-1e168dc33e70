define(['jquery', 'ui42-block'], function($, block) {
    var dialog = {
        bind: function () {
            $('#dialog-placeholder').find('form').submit(function() {
                dialog.wait();
                var form_data = $('#dialog-placeholder').find('form').serialize();
                $.ajax('/buxus/public/dialog/form.php', {
                    type: 'POST',
                    data: form_data,
                    dataType: 'json',
                    success: function(result) {
                        dialog.wait_hide();
                        if (result.result == 'OK') {
                            dialog.process_options(result.data.options);
                            $('#dialog-placeholder .modal-content').html(result.data.html);
                            dialog.bind();
                        } else {
                            require(['jgrowl'], function() {
                                $.jGrowl(result.error, {sticky: true});
                            });
                        }
                    },
                    error: function() {
                        dialog.wait_hide();
                    }
                });
                return false;
            });
        },

        wait: function() {
            block.block('#dialog-placeholder');
        },

        wait_hide: function () {
            block.unblock('#dialog-placeholder');
        },

        process_options: function(options) {
            if (typeof options != 'undefined' && typeof options.class != 'undefined') {
                $('#dialog-placeholder > .modal-dialog').removeClass().addClass(options.class);
            } else {
                $('#dialog-placeholder > .modal-dialog').removeClass().addClass('modal-dialog');
            }
        },

        show: function(tag, additional_data) {
            var request_data = {
                form_tag: tag
            };
            for (i in additional_data) {
                request_data[i] = additional_data[i];
            }
            block.block();

            $.ajax('/buxus/public/dialog/form.php', {
                type: 'POST',
                data: request_data,
                dataType: 'json',
                success: function(result) {
                    block.unblock();
                    if (result.result == 'OK') {
                        var options = {
                            modal: true,
                            beforeClose: function(event, ui) {
                                $('.tip-processed').each(function() {
                                    $(this).attr('data-force-close', '1');
                                    $(this).blur();
                                });
                            }
                        };
                        for (var i in result.data.options) {
                            options[i] = result.data.options[i];
                        }

                        dialog.process_options(result.data.options);
                        $('#dialog-placeholder .modal-content').html(result.data.html);
                        $('#dialog-placeholder').modal(options);
                        dialog.bind();
                    } else {
                        require(['jgrowl'], function() {
                            $.jGrowl(result.error);
                        });
                    }
                },
                error: function() {
                    block.unblock();
                }
            });
        }
    };
    return dialog;
});
