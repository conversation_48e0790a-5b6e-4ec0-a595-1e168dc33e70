define(['jquery', 'bootstrap-select'], function ($) {
    return {
        init: function (languagePickerSelector) {
            let languagePicker = $(languagePickerSelector);

            if (!languagePicker.length) {
                return;
            }

            let alternateLinks = $('head').find('link[rel="alternate"]');

            alternateLinks.each(function(index, element) {
                let alternateLinkElement = $(element);
                let alternateHrefLang = alternateLinkElement.attr('hreflang');

                let languagePickerOption = languagePicker.find(`[data-lang-code=${alternateHrefLang}]`);

                if (languagePickerOption.length) {
                    let urlAttr = languagePickerOption.attr('data-url-attr');

                    if (urlAttr) {
                        languagePickerOption.attr(urlAttr, alternateLinkElement.attr('href'));
                    }
                }
            });
        }
    }
});
