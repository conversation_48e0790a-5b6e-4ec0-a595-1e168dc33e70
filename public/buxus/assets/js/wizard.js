define(['jquery'], function($) {
    var wizard = {
        run: function(wizard, post_data, callback) {
            post_data = $.extend({
                wizard: wizard
            }, post_data);

            $.ajax('/buxus/public/substrate/wizard.php', {
                type: 'POST',
                data: post_data,
                dataType: 'json',
                success: function(result) {
                    if (result.result == 'OK') {
                        callback.apply(this, [result.data]);
                    } else {
                        require(['jgrowl'], function() {
                            $.jGrowl(result.error);
                        });
                    }
                },
                error: function() {
                }
            });
        }
    };

    return wizard;
});