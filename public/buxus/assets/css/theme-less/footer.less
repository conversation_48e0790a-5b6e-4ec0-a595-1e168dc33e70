@footer_background_2: #c2c2c2;
@title_footer_text_2: #252525;
@anchor_footer_text_2: #252525;

footer {
  margin-top:15px;
  padding: 15px 0 25px;

  background-color: @footer_background;

  @media (max-width: @screen-xs-max) {
    text-align: center;
  }

  h2 {
    color: @title_footer_text;
    font-size: 18px;
    margin-top: 10px;
    margin-bottom: 10px;
    a {
      color: @title_footer_text;
    }
  }

  .adresa {
	  margin: 0 0 30px 0;
  }

  #footer, .footer-megamenu {
    ul {
      list-style-type: none;
      padding: 0;
      li {
        margin-bottom: 5px;
      }
      a:hover {
        text-decoration: underline;
      }
    }
  }
  .copyright {
    //padding:0px 15px;

    .pull-right {
      text-align: right;
      @media (max-width: @screen-sm-max) {
        text-align: center !important;
      }
    }
    background-color: @copyright_background;
    p {
      @media (max-width: @screen-sm-max) {
        text-align: center !important;
      }
      a {
        text-decoration: underline;
        color: @copyright_anchor;
      }
      margin: 10px 0;
      color: @copyright_text;
    }
  }

  ul {
    width: 100%;
    margin: 0;
    list-style-type: none;
    padding: 0;

    li {
      margin-bottom: 0.33em;

      a {
        text-decoration: none;
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }

  .footer-bottom {
    border-top: 2px solid gray;
    padding-top: 15px;
    text-align: center;
  }
}
