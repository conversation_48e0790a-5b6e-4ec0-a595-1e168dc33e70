.product-info{
  h1{
    margin-bottom: 15px;
    font-weight: 600;
  }
  .optional{
    margin-top: 15px;
    .availability{
      font-weight: bold;
      color: #80a60d;
    }
  }
  .author{
    color: @anchor_page_text;
  }
  .product-description{
    margin-top: 15px;
    margin-bottom: 15px;
    p{
      line-height: 2.5em;
    }
  }
}

.product-card {
  position: relative;
  height:430px;
  overflow: hidden;

  &.fs-card {
    height:350px;
  }

  &:hover{
    box-shadow: 1px 2px 10px 0 #444;
  }

  .image {
    display: block;
    height: 170px;

    img {
      //width:100%;
      height:auto;
      z-index:100;
    }

  }

  h2{
    font-size: 1.429em;
    text-align: center;
    height: 2em;
    overflow: hidden;

    a{
      text-decoration: none;
      color: @text-color;
      text-transform: uppercase;

      &:hover{
        color: darken(@text-color, 10%);
      }

    }
  }

  .product-info{
    color: @gray-light;
    height: 4em;
    overflow: hidden;
  }

  .price{
    color: @price_text_color;
    font-weight: bold;
    text-align: center;
    font-size:2em;
  }

	.price-old {
		font-size: 16px;
		text-decoration: line-through;
		text-align: center;
	}

  .bottom-caption-info {
    position: absolute;
    bottom: 30px;
    margin-left: -17px;
    width: 100%;
  }

  .product-detail-button {
    position: absolute;
    bottom: 15px;
    left: 0;
    width: 100%;
  }

  .sticker {
    position: absolute;
    left: 5px;
    top: 5px;
    width: 100%;
    img {
      	width: 35%;
		cursor: pointer;
    }
  }
}

.product-detail {
  margin-top:15px;

  .inner-left {
    text-align: center;

    img {
      display: inline-block;
      vertical-align: middle;
      height: auto;
      max-width: 100%;
    }
  }

  .product-detail-background {
    background-color: @product_detail_background;
    padding-top: 40px;
    padding-bottom: 40px;
  }

	.main-product-image {
		display: block;
		position: relative;
		margin-bottom: 10px;

		.sticker {
			position: absolute;
			left: 45px;
			top: 5px;
			width: 100%;
			text-align: left;
			img {
				width: 21%;
			}
		}
	}



  .product-detail-tabs {
    background-color: @product_detail_tabs;
    padding-top: 40px;
    padding-bottom: 40px;
  }

  .inner-right {
    background-color: #FFF;
    color:#000;

    .price{
      font-size: 24px;
    }

	.price-old {
		font-size: 18px;
		text-decoration: line-through;
	}

    .availability {
      padding:15px 0px;
    }

    @media (max-width: @screen-xs-max) {
      height:100px;
      line-height: 7em;
      .basket_button {
        margin-left: 0;
        float: right;
        margin-top: 35px
      }
    }
  }
}


