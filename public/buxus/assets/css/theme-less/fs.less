@fs_border_color:#aaaaaa;
@fs_base_bacgkround: @base_theme_bacgkround;
@fs_text_color: @stranka_text;

#fs .navbar-default, #fs .navbar-default .navbar-brand {
	background-color: @top_menu_pozadie_gradient_1;
	color: @top_menu_text;
}

#fs .navbar-collapse {
	padding-left: 0px;
	padding-right: 0px;
	max-height: 10000px;
}

#fs .navbar-default {
	border: 0;
	box-shadow: none;
}

#fs_results_box .fs-product:last-child {
	.fs-list-thumbnail {
		border-bottom: 0
	}
}

.fs-filter-col.col-xs-12 {
	padding-left: 0;
	padding-right: 0;

	background: #faf6f5;

	@media (min-width: @screen-sm-min) {
		background: none;

		padding-left: 15px;
		padding-right: 15px;
	}
}

.fs-filter {
//	background: #faf6f5;

	&.collapse {
		display: none;
		visibility: hidden;

		&.in {
			display: block;
			visibility: visible;
		}

		@media (min-width: @screen-sm-min) {
			display: block;
			visibility: visible;
		}
	}

	@media (min-width: @screen-sm-min) {
		margin-right: 15px;
	}

	h4 {
		font-family: 'Open sans condensed', sans-serif;
		font-size: 15px;
		font-weight: bold;
		text-transform: uppercase;
		margin: 15px 0 5px 15px;
		padding-top: 20px;
		padding-bottom: 10px;
		color: @fs_text_color;
	}

	.fs-slider-wrapper {
		margin: 10px 20px;

		.slider {
			.slider-handle {
				opacity: 1;
				background: @fs_base_bacgkround;
			}

			.slider-selection {
				background: #dddddd;
				box-shadow: none;
			}

			.tooltip {
				z-index: 5;
			}
		}
	}

	ul.fs-list {
		list-style: none;
		padding-left: 0;

		li {
			display: inline-block;

			@media (min-width: @screen-sm-min) {
				display: block;
			}

			input[type=checkbox] {
				display: none;
			}

			a, i, label {
				color: @fs_text_color;
				display: block;
				padding: 3px 5px 3px 15px;
				text-decoration: none;
				font-size: 13px;
				font-weight: normal;
				cursor: pointer;

				&:before {
					content: ' ';
					display: inline-block;
					border: 1px solid #cccccc;
					background: #ffffff;
					padding: 4px;
					border-radius: 3px;
					margin-right: 5px;
				}

				.glyphicon {
					color: #aaaaaa;
					font-size: 10px;
					margin-left: 5px;
				}

				&:hover {
					background: #f3e9e7;
					&:before {
						border-color: @fs_base_bacgkround;
						background: @fs_base_bacgkround;
					}

					.glyphicon {
						color: #000000;
					}
				}
			}

			&.active {
				a, i, input, label {
					color: #000000;

					&:before {
						border-color: @fs_base_bacgkround;
						background: @fs_base_bacgkround;
					}
				}
			}

			&.disabled {
				i {
					color: #aaaaaa;
					cursor: default;
				}
			}
		}
	}
}

.fs-horizontal {
	margin-bottom: 15px;
	.fs-filter {
		ul.fs-list {
			li {
				display: inline-block;
			}
		}
	}

	.fs-toggle:after {
		/* symbol for "opening" panels */
		font-family: 'Glyphicons Halflings';  /* essential for enabling glyphicon */
		content: "\e114";    /* adjust as needed, taken from bootstrap.css */
		//float: right;        /* adjust as needed */
	}
	.fs-toggle.collapsed:after {
		/* symbol for "collapsed" panels */
		content: "\e080";    /* adjust as needed, taken from bootstrap.css */
	}
}

.fs_sortbar {
	font-size: 13px;
	color: @fs_text_color;
	line-height: 32px;
	margin-bottom: 15px;

	select {
		border: 1px solid @fs_border_color;
		display: inline-block;
		margin-right: 15px;
		margin-left: 10px;
		width: auto;

		&.form-control {
			font-size: 12px;
			padding: 2px 10px;
			height: 28px;
		}
	}

	.btn-group {
		& > .btn {
			background: none;
			padding: 3px 6px;
			display: inline-block;
			margin: 0;
			&.active, &:active {
				box-shadow: none;
				color: @fs_base_bacgkround;
				background: #f8f6f7;
			}
		}

		.fs-btn-view {
			border: 1px solid @fs_border_color;
		}
	}
}

.fs_results_info {
	font-size: 13px;
	color: @fs_text_color;

	line-height: 32px;

	margin-bottom: 15px;
}

.fs-search-summary-list {
	background: #faf6f5;
	padding: 10px;
	margin-bottom: 10px;

	.btn {
		font-size: 13px;
		font-weight: normal;
		font-family: 'Open Sans', sans-serif;

		span {
			display: inline-block;
			margin-left: 5px;

			&.glyphicon {
				color: #cccccc;
				font-size: 10px;
			}
		}

		&:hover {
			span {
				&.glyphicon {
					color: #000000;
				}
			}
		}
	}
}