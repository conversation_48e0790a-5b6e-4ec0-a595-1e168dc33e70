@import "../custom_theme";

/* MENU CONFIGURATION
**********************************************************/
.jetmenu{
	width: 100%;
	padding: 0;
	margin: 0;
	position: relative;
	float: left;
	list-style: none;
	background: @top_menu_pozadie_gradient_1;
}
.jetmenu li{
	display: inline-block;
	float: left;
}
.jetmenu a{
	-o-transition: all .3s linear;
	-webkit-transition: all .3s linear;
	-moz-transition: all .3s linear;
	transition: all .3s linear;
	outline: none;
	z-index: 10;
}
.jetmenu > li > a{
	padding: 20px 24px;
	font-size: 14px;
	color: #fff;
	text-decoration: none;
	display: inline-block;
    font-family: 'Open Sans Condensed', sans-serif;
    text-transform: uppercase;
    font-weight: bold;
}
.jetmenu > li:hover > a,
.jetmenu > li.active > a{
	background: #e0e0e0;
	color: #ffffff;
}

/* DROPDOWN CONFIGURATION
**********************************************************/
.jetmenu ul.dropdown, 
.jetmenu ul.dropdown li ul.dropdown {
	list-style: none;
    margin: 0;
    padding: 0;   
	display: none;
    position: absolute;
    z-index: 99;
	min-width: 134px;
	background: @top_menu_pozadie_gradient_1;
}
.jetmenu ul.dropdown{
    top: 60px;
}
.jetmenu ul.dropdown li ul.dropdown{
    left: 100%;
	top: inherit;
}
.jetmenu ul.dropdown li{
	clear:both;
	width:100%;
	font-size: 12px;
}
.jetmenu ul.dropdown li a{
	width:100%;
	padding: 12px 24px 12px;
	display:inline-block;
	float:left;
	clear:both;
	/*font-family: 'Open Sans', sans-serif;*/
	text-decoration: none;
	color: #ffffff;
	box-sizing:border-box;
	-moz-box-sizing:border-box; 
	-webkit-box-sizing:border-box;
}
.jetmenu ul.dropdown li:hover > a{
	background: #e0e0e0;
	color: #eeeeee;
}

/* dropdowns to left side */
.jetmenu ul.dropdown li ul.dropdown.left{
    left: auto;
	right: 100%;
}

/* SUBMENU INDICATORS
**********************************************************/
.jetmenu .indicator{
	position: relative;
	left: 5px;
	top: 6px;
	font-size: 8px;
	float: right;
}
.jetmenu ul li .indicator{
	font-size: 8px;
	top: 1px;
	left: 10px;
}

/* MEGAMENU
**********************************************************/
.jetmenu > li > .megamenu{
	position: absolute;
	display: none;
	background: #ffffff;
	width: 25%;
	top: 60px;
	/*font-family: 'Open Sans', sans-serif;*/
	font-size: 12px;
	color: #ffffff;
	z-index: 99;
	padding: 20px 45px 20px;
	border-top: 1px solid #eee;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;

    -webkit-box-shadow: 0px 15px 25px 2px rgba(161,161,161,0.6);
    -moz-box-shadow: 0px 15px 25px 2px rgba(161,161,161,0.6);
    box-shadow: 0px 15px 25px 2px rgba(161,161,161,0.6);
}

/* megamenu list */
.jetmenu li > .megamenu ul{
	margin: 0 0 20px 0;
	float: left;
	padding: 0;
	display: block;
	position: relative;
}
.jetmenu li > .megamenu ul li{
	width: 100%;
	padding: 4px 0;
}
.jetmenu li > .megamenu ul li.title {
	margin: 0 0 8px;
	padding: 0 0 5px;
	font-size: 17px;
    font-family: 'Open sans condensed', sans-serif;
    font-weight: bold;
    color: #333333;
	/*border-bottom: solid 1px #eee;*/
}

/* megamenu h5 */
.jetmenu li > .megamenu h5 {
	width: 100%;
	margin: 0;
	padding: 0;
}

/* megamenu links */
.jetmenu li > .megamenu a {
	color: #777777;
	text-decoration: none;
	font-size: 13px;
	-webkit-transition: color 0.3s linear; 
	-moz-transition: color 0.3s linear; 
	-o-transition: color 0.3s linear; 
	transition: color 0.3s linear; 
}
.jetmenu li > .megamenu a:hover{
	color: @top_menu_pozadie_hover_gradient_1;
    text-decoration: underline;
}

.jetmenu li > .megamenu ul li.title a {
    font-size: 17px;
    font-family: 'Open sans condensed', sans-serif;
    font-weight: bold;
    color: #333333;
}

/* megamenu images */
.jetmenu  .megamenu img{
	//width: 100%;
	-webkit-transition: border 0.3s linear; 
	-moz-transition: border 0.3s linear;
	-o-transition: border 0.3s linear;
	transition: border 0.3s linear;
}

/* to fix right attribute on submenus (menu aligned to left (default)) */
.jetmenu > li.fix-sub > .megamenu,
.jetmenu > li.fix-sub > .megamenu.half-width,
.jetmenu > li.fix-sub > .dropdown{
	right: 0;
}

/* MEGAMENU WIDTHS
**********************************************************/
.jetmenu > li > .megamenu.half-width{
	width: 50%;
}
.jetmenu > li > .megamenu.full-width{
	width: 100%;
	left: 0;
}

/* MEGAMENU FORM CONFIGURATION
**********************************************************/
.jetmenu li > .megamenu form{
	width: 100%;
}

/* megamenu inputs */
.jetmenu li > .megamenu form input[type="text"],
.jetmenu li > .megamenu form textarea{
	padding: 5px;
	color: #ffffff;
	background: #444;
	/*font-family: 'Open Sans', sans-serif;*/
	font-size: 14px;
	border: solid 1px transparent;
	outline: none;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-transition: border 0.3s linear; 
	-moz-transition: border 0.3s linear;
	-o-transition: border 0.3s linear;
	transition: border 0.3s linear;
}
.jetmenu li > .megamenu form input[type="text"]:focus,
.jetmenu li > .megamenu form textarea:focus{
	border-color: #e0e0e0;
}
.jetmenu li > .megamenu form input[type="text"]{
	width: 100%;
	margin-top: 10px;
}
.jetmenu li > .megamenu form textarea{
	width: 100%;
	margin-top: 10px;
}
.jetmenu li > .megamenu form input[type="submit"]{
	width: 25%;
	float: right;
	height: 30px;
	margin-top: 10px;
	border: none;
	cursor: pointer;
	background: #444;
	color: #777;
	/*font-family: 'Open Sans', sans-serif;*/
	-webkit-transition: background 0.3s linear; 
	-moz-transition: background 0.3s linear; 
	-o-transition: background 0.3s linear; 
	transition: background 0.3s linear; 
}
.jetmenu li > .megamenu form input[type="submit"]:hover{
	background: #e0e0e0;
	color: #555;
}

/* MEGAMENU GRID SYSTEM
**********************************************************/
.megamenu .row{
	width: 100%;
	margin-top: 15px;
}
.megamenu .row:first-child {
	margin-top: 0;
}
.megamenu .row:before,
.megamenu .row:after {
	display: table;
	content: "";
	line-height: 0;
}
.megamenu .row:after {
	clear: both;
}
.megamenu .row .col1,
.megamenu .row .col2,
.megamenu .row .col3,
.megamenu .row .col4,
.megamenu .row .col5,
.megamenu .row .col6{
	display: block;
	width: 100%;
	min-height: 20px;
	float: left;
	margin-left: 2.127659574468085%;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}
.megamenu .row [class*="col"]:first-child {
	margin-left: 0;
}
.megamenu .row .col1{
	width: 14.893617021276595%;
}
.megamenu .row .col2{
	width: 31.914893617021278%;
}
.megamenu .row .col3{
	width: 48.93617021276595%;
}
.megamenu .row .col4{
	width: 65.95744680851064%;
}
.megamenu .row .col5{
	width: 82.97872340425532%;
}
.megamenu .row .col6{
	width: 100%;
}

/* RIGHT ALIGNMENT (MENU ITEM)
**********************************************************/
.jetmenu > li.right{
	float: right;
}
.jetmenu > li.right > .megamenu,
.jetmenu > li.right > .megamenu.half-width,
.jetmenu > li.right > .dropdown{
	right: 0;
}

/* to fix right attribute on submenus (menu aligned to right) */
.jetmenu > li.jsright{
	float: right;
}
.jetmenu > li.jsright.last > .megamenu,
.jetmenu > li.jsright.last > .megamenu.half-width,
.jetmenu > li.jsright.last > .dropdown{
	right: 0;
}

/* ICONS (FONT AWESOME)
**********************************************************/
.jetmenu > li > a > i{
	line-height: 23px !important;
	margin-right: 6px;
	font-size: 18px;
	float: left;
}

/* COLLAPSIBLE MENU
**********************************************************/
.jetmenu > li.showhide{
	display: none;
	width: 100%;
	height: 50px;
	cursor: pointer;
	color: #fff;
	background: @top_menu_pozadie_gradient_1;
}
.jetmenu > li.showhide span.title{
	margin: 15px 0 0 25px;
	/*font-family: 'Open Sans', sans-serif;*/
	float: left;
}
.jetmenu > li.showhide span.icon{
	margin: 17px 20px;
	float: right;
}
.jetmenu > li.showhide .icon em{
	margin-bottom: 3px;
	display: block;
	width: 20px;
	height: 2px;
	background: #ffffff;
}

/* STYLES
**********************************************************/

/* orange */
.orange > li:hover > a{	background: @top_menu_pozadie_hover_gradient_1; color: #fff; }
.orange > li.active > a{	background: @left_menu_pozadie; color: #fff; }
.orange ul.dropdown li:hover > a{ background: #ff670f; color: #fff; }
.orange li > .megamenu form input[type="text"]:focus, .orange li > .megamenu form textarea:focus{ border-color: #ff670f; }
.orange li > .megamenu form input[type="submit"]:hover{ background: #ff670f; color: #fff; }
.orange li > .megamenu a:hover{ color: #ff670f; }

/* RESPONSIVE LAYOUT
**********************************************************/
@media (max-width: 768px) {
	.jetmenu > li{
		display: block;
		width: 100%;
		box-sizing:border-box;
		-moz-box-sizing:border-box; 
		-webkit-box-sizing:border-box;
	}
	.jetmenu > li > a{
		padding: 15px 25px;
		background: #f39f49;
	}
	.jetmenu a{
		width: 100%;
		box-sizing:border-box;
		-moz-box-sizing:border-box; 
		-webkit-box-sizing:border-box; 
	}
	.jetmenu ul.dropdown, 
	.jetmenu ul.dropdown li ul.dropdown{
		width: 100% !important;
		left: 0;
		position: static !important;
		border: none;
		box-sizing:border-box;
		-moz-box-sizing:border-box; 
		-webkit-box-sizing:border-box; 
	}
	.jetmenu ul.dropdown li {
		background: #fff !important;
		border: none;
	}
	.jetmenu ul.dropdown > li > a{
		padding-left: 40px !important;
	}
	.jetmenu > li > .megamenu{
		width: 100% !important;
		position: static;
		border-top: none;
	}
	.jetmenu > li > .megamenu .row [class*="col"]{
		float: none;
		display: block;
		width: 100% !important;
		margin-left: 0;
		margin-top: 15px;
		-webkit-box-sizing: border-box;
		-moz-box-sizing: border-box;
		box-sizing: border-box;
	}
	.jetmenu > li > .megamenu .row:first-child  [class*="col"]:first-child {
		margin-top: 0;
	}
	.jetmenu > li > .megamenu .row{
		margin-top: 0;
	}
	.jetmenu > li > ul.dropdown > li > a{ padding-left: 40px !important; }
	.jetmenu > li > ul.dropdown > li > ul.dropdown > li > a{ padding-left: 60px !important; }
	.jetmenu > li > ul.dropdown > li > ul.dropdown > li > ul.dropdown > li > a{ padding-left: 80px !important; }
}
@media (min-width: 768px) and (max-width: 900px) {
	.jetmenu > li > .megamenu{
		width: 100% !important;
		left: 0 !important;

        top: 55px;
	}

    .jetmenu > li > a {
        font-size: 12px;
        padding: 20px 18px;
    }

    .jetmenu .indicator {
        top: 5px;
    }
}





























