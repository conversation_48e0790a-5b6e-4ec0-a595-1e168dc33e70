.modern-form__wrapper {
  padding: 0 90px;
}

.modern-radio {
  margin-bottom: 5px;
}

.modern-input {
  margin-bottom: 15px;

  .errors {
    list-style-type: none;
    margin: 0;
    padding: 0;
  }
}

label.modern-input__label {
  position: relative;
  display: block;
  margin: 0;
}

.modern-input__text {
  color: #9E9B9E;
  display: block;
  font-size: 16px;
  font-weight: 400;
  left: 9px;
  position: absolute;
  top: 16px;
  z-index: 10;
  transition: all 150ms ease 0s;
}

input.modern-input__input {
  -moz-appearance: none;
  background-color: #fff;
  border: 1px solid #C6C7C8;
  border-radius: 4px;
  color: #30373b;
  display: inline-block;
  font-size: 16px;
  height: 52px;
  font-weight: normal;
  transition: all 150ms ease 0s;
  width: 100%;
  padding-left: 8px;
  margin: 0;
  outline: none;
  z-index: 15;
}

textarea.modern-input__textarea {
  -moz-appearance: none;
  background-color: #fff;
  border: 1px solid #C6C7C8;
  border-radius: 4px;
  color: #30373b;
  display: inline-block;
  font-size: 16px;
  font-weight: normal;
  transition: all 150ms ease 0s;
  width: 100%;
  padding-left: 8px;
  padding-top: 22px;
  margin: 0;
  outline: none;
  z-index: 15;
}

label.modern-input__label--filled {

  .modern-input__text {
    font-size: 11px;
    top: 3px;
  }

  input.modern-input__input {
    padding-top: 15px;
  }
  textarea.modern-input__textarea {
  }
}
.modern-input__label--readonly {
    input {
        color: #666;
        cursor: not-allowed;
    }
}

.modern-input.has-error {
  input.modern-input__input {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom: 0;
    border-color: #a94442;
  }
  .errors {
    background: #F2DEDE;
    border: 1px solid #a94442;
    border-top: 0;
    border-radius: 4px;
    border-top-left-radius: 0;
    border-top-right-radius: 0;

    li {
      padding: 3px 6px;
      color: #a94442
    }

  }
}
