@import '../components/bootstrap/dist/css/bootstrap.css';
@import '../components/blueimp-gallery/css/blueimp-gallery.css';
@import '../components/blueimp-bootstrap-image-gallery/css/bootstrap-image-gallery.css';
@import '../components/seiyria-bootstrap-slider/css/bootstrap-slider.css';

@import 'import/_imported_styles.css';

@import "../components/bootstrap/less/variables";
@import "krabica-variables";
@import "custom_theme";

body {
  background-color: @stranka_pozadie;
}

/*BOOTSTRAP OVERIDES*/
.row .row {
  margin: 0px;
}

.with-top-margin {
  margin-top: 15px;
}

@import "theme-less/header.less";
@import "theme-less/footer.less";
@import "theme-less/eshop.less";
@import "theme-less/eshop-cart.less";
@import "theme-less/eshop-small-cart.less";
@import "theme-less/menu.less";
@import "theme-less/carousel.less";
@import "theme-less/form.less";
@import "theme-less/homepage.less";
@import "theme-less/fs.less";
@import "theme-less/multilang.less";
@import "theme-less/variants.less";

/*USER PROFILE MENU*/
.profile_menu {
  list-style: none;
  margin: 0px;
  padding: 0px;

  li {
    background: #dbdbdd;
    padding: 0px;

    a {
      display: block;
      padding: 6px;
      background: @gray-light;
      color: @gray;
      font-weight: bold;
      border-bottom: 1px solid #FFF;

      &.active, &:hover {
        background: #ff9c00;
        color: #FFF;
        text-decoration: underline;
      }

      &.logout {
        color: #F00;
      }
    }
  }
}


/*INFO BOXES, in forms, actions*/
.info_box {
  margin: 0px;
  padding: 8px;
  margin-bottom: 24px;
  color: #FFF;
  position: relative;
  background: #ff9c00;

  p {
    margin: 3px 0px;
    padding: 0px;
    color: inherit;
    font-size: inherit;
    line-height: 1.1em;

    a {
      color: #FFF;
      text-decoration: underline;
    }
  }

  &.success {
    background: #4fa45f;
  }

  &.error {
    background: #a44f4f;
    color: #FFF;
  }

  &.info {
    background: @gray;
  }

  .info_box_close {
    background: none repeat scroll 0 0 #000000;
    color: #FFFFFF;
    display: block;
    font-size: 10px;
    font-weight: bold;
    padding: 4px 6px;
    position: absolute;
    right: 4px;
    top: 7px;
  }
}

//ESHOP HTML ALERT BOX
#basket_nadler {
  display: none;
}

.html_alert {
  position: absolute;
  z-index: 1000;
  width: 240px;
  background: #FFF;
  border: 1px solid @gray-dark;

  .title {
    padding: 6px 0px 6px 6px;
    background: #0088cc;
    color: #FFF;
    font-size: 16px;
  }

  .close {
    background: #000;
    color: #ffffff;
    cursor: pointer;
    float: right;
    font-size: 10px;
    margin: 4px 2px 0 0;
    padding: 1px 6px;
    text-align: center;

  }

  .bottom {
    padding-bottom: 8px;

    .content {
      margin: 0px;
      padding: 0px;
      text-align: center;
      padding: 5px 5px;
      font-size: 0.875rem;

      p {
        font-size: inherit;
        margin-bottom: 12px;
      }

      .button_submit {
        font-size: 0.750rem;
      }

      .checkbox {
        margin-bottom: 0px;
      }

      &.error {
        display: none;
        color: #F00;

        a {
          color: #F00 !important;;
          text-decoration: underline;
        }
      }

      &.not_available {
        display: none;
        color: #000;

        a {
          color: #000 !important;
          text-decoration: underline;
        }
      }
    }

    .content.wb {
      padding-bottom: 10px;
      border-bottom: 1px solid #000;
    }

    span {
      color: #000;
      //.basic_font;
      font-size: 0.625rem;
    }
  }
}


// pre vsetky sticky elementy - potencialne viac ako iba top menu
.sticky {
  position: fixed;
  z-index: 9999;
  top: 0;
}

/* make sidebar nav vertical */
@media (min-width: 768px) {
  .sidebar-nav .navbar .navbar-collapse {
    padding: 0;
    max-height: none;
  }

  .sidebar-nav .navbar ul {
    float: none;
    display: block;
  }

  .sidebar-nav .navbar li {
    float: none;
    display: block;
  }

  .sidebar-nav .navbar li a {
    padding-top: 12px;
    padding-bottom: 12px;
  }
}

#left-menu .navbar-default, #left-menu .navbar-default .navbar-brand {
  background-color: @top_menu_pozadie_gradient_1;
  color: @top_menu_text;
}

#left-menu li {
  background: @left_menu_pozadie;
}

#left-menu li a {
  color: @left_menu_text;
}

#left-menu li:hover, #left-menu li a:hover {
  background: @left_menu_pozadie_hover;
}

#left-menu {
  .nav {
    &.navbar-nav {

      li {
        &.active {
          box-shadow: none;
          background-image: none;
          background-color: lighten(@left_menu_pozadie, 10%);

          &:hover {
            background-color: @left_menu_pozadie_hover;
          }

          a {
            box-shadow: none;
            background-image: none;
            background-color: lighten(@left_menu_pozadie, 10%);

            &:hover {
              background-color: @left_menu_pozadie_hover;
            }
          }
        }

        &.nested {
          padding-left: 0.5em;
          box-shadow: none;
          background-image: none;
          background-color: lighten(@left_menu_pozadie, 15%);

          &:hover {
            background-color: @left_menu_pozadie_hover;
          }

          a {
            box-shadow: none;
            background-image: none;
            background-color: lighten(@left_menu_pozadie, 15%);

            &:hover {
              background-color: @left_menu_pozadie_hover;
            }
          }
        }
      }
    }
  }
}

.jumbotron {
  margin: 15px 0;
}