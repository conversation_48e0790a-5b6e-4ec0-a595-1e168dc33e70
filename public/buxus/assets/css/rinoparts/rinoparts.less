@import 'modules/cookie-bar';
@import 'modules/select2';
@import 'components/banners';

.fake-login-header {
  width: 100vw;
  background: #bd0808;
  padding: 20px;
}

.fake-login-header {
  h6 {
    color: #fff;
  }
}

.modern-input ul.errors {
  list-style: none;
}

#transport_payment_form_form ul.bt-cart-tp-list li.bt-cart-tp-item label {
  font-weight: inherit;
}

#transport_payment_form_form ul.bt-cart-tp-list li.bt-cart-tp-item {
  display: flex;
}

#transport_payment_form_form ul.bt-cart-tp-list li.bt-cart-tp-item input.legacy-checkbox {
  margin-right: 5px;
}

.bt-cart-tp-description {
  font-weight: 400;
}

.bt-cart-tp-name {
  font-weight: 700;
  display: flex;
  justify-content: space-between;
}

#delivery_data_form_form {
  label {
    text-align: left;
    padding: 5px 0;
  }

  #delivery_data_note .modern-input {
    display: flex;
    flex-direction: column;
  }

  ul.errors {
    padding: 3px 0;
  }

  .modern-radio {
    display: flex;
    flex-direction: column;

    .radio-inline {
      margin-left: 10px;
    }
  }
}

.badge.shopping-cart-count {
  background-color: #DF0000;
  margin-left: 5px;
  font-size: 13px;
  top: 0;
}

ul.shopping_cart_navigator li:after {
  transform: rotate(90deg);
}

ul.shopping_cart_navigator li.current div.process, ul.shopping_cart_navigator li.current:after, ul.shopping_cart_navigator li.previous div.process, ul.shopping_cart_navigator li.previous:after {
  color: #000;
}

#transport_payment_form_form ul.bt-cart-tp-list li.bt-cart-tp-item label span.bt-cart-tp-price {
  color: #000;
}

.row.cart-form .cart-form-submit .cart-total-price, .row.cart-form .voucher-form-submit .cart-total-price, .row.voucher-form .cart-form-submit .cart-total-price, .row.voucher-form .voucher-form-submit .cart-total-price {
  margin-top: 10px;
}

table.basket_table tr td.basket_item_details .bt-cart-item-amount {
  display: flex;
  justify-content: center;
}

.d-flex {
  display: flex;
}

.mx-auto {
  margin-right: auto;
  margin-left: auto;
}

.justify-content-center {
  justify-content: center;
}

input[type=checkbox].m-checkbox {
  margin: 6px 4px 0 0;
}

.main-header .shopping-cart svg {
  fill: #df0000;
}

.product-name-wrapper p.cart-product-producer {
  color: #999;
  font-size: 0.8rem;
}

.product-name-wrapper span.cart-product-main-code {
  color: #333;
}

dl.zend_form {
  #request_type-label {
    display: none;
  }

  #_token-label {
    display: none;
  }
}

#summary_info_form_form label[for=accept_gdpr] {
  display: block;
  width: calc(130%);
  padding: 0;
  text-align: left;
  margin: 10px -15px;
  color: #423732;
  font-size: 13px;
  font-weight: 700;
}

#summary_info_form_form label[for=accept_gdpr] input {
  margin-right: 10px;
}

.col-xs-6.with-vat {
  display: flex;
  align-items: center;
}

.price.price-small {
  font-size: 15px !important;
}

.product-list-item-title {
  font-size: 1.05em;
}

.suggestion {
  h2.suggestion-title {
    font-size: 1em;
  }
}

.home-cover .cover-sub-title {
  font-weight: 400;
}

.line-through {
  text-decoration: line-through;
}

.fake-login-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
}

.select2-container--default .select2-selection--single {
  height: 100%;
}

.select2-container {
  height: 100%;
}

.h-100 {
  height: 100%;
}

.d-none {
  display: none !important;
}

.registration-back {
  margin-bottom: 20px;
}


.delivery-address__heading {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
}

.delivery-address {
  border: 1px solid rgba(231, 231, 235, 0.95);
  padding: 20px;
  line-height: 12px;
  margin: 20px;
  display: flex;
  flex-direction: column;

  .delivery-address__actions {
    display: flex;
    justify-content: space-between;
    margin-top: 25px;
  }

  table {
    padding: 10px 0;

    td {
      padding: 8px;
    }
  }
}

form.delivery-address-change {
  input {
    margin: 10px 0;
  }
}

.list-group-item.active, .list-group-item.active:focus, .list-group-item.active:hover {
  border: 1px solid #ddd;
}

.justify-content-between {
  justify-content: space-between;
}

.payment-state {
  display: flex;
  align-items: baseline;

  .payment-state-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;

    &.payment-state-paid {
      background: #38BC09;
    }

    &.payment-state-partly-paid {
      background: #ecb500;
    }

    &.payment-state-unpaid {
      background: #ee0017;
    }
  }

  .payment-state-text-partly-paid {
    color: #ecb500;
  }

  .payment-state-text-unpaid {
    color: #ee0017;
  }
}

.cart-availability-tag {
  border-radius: 15px;
  padding: 0 10px;
  display: flex;
  align-items: center;
  white-space: nowrap;
  margin: 5px;
  font-size: 12px;
  font-weight: 700;
  width: fit-content;

  &.cart-availability-tag-stock {
    background-color: #e6f7ef;
  }

  &.cart-availability-tag-supplier {
    background-color: #fff3e6;
  }

  &.cart-availability-tag-unavailable {
    background-color: #ffe6e6;
  }

  .availability-state-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;

    &.availability-state-stock {
      background: #00af50;
    }

    &.availability-state-supplier {
      background: #f98f17;
    }

    &.availability-state-unavailable {
      background: #f91717;
    }
  }
}

.my-2 {
  margin-top: .5rem;
  margin-bottom: .5rem;
}

.ml-auto {
  margin-left: auto;
}

.cart-button__wrapper {
  border: 1px solid #E0E0E0;
  border-radius: 25px;

  .cart-button {
    display: flex;
    justify-content: space-evenly;
    padding: 5px 15px;
    color: #000;

    &__icon__wrapper {
      margin-right: 10px;
    }
  }
}

.cart-dropdown__wrapper {
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  background: none;

  .cart-dropdown {
    display: flex;
    justify-content: space-evenly;
    padding: 5px 15px;
    color: #000;
    align-items: baseline;

    &__icon__wrapper {
      margin-right: 10px;
    }
  }
}

.cart-dropdown {
  &__item {
    display: flex !important;
    align-items: baseline;
    width: 320px;
    border-collapse: collapse;
    padding: 7px 20px !important;

    &__icon__wrapper {
      img {
        height: 20px;
        width: auto;
        min-width: fit-content;
        margin-right: 10px;
      }

      min-width: 30px;
    }
  }
}

.cart-actions {
  display: flex;

  .cart-action {
    margin-right: 20px;

    &.restore {
      margin-left: auto;
    }
  }
}

.cart-action {
  &.restore {
    margin-left: auto;
  }
}

.price-offers {
  .price-offer {
    border-top: 1px solid #e0e0e0;
    border-bottom: 1px solid #e0e0e0;
    border-collapse: collapse;
    padding: 5px;

    a.restore-price-offer {
      color: #000;
      margin: auto 15px auto 0;
    }
  }
}

.row.cart-form {
  margin-top: 15px;
  border: none;
}

.cart-total-price {
  border: none !important;

  .cart-price-row {
    border-bottom: 1px solid #e0e0e0;
    padding: 15px 5px 5px 2px;
    font-weight: 600;
    line-height: 21px;
    font-size: 16px;
    text-align: left;

    > .row {
      align-items: baseline;
    }
  }

  .cart-price-row:first-child {
    padding-top: 0;
  }

  h3.price-without-vat {
    font-size: 24px !important;
  }
}

.cart-delete-button__icon__wrapper {
  border: 1px solid #E0E0E0;
  padding: 4px 12px;
  border-radius: 8px;
}

.filtered-product-card {
  opacity: 50%;
}

.modal-center {
  transform: translate(0, 50vh) translate(0, -50%) !important;
  margin-top: 0 !important;
}

.dropdown-submenu {
  position: relative;
}

.dropdown-submenu > .dropdown-menu {
  top: 0;
  left: 100%;
  margin-top: -6px;
  margin-left: -1px;
  -webkit-border-radius: 0 6px 6px 6px;
  -moz-border-radius: 0 6px 6px 6px;
  border-radius: 0 6px 6px 6px;
}

.dropdown-submenu:hover > .dropdown-menu {
  display: block;
}

.dropdown-submenu > a:after {
  display: block;
  content: " ";
  float: right;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
  border-width: 5px 0 5px 5px;
  border-left-color: #aaa;
  margin-top: 5px;
  margin-right: -10px;
  margin-left: auto;
}

.dropdown-submenu:hover > a:after {
  border-left-color: #ffffff;
}

.dropdown-submenu.pull-left {
  float: none;
}

.dropdown-submenu.pull-left > .dropdown-menu {
  left: -100%;
  margin-left: 10px;
  -webkit-border-radius: 6px 0 6px 6px;
  -moz-border-radius: 6px 0 6px 6px;
  border-radius: 6px 0 6px 6px;
}

.product-amount__notification {
  padding: 10px 3px 5px 3px;
  color: #2F3538;
  opacity: 60%;
  font-size: 0.9em;
}

.fill-warning {
  fill: #f0ad4e;
}

.product-list-item {
  .product-list-item-link {
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    > a {
      color: #2F3538;
      text-decoration: none;
    }

    .product-list-item-top-row {
      display: flex;
      justify-content: space-between;
    }

    div.warning-icons {
      display: flex;
      gap: 10px;
    }

    img.warning-icon, img.febi-bilstein-guarantee-logo {
      margin: initial;
    }

    img.febi-bilstein-guarantee-logo {
      position: absolute;
      top: 60px;
    }

    .product-card-add-to-cart-section {
      display: flex;
      justify-content: space-between;
      max-height: 40px;
      margin-top: 15px;

      .input-number-with-controls {
        > * {
          height: 40px;
          width: 40px;
          font-size: 1em;
        }

        button {
          line-height: 0;
          border-width: 2px;
        }

        input {
          border: 2px solid #EBEBEB;
          border-right: none;
          border-left: none;
        }

        svg {
          width: 10px;
          height: 10px;
        }
      }

      .product-card-button {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 8px;

        img.product-cart-icon {
          width: 15px;
          height: 15px;
          margin: initial;
        }
      }
    }
  }

  .product-delivery-time-info {
    display: flex;
    margin: 10px 0;

    img {
      width: 25px;
      margin: 0 0.625rem 0 0;
    }

    span {
      font-weight: 500;
    }
  }
}

.table.cart-transport-table {
  th {
    width: 40% !important;
  }

  td {
    padding-left: 15px;
  }
}


.badge-info {
  color: #fff;
  background-color: #17a2b8;
}

.dropdown-menu.dropdown-menu-bs.dropdown-menu-right {
  top: initial;
}

td.actions-row {
  padding-top: 0 !important;
  border-top: 0 !important;
  text-align: end;

  @media (max-width: 768px) {
    text-align: start;
  }

  &--invoices {
    .actions {
      display: flex;
      justify-content: end;
      gap: 10px;
    }
  }
}

.related-products-info-badge {
  img {
    height: 30px;
    position: absolute;
    right: 40px;

    &:hover {
      transform: scale(1.033);
    }
  }

  &:hover {
    transform: scale(1.033);
  }
}

section.product-list.related-product-list {
  box-shadow: rgba(0, 0, 0, 0.1) 0px -20px 14px -26px;

  .related-products {
    margin-top: 20px;
  }
}

.m-auto {
  margin: auto;
}

.flex-direction-column {
  flex-direction: column;
}

.campaign-row {
  gap: 25px;
  margin: 30px 0;
  font-size: 20px !important;

  .jumbotron p {
    font-size: 20px !important;
  }
}

.benefits-row {
  justify-content: center;
  max-width: 1000px;
  margin: 0 auto;
}

.benefit-card {
  display: flex;
  flex-direction: column;
  gap: 20px;

  .benefit-card-icon {
    display: flex;
    justify-content: center;
    width: 300px;
    height: 100px;

    img {
      height: max-content;
      margin: auto 0;
      width: 100px;
    }
  }

  .benefit-card-title {
    font-size: 20px;
    width: 300px;
    text-align: center;
  }
}

section.reviews {
  background: #303030;
  padding: 30px 0;

  @media (min-width: 1280px) {
    background-image: url('/buxus/assets/images/campaign/frame.png');
    background-repeat: no-repeat;
    background-position: center;
    background-color: #303030;
  }

  .review-card-wrapper {
    display: flex;
    justify-content: center;
  }

  .reviews-section-title {
    color: #fff;
    text-align: center;
  }

  .reviews-row .h2 {
    margin-bottom: 30px;
  }

  .review-card {
    background: #fff;
    padding: 20px;
    border-radius: 10px;
    max-width: 300px;
    margin: 10px 0;

    text-align: center;

    .reviewer-card__reviewer-name {
      font-weight: 700;
      padding: 15px 0;
    }

    .reviewer-card__review {
      font-weight: 300;
    }
  }

  .reviews-row__reviews {
    margin: 0 auto;
  }
}

.login-section {
  padding: 20px 0;
}

.home-cover.home-cover__febi-campaign {
  background: #303030;
}

.benefit-card-icon.benefit-card-icon--with-text {
  img {
    width: auto;
    height: 110px;
  }
}

.notification-badge {
    background-color: #DF0000;
    margin-left: 5px;
    font-size: 13px;
    top: 0;
}

.badge.notification-badge {
    background-color: #DF0000;
}

.table > tbody > tr > td.notification-td {
    text-align: center;
    vertical-align: middle;
    padding: 0;
    width: 12px;
}

.has-notification .notification-td span {
    background-color: #ff0000;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    position: relative;
}
