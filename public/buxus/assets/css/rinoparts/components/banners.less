.homepage-banner-list {
  background: none;
  margin-top: 30px;
  padding-bottom: 15px;
  width: 100vw;

  .cover-carousel {
    width: 98vw;
  }
}

.swiper-pagination {
  position: static;
  width: fit-content !important;
  margin: 0 auto;
}

.swiper-pagination-bullets {
  padding: 5px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  border-radius: 24px;
}

.swiper-navigation {
  padding: 15px;
}

.swiper-pagination-bullet {
  background: #999999;

  &.swiper-pagination-bullet-active {
    background: #DF0000;
  }
}

@media (max-width: 580px) {
  .swiper-pagination {
    position: absolute;
    top: 0;
    right: 0;
    width: auto !important;
    margin: 0;
  }

  .swiper-pagination-bullets {
    padding: 0;
    box-shadow: none;
    border-radius: 0;
  }

  .swiper-pagination-bullet {
    background: #DF0000;
    width: 10px;
    height: 10px;
    margin: 0 5px;
    border-radius: 50%;
  }
}

.bt-banner-wrapper {
  @media (max-width: 800px) {
    font-size: 1rem;
  }

  @media (max-width: 450px) {
    font-size: 0.85rem;
  }
}

.bt-banner {
  background-size: auto 100%;
  background-repeat: no-repeat;
  background-position: center;
  width: 100%;
  height: 300px;

  .bt-banner-wrapper {
    position: absolute;
    text-align: left;
    bottom: 20px;
    left: 20px;
    width: 50%;
  }

  .bt-banner-wrapper * {
    margin: 0;
    border: none;
  }

  span.bt-banner-title {
    background: #DF0000;
    color: white;
    padding: 8px 16px;

    @media (max-width: 800px) {
      font-size: 0.9rem;
    }
  }

  p.bt-banner-description {
    background: white;
    color: #333;
    padding: 12px 16px;
    width: fit-content;
    max-width: 100%;
    font-size: 1.3rem;
    font-weight: 600;

    @media (max-width: 800px) {
      font-size: 1rem;
    }

    @media (max-width: 450px) {
      font-size: 0.85rem;
      padding: 6px 9px;
    }
  }

  .btn-cta {
    color: white;

    &:after {
      content: " →";
    }

    @media (max-width: 800px) {
      font-size: 1rem;
    }

    @media (max-width: 450px) {
      font-size: 0.85rem;
    }
  }

  .cover__navigation-button-wrapper {
    position: absolute;
    z-index: 103;
    top: -550%;
  }
}

.swiper-button-next, .swiper-button-prev {
  top: -550%;
  color: #000;
  padding: 24px;
  font-size: 1.3rem;
  background: #fff;
  border: none;
  border-radius: 100%;

  &:after {
    content: none;
  }

  @media (max-width: 1200px) {
    top: 0;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  }
}

.swiper-button-next {
  right: 24%;
  left: auto;

  @media (max-width: 1200px) {
    right: 10%;
  }
}

.swiper-button-prev {
  left: 24%;
  right: auto;

  @media (max-width: 1200px) {
    left: 10%;
  }
}


