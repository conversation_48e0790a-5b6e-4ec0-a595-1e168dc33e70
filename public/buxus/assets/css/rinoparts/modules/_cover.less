.cover {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    text-align: center;
    background-position: center;
    background-repeat: no-repeat;
    background-size: auto 100%;
    background-color: #101010;
    overflow: hidden;

    .container {
        position: relative;
    }

    .cover-title {
        font-size: .rem(20px)[@value];
        margin-bottom: .25rem;
        color: #FFFFFF;

        .breakpoint(sm, {
            font-size: .rem(24px)[@value];
        });
    }

    .cover-sub-title {
        font-size: .rem(16px)[@value];
        color: #FFFFFF;

        .breakpoint(sm, {
            font-size: .rem(18px)[@value];
        });
    }
}

.home-cover {
    .cover();
    height: .rem(420px)[@value];
    padding: 3.5rem 0;

    .breakpointMax(sm, {
        height: .rem(440px)[@value];
        align-items: flex-start;
    });

    .container {
        .breakpoint(md, {
            margin-top: -4rem;
        });
    }

    .home-cover-image-wrap {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
        height: 100%;
        max-width: .rem(1600px)[@value];
        z-index: 0;
    }

    .homepage-cover-image {
        display: none;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        min-width: 100%;
        min-height: 100%;
        max-width: none;
        max-height: none;
        pointer-events: none;
        user-select: none;
    }

    .home-cover-image-desktop {
        .homepage-cover-image();

        left: 60%;
        margin-left: -10rem;

        .breakpoint(sm, {
            display: block;
        });

        .breakpoint(lg, {
            margin-left: 0;
            left: 50%;
            transform: translate(-50%, -50%);
        });
    }

    .home-cover-image-mobile {
        .homepage-cover-image();
        top: auto;
        bottom: -3%;
        transform: translate(-50%, 0);

        .breakpointMax(sm, {
            display: block;
        })
    }

    .cover-sub-title {
        margin-bottom: .rem(18px)[@value];

        .breakpoint(sm, {
            margin-bottom: 2rem;
        });
    }

    .cover-search {
        position: relative;
        display: flex;
        justify-content: center;
        max-width: .rem(460px)[@value];
        width: 100%;
        height: .rem(50px)[@value];
        margin: 0 auto;
        box-shadow: 0 .rem(6px)[@value] .rem(12px)[@value] 0 rgba(@brand-primary, .2);

        label {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            width: .rem(24px)[@value];
            padding: 0 .rem(12px)[@value];
            margin-bottom: 0;
            box-sizing: content-box;
            cursor: text;

            .icon {
                height: 100%;
                width: 100%;
            }
        }

        input {
            display: inline-block;
            width: 100%;
            height: auto;
            padding: .rem(12px)[@value] .rem(12px)[@value] .rem(12px)[@value] .rem(52px)[@value];
            border-radius: @border-radius-base 0 0 @border-radius-base;
            border: none;
            font-size: .rem(18px)[@value];
            font-weight: 600;
        }

        button {
            .btn-icon-right();
            padding-left: 1rem;
            padding-right: 1rem;
            border-radius: 0 @border-radius-base @border-radius-base 0;
            flex-shrink: 0;

            .home-cover-search-submit-text {
                .breakpointMax(sm, {
                    display: none;
                });
            }

            .icon {
                width: .rem(19px)[@value];
                fill: #FFFFFF;

                .breakpointMax(sm, {
                    margin-left: 0;
                });
            }
        }
    }
}

.subpage-cover {
    .cover();
    padding: 2rem 0;
    background-color: #111111;
    height: .rem(160px)[@value];

    .breakpoint(sm, {
        height: .rem(200px)[@value];
    });

    .cover-title {
        font-style: italic;
        font-weight: 700;
        font-size: .rem(22px)[@value];

        .breakpoint(sm, {
            font-size: .rem(28px)[@value];
        });

        em {
            display: inline-block;
            position: relative;
            z-index: 10;

            &::after {
                content: '';
                display: block;
                position: absolute;
                top: -.3rem;
                right: -.35rem;
                bottom: -.3rem;
                left: -.35rem;
                z-index: -1;
                background-color: rgba(@brand-primary, .6);
                transform: skew(-12deg, 0);

                .breakpoint(sm, {
                    top: -.4rem;
                    bottom: -.4rem;
                });
            }
        }
    }
}
