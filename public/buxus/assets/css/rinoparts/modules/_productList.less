.product-list {
    .product-list-header {
        background-color: #F4FAFB;
        padding-bottom: 1.5rem;
    }

    .product-list-item {
        display: flex;
        margin-bottom: .rem(26px)[@value];

        .product-list-item-link {
            display: block;
            width: 100%;
            padding: 1.4rem 1.3rem;
            background-color: #FFFFFF;
            box-shadow: 0 12px 24px 6px rgba(14,25,67,0.08);
            border-radius: 1rem;
            margin: 0 -3px;
            .transition();

            &:hover {
                transform: scale(1.033);
                box-shadow: 0 12px 24px 6px rgba(14,25,67,0.1);
            }
            color: @text-color;
            text-decoration: none;
        }

        .product-list-item-badge {
            display: inline-block;
            font-size: .rem(14px)[@value];
            font-style: italic;
            font-weight: 600;
            padding: .35rem .8rem;
            background-color: @gray-lighter;
            border-radius: 10rem;
            margin-bottom: .2rem;
        }

        img {
            display: block;
            margin: 0 auto 1.25rem;
        }

        .product-list-item-category {
            font-size: .rem(12px)[@value];
            font-weight: 600;
            margin-bottom: .25rem;
        }

        .product-list-item-title {
            margin-bottom: .75rem;
        }

        .product-list-item-price-title {
            font-size: .rem(14px)[@value];
            font-style: italic;
            color: @text-color-light;
            font-weight: 600;
            margin-bottom: .5rem;
        }

        .product-list-item-price {
            display: block;
            margin-bottom: .rem(10px)[@value];
        }

        .product-list-item-current-price {
            font-size: .rem(18px)[@value];
            margin-right: .rem(4px)[@value];
            font-weight: 700;
        }

        .product-list-item-original-price {
            font-size: .rem(14px)[@value];
            color: @text-color-light;
            font-weight: 500;
        }

        .product-list-item-availability {
            display: flex;
            align-items: center;
            font-weight: 500;

            .icon {
                width: 1.5rem;
                height: 1.5rem;
                margin-right: .rem(10px)[@value];
            }
        }

        .product-list-button {
            .btn();
            width: 100%;
            color: @text-color;
            font-weight: 600;
            border-radius: 10rem;
            border: 1px solid @gray-light;
            background-color: transparent;

            &:hover {
                background-color: @gray-light;
            }
        }
    }
}
