@page {
    margin-footer: 29px;
}

@media print {
    footer {
        position: fixed;
        bottom: 0;
    }
}

body {
    font-family: 'baijam<PERSON>e', sans-serif;
}

.container {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    box-sizing: border-box;
}

.row {
    width: 100%;
}

.col-1, .col-2, .col-3, .col-35, .col-4, .col-45, .col-5, .col-55, .col-6, .col-7, .col-8, .col-9, .col-10 {
    position: relative;
    float: left;
    box-sizing: border-box;
    display: inline-block !important;
}

.col-1 {
    width: 9%;
}

.col-2 {
    width: 19%;
}

.col-3 {
    width: 29%;
}

.col-35 {
    width: 33%;
}

.col-4 {
    width: 39%;
}

.col-45 {
    width: 44%;
}

.col-5 {
    width: 49%;
}

.col-55 {
    width: 54%;
}

.col-6 {
    width: 59%;
}

.col-7 {
    width: 69%;
}

.col-8 {
    width: 79%;
}

.col-9 {
    width: 89%;
}

.col-10 {
    width: 99%;
    float: none;
}

.text-left {
    text-align: left;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.w-100 {
    width: 100%;
}

strong {
    color: #DF0000;
}

.line-height-15 {
    line-height: 15px;
}

.font-size-12 {
    font-size: 12px;
}

.footer {
    position: absolute;
    height: 27px;
    left: 32px;
    top: 790px;

}

.footer {
    padding: 32px 0 0;
    margin: 0;
    color: #000;
}

.footer a {
    color: #DF0000;
    text-decoration: none;
}

table {
    border-collapse: collapse;
}

table tr {
    border: 1px none #e0e0e0;
    border-bottom-style: solid;
}

table td {
    text-align: center;
    max-width: fit-content;
}

table tr.heading-row {
    background: #F5F5F5;
}

table th, td {
    padding: 15px;
}

.items-summary {
    margin: 20px 0;
}

table tr.price-row {
    border: 1px none #e0e0e0;
}

.price-row td {
    padding-top: 25px;
    font-size: 13px;
}

.info {
    line-height: 5px;
    font-size: 12px;
}

.logo {
    margin-bottom: 20px;
}

p.date {
    color: #2F3538;
}

.description {
    line-height: 2px;
}

.company-info {
    line-height: 4px;
}

td img.item-image {
    width: 28px;
    height: 28px;
}

.text-black {
    color: #000;
}

.price-column {
    white-space: nowrap;
}


.cart-availability-tag {
    border-radius: 15px;
    padding: 0 10px;
    align-items: center;
    white-space: nowrap;
    margin: 5px;
    font-size: 14px;
    font-weight: 700;
    width: fit-content;

}

.cart-availability-tag.cart-availability-tag-stock {
    color: #007243;
}

.cart-availability-tag.cart-availability-tag-supplier {
    color: #ff8400;
}

.cart-availability-tag.cart-availability-tag-unavailable {
    color: #ff0000;
}

.cart-availability-tag .availability-state-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;
}

.cart-availability-tag .availability-state-dot.availability-state-stock {
    background: #00af50;
}

.cart-availability-tag .availability-state-dot.availability-state-supplier {
    background: #f98f17;
}

.cart-availability-tag .availability-state-dot.availability-state-unavailable {
    background: #f91717;
}